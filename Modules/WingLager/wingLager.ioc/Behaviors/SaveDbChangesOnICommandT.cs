using MediatR;
using wingLager.application.Contracts.IRequests;
using wingLager.data;

namespace wingLager.ioc.Behaviors;

public class SaveDbChangesOnICommandT<TRequest, TResponse>(WingLagerDbContext dbContext) : IPipelineBehavior<TRequest, TResponse>
    where TRequest : ICommand
{
    public async Task<TResponse> Handle(TRequest request, RequestHandlerDelegate<TResponse> next, CancellationToken cancellationToken)
    {
        var response = await next();
        await dbContext.SaveChangesAsync(cancellationToken);
        return response;
    }
}
