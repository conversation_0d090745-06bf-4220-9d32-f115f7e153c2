using System.Runtime.CompilerServices;
using System.Threading;
using System.Threading.Tasks;
using wingLager.application.Contracts;
using wingLager.data;

namespace wingLager.ioc.Middlewares;

public static class UnitOfWorkMiddleware
{
    [MethodImpl(MethodImplOptions.AggressiveInlining)]
    public static async Task AfterAsync(IUnitOfWork unityOfWork, CancellationToken cancellationToken)
    {
        await unityOfWork.SaveChangesAsync(cancellationToken);
    }
}
