using Dapper;
using Licencing;
using MassTransit;
using LindeFahrerlosTransportsystem;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using StateMachineMasstransit;
using wingLager.application.ComboBoxs;
using wingLager.application.Contracts;
using wingLager.application.Contracts.LindeQueueRepository;
using wingLager.application.Contracts.WarehouseManagementRepositories;
using wingLager.application.IServices.LindeQueueIService;
using wingLager.application.IServices.WarehouseManagementIServices;
using wingLager.data;
using wingLager.data.Repositories;
using wingLager.data.Repositories.LindeQueueRepository;
using wingLager.data.Repositories.WarehouseManagementRepositories;
using wingLager.data.Services;
using wingLager.data.Services.LindeQueueService;
using wingLager.data.Services.WarehouseManagementServices;
using wingLager.domain.WarehouseManagementModels.Status;
using wingLager.ioc.Behaviors;
using wingPrinterListLabel.application.Masstransit;

namespace wingLager.ioc;

public static class WingLagerConfiguration
{
    public static void AddWingLagerServices(this IServiceCollection serviceCollection)
    {
        /*
        var serviceProvider = serviceCollection.BuildServiceProvider();
        var configurationForAllMandantService = serviceProvider.GetService<IConfigurationForAllMandantService>();

        try
        {
            if(configurationForAllMandantService is not null &&
               !configurationForAllMandantService.CheckIfLicenseClaimIsSet(LicenseClaimType.ProcessOderHandlingValue).GetAwaiter().GetResult())
                return;
        }
        catch
        {
            return;
        }*/
        serviceCollection.AddScoped<IWingLagerDbContextAccessor, WingLagerDbContextAccessor>();
        serviceCollection.AddScoped<WingLagerDbContextFactory>();

        serviceCollection.AddScoped(provider => provider.GetRequiredService<WingLagerDbContextFactory>().CreateDbContext());
        serviceCollection.AddScoped<DbContext>(provider => provider.GetRequiredService<WingLagerDbContext>());

        serviceCollection.AddScoped<IUnitOfWork>(e => e.GetRequiredService<WingLagerDbContext>());

        serviceCollection.AddScoped<IProcessOrderHeaderRepository, ProcessOrderHeaderRepository>();
        serviceCollection.AddScoped<IProcessOrderPositionRepository, ProcessOrderPositionRepository>();
        serviceCollection.AddScoped<IPaletLagerRepository, PaletLagerRepository>();
        serviceCollection.AddScoped<IComboboxRepository, ComboboxRepository>();
        serviceCollection.AddScoped<IProcessArticleRepository, ProcessArticleRepository>();
        serviceCollection.AddScoped<IAuditLogRepository, AuditLogRepository>();
        serviceCollection.AddScoped<IWmHallRepository, WmHallRepository> ();
        serviceCollection.AddScoped<IWmEbeneRepository, WmEbeneRepository>();
        serviceCollection.AddScoped<IWmReiheRepository, WmReiheRepository>();
        serviceCollection.AddScoped<IWmFeldRepository, WmFeldRepository>();
        serviceCollection.AddScoped<IWmStellplatzRepository, WmStellplatzRepository>();
        serviceCollection.AddScoped<IWmVbrRepository, WmVbrRepository>();
        serviceCollection.AddScoped<IWmWepRepository, WmWepRepository>();
        serviceCollection.AddScoped<ILindeQueueRepository, LindeQueueRepository>();

        serviceCollection.AddScoped<IWmStellplatzService, WmStellplatzService>();
        serviceCollection.AddScoped<IWmEbeneService, WmEbeneService>();
        serviceCollection.AddScoped<IWmReiheService, WmReiheService>();
        serviceCollection.AddScoped<IWmFeldService, WmFeldService>();
        serviceCollection.AddScoped<IWmHallService, WmHallService>();
        serviceCollection.AddScoped<IWmBestandService, WmBestandService>();
        serviceCollection.AddScoped<ICreateLagerFromCsv, CreateLagerFromCsvService>();
        serviceCollection.AddScoped<IWmVbrService, WmVbrService>();
        serviceCollection.AddScoped<IWmWepService, WmWepService>();
        serviceCollection.AddScoped<ILindeQueueService, LindeQueueService>();
        serviceCollection.AddScoped<IWingLagerDbConnectionBuilder, WingLagerDbConnectionBuilder>();
        serviceCollection.AddScoped<IWmOrderService, WmOrderService>();
        SqlMapper.AddTypeHandler(new WmStellplatzStatusHandler());

        
        
        serviceCollection.AddScoped<ICheckComboBox, CheckComboBox>();
        
        serviceCollection.AddMediatR(mediatRServiceConfiguration =>
        {
            mediatRServiceConfiguration.RegisterServicesFromAssemblies(Assemblies.Application, typeof(WingLagerConfiguration).Assembly);
             mediatRServiceConfiguration.AddOpenBehavior(typeof(SaveDbChangesICommand<,>));
            mediatRServiceConfiguration.AddOpenBehavior(typeof(SaveDbChangesOnICommandT<,>));
        });

        

        
        serviceCollection.AddMassTransit(x =>
        {
            x.SetKebabCaseEndpointNameFormatter();

            x.AddSagaStateMachine<ProcessOrderPrintMachine, BackgroundState>()
                .EntityFrameworkRepository(r =>
                {
                    r.ExistingDbContext<WingLagerDbContext>();
                    r.ConcurrencyMode = ConcurrencyMode.Pessimistic;
                });

            x.UsingInMemory((context, cfg) =>
            {
                cfg.UseDelayedMessageScheduler();
                cfg.ConfigureEndpoints(context);
            });
        });
        
        //TODO add if License Lager is set the use this 
        //serviceCollection.AddHostedService<StartupTask>();
        
        serviceCollection.LindeFahrerlosTransportsystemApiConfiguration();
    }
}