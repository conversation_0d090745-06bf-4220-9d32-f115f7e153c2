using MassTransit;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using StateMachineMasstransit;
using wingLager.data;

namespace wingLager.ioc;
public class StartupTask(IBus bus, IServiceScopeFactory serviceScopeFactory, ILogger<StartupTask> logger) : IHostedService
{
    public async Task StartAsync(CancellationToken cancellationToken)
    {
        try
        {

            using var scope = serviceScopeFactory.CreateScope();
            var dbContext = scope.ServiceProvider.GetRequiredService<WingLagerDbContext>();
            var unfinishedSagas = dbContext.Set<BackgroundState>()
                .Where(s => s.CurrentState == "StartetPrint")
                .ToList();

            foreach (var saga in unfinishedSagas)
            {
                await bus.Publish(new ProcessPrinterForProcessOrderHeaderEvent(saga.CorrelationId, saga.Data), cancellationToken);
            }
        }
        catch (Exception e)
        {
            logger.LogError(e.InnerException?.Message ?? e.Message);
        }
    }

    public Task StopAsync(CancellationToken cancellationToken) => Task.CompletedTask;
}