using Moq;
using Shouldly;
using wingLager.application.Contracts.WarehouseManagementRepositories;
using wingLager.application.IServices.WarehouseManagementIServices;
using wingLager.application.WarehouseManagement.CommandHandler;
using wingLager.application.WarehouseManagement.Commands;
using wingLager.application.WarehouseManagement.Queries;
using wingLager.application.WarehouseManagement.QueryHandler;
using wingLager.domain.WarehouseManagementModels;

namespace wingLager.unitTests;

public class WmHallTest
{
    
    [Fact]
    public async Task CreateUpdateDeleteWmHall_WithCommandHandlers_MockedDependencies()
    {
        // Arrange
        var mockRepository = new Mock<IWmHallRepository>();
        var mockService = new Mock<IWmHallService>();
        

        mockRepository.Setup(r => r.AddAsync(It.IsAny<WmHall>(), It.IsAny<CancellationToken>()))
                      .Returns(Task.CompletedTask);

        mockRepository.Setup(r => r.UpdateAsync(It.IsAny<WmHall>(), It.IsAny<CancellationToken>()))
                      .Returns(Task.CompletedTask);

        mockRepository.Setup(r => r.DeleteAsync(It.IsAny<WmHall>(), It.IsAny<CancellationToken>()))
                      .Returns(Task.CompletedTask);

        var createHandler = new CreateWmHallCommandHandler(mockRepository.Object);
        var updateHandler = new UpdateWmHallCommandHandler(mockRepository.Object);
        var deleteHandler = new DeleteWmHallCommandHandler(mockRepository.Object);
        var queryHandler = new GetAllWmHallQueryHandler(mockService.Object);

        // Act
        var createCommand = new CreateWmHallCommand("Testhalle", "H-001", "Location A");
        var createdHall = await createHandler.Handle(createCommand, CancellationToken.None);
        var hallList = new List<WmHall> { createdHall };
        
        mockService.Setup(s => s.GetAllWithRowsAsync())
            .ReturnsAsync(hallList);

        // Assert Create
        Assert.NotNull(createdHall);
        createdHall.Description.ShouldBe("Testhalle");
        createdHall.Number.ShouldBe("H-001");
        createdHall.Location.ShouldBe("Location A");

        var queriedHalls = await queryHandler.Handle(new GetAllWmHallQuery(), CancellationToken.None);
        queriedHalls.ShouldContain(createdHall);

        // Act Update
        var updatedHall = createdHall with { Number = "H-002", Location = "Location B" };
        await updateHandler.Handle(new UpdateWmHallCommand(updatedHall), CancellationToken.None);

        // Simuliere aktualisierte Daten für den Query
        mockService.Setup(s => s.GetAllWithRowsAsync())
                   .ReturnsAsync(new List<WmHall> { updatedHall });

        var updatedList = await queryHandler.Handle(new GetAllWmHallQuery(), CancellationToken.None);
        var updated = updatedList.First();

        // Assert Update
        updated.Number.ShouldBe("H-002");
        updated.Location.ShouldBe("Location B");

        // Act Delete
        await deleteHandler.Handle(new DeleteWmHallCommand(updated.Id), CancellationToken.None);

        // Simuliere leere Liste nach Delete
        mockService.Setup(s => s.GetAllWithRowsAsync())
                   .ReturnsAsync(new List<WmHall>());

        var afterDelete = await queryHandler.Handle(new GetAllWmHallQuery(), CancellationToken.None);

        // Assert Delete
        afterDelete.ShouldBeEmpty();
    }
}