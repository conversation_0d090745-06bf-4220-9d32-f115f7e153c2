using WingCore.application.Contract.IModels;

namespace wingLager.unitTests;

public class ApplicationPath : IApplicationPath
{
    public string GetPath() => AppContext.BaseDirectory;
    public string GetStammDbPath()
    {
        return Path.Combine( GetPath(), "DBConfig");
    }
    public bool IAmBlazorServer() => false;
    public bool IAmMauiAppBlazor() => false;
    public bool IAmOpenApiServer() => false;
}