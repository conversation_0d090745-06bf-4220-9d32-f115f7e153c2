
using MediatR;
using wingLager.application.Contracts;
using wingLager.application.SearchParams;
using wingLager.domain.ComboBoxs;

namespace wingLager.application.ComboBoxs.Handlers;


public class GetAllComboBoxsQueryHandler(IComboboxRepository iComboboxRepository) : IRequestHandler<GetAllComboBoxsQuery,IEnumerable<Combobox>>
{
    public async Task<IEnumerable<Combobox>> <PERSON>le(GetAllComboBoxsQuery query, CancellationToken cancellationToken)
    {
        var newObject = await iComboboxRepository.GetAllAsync(false,new ComboBoxSearchParam() );
        
        return newObject ?? [];
    }
}


public class GetAllComboBoxByTypeQueryHandler(IComboboxRepository iComboboxRepository) : IRequestHandler<GetAllComboBoxByTypeQuery,IEnumerable<Combobox>>
{
    public async Task<IEnumerable<Combobox>> <PERSON>le(GetAllComboBoxByTypeQuery query, CancellationToken cancellationToken)
    {
        var newObject = await iComboboxRepository.GetAllAsync(false,new ComboBoxSearchParam { Type = query.ComboboxType });
        
        return newObject ?? [];
    }
}

public class GetComboBoxQueryHandler(IComboboxRepository iComboboxRepository) : IRequestHandler<GetComboBoxQuery,Combobox?>
{
    public async Task<Combobox?> Handle(GetComboBoxQuery query, CancellationToken cancellationToken)
    {
        var newObject = await iComboboxRepository.GetAllAsync(false, new ComboBoxSearchParam { Id = query.Id });

        return newObject?.FirstOrDefault();
    }
}