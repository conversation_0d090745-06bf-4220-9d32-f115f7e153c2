using Commons.ComboBoxs;
using wingLager.application.Contracts;

namespace wingLager.application.ComboBoxs;

public class CheckComboBox : ICheckComboBox
{
    public ValueTask CheckDescription(string description)
    {
        if (string.IsNullOrWhiteSpace(description))
            throw new Exception("Beschreibung darf nicht leer sein.");
        
        return ValueTask.CompletedTask;
    }

    public ValueTask CheckType(ComboboxType type)
    {
        if (string.IsNullOrWhiteSpace(type.Value))
            throw new Exception("Type darf nicht leer sein.");
        
        if(ComboboxType.PossibleComboboxTypesForWingLager.FirstOrDefault(e => e.Value == type.Value) is null)
            throw new Exception($"{type.Value} ist für die Schnittstelle nicht zulässig.");
        
        return ValueTask.CompletedTask;
    }

    public ValueTask CheckKey(string key)
    {
        if (string.IsNullOrWhiteSpace(key))
            throw new Exception("Index darf nicht leer sein.");

        return ValueTask.CompletedTask;
    }

    public ValueTask CheckValue(string value)
    {
        if (string.IsNullOrWhiteSpace(value))
            throw new Exception("Beschreibung darf nicht leer sein.");

        return ValueTask.CompletedTask;
    }
}