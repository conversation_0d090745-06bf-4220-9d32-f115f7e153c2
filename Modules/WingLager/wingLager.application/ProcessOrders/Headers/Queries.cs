using MediatR;
using wingLager.application.SearchParams;
using wingLager.domain.ProcessOrders;

namespace wingLager.application.ProcessOrders.Headers;


public sealed record ProcessOrdersHeaderQuery(long ProcessOrderNumber, bool WithAllData) : IRequest<ProcessOrderHeader?>;
public sealed record ProcessOrdersHeadersQuery(OrderProcessHeaderSearchParam SearchParam) : IRequest<IEnumerable<ProcessOrderHeader>>;

