using MediatR;
using wingLager.domain.Common;
using wingLager.domain.ProcessOrders;

namespace wingLager.application.ProcessOrders.Headers.Handlers;

public class CreateWithOutSaveProcessOrderHeaderCommandHandler() : IRequestHandler<CreateWithOutSaveProcessOrderHeaderCommand, ProcessOrderHeader>
{
    public Task<ProcessOrderHeader> Handle(CreateWithOutSaveProcessOrderHeaderCommand command, CancellationToken cancellationToken)
    {
        var userName = "";
        ProcessOrderHeader processOrderHeader = new()
        {
            Description = command.SelectedMainArticle.ArtBezText1 ?? string.Empty,
            ArticleNumber = command.SelectedMainArticle.Artikelnr,
            BeginnDateTime = DateTime.Now,
            EndDateTime = DateTime.Now,
            WeightInKg = command.MaxWeightIngKg,
            StartFirstPrintDateTime = DateTime.Now,
            Status = ProcessOrderHeaderStatus.Open,
            CreationUser = userName,
            CreatedAt = DateTime.UtcNow,
            UpdateUser = userName,
            UpdatedAt = DateTime.UtcNow,
            Positionen = []
        };

        var diffWeightInKg = command.MaxWeightIngKg;
        foreach (var order in command.OrderHeaderWithPos)
        {
            foreach (var orderPos in order.Value)
            {
                var processOrderPos = new ProcessOrderPosition
                                                   {
                                                       OrderNumber = order.Key.Auftragsnummer ?? 0,
                                                       OrderPostionId = orderPos.Id,
                                                       Number = string.Empty,
                                                       ArticleNumber = orderPos.ArtikelNummer ?? 0,
                                                       WeightInKg = orderPos.GesMenge ?? 0,
                                                       WarenEmpfaengerName = order.Key.WarenEmpfaenger.FullName ?? string.Empty,
                                                       CreationUser = userName,
                                                       CreatedAt = DateTime.UtcNow,
                                                       UpdateUser = userName,
                                                       UpdatedAt = DateTime.UtcNow
                                                   };
                diffWeightInKg -= processOrderPos.WeightInKg;
                processOrderHeader.Positionen.Add(processOrderPos);
            }
        }

        
        //Pseudo positionen erstellen
        if (diffWeightInKg > 0)
        {
            processOrderHeader.Positionen.Add(new ProcessOrderPosition
            {
                OrderNumber = 0,
                OrderPostionId = 0,
                Number = string.Empty,
                ArticleNumber = command.SelectedMainArticle.Artikelnr,
                WeightInKg = diffWeightInKg,
                WarenEmpfaengerName = "NO NAME",
                CreationUser = userName,
                CreatedAt = DateTime.UtcNow,
                UpdateUser = userName,
                UpdatedAt = DateTime.UtcNow,
                Description = "Pseudo"
            });
        }
        
        return Task.FromResult(processOrderHeader);
    }
}
