using MediatR;
using WingCore.application.Contract.Services;
using wingLager.application.Contracts;
using wingLager.domain.ProcessOrders;

namespace wingLager.application.ProcessOrders.Headers.Handlers;

public class DeleteProcessOrderHeaderCommandHandler(IProcessOrderHeaderRepository processOrderHeaderRepository,
                                                    IOrderPosService orderPosService,
                                                    IUnitOfWork iUnitOfWork) : IRequestHandler<DeleteProcessOrderHeaderCommand>
{
    public async Task Handle(DeleteProcessOrderHeaderCommand command, CancellationToken cancellationToken)
    {
        var allOrderPostionsId = await processOrderHeaderRepository.GetAllOrderPostionsId(command.ProcessOrderHeaderId);
        
        await processOrderHeaderRepository.Delete(command.ProcessOrderHeaderId);
        
        if(allOrderPostionsId is not null)
            await orderPosService.RemoveFlagProcessOrderCreatedAsync(allOrderPostionsId.ToList());
        await iUnitOfWork.SaveChangesAsync(CancellationToken.None);
    }
}
