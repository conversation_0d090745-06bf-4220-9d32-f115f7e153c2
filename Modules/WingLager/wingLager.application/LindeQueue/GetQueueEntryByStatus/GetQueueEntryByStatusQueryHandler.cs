using MediatR;
using wingLager.application.IServices.LindeQueueIService;
using wingLager.domain.LindeQueueModel;

namespace wingLager.application.LindeQueue.GetQueueEntryByStatus;

public class GetQueueEntryByStatusQueryHandler(ILindeQueueService service) : IRequestHandler<GetQueueEntryByStatusQuery, ICollection<LindeQueueEntry>>
{
    public async Task<ICollection<LindeQueueEntry>> Handle(GetQueueEntryByStatusQuery request, CancellationToken cancellationToken)
    {
        return await service.GetByStatusAsync(request.Status);
    }
}