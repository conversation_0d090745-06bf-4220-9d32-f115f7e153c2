using MediatR;
using wingLager.application.IServices.LindeQueueIService;
using wingLager.domain.LindeQueueModel;

namespace wingLager.application.LindeQueue.GetQueueEntryById;

public record GetQueueEntryByIdQueryHandler(ILindeQueueService Service) : IRequestHandler<GetQueueEntryByIdQuery, LindeQueueEntry>
{
    public async Task<LindeQueueEntry> Handle(GetQueueEntryByIdQuery request, CancellationToken cancellationToken)
    {
        var entry = await Service.GetByIdAsync(request.Id);
        if (entry is null)
            throw new Exception("LindeQueueEntry not found");
        return entry;
    }
}