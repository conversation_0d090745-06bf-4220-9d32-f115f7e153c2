using MediatR;
using wingLager.application.Contracts.LindeQueueRepository;

namespace wingLager.application.LindeQueue.SetQueueEntryStatus;

public class SetQueueEntryStatusCommandHandler(ILindeQueueRepository repository): IRequestHandler<SetQueueEntryStatusCommand>
{
    public async Task Handle(SetQueueEntryStatusCommand request, CancellationToken cancellationToken)
    {
        await repository.UpdateStatusAsync(request.Id, request.Status, cancellationToken);
    }
}