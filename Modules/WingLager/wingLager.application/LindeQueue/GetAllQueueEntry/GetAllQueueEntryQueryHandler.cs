using MediatR;
using wingLager.application.IServices.LindeQueueIService;
using wingLager.domain.LindeQueueModel;

namespace wingLager.application.LindeQueue.GetAllQueueEntry;

public record GetAllQueueEntryQueryHandler(ILindeQueueService Service) : IRequestHandler<GetAllQueueEntryQuery, ICollection<LindeQueueEntry>>
{
    public async Task<ICollection<LindeQueueEntry>> <PERSON><PERSON>(GetAllQueueEntryQuery request, CancellationToken cancellationToken)
    {
        return await Service.GetAllAsync();
    }
}