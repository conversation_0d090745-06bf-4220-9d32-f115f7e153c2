using MediatR;
using wingLager.application.Contracts;
using wingLager.application.Contracts.LindeQueueRepository;
using wingLager.application.Contracts.WarehouseManagementRepositories;
using wingLager.application.IServices.WarehouseManagementIServices;
using wingLager.domain.LindeQueueModel;
using wingLager.domain.WarehouseManagementModels.Status;

namespace wingLager.application.LindeQueue.CreateQueueEntry;

public class CreateComissioningLindeQueueEntryCommandHandler(ILindeQueueRepository lindeQueueRepositoryrepository, IWmStellplatzService stellplatzService, IWmStellplatzRepository stellplatzRepository, IPaletLagerRepository paletLagerRepository) : IRequestHandler<CreateCommissioningLindeQueueEntryCommand, LindeQueueEntry>
{
    public async Task<LindeQueueEntry> Handle(CreateCommissioningLindeQueueEntryCommand request, CancellationToken cancellationToken)
    {
        var (paletLagerId,endlocation, hallenPositionInfo) = request;
        var paletLager = (await paletLagerRepository.GetByIdAsync(paletLagerId, false)).FirstOrDefault();

        if (paletLager is null)
            throw new Exception("PaletLager not found");;
        paletLager.ClearLinde();
        paletLager.FlagsWorker.SetLindeQueueCreatedFlag();
        await paletLagerRepository.UpdateWithoutTracking(paletLager, "", cancellationToken);
        var lindeQueueEntry = paletLager.ToLindeQueueEntry(hallenPositionInfo.HallenPosition, endlocation);
        await lindeQueueRepositoryrepository.AddAsync(lindeQueueEntry, cancellationToken);
        
        return lindeQueueEntry;
    }
}