
using wingLager.domain.LindeQueue;
using wingLager.domain.LindeQueueModel;

namespace wingLager.application.IServices.LindeQueueIService;

public interface ILindeQueueService
{
    Task<ICollection<LindeQueueEntry>> GetAllAsync();
    Task<LindeQueueEntry?> GetByIdAsync(Guid requestId);
    Task<ICollection<LindeQueueEntry>> GetByStatusAsync(LindeQueueStatus status);
    Task<ICollection<LindeQueueEntry>> GetAllOpenEntries();
}