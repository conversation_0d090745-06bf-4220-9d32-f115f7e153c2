using MediatR;
using wingLager.domain.Api.DTO;
using wingLager.domain.PaletLagers;
using wingLager.domain.ProcessOrders;
using wingLager.domain.WarehouseManagementModels;

namespace wingLager.application.PaletLagers;


public record GetPaletLagerEntriesQuery : IRequest<IEnumerable<PaletLagerResponse>>;
public record GetPaletLagerEntriesFromOrderHeaderIdQuery(long OrderHeaderId) : IRequest<IEnumerable<PaletLager>>;
public record GetPalletTypeOfPaletLagerQuery(long Id) : IRequest<string>;
public record GetProcessOrderHeaderOfPaletLaderQuery(long Id) : IRequest<ProcessOrderHeader>;
public record GetStellplatzOfPalletLagerQuery(long Id) : IRequest<WmStellplatz>;

