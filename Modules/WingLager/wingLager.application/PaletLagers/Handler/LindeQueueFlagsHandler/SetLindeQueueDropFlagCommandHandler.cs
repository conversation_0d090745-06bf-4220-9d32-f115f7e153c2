using MediatR;
using wingLager.application.Contracts;
using wingLager.domain.PaletLagers;

namespace wingLager.application.PaletLagers.Handler.LindeQueueFlagsHandler;

public class SetLindeQueueDropFlagCommandHandler(IPaletLagerRepository repository) : IRequestHandler<SetLindeQueueDropFlagCommand, PaletLager>
{
    public async Task<PaletLager> Handle(SetLindeQueueDropFlagCommand request, CancellationToken cancellationToken)
    {
        var palletLager = await repository.GetByIdWithoutTrackingAsync(request.Id);
        if (palletLager is null)
            throw new Exception($"PaletLager with Id {request.Id} not found");
        palletLager.FlagsWorker.SetLindeQueueDropFlag();
        await repository.UpdateWithoutTracking(palletLager, "", cancellationToken);
        return palletLager;
    }
}