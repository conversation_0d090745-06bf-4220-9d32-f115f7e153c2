using MediatR;
using wingLager.application.Contracts;
using wingLager.domain.PaletLagers;

namespace wingLager.application.PaletLagers.Handler.LindeQueueFlagsHandler;

public class SetLindeQueueWaitingFlagCommandHandler(IPaletLagerRepository repository) : IRequestHandler<SetLindeQueueWaitingFlagCommand, PaletLager>
{
    public async Task<PaletLager> Handle(SetLindeQueueWaitingFlagCommand request, CancellationToken cancellationToken)
    {
        var palletLager = await repository.GetByIdWithoutTrackingAsync(request.Id);
        if (palletLager is null)
            throw new Exception($"PaletLager with Id {request.Id} not found");
        palletLager.FlagsWorker.SetLindeQueueWaitingFlag();
        await repository.UpdateWithoutTracking(palletLager, "", cancellationToken);
        return palletLager;
    }
}