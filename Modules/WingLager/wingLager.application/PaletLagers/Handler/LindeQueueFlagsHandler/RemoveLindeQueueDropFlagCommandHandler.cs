using MediatR;
using wingLager.application.Contracts;
using wingLager.domain.PaletLagers;

namespace wingLager.application.PaletLagers.Handler.LindeQueueFlagsHandler;

public class RemoveLindeQueueDropFlagCommandHandler(IPaletLagerRepository repository) : IRequestHandler<RemoveLindeQueueDropFlagCommand, PaletLager>
{
    public async Task<PaletLager> Handle(RemoveLindeQueueDropFlagCommand request, CancellationToken cancellationToken)
    {
        var palletLager = await repository.GetByIdWithoutTrackingAsync(request.Id);
        if (palletLager is null)
            throw new Exception($"PaletLager with Id {request.Id} not found");
        palletLager.FlagsWorker.RemoveLindeQueueDropFlag();
        await repository.UpdateWithoutTracking(palletLager, "", cancellationToken);
        return palletLager;
    }
}