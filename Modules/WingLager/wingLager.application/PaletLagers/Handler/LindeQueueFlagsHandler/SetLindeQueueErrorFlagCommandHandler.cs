using MediatR;
using wingLager.application.Contracts;
using wingLager.domain.PaletLagers;

namespace wingLager.application.PaletLagers.Handler.LindeQueueFlagsHandler;

public class SetLindeQueueErrorFlagCommandHandler(IPaletLagerRepository repository) : IRequestHandler<SetLindeQueueErrorFlagCommand, PaletLager>
{
    public async Task<PaletLager> Handle(SetLindeQueueErrorFlagCommand request, CancellationToken cancellationToken)
    {
        var paletLager =  await repository.GetByIdWithoutTrackingAsync(request.Id);
        if (paletLager is null)
            throw new Exception($"PaletLager with Id {request.Id} not found");
        paletLager.FlagsWorker.SetLindeQueueErrorFlag();
        await repository.UpdateWithoutTracking(paletLager, "", cancellationToken);
        return paletLager;
    }
}