using MediatR;
using wingLager.application.Contracts;
using wingLager.domain.PaletLagers;

namespace wingLager.application.PaletLagers.Handler;

public class RemoveFlagPrintedStartPaletLagerCommandHandler(IPaletLagerRepository paletLagerRepository, IUnitOfWork unitOfWork) : IRequestHandler<RemoveFlagPrintedStartPaletLagerCommand,PaletLager>
{
    public async Task<PaletLager> Handle(RemoveFlagPrintedStartPaletLagerCommand request, CancellationToken cancellationToken)
    {
        var result = await paletLagerRepository.RemoveFlagPrintedStartPaletLager(request.Id);

        if (result is null)
            throw new Exception($"PaletLager with Id {request.Id} not found");
        
        await unitOfWork.SaveChangesAsync(cancellationToken);
        unitOfWork.Detach(result);
        
        return result;
    }
}