using MediatR;
using wingLager.application.Contracts;
using wingLager.domain.PaletLagers;

namespace wingLager.application.PaletLagers.Handler;

public class SetFlagPrintedStartPaletLagerCommandHandler(IPaletLagerRepository paletLagerRepository, IUnitOfWork unitOfWork) : IRequestHandler<SetFlagPrintedStartPaletLagerCommand,PaletLager>
{
    public async Task<PaletLager> Handle(SetFlagPrintedStartPaletLagerCommand request, CancellationToken cancellationToken)
    {
        var result = await paletLagerRepository.SetFlagPrintedStartPaletLager(request.Id);

        if (result is null)
            throw new Exception($"PaletLager with Id {request.Id} not found");
        
        await unitOfWork.SaveChangesAsync(cancellationToken);
        unitOfWork.Detach(result);
        
        return result;
    }
}