using MediatR;
using wingLager.application.Contracts;

namespace wingLager.application.PaletLagers.Handler;

public class SetManualUnstoreFlagCommandHandler(IPaletLagerRepository paletLagerRepository): IRequestHandler<SetManualUnstoredFlagCommand>
{
    public async Task Handle(SetManualUnstoredFlagCommand request, CancellationToken cancellationToken)
    {
        var paletLager = await paletLagerRepository.GetByIdWithoutTrackingAsync(request.Id);
        if (paletLager is null)
            throw new Exception($"PaletLager with Id {request.Id} not found");
        paletLager.FlagsWorker.RemoveStoredInWareHouseFlag();
        paletLager.FlagsWorker.SetManualUnStoredFlag();
        await paletLagerRepository.UpdateWithoutTracking(paletLager, "", cancellationToken);
    }
}