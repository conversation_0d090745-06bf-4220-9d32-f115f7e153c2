using Microsoft.EntityFrameworkCore.ChangeTracking;

namespace wingLager.application.Contracts;

public interface IAuditLogRepository
{
    void AddLogChanges(object currObject, string userName);
    void AddLogChanges(object currObject, string action, string userName);
    void AddLogChanges(EntityEntry oldObject, string userName);
    void AddLogChanges(EntityEntry oldObject, EntityEntry newObject, string userName);
    void AddLogChanges(EntityEntry oldObject, string action, string userName);
    void AddLogChanges(EntityEntry oldObject, string action, string modifiedData ,string userName);
}
