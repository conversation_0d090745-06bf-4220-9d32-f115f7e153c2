using wingLager.domain.ProcessOrders;

namespace wingLager.application.Contracts;

public interface IProcessOrderPositionRepository
{
    Task<IEnumerable<ProcessOrderPosition>?> GetAllAsync(bool trackChanges = false);
    Task<IEnumerable<ProcessOrderPosition>?> GetAllWithProcessArticleIdAsync(long processArticleId);
    Task<IEnumerable<ProcessOrderPosition>?> GetByIdAsync(long number, bool trackChanges);
    Task<IEnumerable<ProcessOrderPosition>?> GetByNumberAsync(long id, bool trackChanges);
    Task<IEnumerable<ProcessOrderPosition>?> GetByHeaderNumberAsync(long headerNumber, bool trackChanges);
    Task Update(ProcessOrderPosition processOrderPosition, string userName);
    Task DeleteWIthSameHeaderNumber(long headerNumber);
}
