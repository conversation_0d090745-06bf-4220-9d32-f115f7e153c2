using wingLager.application.SearchParams;
using wingLager.domain.PaletLagers;
using wingLager.domain.ProcessOrders;
using wingLager.domain.WarehouseManagementModels;

namespace wingLager.application.Contracts;

public interface IPaletLagerRepository
{
    Task SetNextSsccNrUpdateAndSave(PaletLager paletLager, string userName);
    Task Update(PaletLager paletLager, string userName);
    Task UpdateWithoutTracking(PaletLager paletLager, string userName, CancellationToken cancellationToken = default);
    Task Delete(long paletLagerId);
    Task<long> Create(PaletLager paletLager, string username);
    Task CreatePaletLagerWithoutTracking(PaletLager paletLager, string userName);
    Task<IEnumerable<PaletLager>?> GetAllAsync(bool trackChanges, PaletLagerSearchParam searchParam);
    Task<IEnumerable<PaletLager>?> GetByIdAsync(long id, bool trackChanges);
    Task<PaletLager?> GetByIdWithoutTrackingAsync(long id);
    Task<ProcessOrderHeader> GetProcessOrderHeaderOfPaletLager(long id);
    Task<WmStellplatz> GetStellplatzOfPalletLager(long number);
    Task<PaletLager?> SetFlagPrintedStartPaletLager(long id);
    Task<PaletLager?> RemoveFlagPrintedStartPaletLager(long id);
    Task<string?> GetPalletTypeOfPaletLager(long id);
    Task SaveChangesAsync();
    
}

public class Stellplatz
{
}