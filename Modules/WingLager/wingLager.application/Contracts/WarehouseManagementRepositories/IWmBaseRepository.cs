using wingLager.domain.Common;

namespace wingLager.application.Contracts.WarehouseManagementRepositories;

public interface IWmBaseRepository<T> where T : BaseData
{
    ValueTask<T?> GetAsync(Guid id, CancellationToken token = default);
    Task UpdateAsync(T entity, CancellationToken token = default);
    Task UpdateRangeAsync(IEnumerable<T> entities, CancellationToken token = default);
    Task AddAsync(T entity, CancellationToken token = default);
    Task AddRangeAsync(IEnumerable<T> entities, CancellationToken token = default);
    Task DeleteAsync(T entity, CancellationToken token = default);
}