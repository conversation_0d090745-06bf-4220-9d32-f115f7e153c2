using wingLager.application.SearchParams;
using wingLager.domain.Common;
using wingLager.domain.PaletLagers;
using wingLager.domain.ProcessOrders;

namespace wingLager.application.Contracts;

public interface IProcessOrderHeaderRepository
{
    Task<IEnumerable<ProcessOrderHeader>?> GetAllAsync(bool trackChanges, OrderProcessHeaderSearchParam searchParam);
    Task<ICollection<ProcessOrderHeader>> GetAllByStatusAsync(ProcessOrderHeaderStatus status);
    Task<IEnumerable<long>?> GetAllOrderPostionsId(long id);
    Task<IEnumerable<ProcessOrderHeader>?> GetByNumberAsync(long number, bool trackChanges);
    Task<ProcessOrderHeader?> GetByIdAsync(long id, bool trackChanges);
    Task<ProcessOrderHeader?> GetByIdWithoutTrackingAsync(long id);
    Task<ICollection<PaletLager>> GetAllPalletLagerOfProcessOrderHeader(long id);
    Task<ProcessOrderHeader> Create(ProcessOrderHeader newProcessOrderHeader, string userName);
    Task Update(ProcessOrderHeader processOrderHeader, string userName, bool withOutTracking = false);
    Task Delete(long processOrderHeaderId);
    Task SetDataForFirstPrintAndSave(long processOrderHeaderNumber);
    Task SetDataForIsPrintedAndSave(long processOrderHeaderNumber);
}