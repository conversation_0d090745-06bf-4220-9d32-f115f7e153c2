using PrinterService;
using WingCore.domain.Models;
using wingLager.domain.PaletLagers;

namespace wingLager.application.PrintableDtos;

public record PalletNoteDto : IPrintObject{
    
    public List<PaletLager> PaletLagerList { get; set; } = [];
    public Kunden Kunde { get; set; }
    public Auftragskopf Auftragskopf { get; set; }
    public List<Auftragspo> Auftragspos { get; set; } = [];
    public string PrintType() => "PS_";

    public string ObjName() => "PalletNoteDto";

    public static PalletNoteDto Create(List<PaletLager> lagerList, Kunden? requestCommissionCustomer, Auftragskopf auftragskopf, List<Auftragspo> auftragspos)
    {
        return new PalletNoteDto
        {
            PaletLagerList = lagerList,
            
            Kunde =  requestCommissionCustomer ?? new Kunden
            {
                Kdname1 = string.Empty,
                Kdname2 = string.Empty,
                Kdname3 = string.Empty,
                Kdstrasse = string.Empty,
                Kdplz = string.Empty,
                Kdort = string.Empty,
                Kdland = string.Empty,
                Kdtelefon1 = string.Empty,
                Kdemail = string.Empty
            },
            Auftragskopf = auftragskopf,
            Auftragspos = auftragspos

        };
    }

    public static PalletNoteDto Dummy => new PalletNoteDto
    {
        Kunde = new Kunden
        {
            Kdname1 = "Müller GmbH & Co. KG",
            Kdname2 = "Traditionsbäckerei",
            Kdname3 = "z.Hd. Herr Wolfgang Müller",
            Kdstrasse = "Backstraße 42",
            Kdplz = "12345",
            Kdort = "Mehlhausen",
            Kdland = "Deutschland",
            Kdtelefon1 = "+49 123 456789",
            Kdemail = "<EMAIL>",
            Kdnummer = 45678
        },
        Auftragskopf = new Auftragskopf
        {
            Auftragsnummer = 9257834,
            Bestellnr = "2024-B123456",
            AStatus = "status",
            Eldatum = DateTime.Now.AddDays(10),
            UhrzeitTxt = "jetzt",
            GesGew = 4350.5,
            KundenNrWE = 12309123,
            NrtelefonWe = "92378045",
        },
        Auftragspos = new List<Auftragspo>
{
    new Auftragspo
    {
        Auftragsnummer = 1001,
        Status = "Neu",
        GridPos = 1,
        Erfassungsschema = "Standard",
        ArtikelNummer = 12345,
        Bezeichnung = "Testprodukt 1",
        Anzahl = 100,
        BezGr = "ST",
        GesStck = 100,
        GesMenge = 100.0m,
        Epreis = 9.99m,
        Gpreis = 999.00m,
        Mwst = 19.0m,
        Faktor = "1.0m",
        ChargenNr = "*********",
        KontraktNr = 2025-001,
        StreckenNr = 101,
        Mhd = new DateTime(2025, 12, 31).ToLongDateString(),
        Wgsnr = 001,
        Fbzgnr = 001,
        Mwst2 = 0.0m,
        VerpckNr = 001,
        ArtikelNummer2 = 12345,
        Selektionskennung = "SEL1",
        AlteMenge = 0.0m,
        LsdepotNr = 001,
        Fbneu = "true",
        Pfracht = 25.0m,
        Baeckerzelle = "BZ01",
        Lkwkammer = "LK01",
        Muehlenzelle = "MZ01",
        Rabatt = "0.0m",
        GpreisRab = 999.00m,
        NuebTexte = "Testnotiz",
        KdtxTcheck = true,
        AvisAnzahl = 1,
        FilialNr = 001,
        LagerNr = 001,
        Nvobilanziert = false,
        NvobilanzNr = null,
        Nvonr = null,
        Nvo = false,
        ArtEinhVp = 2,
        PakGewicht = 10.5m,
        PalGew = 800.0m,
        KolliInh = 10,
        PalArt = "EUR",
        AltNr = 001,
        Id = 1,
        LoeschKng = false,
        Eannr = "*************",
        PosNrBest = "P001",
        BestNr = "B2025001",
        EdiQalifier = 91,
        ProduktionsvorgangId = 12345,
        ZtxtKng = false
    },
    new Auftragspo
    {
        Auftragsnummer = 1001,
        Status = "Neu",
        GridPos = 2,
        Erfassungsschema = "Standard",
        ArtikelNummer = 98765,
        Bezeichnung = "Testprodukt 2",
        Anzahl = 50,
        BezGr = "KG",
        GesStck = 50,
        GesMenge = 50.0m,
        Epreis = 19.99m,
        Gpreis = 999.50m,
        Mwst = 19.0m,
        Faktor = "1",
        ChargenNr = "CH2025002",
        KontraktNr = 2025-001,
        StreckenNr = 101,
        Mhd = new DateTime(2025, 12, 31).ToLongDateString(),
        Wgsnr = 002,
        Fbzgnr = 002,
        Mwst2 = 0.0m,
        VerpckNr = 002,
        ArtikelNummer2 = 98765,
        Selektionskennung = "SEL2",
        AlteMenge = 0.0m,
        LsdepotNr = 001,
        Fbneu = "true",
        Pfracht = 25.0m,
        Baeckerzelle = "BZ02",
        Lkwkammer = "LK01",
        Muehlenzelle = "MZ02",
        Rabatt = "5.0m",
        GpreisRab = 949.53m,
        NuebTexte = "Testnotiz 2",
        KdtxTcheck = true,
        AvisAnzahl = 1,
        FilialNr = 001,
        LagerNr = 001,
        Nvobilanziert = false,
        NvobilanzNr = null,
        Nvonr = null,
        Nvo = false,
        ArtEinhVp =1,
        PakGewicht = 25.0m,
        PalGew = 1000.0m,
        KolliInh = 20,
        PalArt = "EUR",
        AltNr = 002,
        Id = 2,
        LoeschKng = false,
        Eannr = "*************",
        PosNrBest = "P002",
        BestNr = "B2025001",
        EdiQalifier = 91,
        ProduktionsvorgangId = 12346,
        ZtxtKng = false
    }
},
        PaletLagerList = new List<PaletLager>
        {
            new PaletLager
            {
                NVENr = "NVE100123",
                ArtBez1 = "Premium Mehl Type 405", 
                ChargNr = "1001",
                Gewicht = 1000.0m,
                MHD = DateTime.Now.AddMonths(12),
                AnzSack = 20,
                KundNrWE = 45678
            },
            new PaletLager
            {
                NVENr = "NVE100124",
                ArtBez1 = "Bio Dinkelvollkornmehl",
                ChargNr = "1002", 
                Gewicht = 750.5m,
                MHD = DateTime.Now.AddMonths(8),
                AnzSack = 15,
                KundNrWE = 45679
            },
            new PaletLager
            {
                NVENr = "NVE100125",
                ArtBez1 = "Roggenmehl Type 1150",
                ChargNr = "1003",
                Gewicht = 1250.0m,
                MHD = DateTime.Now.AddMonths(10),
                AnzSack = 25,
                KundNrWE = 45680
            },
            new PaletLager
            {
                NVENr = "NVE100126",
                ArtBez1 = "Weizengrieß",
                ChargNr = "1004",
                Gewicht = 500.0m,
                MHD = DateTime.Now.AddMonths(15),
                AnzSack = 10,
                KundNrWE = 45681
            },
            new PaletLager
            {
                NVENr = "NVE100127",
                ArtBez1 = "Bio Backschrot",
                ChargNr = "1005",
                Gewicht = 850.0m,
                MHD = DateTime.Now.AddMonths(6),
                AnzSack = 17,
                KundNrWE = 45682
            }
        }
    };
}