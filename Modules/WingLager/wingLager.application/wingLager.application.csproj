<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>
    
    <ItemGroup>
      <ProjectReference Include="..\..\..\libs\ExternalInterfaces\LindeFahrerlosTransportsystem\LindeFahrerlosTransportsystem.csproj" />
      <ProjectReference Include="..\..\WingCore\WingCore.application\WingCore.application.csproj" />
      <ProjectReference Include="..\wingLager.domain\wingLager.domain.csproj" />
      <ProjectReference Include="..\..\..\libs\StateMachineMasstransit\StateMachineMasstransit.csproj" />
    </ItemGroup>
    
    <ItemGroup>
      <PackageReference Include="MediatR" Version="[12.5.0]" />
    </ItemGroup>

    <ItemGroup Condition="!$([MSBuild]::IsOSPlatform('Windows'))">
        <Reference Include="PrinterService">
            <HintPath>..\..\..\PrinterService\PublishDlls\PrinterService.dll</HintPath>
            <Private>true</Private>
        </Reference>
    </ItemGroup>

    <ItemGroup Condition="$([MSBuild]::IsOSPlatform('Windows'))">
        <ProjectReference Include="..\..\..\PrinterService\PrinterService.csproj" />
    </ItemGroup>

</Project>
