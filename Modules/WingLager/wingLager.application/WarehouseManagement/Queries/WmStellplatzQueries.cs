using MediatR;
using wingLager.application.Contracts.IRequests;
using wingLager.domain.WarehouseManagementModels;

namespace wingLager.application.WarehouseManagement.Queries;

public record GetAllWmStellplaetzeQuery : IQuery<ICollection<WmStellplatz>>;
public record GetWmStellplaetzeByEbeneIdQuery(Guid Id) : IQuery<ICollection<WmStellplatz>>;
public record GetHallenPosInfoOfEmptyWmStellplaetzeQuery : IQuery<ICollection<HallenPositionInfo>>;
public record GetWmStellplatzByHallPositionQuery(HallenPositionInfo HallPosition) : IQuery<WmStellplatz>;
public record GetHallPositionOfStellplatzQuery(Guid id) : IQuery<HallenPositionInfo>;
public record GetAllWepStellplatzQuery : IQuery<ICollection<WmStellplatz>>;
public record GetEmptyStellplaetzeByFeldIdListQuery(List<Guid> FeldId) : IQuery<ICollection<WmStellplatz>>;
public record GetHallenPosInfoListOfEmptyStellplatzByPalletTypeQuery(string PalletType) : IQuery<ICollection<HallenPositionInfo>>;
public record GetEmptyStellplatzListByPalletTypeQuery(string PalletType, bool Automatic) : IQuery<ICollection<WmStellplatz>>;


