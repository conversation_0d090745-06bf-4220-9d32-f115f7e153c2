using MediatR;
using wingLager.application.IServices.WarehouseManagementIServices;
using wingLager.application.WarehouseManagement.Queries;
using wingLager.domain.WarehouseManagementModels;

namespace wingLager.application.WarehouseManagement.QueryHandler;

public record GetAllWmReiheQueryHandler(IWmReiheService Service) : IRequestHandler<GetAllWmReiheQuery, ICollection<WmReihe>>
{
    public Task<ICollection<WmReihe>> Handle(GetAllWmReiheQuery request, CancellationToken cancellationToken)
    {
        return Service.GetAllAsync();
    }
}