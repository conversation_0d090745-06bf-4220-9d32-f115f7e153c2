using MediatR;
using wingLager.application.IServices.WarehouseManagementIServices;
using wingLager.application.WarehouseManagement.Queries;
using wingLager.domain.WarehouseManagementModels;

namespace wingLager.application.WarehouseManagement.QueryHandler;

public record GetAllVbrsWithStellplaetzeQueryHandler(IWmVbrService WmVbrService) : IRequestHandler<GetAllVbrsWithStellplaetzeQuery, ICollection<WmVbr>>
{
    public async Task<ICollection<WmVbr>> <PERSON>le(GetAllVbrsWithStellplaetzeQuery request, CancellationToken cancellationToken)
    {
        return await WmVbrService.GetAllVbrsWithStellplaetzeAsync();
    }
}