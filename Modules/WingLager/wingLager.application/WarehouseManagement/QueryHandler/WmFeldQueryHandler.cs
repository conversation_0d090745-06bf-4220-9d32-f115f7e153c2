using MediatR;
using wingLager.application.Contracts.WarehouseManagementRepositories;
using wingLager.application.IServices.WarehouseManagementIServices;
using wingLager.application.WarehouseManagement.Commands;
using wingLager.application.WarehouseManagement.Queries;
using wingLager.domain.WarehouseManagementModels;

namespace wingLager.application.WarehouseManagement.QueryHandler;

public record GetAllWmFeldQueryHandler(IWmFeldService Service) : IRequestHandler<GetAllWmFeldQuery, ICollection<WmFeld>>
{
    public async Task<ICollection<WmFeld>> Handle(GetAllWmFeldQuery request, CancellationToken cancellationToken)
    {
        return await Service.GetAllAsync();
    }
}

public record GetWmFeldByIdQueryHandler(IWmFeldService Service) : IRequestHandler<GetWmFeldByIdQuery, WmFeld>
{
    public async Task<WmFeld> Handle(GetWmFeldByIdQuery request, CancellationToken cancellationToken)
    {
        return await Service.GetByIdAsync(request.FeldId) ?? new WmFeld();
    }
}

public record GetWmFeldByReiheIdQueryHandler(IWmFeldRepository Repository) : IRequestHandler<GetWmFeldByReiheIdQuery, ICollection<WmFeld>>
{
    public async Task<ICollection<WmFeld>> Handle(GetWmFeldByReiheIdQuery request, CancellationToken cancellationToken)
    {
        return await Repository.GetByReiheIdAsync(request.ReiheId, cancellationToken);
    }
}

public class GetWmFeldByPalletTypeOrEmptyQueryHandler(IWmFeldService service) : IRequestHandler<GetWmFeldByPalletTypeOrEmptyQuery, ICollection<WmFeld>>
{
    public async Task<ICollection<WmFeld>> Handle(GetWmFeldByPalletTypeOrEmptyQuery request, CancellationToken cancellationToken)
    {
        return await service.GetFeldWithPalletTypeOrEmpty(request.PalletType ?? string.Empty);
    }
}