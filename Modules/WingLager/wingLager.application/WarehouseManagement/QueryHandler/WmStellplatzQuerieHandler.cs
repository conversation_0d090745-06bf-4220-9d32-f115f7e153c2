using MediatR;
using wingLager.application.Contracts.WarehouseManagementRepositories;
using wingLager.application.IServices.WarehouseManagementIServices;
using wingLager.application.WarehouseManagement.Queries;
using wingLager.domain.WarehouseManagementModels;

namespace wingLager.application.WarehouseManagement.QueryHandler;

public record GetAllWmStellplaetzeQueryHandler(IWmStellplatzService Service)
    : IRequestHandler<GetAllWmStellplaetzeQuery, ICollection<WmStellplatz>>
{
    public async Task<ICollection<WmStellplatz>> Handle(GetAllWmStellplaetzeQuery request,
        CancellationToken cancellationToken)
    {
        return await Service.GetAllWmStellplaetzeAsync();
    }
}

public record GetWmStellplaetzeByEbeneIdQueryHandler(IWmStellplatzService Service)
    : IRequestHandler<GetWmStellplaetzeByEbeneIdQuery, ICollection<WmStellplatz>>
{
    public async Task<ICollection<WmStellplatz>> Handle(GetWmStellplaetzeByEbeneIdQuery request,
        CancellationToken cancellationToken)
    {
        return await Service.GetWmStellplaetzeByEbenenIddAsync(request.Id);
    }
}

public record GetHallenPosInfoOfEmptyWmStellplaetzeQueryHandler(IWmStellplatzService Service)
    : IRequestHandler<GetHallenPosInfoOfEmptyWmStellplaetzeQuery, ICollection<HallenPositionInfo>>
{
    public async Task<ICollection<HallenPositionInfo>> Handle(GetHallenPosInfoOfEmptyWmStellplaetzeQuery request,
        CancellationToken cancellationToken)
    {
        return await Service.GetHallenPosInfoOfEmptyWmStellplaetzeAsync();
    }
}


public record GetWmStellplatzByHallPositionQueryHandler(IWmStellplatzService Service) : IRequestHandler<GetWmStellplatzByHallPositionQuery, WmStellplatz>
{
    public async Task<WmStellplatz> Handle(GetWmStellplatzByHallPositionQuery request, CancellationToken cancellationToken)
    {
        var stellplatz = await Service.GetByHallposition(request.HallPosition);
        if (stellplatz is null)
            throw new Exception("Stellplatz nicht gefunden");
        return stellplatz;
    }
}

public record GetHallPositionOfStellplatzQueryHandler(IWmStellplatzService Service) : IRequestHandler<GetHallPositionOfStellplatzQuery, HallenPositionInfo>
{
    public async Task<HallenPositionInfo> Handle(GetHallPositionOfStellplatzQuery request, CancellationToken cancellationToken)
    {
        var stellplatz = await Service.GetWmStellplatzByIdAsync(request.id);
        var hallposition = await Service.GetHallenPositionInfoByStellplatzIdAsync(request.id);
        if(hallposition is null)
            throw new Exception("Stellplatz nicht gefunden");
        return hallposition;
    }
}

public class GetAllWepStellplatzQueryHandler(IWmStellplatzService service) : IRequestHandler<GetAllWepStellplatzQuery, ICollection<WmStellplatz>>
{
    public async Task<ICollection<WmStellplatz>> Handle(GetAllWepStellplatzQuery request, CancellationToken cancellationToken)
    {
        return await service.GetAllWepStellplaetzeAsync();
    }
}

public class GetEmptyStellplaetzeByFeldIdListQueryHandler(IWmStellplatzService service) : IRequestHandler<GetEmptyStellplaetzeByFeldIdListQuery, ICollection<WmStellplatz>>
{
    public async Task<ICollection<WmStellplatz>> Handle(GetEmptyStellplaetzeByFeldIdListQuery request, CancellationToken cancellationToken)
    {
        return await service.GetEmptyStellplaetzeByFeldId(request.FeldId);
    }
}

public class GetHallenPosInfoListOfEmptyStellplatzByPalletTypeQueryHandler(IWmStellplatzService service) : IRequestHandler<GetHallenPosInfoListOfEmptyStellplatzByPalletTypeQuery, ICollection<HallenPositionInfo>>
{
    public async Task<ICollection<HallenPositionInfo>> Handle(GetHallenPosInfoListOfEmptyStellplatzByPalletTypeQuery request, CancellationToken cancellationToken)
    {
        if (string.IsNullOrWhiteSpace(request.PalletType))
            return await service.GetHallenPosInfoOfEmptyWmStellplaetzeAsync();
        return await service.GetHallenPosInfoListOfEmptyStellplatzByPalletType(request.PalletType);
    }
}

public class GetEmptyStellplatzListByPalletTypeQueryHandler(IWmStellplatzService service) : IRequestHandler<GetEmptyStellplatzListByPalletTypeQuery, ICollection<WmStellplatz>>
{
    public async Task<ICollection<WmStellplatz>> Handle(GetEmptyStellplatzListByPalletTypeQuery request, CancellationToken cancellationToken)
    {
        if (request.Automatic)
        {
            return await service.GetNextStellplatzByPalletTypeForRobotAsync(request.PalletType);
        }

        return await service.GetWmStellplatzByPalletTypeAsync(request.PalletType);
    }
}