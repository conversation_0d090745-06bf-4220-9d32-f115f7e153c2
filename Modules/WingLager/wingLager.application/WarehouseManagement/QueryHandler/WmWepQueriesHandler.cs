using MediatR;
using wingLager.application.IServices.WarehouseManagementIServices;
using wingLager.application.WarehouseManagement.Queries;
using wingLager.domain.WarehouseManagementModels;

namespace wingLager.application.WarehouseManagement.QueryHandler;

public record GetAllWepWithStellplaetzeQueryHandler(IWmWepService Service) : IRequestHandler<GetAllWepWithStellplaetzeQuery ,ICollection<WmWep>>
{
    public Task<ICollection<WmWep>> Handle(GetAllWepWithStellplaetzeQuery request, CancellationToken cancellationToken)
    {
        return Service.GetAllWepWithStellplaetzeAsync();
    }
}