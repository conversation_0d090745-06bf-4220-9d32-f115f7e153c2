using MediatR;
using wingLager.application.IServices.WarehouseManagementIServices;
using wingLager.application.WarehouseManagement.Queries;
using wingLager.domain.WarehouseManagementModels;
using wingLager.domain.WarehouseManagementModels.SearchParams;

namespace wingLager.application.WarehouseManagement.QueryHandler;

public record GetAllWmBestandWithSearchParamsQueryHandler(IWmBestandService WmBestandService) : IRequestHandler<GetAllWmBestandWithSearchParamsQuery, ICollection<WmBestand>>
{

    public async Task<ICollection<WmBestand>> <PERSON>le(GetAllWmBestandWithSearchParamsQuery request, CancellationToken cancellationToken)
    {
        var wmBestandList = await WmBestandService.GetWmBestandAsync(request.SearchParams);
        if (request.SearchParams.IsInWarehouse)
            return wmBestandList.Where(x => x.IsInWareHouse()).ToList();
        else
        {
            return wmBestandList.Where(x => !x.IsIn<PERSON>ouse()).ToList();
        }
    }
}