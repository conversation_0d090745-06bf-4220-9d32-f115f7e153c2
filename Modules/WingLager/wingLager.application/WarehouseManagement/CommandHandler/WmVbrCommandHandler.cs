using MediatR;
using wingLager.application.Contracts.WarehouseManagementRepositories;
using wingLager.application.IServices.WarehouseManagementIServices;
using wingLager.application.WarehouseManagement.Commands;
using wingLager.domain.WarehouseManagementModels;

namespace wingLager.application.WarehouseManagement.CommandHandler;

public record CreateWmVbrCommandHandler(IWmVbrRepository WmVbrRepository) : IRequestHandler<CreateWmVbrCommand, WmVbr>
{
    public async Task<WmVbr> Handle(CreateWmVbrCommand request, CancellationToken cancellationToken)
    {
        var entry = WmVbr.Create(request.Description, request.Number);
        await WmVbrRepository.AddAsync(entry, cancellationToken);
        return entry;
    }
}

public record UpdateWmVbrCommandHandler(IWmVbrRepository WmVbrRepository) : IRequestHandler<UpdateWmVbrCommand>
{
    public async Task Handle(UpdateWmVbrCommand request, CancellationToken cancellationToken)
    {
        await WmVbrRepository.UpdateAsync(request.Vbr, cancellationToken);
    }
}
public record DeleteWmVbrCommandHandler(IWmVbrRepository WmVbrRepository) : IRequestHandler<DeleteWmVbrCommand>
{
    public async Task Handle(DeleteWmVbrCommand request, CancellationToken cancellationToken)
    {
       await WmVbrRepository.DeleteTreeAsync(request.Vbr, cancellationToken);
    }
}