using MediatR;
using wingLager.application.Contracts.WarehouseManagementRepositories;
using wingLager.application.WarehouseManagement.Commands;
using wingLager.domain.WarehouseManagementModels;

namespace wingLager.application.WarehouseManagement.CommandHandler;

public record CreateWmEbeneCommandHandler(IWmEbeneRepository Repository) : IRequestHandler<CreateWmEbeneCommand, WmEbene>
{
    public async Task<WmEbene> Handle(CreateWmEbeneCommand request, CancellationToken cancellationToken)
    {
        var wmEbene = WmEbene.Create(request.Description, request.Number, request.WmFeldId, request.MaxHeight, request.Restrictions);
        await Repository.AddAsync(wmEbene, cancellationToken);
        return wmEbene;
    }
}

public record CreateWmEbeneFromListCommandHandler(IWmEbeneRepository Repository) : IRequestHandler<CreateWmEbeneFromListCommand>
{
    public async Task Handle(CreateWmEbeneFromListCommand request, CancellationToken cancellationToken)
    {
        await Repository.AddRangeAsync(request.EbeneList, cancellationToken);
    }
}

public record UpdateWmEbeneCommandHandler(IWmEbeneRepository Repository) : IRequestHandler<UpdateWmEbeneCommand>
{
    public async Task Handle(UpdateWmEbeneCommand request, CancellationToken cancellationToken)
    {
        await Repository.UpdateAsync(request.Ebene, cancellationToken);
    }
}

public record UpdateWmEbeneFromListCommandHandler(IWmEbeneRepository Repository) : IRequestHandler<UpdateWmEbeneFromListCommand>
{
    public async Task Handle(UpdateWmEbeneFromListCommand request, CancellationToken cancellationToken)
    {
        await Repository.UpdateRangeAsync(request.EbeneList, cancellationToken);
    }
}

public record DeleteWmEbeneCommandHandler(IWmEbeneRepository Repository) : IRequestHandler<DeleteWmEbeneCommand>
{
    public async Task Handle(DeleteWmEbeneCommand request, CancellationToken cancellationToken)
    {
        await Repository.DeleteTreeAsync(request.EbeneId, cancellationToken);
    }
}