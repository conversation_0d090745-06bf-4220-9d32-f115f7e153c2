using MediatR;
using wingLager.application.Contracts.IRequests;
using wingLager.domain.WarehouseManagementModels;
using wingLager.domain.WarehouseManagementModels.Status;

namespace wingLager.application.WarehouseManagement.Commands;

public sealed record CreateWmStellplatzCommand(
    string? Description,
    string Number,
    Guid? EbeneId,
    List<string> PossiblePalletType,
    WmStellplatzStatus Status,
    int MaxWeight,
    int Length,
    int Height,
    int Width,
    bool Automatic) : ICommand<WmStellplatz>;

public record CreateVbrStellplatzCommand(string? Description, string Number, Guid? VbrId, WmStellplatzStatus Status)
    : ICommand<WmStellplatz>;

public record CreateWepStellplatzCommand(string? Description, string Number, Guid? WepId, WmStellplatzStatus Status)
    : ICommand<WmStellplatz?>;

public record CreateWmStellplatzFromListCommand(List<WmStellplatz> Stellplaetze) : ICommand;

public sealed record UpdateWmStellplatzCommand(WmStellplatz Stellplatz) : ICommand;

public record UpdateWmStellplatzFromListCommand(List<WmStellplatz> Stellplaetze) : ICommand;

public sealed record DeleteWmStellplatzCommand(Guid StellplatzId) : ICommand;

public record StorePalletOnStellplatzCommand(
    Guid StellplatzId,
    string? Ean,
    string? Env,
    string? PalletDescription,
    string? ReferenceNumber,
    WmStellplatzLoadType LoadType, long PalletLagerId, string PallettType = "") : ICommand;

public record ClearStellplatzCommand(string HallPosition, long PalletLagerId) : ICommand;


public record UnStorePalletOnStellplatzCommand(Guid StellplatzId) : ICommand;