using MediatR;
using wingLager.application.Contracts.IRequests;
using wingLager.domain.WarehouseManagementModels;

namespace wingLager.application.WarehouseManagement.Commands;

public record CreateWmFeldCommand(string? Description, string Number, Guid WmReiheId) : ICommand<WmFeld>;
public record CreateWmFeldFromListCommand(List<WmFeld> FeldList) : ICommand;
public record GetWmFeldByReiheIdQuery(Guid ReiheId) : ICommand<ICollection<WmFeld>>;
public sealed record UpdateWmFeldCommand(WmFeld Feld) : ICommand;
public record UpdateWmFeldFromListCommand(List<WmFeld> FeldList) : ICommand;
public record DeleteWmFeldCommand(Guid FeldId) : ICommand;
public record BlockStellplaetzeInFeldWithoutPalletTypeCommand(string PalletType, Guid FeldId) : ICommand;