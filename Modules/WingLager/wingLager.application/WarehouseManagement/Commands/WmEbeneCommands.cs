using MediatR;
using wingLager.application.Contracts.IRequests;
using wingLager.domain.WarehouseManagementModels;
using wingLager.domain.WarehouseManagementModels.Status;

namespace wingLager.application.WarehouseManagement.Commands;

public record CreateWmEbeneCommand(string? Description, string Number, Guid WmFeldId, int MaxHeight, WmEbenenRestriction Restrictions) : ICommand<WmEbene>;
public record CreateWmEbeneFromListCommand(List<WmEbene> EbeneList) : ICommand;
public record UpdateWmEbeneCommand(WmEbene Ebene) : ICommand;
public record UpdateWmEbeneFromListCommand(List<WmEbene> EbeneList) : ICommand;
public record DeleteWmEbeneCommand(Guid EbeneId) : ICommand;
