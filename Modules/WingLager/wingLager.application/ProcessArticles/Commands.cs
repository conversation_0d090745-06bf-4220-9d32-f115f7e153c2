using MediatR;
using wingLager.domain.ProcessArticles;

namespace wingLager.application.ProcessArticles;


public sealed record CreateProcessArticleCommand(ProcessArticle ProcessArticle) : IRequest<ProcessArticle>;
internal interface IProcessArticleCommand
{
    long Id { get; init; }
}

public sealed record UpdateProcessArticleCommand(ProcessArticle ProcessArticle) : IRequest<ProcessArticle>;
public sealed record DeleteProcessArticleCommand(long Id) : IProcessArticleCommand, IRequest;