using MediatR;
using WingCore.application.Contract.IModels.Helper;
using WingCore.application.Contract.Services;
using wingLager.application.Contracts;
using wingLager.application.SearchParams;
using wingLager.domain.ProcessArticles;

namespace wingLager.application.ProcessArticles.Handlers;

public class ProcessArticlesOverNumberQueryHandler(IProcessArticleRepository iProcessArticleRepository) : IRequestHandler<ProcessArticlesOverNumberQuery,ProcessArticle?>
{
    public async Task<ProcessArticle?> Handle(ProcessArticlesOverNumberQuery query, CancellationToken cancellationToken) => await iProcessArticleRepository.GetByNumberAsync(query.ProcessArticleNumber,false);
}

public class ProcessArticlesQueryHandler(IProcessArticleRepository iProcessArticleRepository) : IRequestHandler<ProcessArticlesQuery,IEnumerable<ProcessArticle>?>
{
    public async Task<IEnumerable<ProcessArticle>?> <PERSON><PERSON>(ProcessArticlesQuery query, CancellationToken cancellationToken) => await iProcessArticleRepository.GetAllAsync(false, query.SearchParam);
}

public class ProcessArticlesOverWingArticleNumbersQueryHandler(IProcessArticleRepository iProcessArticleRepository, IArticleService articleService) : IRequestHandler<ProcessArticlesOverWingArticleNumbersQuery,IEnumerable<ProcessArticle>?>
{
    public async Task<IEnumerable<ProcessArticle>?> Handle(ProcessArticlesOverWingArticleNumbersQuery query, CancellationToken cancellationToken)
    {
        var allArticle = await articleService.Get(false, new ArticleSearchParam(){ArticleNumbers = query.WingArticleNumbers});
        return await iProcessArticleRepository.GetAllAsync(false, new ProcessArticleSearchParam()
        {
            BaseArticleIds = allArticle.Select(a => a.Id)
        });
    }
    
}