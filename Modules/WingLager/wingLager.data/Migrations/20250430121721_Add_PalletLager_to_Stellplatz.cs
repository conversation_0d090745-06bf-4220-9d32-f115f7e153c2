using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace wingLager.data.Migrations
{
    /// <inheritdoc />
    public partial class Add_PalletLager_to_Stellplatz : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "LindeQueue");

            migrationBuilder.AddColumn<long>(
                name: "pallet_lager_id",
                table: "stellplaetze",
                type: "bigint",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "pallet_lager_id",
                table: "stellplaetze");

            migrationBuilder.CreateTable(
                name: "LindeQueue",
                columns: table => new
                {
                    pallet_lager_id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    created_at = table.Column<DateTime>(type: "datetime2", nullable: false),
                    creation_user = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    destination = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    executed_at = table.Column<DateTime>(type: "datetime2", nullable: true),
                    robot_answer = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    status = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    update_user = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    updated_at = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_linde_queue", x => x.pallet_lager_id);
                });
        }
    }
}
