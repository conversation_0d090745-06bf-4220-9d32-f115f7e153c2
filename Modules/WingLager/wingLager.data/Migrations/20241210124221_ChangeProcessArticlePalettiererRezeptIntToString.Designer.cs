// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using wingLager.data;

#nullable disable

namespace wingLager.data.Migrations
{
    [DbContext(typeof(WingLagerDbContext))]
    [Migration("20241210124221_ChangeProcessArticlePalettiererRezeptIntToString")]
    partial class ChangeProcessArticlePalettiererRezeptIntToString
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.10")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("wingLager.domain.ComboBoxs.Combobox", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("ID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Description");

                    b.Property<string>("Key")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Key");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasColumnName("Type");

                    b.Property<string>("Value")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Value");

                    b.HasKey("Id")
                        .HasName("Combobox$ID");

                    b.ToTable("Combobox", (string)null);
                });

            modelBuilder.Entity("wingLager.domain.PaletLagers.PaletLager", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("ID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<DateTime?>("AbgDatum")
                        .HasColumnType("datetime2")
                        .HasColumnName("AbgDatum");

                    b.Property<short?>("AnzEtik")
                        .HasColumnType("smallint")
                        .HasColumnName("AnzEtik");

                    b.Property<short?>("AnzSack")
                        .HasColumnType("smallint")
                        .HasColumnName("AnzSack");

                    b.Property<string>("ArtBez1")
                        .IsRequired()
                        .HasMaxLength(120)
                        .HasColumnType("nvarchar(120)")
                        .HasColumnName("ArtBez1");

                    b.Property<string>("ArtBez2")
                        .HasMaxLength(120)
                        .HasColumnType("nvarchar(120)")
                        .HasColumnName("ArtBez2");

                    b.Property<string>("ArtBez3")
                        .HasMaxLength(120)
                        .HasColumnType("nvarchar(120)")
                        .HasColumnName("ArtBez3");

                    b.Property<string>("ArtBez4")
                        .HasMaxLength(120)
                        .HasColumnType("nvarchar(120)")
                        .HasColumnName("ArtBez4");

                    b.Property<string>("ArtBezGr")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)")
                        .HasColumnName("ArtBezGr");

                    b.Property<string>("ArtEAN")
                        .HasMaxLength(15)
                        .HasColumnType("nvarchar(15)")
                        .HasColumnName("ArtEAN");

                    b.Property<string>("ArtNrLieferant")
                        .HasMaxLength(30)
                        .HasColumnType("nvarchar(30)")
                        .HasColumnName("ArtNrLieferant");

                    b.Property<long?>("ArtikelNr")
                        .HasColumnType("bigint")
                        .HasColumnName("ArtikelNr");

                    b.Property<long?>("AuftrNr")
                        .HasColumnType("bigint")
                        .HasColumnName("AuftrNr");

                    b.Property<string>("BestellNr")
                        .HasMaxLength(30)
                        .HasColumnType("nvarchar(30)")
                        .HasColumnName("BestellNr");

                    b.Property<string>("ChargNr")
                        .HasMaxLength(25)
                        .HasColumnType("nvarchar(25)")
                        .HasColumnName("ChargNr");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreatedAt");

                    b.Property<string>("CreationUser")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("CreationUser");

                    b.Property<string>("EANKonsEinh")
                        .HasMaxLength(15)
                        .HasColumnType("nvarchar(15)")
                        .HasColumnName("EANKonsEinh");

                    b.Property<string>("EnthEAN")
                        .HasMaxLength(15)
                        .HasColumnType("nvarchar(15)")
                        .HasColumnName("enthEAN");

                    b.Property<DateTime?>("ExportDatum")
                        .HasColumnType("datetime2")
                        .HasColumnName("ExportDatum");

                    b.Property<DateTime?>("ExportUhrzeit")
                        .HasColumnType("datetime2")
                        .HasColumnName("ExportUhrzeit");

                    b.Property<bool?>("FWare")
                        .HasColumnType("bit")
                        .HasColumnName("FWare");

                    b.Property<bool?>("FremdUeberg")
                        .HasColumnType("bit")
                        .HasColumnName("FremdUeberg");

                    b.Property<double?>("Gewicht")
                        .HasColumnType("float")
                        .HasColumnName("Gewicht");

                    b.Property<DateTime>("JobDatum")
                        .HasColumnType("datetime2")
                        .HasColumnName("JobDatum");

                    b.Property<DateTime?>("JobUhrzeit")
                        .HasColumnType("datetime2")
                        .HasColumnName("JobUhrzeit");

                    b.Property<string>("KolliAnz")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)")
                        .HasColumnName("KolliAnz");

                    b.Property<string>("KolliInh")
                        .HasMaxLength(15)
                        .HasColumnType("nvarchar(15)")
                        .HasColumnName("KolliInh");

                    b.Property<string>("KundName1")
                        .HasMaxLength(30)
                        .HasColumnType("nvarchar(30)")
                        .HasColumnName("KundName1");

                    b.Property<string>("KundName2")
                        .HasMaxLength(30)
                        .HasColumnType("nvarchar(30)")
                        .HasColumnName("KundName2");

                    b.Property<long?>("KundNrWE")
                        .HasColumnType("bigint")
                        .HasColumnName("KundNrWE");

                    b.Property<string>("KundOrt")
                        .HasMaxLength(30)
                        .HasColumnType("nvarchar(30)")
                        .HasColumnName("KundOrt");

                    b.Property<string>("KundPLZ")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)")
                        .HasColumnName("KundPLZ");

                    b.Property<string>("KundStrasse")
                        .HasMaxLength(30)
                        .HasColumnType("nvarchar(30)")
                        .HasColumnName("KundStrasse");

                    b.Property<bool>("LagerBest")
                        .HasColumnType("bit")
                        .HasColumnName("LagerBest");

                    b.Property<long?>("LagerNr")
                        .HasColumnType("bigint")
                        .HasColumnName("LagerNr");

                    b.Property<DateTime?>("LieferDatum")
                        .HasColumnType("datetime2")
                        .HasColumnName("LieferDatum");

                    b.Property<DateTime>("MHD")
                        .HasColumnType("datetime2")
                        .HasColumnName("MHD");

                    b.Property<long?>("MandNr")
                        .HasColumnType("bigint")
                        .HasColumnName("MandNr");

                    b.Property<short?>("MaschNr")
                        .HasColumnType("smallint")
                        .HasColumnName("MaschNr");

                    b.Property<long?>("NVEIndexNr")
                        .HasColumnType("bigint")
                        .HasColumnName("NVEIndexNr");

                    b.Property<string>("NVENr")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("NVENr");

                    b.Property<short?>("PalAnz")
                        .HasColumnType("smallint")
                        .HasColumnName("PalAnz");

                    b.Property<long?>("PalNr")
                        .HasColumnType("bigint")
                        .HasColumnName("PalNr");

                    b.Property<long>("ProcessOrderPositionId")
                        .HasColumnType("bigint")
                        .HasColumnName("process_order_position_id");

                    b.Property<string>("ProtNr")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("ProtNr");

                    b.Property<bool?>("SEtikett")
                        .HasColumnType("bit")
                        .HasColumnName("SEtikett");

                    b.Property<string>("Status")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)")
                        .HasColumnName("Status");

                    b.Property<string>("UpdateUser")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("UpdateUser");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("UpdatedAt");

                    b.Property<string>("Version")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasColumnName("Version");

                    b.Property<string>("Zutaten")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Zutaten");

                    b.HasKey("Id")
                        .HasName("PaletLager$ID");

                    b.HasIndex("ProcessOrderPositionId")
                        .IsUnique()
                        .HasDatabaseName("ix_palet_lager_process_order_position_id");

                    b.ToTable("PaletLager", (string)null);
                });

            modelBuilder.Entity("wingLager.domain.ProcessArticles.ProcessArticle", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("ID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<long>("BasisArticleId")
                        .HasColumnType("bigint")
                        .HasColumnName("BasisArticleId");

                    b.Property<long>("BasisArticleNumber")
                        .HasColumnType("bigint")
                        .HasColumnName("BasisArticleNumber");

                    b.Property<string>("ChargeNr")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ChargeNr");

                    b.Property<DateTime?>("CreatedAt")
                        .IsRequired()
                        .HasColumnType("datetime2")
                        .HasColumnName("CreatedAt");

                    b.Property<string>("CreationUser")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("CreationUser");

                    b.Property<long>("CustomerId")
                        .HasColumnType("bigint")
                        .HasColumnName("CustomerId");

                    b.Property<long>("CustomerNumber")
                        .HasColumnType("bigint")
                        .HasColumnName("CustomerNumber");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(120)
                        .HasColumnType("nvarchar(120)")
                        .HasColumnName("Description");

                    b.Property<string>("Description2")
                        .HasMaxLength(120)
                        .HasColumnType("nvarchar(120)")
                        .HasColumnName("Description2");

                    b.Property<string>("Description3")
                        .HasMaxLength(120)
                        .HasColumnType("nvarchar(120)")
                        .HasColumnName("Description3");

                    b.Property<string>("Description4")
                        .HasMaxLength(120)
                        .HasColumnType("nvarchar(120)")
                        .HasColumnName("Description4");

                    b.Property<string>("EanPalette")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("EanPalette");

                    b.Property<string>("EanSack")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("EanSack");

                    b.Property<string>("EtikTyp")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("EtikTyp");

                    b.Property<string>("LayoutPalEtik")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("LayoutPalEtik");

                    b.Property<string>("LayoutSackEtik")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("LayoutSackEtik");

                    b.Property<short?>("MhdTimeSpan")
                        .HasColumnType("smallint")
                        .HasColumnName("MhdTimeSpan");

                    b.Property<string>("Note")
                        .HasMaxLength(512)
                        .HasColumnType("nvarchar(512)")
                        .HasColumnName("Note");

                    b.Property<long>("Number")
                        .HasColumnType("bigint")
                        .HasColumnName("Number");

                    b.Property<string>("PalettenTyp")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("PalettenTyp");

                    b.Property<string>("PalettiererRezept")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("PalettiererRezept");

                    b.Property<string>("RezeptWickler")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("RezeptWickler");

                    b.Property<string>("SackAnzahl")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("SackAnzahl");

                    b.Property<long?>("SackInhalt")
                        .HasColumnType("bigint")
                        .HasColumnName("SackInhalt");

                    b.Property<string>("SackTyp")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("SackTyp");

                    b.Property<string>("UpdateUser")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("UpdateUser");

                    b.Property<DateTime?>("UpdatedAt")
                        .IsRequired()
                        .HasColumnType("datetime2")
                        .HasColumnName("UpdatedAt");

                    b.HasKey("Id")
                        .HasName("ProcessArticle$ID");

                    b.ToTable("ProcessArticle", (string)null);
                });

            modelBuilder.Entity("wingLager.domain.ProcessOrders.ProcessOrderHeader", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("ID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("AdditionalInformation1")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("AdditionalInformation1");

                    b.Property<string>("AdditionalInformation2")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("AdditionalInformation2");

                    b.Property<string>("AdditionalInformation3")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("AdditionalInformation3");

                    b.Property<long>("ArticleNumber")
                        .HasColumnType("bigint")
                        .HasColumnName("ArticleNumber");

                    b.Property<DateTime>("BeginnDateTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("Beginn");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreatedAt");

                    b.Property<string>("CreationUser")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("CreationUser");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("Description");

                    b.Property<DateTime>("EndDateTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("End");

                    b.Property<long>("Flags")
                        .HasColumnType("bigint")
                        .HasColumnName("Flags");

                    b.Property<long>("Number")
                        .HasColumnType("bigint")
                        .HasColumnName("Number");

                    b.Property<DateTime>("StartFirstPrintDateTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("StartFirstPrint");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Status");

                    b.Property<string>("UpdateUser")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("UpdateUser");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("UpdatedAt");

                    b.Property<decimal>("WeightInKg")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(18,2)")
                        .HasDefaultValue(0.0m)
                        .HasColumnName("WeightInKg");

                    b.HasKey("Id")
                        .HasName("ProcessOrderHeader$ID");

                    b.HasAlternateKey("Number")
                        .HasName("ak_process_order_header_number");

                    b.ToTable("ProcessOrderHeader", (string)null);
                });

            modelBuilder.Entity("wingLager.domain.ProcessOrders.ProcessOrderLock", b =>
                {
                    b.Property<DateOnly?>("LockStart")
                        .HasColumnType("date")
                        .HasColumnName("lock_start");

                    b.Property<string>("LockUser")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("lock_user");

                    b.ToTable("ProcessOrderLock", (string)null);
                });

            modelBuilder.Entity("wingLager.domain.ProcessOrders.ProcessOrderPosition", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("ID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<long>("ArticleNumber")
                        .HasColumnType("bigint")
                        .HasColumnName("ArticleNumber");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreatedAt");

                    b.Property<string>("CreationUser")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("CreationUser");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("Description");

                    b.Property<long>("Flags")
                        .HasColumnType("bigint")
                        .HasColumnName("Flags");

                    b.Property<string>("LayoutPet")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("LayoutPet");

                    b.Property<string>("LayoutSet")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("LayoutSet");

                    b.Property<string>("Number")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Number");

                    b.Property<long>("OrderNumber")
                        .HasColumnType("bigint")
                        .HasColumnName("OrderNumber");

                    b.Property<long>("OrderPostionId")
                        .HasColumnType("bigint")
                        .HasColumnName("OrderPostionId");

                    b.Property<long>("ProcessArticleId")
                        .HasColumnType("bigint")
                        .HasColumnName("ProcessArticleId");

                    b.Property<long>("ProcessOrderHeaderNumber")
                        .HasColumnType("bigint")
                        .HasColumnName("ProcessOrderHeaderNumber");

                    b.Property<DateTime>("StartFirstPrintDateTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("StartFirstPrint");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Status");

                    b.Property<string>("UpdateUser")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("UpdateUser");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("UpdatedAt");

                    b.Property<string>("WarenEmpfaengerName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("WarenEmpfaengerName");

                    b.Property<decimal>("WeightInKg")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(18,2)")
                        .HasDefaultValue(0.0m)
                        .HasColumnName("WeightInKg");

                    b.HasKey("Id")
                        .HasName("ProcessOrderPosition$ID");

                    b.HasIndex("ProcessOrderHeaderNumber")
                        .HasDatabaseName("ix_process_order_position_process_order_header_number");

                    b.ToTable("ProcessOrderPosition", (string)null);
                });

            modelBuilder.Entity("wingLager.domain.PaletLagers.PaletLager", b =>
                {
                    b.HasOne("wingLager.domain.ProcessOrders.ProcessOrderPosition", null)
                        .WithOne("PaletLager")
                        .HasForeignKey("wingLager.domain.PaletLagers.PaletLager", "ProcessOrderPositionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_palet_lager_process_order_position_process_order_position_id");
                });

            modelBuilder.Entity("wingLager.domain.ProcessOrders.ProcessOrderPosition", b =>
                {
                    b.HasOne("wingLager.domain.ProcessOrders.ProcessOrderHeader", null)
                        .WithMany("Positionen")
                        .HasForeignKey("ProcessOrderHeaderNumber")
                        .HasPrincipalKey("Number")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_process_order_position_process_order_header_process_order_header_number");
                });

            modelBuilder.Entity("wingLager.domain.ProcessOrders.ProcessOrderHeader", b =>
                {
                    b.Navigation("Positionen");
                });

            modelBuilder.Entity("wingLager.domain.ProcessOrders.ProcessOrderPosition", b =>
                {
                    b.Navigation("PaletLager");
                });
#pragma warning restore 612, 618
        }
    }
}
