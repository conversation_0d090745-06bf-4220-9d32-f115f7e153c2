using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace wingLager.data.Migrations
{
    /// <inheritdoc />
    public partial class Add_PrimaryKey_for_LindeQueue : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "LindeQueue",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    pallet_lager_id = table.Column<long>(type: "bigint", nullable: false),
                    destination = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    executed_at = table.Column<DateTime>(type: "datetime2", nullable: true),
                    robot_answer = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    status = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    creation_user = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    created_at = table.Column<DateTime>(type: "datetime2", nullable: false),
                    update_user = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    updated_at = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_linde_queue", x => x.id);
                });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "LindeQueue");
        }
    }
}
