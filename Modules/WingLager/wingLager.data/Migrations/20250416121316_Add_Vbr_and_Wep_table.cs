using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace wingLager.data.Migrations
{
    /// <inheritdoc />
    public partial class Add_Vbr_and_Wep_table : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<Guid>(
                name: "wm_ebenen_id",
                table: "stellplaetze",
                type: "uniqueidentifier",
                nullable: true,
                oldClrType: typeof(Guid),
                oldType: "uniqueidentifier");

            migrationBuilder.AlterColumn<int>(
                name: "width",
                table: "stellplaetze",
                type: "int",
                nullable: false,
                oldClrType: typeof(decimal),
                oldType: "decimal(18,4)",
                oldPrecision: 18,
                oldScale: 4);

            migrationBuilder.AlterColumn<int>(
                name: "max_load",
                table: "stellplaetze",
                type: "int",
                nullable: false,
                oldClrType: typeof(decimal),
                oldType: "decimal(18,4)",
                oldPrecision: 18,
                oldScale: 4);

            migrationBuilder.AlterColumn<int>(
                name: "length",
                table: "stellplaetze",
                type: "int",
                nullable: false,
                oldClrType: typeof(decimal),
                oldType: "decimal(18,4)",
                oldPrecision: 18,
                oldScale: 4);

            migrationBuilder.AlterColumn<int>(
                name: "height",
                table: "stellplaetze",
                type: "int",
                nullable: false,
                oldClrType: typeof(decimal),
                oldType: "decimal(18,4)",
                oldPrecision: 18,
                oldScale: 4);

            migrationBuilder.AddColumn<long>(
                name: "flags",
                table: "stellplaetze",
                type: "bigint",
                nullable: false,
                defaultValue: 0L);

            migrationBuilder.AddColumn<Guid>(
                name: "wm_vbr",
                table: "stellplaetze",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "wm_wep",
                table: "stellplaetze",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "Vbrs",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    description = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    number = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    creation_user = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    created_at = table.Column<DateTime>(type: "datetime2", nullable: false),
                    update_user = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    updated_at = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_vbrs", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "Wep",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    description = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    number = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    creation_user = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    created_at = table.Column<DateTime>(type: "datetime2", nullable: false),
                    update_user = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    updated_at = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_wep", x => x.id);
                });

            /*migrationBuilder.CreateIndex(
                name: "ix_stellplaetze_wm_vbr",
                table: "stellplaetze",
                column: "wm_vbr");

            migrationBuilder.CreateIndex(
                name: "ix_stellplaetze_wm_wep",
                table: "stellplaetze",
                column: "wm_wep");*/

            migrationBuilder.AddForeignKey(
                name: "fk_stellplaetze_vbrs_wm_vbr",
                table: "stellplaetze",
                column: "wm_vbr",
                principalTable: "Vbrs",
                principalColumn: "id");

            migrationBuilder.AddForeignKey(
                name: "fk_stellplaetze_wep_wm_wep",
                table: "stellplaetze",
                column: "wm_wep",
                principalTable: "Wep",
                principalColumn: "id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "fk_stellplaetze_vbrs_wm_vbr",
                table: "stellplaetze");

            migrationBuilder.DropForeignKey(
                name: "fk_stellplaetze_wep_wm_wep",
                table: "stellplaetze");

            migrationBuilder.DropTable(
                name: "Vbrs");

            migrationBuilder.DropTable(
                name: "Wep");

            /*migrationBuilder.DropIndex(
                name: "ix_stellplaetze_wm_vbr",
                table: "stellplaetze");

            migrationBuilder.DropIndex(
                name: "ix_stellplaetze_wm_wep",
                table: "stellplaetze");*/

            migrationBuilder.DropColumn(
                name: "flags",
                table: "stellplaetze");

            migrationBuilder.DropColumn(
                name: "wm_vbr",
                table: "stellplaetze");

            migrationBuilder.DropColumn(
                name: "wm_wep",
                table: "stellplaetze");

            migrationBuilder.AlterColumn<Guid>(
                name: "wm_ebenen_id",
                table: "stellplaetze",
                type: "uniqueidentifier",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"),
                oldClrType: typeof(Guid),
                oldType: "uniqueidentifier",
                oldNullable: true);

            migrationBuilder.AlterColumn<decimal>(
                name: "width",
                table: "stellplaetze",
                type: "decimal(18,4)",
                precision: 18,
                scale: 4,
                nullable: false,
                oldClrType: typeof(int),
                oldType: "int");

            migrationBuilder.AlterColumn<decimal>(
                name: "max_load",
                table: "stellplaetze",
                type: "decimal(18,4)",
                precision: 18,
                scale: 4,
                nullable: false,
                oldClrType: typeof(int),
                oldType: "int");

            migrationBuilder.AlterColumn<decimal>(
                name: "length",
                table: "stellplaetze",
                type: "decimal(18,4)",
                precision: 18,
                scale: 4,
                nullable: false,
                oldClrType: typeof(int),
                oldType: "int");

            migrationBuilder.AlterColumn<decimal>(
                name: "height",
                table: "stellplaetze",
                type: "decimal(18,4)",
                precision: 18,
                scale: 4,
                nullable: false,
                oldClrType: typeof(int),
                oldType: "int");
        }
    }
}
