// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using wingLager.data;

#nullable disable

namespace wingLager.data.Migrations
{
    [DbContext(typeof(WingLagerDbContext))]
    [Migration("20250428093001_Added_LindeQueue")]
    partial class Added_LindeQueue
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.4")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("StateMachineMasstransit.BackgroundState", b =>
                {
                    b.Property<Guid>("CorrelationId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("CorrelationId");

                    b.Property<long>("Counter")
                        .HasColumnType("bigint")
                        .HasColumnName("Counter");

                    b.Property<DateTime>("CreationDateTimeUtc")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreationDateTimeUtc");

                    b.Property<string>("CurrentState")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("CurrentState");

                    b.Property<string>("Data")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Data");

                    b.Property<string>("InfoMessage")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("InfoMessage");

                    b.Property<string>("ObjectName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ObjectName");

                    b.HasKey("CorrelationId")
                        .HasName("pk_background_state");

                    b.ToTable("BackgroundState", (string)null);
                });

            modelBuilder.Entity("wingLager.domain.AduitLogs.AuditEntry", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("ID");

                    b.Property<string>("Action")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Action");

                    b.Property<DateTime>("CratedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("CratedAt");

                    b.Property<string>("EntityId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("EntityId");

                    b.Property<string>("EntityType")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("EntityType");

                    b.Property<string>("ErrorMessage")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("ErrorMessage");

                    b.Property<string>("Metadata")
                        .IsRequired()
                        .HasColumnType("VARCHAR(MAX)")
                        .HasColumnName("Metadata");

                    b.Property<string>("ModifiedData")
                        .IsRequired()
                        .HasColumnType("VARCHAR(MAX)")
                        .HasColumnName("ModifiedData");

                    b.Property<bool>("Succeeded")
                        .HasColumnType("bit")
                        .HasColumnName("Succeeded");

                    b.HasKey("Id")
                        .HasName("AuditLogs$ID");

                    b.ToTable("AuditLogs", (string)null);
                });

            modelBuilder.Entity("wingLager.domain.ComboBoxs.Combobox", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("ID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Description");

                    b.Property<string>("Key")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Key");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasColumnName("Type");

                    b.Property<string>("Value")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Value");

                    b.HasKey("Id")
                        .HasName("Combobox$ID");

                    b.ToTable("Combobox", (string)null);
                });

            modelBuilder.Entity("wingLager.domain.LindeQueueModel.LindeQueueEntry", b =>
                {
                    b.Property<long>("PalletLagerId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("pallet_lager_id");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("PalletLagerId"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("created_at");

                    b.Property<string>("CreationUser")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("creation_user");

                    b.Property<string>("Destination")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("destination");

                    b.Property<DateTime?>("ExecutedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("executed_at");

                    b.Property<string>("RobotAnswer")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("robot_answer");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("status");

                    b.Property<string>("UpdateUser")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("update_user");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("updated_at");

                    b.HasKey("PalletLagerId")
                        .HasName("pk_linde_queue");

                    b.ToTable("LindeQueue", (string)null);
                });

            modelBuilder.Entity("wingLager.domain.PaletLagers.PaletLager", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("ID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<DateTime?>("AbgDatum")
                        .HasColumnType("datetime2")
                        .HasColumnName("AbgDatum");

                    b.Property<short?>("AnzEtik")
                        .HasColumnType("smallint")
                        .HasColumnName("AnzEtik");

                    b.Property<short?>("AnzSack")
                        .HasColumnType("smallint")
                        .HasColumnName("AnzSack");

                    b.Property<string>("ArtBez1")
                        .IsRequired()
                        .HasMaxLength(120)
                        .HasColumnType("nvarchar(120)")
                        .HasColumnName("ArtBez1");

                    b.Property<string>("ArtBez2")
                        .HasMaxLength(120)
                        .HasColumnType("nvarchar(120)")
                        .HasColumnName("ArtBez2");

                    b.Property<string>("ArtBez3")
                        .HasMaxLength(120)
                        .HasColumnType("nvarchar(120)")
                        .HasColumnName("ArtBez3");

                    b.Property<string>("ArtBez4")
                        .HasMaxLength(120)
                        .HasColumnType("nvarchar(120)")
                        .HasColumnName("ArtBez4");

                    b.Property<string>("ArtBezGr")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)")
                        .HasColumnName("ArtBezGr");

                    b.Property<string>("ArtEAN")
                        .HasMaxLength(15)
                        .HasColumnType("nvarchar(15)")
                        .HasColumnName("ArtEAN");

                    b.Property<string>("ArtNrLieferant")
                        .HasMaxLength(30)
                        .HasColumnType("nvarchar(30)")
                        .HasColumnName("ArtNrLieferant");

                    b.Property<long?>("ArtikelNr")
                        .HasColumnType("bigint")
                        .HasColumnName("ArtikelNr");

                    b.Property<long?>("AuftrNr")
                        .HasColumnType("bigint")
                        .HasColumnName("AuftrNr");

                    b.Property<string>("BestellNr")
                        .HasMaxLength(30)
                        .HasColumnType("nvarchar(30)")
                        .HasColumnName("BestellNr");

                    b.Property<string>("ChargNr")
                        .HasMaxLength(25)
                        .HasColumnType("nvarchar(25)")
                        .HasColumnName("ChargNr");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreatedAt");

                    b.Property<string>("CreationUser")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("CreationUser");

                    b.Property<string>("EANKonsEinh")
                        .HasMaxLength(15)
                        .HasColumnType("nvarchar(15)")
                        .HasColumnName("EANKonsEinh");

                    b.Property<string>("EnthEAN")
                        .HasMaxLength(15)
                        .HasColumnType("nvarchar(15)")
                        .HasColumnName("enthEAN");

                    b.Property<DateTime?>("ExportDatum")
                        .HasColumnType("datetime2")
                        .HasColumnName("ExportDatum");

                    b.Property<DateTime?>("ExportUhrzeit")
                        .HasColumnType("datetime2")
                        .HasColumnName("ExportUhrzeit");

                    b.Property<bool?>("FWare")
                        .HasColumnType("bit")
                        .HasColumnName("FWare");

                    b.Property<long>("Flags")
                        .HasColumnType("bigint")
                        .HasColumnName("Flags");

                    b.Property<bool?>("FremdUeberg")
                        .HasColumnType("bit")
                        .HasColumnName("FremdUeberg");

                    b.Property<decimal?>("Gewicht")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("Gewicht");

                    b.Property<DateTime>("JobDatum")
                        .HasColumnType("datetime2")
                        .HasColumnName("JobDatum");

                    b.Property<DateTime?>("JobUhrzeit")
                        .HasColumnType("datetime2")
                        .HasColumnName("JobUhrzeit");

                    b.Property<string>("KolliAnz")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)")
                        .HasColumnName("KolliAnz");

                    b.Property<string>("KolliInh")
                        .HasMaxLength(15)
                        .HasColumnType("nvarchar(15)")
                        .HasColumnName("KolliInh");

                    b.Property<string>("KundName1")
                        .HasMaxLength(30)
                        .HasColumnType("nvarchar(30)")
                        .HasColumnName("KundName1");

                    b.Property<string>("KundName2")
                        .HasMaxLength(30)
                        .HasColumnType("nvarchar(30)")
                        .HasColumnName("KundName2");

                    b.Property<long?>("KundNrWE")
                        .HasColumnType("bigint")
                        .HasColumnName("KundNrWE");

                    b.Property<string>("KundOrt")
                        .HasMaxLength(30)
                        .HasColumnType("nvarchar(30)")
                        .HasColumnName("KundOrt");

                    b.Property<string>("KundPLZ")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)")
                        .HasColumnName("KundPLZ");

                    b.Property<string>("KundStrasse")
                        .HasMaxLength(30)
                        .HasColumnType("nvarchar(30)")
                        .HasColumnName("KundStrasse");

                    b.Property<bool>("LagerBest")
                        .HasColumnType("bit")
                        .HasColumnName("LagerBest");

                    b.Property<long?>("LagerNr")
                        .HasColumnType("bigint")
                        .HasColumnName("LagerNr");

                    b.Property<DateTime?>("LieferDatum")
                        .HasColumnType("datetime2")
                        .HasColumnName("LieferDatum");

                    b.Property<DateTime>("MHD")
                        .HasColumnType("datetime2")
                        .HasColumnName("MHD");

                    b.Property<long?>("MandNr")
                        .HasColumnType("bigint")
                        .HasColumnName("MandNr");

                    b.Property<short?>("MaschNr")
                        .HasColumnType("smallint")
                        .HasColumnName("MaschNr");

                    b.Property<long?>("NVEIndexNr")
                        .HasColumnType("bigint")
                        .HasColumnName("NVEIndexNr");

                    b.Property<string>("NVENr")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("NVENr");

                    b.Property<short?>("PalAnz")
                        .HasColumnType("smallint")
                        .HasColumnName("PalAnz");

                    b.Property<long?>("PalNr")
                        .HasColumnType("bigint")
                        .HasColumnName("PalNr");

                    b.Property<long>("ProcessOrderPositionId")
                        .HasColumnType("bigint")
                        .HasColumnName("process_order_position_id");

                    b.Property<string>("ProtNr")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("ProtNr");

                    b.Property<bool?>("SEtikett")
                        .HasColumnType("bit")
                        .HasColumnName("SEtikett");

                    b.PrimitiveCollection<string>("SackNumbersWasPrinted")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("sack_numbers_was_printed");

                    b.Property<string>("Status")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)")
                        .HasColumnName("Status");

                    b.Property<string>("UpdateUser")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("UpdateUser");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("UpdatedAt");

                    b.Property<string>("Version")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasColumnName("Version");

                    b.Property<string>("Zutaten")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Zutaten");

                    b.HasKey("Id")
                        .HasName("PaletLager$ID");

                    b.HasIndex("ProcessOrderPositionId")
                        .HasDatabaseName("ix_palet_lager_process_order_position_id");

                    b.ToTable("PaletLager", (string)null);
                });

            modelBuilder.Entity("wingLager.domain.ProcessArticles.ProcessArticle", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("ID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<long>("BasisArticleId")
                        .HasColumnType("bigint")
                        .HasColumnName("BasisArticleId");

                    b.Property<long>("BasisArticleNumber")
                        .HasColumnType("bigint")
                        .HasColumnName("BasisArticleNumber");

                    b.Property<string>("ChargeNr")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ChargeNr");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreatedAt");

                    b.Property<string>("CreationUser")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("CreationUser");

                    b.Property<long>("CustomerId")
                        .HasColumnType("bigint")
                        .HasColumnName("CustomerId");

                    b.Property<long>("CustomerNumber")
                        .HasColumnType("bigint")
                        .HasColumnName("CustomerNumber");

                    b.Property<string>("CustomerReference")
                        .HasMaxLength(512)
                        .HasColumnType("nvarchar(512)")
                        .HasColumnName("CustomerReference");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(120)
                        .HasColumnType("nvarchar(120)")
                        .HasColumnName("Description");

                    b.Property<string>("Description2")
                        .HasMaxLength(120)
                        .HasColumnType("nvarchar(120)")
                        .HasColumnName("Description2");

                    b.Property<string>("Description3")
                        .HasMaxLength(120)
                        .HasColumnType("nvarchar(120)")
                        .HasColumnName("Description3");

                    b.Property<string>("Description4")
                        .HasMaxLength(120)
                        .HasColumnType("nvarchar(120)")
                        .HasColumnName("Description4");

                    b.Property<string>("DetailDescription")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("nvarchar(250)")
                        .HasColumnName("DetailDescription");

                    b.Property<string>("DimensionInMilliMeter")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("DimensionInMilliMeter");

                    b.Property<string>("EanPalette")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("EanPalette");

                    b.Property<string>("EanSack")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("EanSack");

                    b.Property<string>("EtikTyp")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("EtikTyp");

                    b.Property<string>("LayoutPalEtik")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("LayoutPalEtik");

                    b.Property<string>("LayoutSackEtik")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("LayoutSackEtik");

                    b.Property<short?>("MhdTimeSpan")
                        .HasColumnType("smallint")
                        .HasColumnName("MhdTimeSpan");

                    b.Property<string>("Note")
                        .HasMaxLength(512)
                        .HasColumnType("nvarchar(512)")
                        .HasColumnName("Note");

                    b.Property<long>("Number")
                        .HasColumnType("bigint")
                        .HasColumnName("Number");

                    b.Property<int?>("NumberOfPalletEtikettToPrint")
                        .HasColumnType("int")
                        .HasColumnName("NumberOfPalletEtikettToPrint");

                    b.Property<string>("PalettenTyp")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("PalettenTyp");

                    b.Property<string>("PalettiererRezept")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("PalettiererRezept");

                    b.Property<string>("RezeptWickler")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("RezeptWickler");

                    b.Property<short?>("SackAnzahl")
                        .HasColumnType("smallint")
                        .HasColumnName("SackAnzahl");

                    b.Property<long?>("SackInhalt")
                        .HasColumnType("bigint")
                        .HasColumnName("SackInhalt");

                    b.Property<string>("SackTyp")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("SackTyp");

                    b.Property<string>("UpdateUser")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("UpdateUser");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("UpdatedAt");

                    b.HasKey("Id")
                        .HasName("ProcessArticle$ID");

                    b.ToTable("ProcessArticle", (string)null);
                });

            modelBuilder.Entity("wingLager.domain.ProcessOrders.ProcessOrderHeader", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("ID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("AdditionalInformation1")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("AdditionalInformation1");

                    b.Property<string>("AdditionalInformation2")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("AdditionalInformation2");

                    b.Property<string>("AdditionalInformation3")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("AdditionalInformation3");

                    b.Property<long>("ArticleNumber")
                        .HasColumnType("bigint")
                        .HasColumnName("ArticleNumber");

                    b.Property<DateTime>("BeginnDateTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("Beginn");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreatedAt");

                    b.Property<string>("CreationUser")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("CreationUser");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("Description");

                    b.Property<DateTime>("EndDateTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("End");

                    b.Property<long>("Flags")
                        .HasColumnType("bigint")
                        .HasColumnName("Flags");

                    b.Property<long>("Number")
                        .HasColumnType("bigint")
                        .HasColumnName("Number");

                    b.Property<DateTime>("StartFirstPrintDateTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("StartFirstPrint");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Status");

                    b.Property<string>("UpdateUser")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("UpdateUser");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("UpdatedAt");

                    b.Property<decimal>("WeightInKg")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(18,2)")
                        .HasDefaultValue(0.0m)
                        .HasColumnName("WeightInKg");

                    b.HasKey("Id")
                        .HasName("ProcessOrderHeader$ID");

                    b.HasAlternateKey("Number")
                        .HasName("ak_process_order_header_number");

                    b.ToTable("ProcessOrderHeader", (string)null);
                });

            modelBuilder.Entity("wingLager.domain.ProcessOrders.ProcessOrderLock", b =>
                {
                    b.Property<DateOnly?>("LockStart")
                        .HasColumnType("date")
                        .HasColumnName("lock_start");

                    b.Property<string>("LockUser")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("lock_user");

                    b.ToTable("ProcessOrderLock", (string)null);
                });

            modelBuilder.Entity("wingLager.domain.ProcessOrders.ProcessOrderPosition", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("ID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<long>("ArticleNumber")
                        .HasColumnType("bigint")
                        .HasColumnName("ArticleNumber");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreatedAt");

                    b.Property<string>("CreationUser")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("CreationUser");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("Description");

                    b.Property<long>("Flags")
                        .HasColumnType("bigint")
                        .HasColumnName("Flags");

                    b.Property<string>("LayoutPet")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("LayoutPet");

                    b.Property<string>("LayoutSet")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("LayoutSet");

                    b.Property<string>("Number")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Number");

                    b.Property<long>("OrderNumber")
                        .HasColumnType("bigint")
                        .HasColumnName("OrderNumber");

                    b.Property<long>("OrderPostionId")
                        .HasColumnType("bigint")
                        .HasColumnName("OrderPostionId");

                    b.Property<int>("Position")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(0)
                        .HasColumnName("Position");

                    b.Property<long>("ProcessArticleId")
                        .HasColumnType("bigint")
                        .HasColumnName("ProcessArticleId");

                    b.Property<long>("ProcessOrderHeaderNumber")
                        .HasColumnType("bigint")
                        .HasColumnName("ProcessOrderHeaderNumber");

                    b.Property<DateTime>("StartFirstPrintDateTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("StartFirstPrint");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Status");

                    b.Property<string>("UpdateUser")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("UpdateUser");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("UpdatedAt");

                    b.Property<string>("WarenEmpfaengerName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("WarenEmpfaengerName");

                    b.Property<decimal>("WeightInKg")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(18,2)")
                        .HasDefaultValue(0.0m)
                        .HasColumnName("WeightInKg");

                    b.HasKey("Id")
                        .HasName("ProcessOrderPosition$ID");

                    b.HasIndex("ProcessOrderHeaderNumber")
                        .HasDatabaseName("ix_process_order_position_process_order_header_number");

                    b.ToTable("ProcessOrderPosition", (string)null);
                });

            modelBuilder.Entity("wingLager.domain.WarehouseManagementModels.WmEbene", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("created_at");

                    b.Property<string>("CreationUser")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("creation_user");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("description");

                    b.Property<int>("MaxHeight")
                        .HasColumnType("int")
                        .HasColumnName("max_height");

                    b.Property<string>("Number")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("number");

                    b.Property<string>("Restrictions")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("restrictions");

                    b.Property<string>("UpdateUser")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("update_user");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("updated_at");

                    b.Property<Guid>("WmFeldId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("wm_feld_id");

                    b.HasKey("Id")
                        .HasName("pk_ebenen");

                    b.HasIndex("WmFeldId")
                        .HasDatabaseName("ix_ebenen_wm_feld_id");

                    b.ToTable("ebenen", (string)null);
                });

            modelBuilder.Entity("wingLager.domain.WarehouseManagementModels.WmFeld", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("created_at");

                    b.Property<string>("CreationUser")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("creation_user");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("description");

                    b.Property<string>("Number")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("number");

                    b.Property<string>("UpdateUser")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("update_user");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("updated_at");

                    b.Property<Guid>("WmReiheId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("wm_reihe_id");

                    b.HasKey("Id")
                        .HasName("pk_felder");

                    b.HasIndex("WmReiheId")
                        .HasDatabaseName("ix_felder_wm_reihe_id");

                    b.ToTable("felder", (string)null);
                });

            modelBuilder.Entity("wingLager.domain.WarehouseManagementModels.WmHall", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("created_at");

                    b.Property<string>("CreationUser")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("creation_user");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("description");

                    b.Property<string>("Location")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("location");

                    b.Property<string>("Number")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("number");

                    b.Property<string>("UpdateUser")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("update_user");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("updated_at");

                    b.HasKey("Id")
                        .HasName("pk_halls");

                    b.ToTable("Halls", (string)null);
                });

            modelBuilder.Entity("wingLager.domain.WarehouseManagementModels.WmReihe", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("created_at");

                    b.Property<string>("CreationUser")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("creation_user");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("description");

                    b.Property<string>("Number")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("number");

                    b.Property<string>("UpdateUser")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("update_user");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("updated_at");

                    b.Property<Guid>("WmHallId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("wm_hall_id");

                    b.HasKey("Id")
                        .HasName("pk_reihen");

                    b.HasIndex("WmHallId")
                        .HasDatabaseName("ix_reihen_wm_hall_id");

                    b.ToTable("reihen", (string)null);
                });

            modelBuilder.Entity("wingLager.domain.WarehouseManagementModels.WmStellplatz", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("created_at");

                    b.Property<string>("CreationUser")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("creation_user");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("description");

                    b.Property<string>("Ean")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("ean");

                    b.Property<long>("Flags")
                        .HasColumnType("bigint")
                        .HasColumnName("flags");

                    b.Property<int>("Height")
                        .HasColumnType("int")
                        .HasColumnName("height");

                    b.Property<int>("Length")
                        .HasColumnType("int")
                        .HasColumnName("length");

                    b.Property<string>("LoadType")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("load_type");

                    b.Property<int>("MaxLoad")
                        .HasColumnType("int")
                        .HasColumnName("max_load");

                    b.Property<string>("Number")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("number");

                    b.Property<string>("Nve")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("nve");

                    b.Property<string>("PalletDescription")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("pallet_description");

                    b.PrimitiveCollection<string>("PossiblePalletType")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("possible_pallet_type");

                    b.Property<string>("ReferenceNumber")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("reference_number");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("status");

                    b.Property<string>("UpdateUser")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("update_user");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("updated_at");

                    b.Property<int>("Width")
                        .HasColumnType("int")
                        .HasColumnName("width");

                    b.Property<Guid?>("WmEbeneId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("wm_ebene_id");

                    b.Property<Guid?>("WmEbenenId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("wm_ebenen_id");

                    b.Property<Guid?>("WmVbr")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("wm_vbr");

                    b.Property<Guid?>("WmWep")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("wm_wep");

                    b.HasKey("Id")
                        .HasName("pk_stellplaetze");

                    b.HasIndex("WmEbeneId")
                        .HasDatabaseName("ix_stellplaetze_wm_ebene_id");

                    b.HasIndex("WmVbr")
                        .HasDatabaseName("ix_stellplaetze_wm_vbr");

                    b.HasIndex("WmWep")
                        .HasDatabaseName("ix_stellplaetze_wm_wep");

                    b.ToTable("stellplaetze", (string)null);
                });

            modelBuilder.Entity("wingLager.domain.WarehouseManagementModels.WmVbr", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("created_at");

                    b.Property<string>("CreationUser")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("creation_user");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("description");

                    b.Property<string>("Number")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("number");

                    b.Property<string>("UpdateUser")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("update_user");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("updated_at");

                    b.HasKey("Id")
                        .HasName("pk_vbrs");

                    b.ToTable("Vbrs", (string)null);
                });

            modelBuilder.Entity("wingLager.domain.WarehouseManagementModels.WmWep", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("created_at");

                    b.Property<string>("CreationUser")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("creation_user");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("description");

                    b.Property<string>("Number")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("number");

                    b.Property<string>("UpdateUser")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("update_user");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("updated_at");

                    b.HasKey("Id")
                        .HasName("pk_wep");

                    b.ToTable("Wep", (string)null);
                });

            modelBuilder.Entity("wingLager.domain.PaletLagers.PaletLager", b =>
                {
                    b.HasOne("wingLager.domain.ProcessOrders.ProcessOrderPosition", null)
                        .WithMany("PaletLager")
                        .HasForeignKey("ProcessOrderPositionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_palet_lager_process_order_position_process_order_position_id");
                });

            modelBuilder.Entity("wingLager.domain.ProcessOrders.ProcessOrderPosition", b =>
                {
                    b.HasOne("wingLager.domain.ProcessOrders.ProcessOrderHeader", null)
                        .WithMany("Positionen")
                        .HasForeignKey("ProcessOrderHeaderNumber")
                        .HasPrincipalKey("Number")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_process_order_position_process_order_header_process_order_header_number");
                });

            modelBuilder.Entity("wingLager.domain.WarehouseManagementModels.WmEbene", b =>
                {
                    b.HasOne("wingLager.domain.WarehouseManagementModels.WmFeld", null)
                        .WithMany("EbenenList")
                        .HasForeignKey("WmFeldId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_ebenen_felder_wm_feld_id");
                });

            modelBuilder.Entity("wingLager.domain.WarehouseManagementModels.WmFeld", b =>
                {
                    b.HasOne("wingLager.domain.WarehouseManagementModels.WmReihe", null)
                        .WithMany("FeldList")
                        .HasForeignKey("WmReiheId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_felder_reihen_wm_reihe_id");
                });

            modelBuilder.Entity("wingLager.domain.WarehouseManagementModels.WmReihe", b =>
                {
                    b.HasOne("wingLager.domain.WarehouseManagementModels.WmHall", null)
                        .WithMany("Reihen")
                        .HasForeignKey("WmHallId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_reihen_halls_wm_hall_id");
                });

            modelBuilder.Entity("wingLager.domain.WarehouseManagementModels.WmStellplatz", b =>
                {
                    b.HasOne("wingLager.domain.WarehouseManagementModels.WmEbene", null)
                        .WithMany("StellplatzList")
                        .HasForeignKey("WmEbeneId")
                        .HasConstraintName("fk_stellplaetze_ebenen_wm_ebene_id");

                    b.HasOne("wingLager.domain.WarehouseManagementModels.WmVbr", null)
                        .WithMany("VbrStellplatze")
                        .HasForeignKey("WmVbr")
                        .HasConstraintName("fk_stellplaetze_vbrs_wm_vbr");

                    b.HasOne("wingLager.domain.WarehouseManagementModels.WmWep", null)
                        .WithMany("Stellplaetze")
                        .HasForeignKey("WmWep")
                        .HasConstraintName("fk_stellplaetze_wep_wm_wep");
                });

            modelBuilder.Entity("wingLager.domain.ProcessOrders.ProcessOrderHeader", b =>
                {
                    b.Navigation("Positionen");
                });

            modelBuilder.Entity("wingLager.domain.ProcessOrders.ProcessOrderPosition", b =>
                {
                    b.Navigation("PaletLager");
                });

            modelBuilder.Entity("wingLager.domain.WarehouseManagementModels.WmEbene", b =>
                {
                    b.Navigation("StellplatzList");
                });

            modelBuilder.Entity("wingLager.domain.WarehouseManagementModels.WmFeld", b =>
                {
                    b.Navigation("EbenenList");
                });

            modelBuilder.Entity("wingLager.domain.WarehouseManagementModels.WmHall", b =>
                {
                    b.Navigation("Reihen");
                });

            modelBuilder.Entity("wingLager.domain.WarehouseManagementModels.WmReihe", b =>
                {
                    b.Navigation("FeldList");
                });

            modelBuilder.Entity("wingLager.domain.WarehouseManagementModels.WmVbr", b =>
                {
                    b.Navigation("VbrStellplatze");
                });

            modelBuilder.Entity("wingLager.domain.WarehouseManagementModels.WmWep", b =>
                {
                    b.Navigation("Stellplaetze");
                });
#pragma warning restore 612, 618
        }
    }
}
