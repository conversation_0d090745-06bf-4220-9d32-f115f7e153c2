using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace wingLager.data.Migrations
{
    /// <inheritdoc />
    public partial class AddPaletLagerTable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "PaletLager",
                columns: table => new
                {
                    ID = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    NVEIndexNr = table.Column<long>(type: "bigint", nullable: true),
                    NVENr = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    ArtikelNr = table.Column<long>(type: "bigint", nullable: true),
                    ArtBez1 = table.Column<string>(type: "nvarchar(30)", maxLength: 30, nullable: false),
                    ArtBez2 = table.Column<string>(type: "nvarchar(30)", maxLength: 30, nullable: true),
                    ArtBez3 = table.Column<string>(type: "nvarchar(30)", maxLength: 30, nullable: true),
                    ArtBez4 = table.Column<string>(type: "nvarchar(30)", maxLength: 30, nullable: true),
                    ArtEAN = table.Column<string>(type: "nvarchar(15)", maxLength: 15, nullable: true),
                    ArtBezGr = table.Column<string>(type: "nvarchar(10)", maxLength: 10, nullable: true),
                    ChargNr = table.Column<string>(type: "nvarchar(25)", maxLength: 25, nullable: true),
                    JobDatum = table.Column<DateTime>(type: "datetime2", nullable: false),
                    MHD = table.Column<DateTime>(type: "datetime2", nullable: false),
                    Gewicht = table.Column<double>(type: "float", nullable: true),
                    AnzEtik = table.Column<short>(type: "smallint", nullable: true),
                    Zutaten = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    LagerBest = table.Column<bool>(type: "bit", nullable: false),
                    AuftrNr = table.Column<long>(type: "bigint", nullable: true),
                    AbgDatum = table.Column<DateTime>(type: "datetime2", nullable: true),
                    FremdUeberg = table.Column<bool>(type: "bit", nullable: true),
                    JobUhrzeit = table.Column<DateTime>(type: "datetime2", nullable: true),
                    LagerNr = table.Column<long>(type: "bigint", nullable: true),
                    ExportDatum = table.Column<DateTime>(type: "datetime2", nullable: true),
                    ExportUhrzeit = table.Column<DateTime>(type: "datetime2", nullable: true),
                    enthEAN = table.Column<string>(type: "nvarchar(15)", maxLength: 15, nullable: true),
                    KolliAnz = table.Column<string>(type: "nvarchar(10)", maxLength: 10, nullable: true),
                    KolliInh = table.Column<string>(type: "nvarchar(15)", maxLength: 15, nullable: true),
                    EANKonsEinh = table.Column<string>(type: "nvarchar(15)", maxLength: 15, nullable: true),
                    MaschNr = table.Column<short>(type: "smallint", nullable: true),
                    ProtNr = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: true),
                    Status = table.Column<string>(type: "nvarchar(10)", maxLength: 10, nullable: true),
                    AnzSack = table.Column<short>(type: "smallint", nullable: true),
                    FWare = table.Column<bool>(type: "bit", nullable: true),
                    SEtikett = table.Column<bool>(type: "bit", nullable: true),
                    Version = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    PalNr = table.Column<long>(type: "bigint", nullable: true),
                    MandNr = table.Column<long>(type: "bigint", nullable: true),
                    PalAnz = table.Column<short>(type: "smallint", nullable: true),
                    CreationUser = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    UpdateUser = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    BestellNr = table.Column<string>(type: "nvarchar(30)", maxLength: 30, nullable: true),
                    ArtNrLieferant = table.Column<string>(type: "nvarchar(30)", maxLength: 30, nullable: true),
                    KundNrWE = table.Column<long>(type: "bigint", nullable: true),
                    KundName1 = table.Column<string>(type: "nvarchar(30)", maxLength: 30, nullable: true),
                    KundName2 = table.Column<string>(type: "nvarchar(30)", maxLength: 30, nullable: true),
                    KundStrasse = table.Column<string>(type: "nvarchar(30)", maxLength: 30, nullable: true),
                    KundPLZ = table.Column<string>(type: "nvarchar(10)", maxLength: 10, nullable: true),
                    KundOrt = table.Column<string>(type: "nvarchar(30)", maxLength: 30, nullable: true),
                    LieferDatum = table.Column<DateTime>(type: "datetime2", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PaletLager$ID", x => x.ID);
                });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "PaletLager");
        }
    }
}
