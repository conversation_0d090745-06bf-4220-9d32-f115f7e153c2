using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace wingLager.data.Migrations
{
    /// <inheritdoc />
    public partial class ChangeProcessArticlePalettiererRezeptIntToString : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<string>(
                name: "PalettiererRezept",
                table: "ProcessArticle",
                type: "nvarchar(max)",
                nullable: true,
                oldClrType: typeof(int),
                oldType: "int");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<int>(
                name: "PalettiererRezept",
                table: "ProcessArticle",
                type: "int",
                nullable: false,
                defaultValue: 0,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldNullable: true);
        }
    }
}
