using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace wingLager.data.Migrations
{
    /// <inheritdoc />
    public partial class AddNoteAndChangeNameForProcessArticle : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "AnzahlDerGebinde",
                table: "ProcessArticle",
                newName: "PalettiererRezept");

            migrationBuilder.AddColumn<string>(
                name: "Note",
                table: "ProcessArticle",
                type: "nvarchar(512)",
                maxLength: 512,
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Note",
                table: "ProcessArticle");

            migrationBuilder.RenameColumn(
                name: "PalettiererRezept",
                table: "ProcessArticle",
                newName: "AnzahlDerGebinde");
        }
    }
}
