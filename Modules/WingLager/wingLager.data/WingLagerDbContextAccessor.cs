using WingCore.application.Contract;
using WingCore.application.Contract.IModels;
using wingLager.application.Contracts;

namespace wingLager.data;

public class WingLagerDbContextAccessor(ISelectedMandant selectedMandant) : IWingLagerDbContextAccessor
{
    private string ConnectionString { get; set; } = string.Empty;

    public string GetConnectionString()
    {
        LoadConnectionStringFromFileAndSet();
        return ConnectionString;
    }
    
    public bool ValidateConnectionData()
    {
        var param = LoadConnectionObjectFromFile();

        return param is not null && param.IsLagerDbFieldsValid();
    }
    
    public void SetConnectionString(string dbPath, string dbName, string dbUserName, string dbPassword)
    {
        ConnectionString = $"Data Source={dbPath};Initial Catalog={dbName};TrustServerCertificate=True;User={dbUserName};Password={dbPassword};MultipleActiveResultSets=True";
    }

    public void SetConnectionString(string connectionString)
    {
        ConnectionString = connectionString;
    }

    private void LoadConnectionStringFromFileAndSet()
    {
        var param = LoadConnectionObjectFromFile();

        if(param is null)
            return;
        
        SetConnectionString(param.LagerDbPath,param.LagerDbName,param.LagerDbUsername,param.LagerDbPassword);
    }
    
    public IDbParameter? LoadConnectionObjectFromFile() => selectedMandant.LoadConnectionObjectFromFile().GetAwaiter().GetResult();
    public async Task SaveDbConnectionData(IDbParameter dbParameter) => await selectedMandant.SaveDbConnectionData(dbParameter);
}