using Commons.ComboBoxs;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using StateMachineMasstransit;
using wingLager.application.Contracts;
using wingLager.data.EntityConfigurations;
using wingLager.data.EntityConfigurations.LindeQueueConfiguration;
using wingLager.data.EntityConfigurations.WareHouseManagement;
using wingLager.domain.AduitLogs;
using wingLager.domain.ComboBoxs;
using wingLager.domain.Common;
using wingLager.domain.LindeQueue;
using wingLager.domain.LindeQueueModel;
using wingLager.domain.PaletLagers;
using wingLager.domain.ProcessArticles;
using wingLager.domain.ProcessOrders;
using wingLager.domain.WarehouseManagementModels;

namespace wingLager.data;


public sealed class WingLagerDbContext : DbContext, IUnitOfWork
{
    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
    {
        base.OnConfiguring(optionsBuilder);
        optionsBuilder.UseSnakeCaseNamingConvention()
            .UseQueryTrackingBehavior(QueryTrackingBehavior.NoTracking);
        
#if (DEBUG)

        optionsBuilder.LogTo(Console.WriteLine, LogLevel.Information).EnableSensitiveDataLogging();;
        //optionsBuilder.UseSqlServer("Data Source=**********\\VASEXPRESS;Initial Catalog=WingLager;TrustServerCertificate=True;User=***;Password=***;MultipleActiveResultSets=True");
       //optionsBuilder.UseSqlServer("Server=localhost,1433;Database=WingLager;TrustServerCertificate=True;User Id=sa;Password=Test1234;");
#endif
    }
    
    public WingLagerDbContext(DbContextOptions<WingLagerDbContext> options, bool tryMigration)
        : base(options)
    {
        if(!tryMigration)
            return;

        if (!Database.GetPendingMigrations().Any())
            return;
        
        Database.Migrate();
    }
    
    public void Detach<TEntity>(TEntity entity) where TEntity : class
    {
        var entry = Entry(entity);
        entry.State = EntityState.Detached;
    }
    
   
    public DbSet<ProcessOrderHeader> ProcessOrderHeaders { get; set; }
    public DbSet<ProcessOrderPosition> ProcessOrderPositions { get; set; }
    public DbSet<ProcessOrderLock> ProcessOrderLocks { get; set; }
    public DbSet<PaletLager> PaletLagers { get; set; }
    public DbSet<Combobox> Comboboxs { get; set; }
    public DbSet<ProcessArticle> ProcessArticles { get; set; }
    public DbSet<AuditEntry> AuditLogs { get; set; }

    public DbSet<WmHall> Halls { get; set; }
    public DbSet<WmReihe> Reihen { get; set; }
    public DbSet<WmFeld> Felder { get; set; }
    public DbSet<WmEbene> Ebenen { get; set; }
    public DbSet<WmStellplatz> Stellplaetze { get; set; }
    public DbSet<WmVbr> Vbrs { get; set; }
    public DbSet<WmWep> Weps { get; set; }
    public DbSet<LindeQueueEntry> LindeQueue { get; set; }
    
    public DbSet<BackgroundState> BackgroundStates { get; set; }
    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        new ProcessOrderHeaderConfiguration().Configure(modelBuilder.Entity<ProcessOrderHeader>());
        new ProcessOrderLockConfiguration().Configure(modelBuilder.Entity<ProcessOrderLock>());
        new ProcessOrderPositionConfiguration().Configure(modelBuilder.Entity<ProcessOrderPosition>());
        new ProcessArticleConfiguration().Configure(modelBuilder.Entity<ProcessArticle>());

        new PaletLagerConfiguration().Configure(modelBuilder.Entity<PaletLager>());

        new ComboboxsConfiguration().Configure(modelBuilder.Entity<Combobox>());
        
        new AuditLogsConfiguration().Configure(modelBuilder.Entity<AuditEntry>());
        new BackgroundStateConfiguration().Configure(modelBuilder.Entity<BackgroundState>());
        new WmHallConfiguration().Configure(modelBuilder.Entity<WmHall>());
        new WmStellplatzConfiguration().Configure(modelBuilder.Entity<WmStellplatz>());
        new WmEbeneConfiguration().Configure(modelBuilder.Entity<WmEbene>());
        new WmVbrConfiguration().Configure(modelBuilder.Entity<WmVbr>());
        new WmWepConfiguration().Configure(modelBuilder.Entity<WmWep>());
        new LindeQueueConfiguration().Configure(modelBuilder.Entity<LindeQueueEntry>());
    }
    
    protected override void ConfigureConventions(ModelConfigurationBuilder configurationBuilder)
    {
        configurationBuilder
            .Properties<ProcessOrderHeaderStatus>()
            .HaveConversion<ProcessOrderHeaderStatusConverter>();

        configurationBuilder
            .Properties<ProcessOrderPositionStatus>()
            .HaveConversion<ProcessOrderPositionStatusConverter>();

        configurationBuilder
            .Properties<ComboboxType>()
            .HaveConversion<ComboboxTypeConverter>();
    }
}