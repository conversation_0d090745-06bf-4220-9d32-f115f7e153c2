using Microsoft.EntityFrameworkCore.ChangeTracking;
using wingLager.application.Contracts;
using wingLager.data.Extensions;
using wingLager.domain.AduitLogs;

namespace wingLager.data.Repositories;

public class AuditLogRepository(WingLagerDbContext repositoryContext)
    : RepositoryBase<AuditEntry>(repositoryContext), IAuditLogRepository
{
    public void AddLogChanges(object currObject, string userName)
    {
        var currentEntry = RepositoryContext.Entry(currObject);

        AddLogChanges(currentEntry, currentEntry.State.ToString(), userName);
    }

    public void AddLogChanges(object currObject, string action, string userName)
    {
        var currentEntry = RepositoryContext.Entry(currObject);

        AddLogChanges(currentEntry, action, userName);
    }

    public void AddLogChanges(EntityEntry currentObject, string userName)
    {
        AddLogChanges(currentObject, currentObject.State.ToString(), userName);
    }

    public void AddLogChanges(EntityEntry oldObject, EntityEntry newObject, string userName)
    {
        AddLogChanges(newObject, newObject.State.ToString(), newObject.GetModifiedProperties(oldObject), userName);
    }
    
    public void AddLogChanges(EntityEntry oldObject, string action, string userName)
    {
        AddLogChanges(oldObject, action, "", userName);
    }
    
    public void AddLogChanges(EntityEntry currentObject,
                            string action,
                            string modifiedData,
                            string userName)
    {
        var auditEntry = new AuditEntry
        {
            CratedAt = DateTime.UtcNow,
            Action = action,
            EntityType = currentObject.Entity.GetType().Name,
            EntityId = currentObject.GetEntityId(),
            Metadata = currentObject.GetAllViewModelProperties(),
            ModifiedData = modifiedData,
            Succeeded = true
        };

        Create(auditEntry);
    }
}