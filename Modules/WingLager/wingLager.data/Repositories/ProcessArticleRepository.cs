using Microsoft.EntityFrameworkCore;
using wingLager.application.Contracts;
using wingLager.application.SearchParams;
using wingLager.data.Repositories.Helper;
using wingLager.domain.ProcessArticles;

namespace wingLager.data.Repositories;

public class ProcessArticleRepository(WingLagerDbContext repositoryContext, IAuditLogRepository auditLogRepository)
    : RepositoryBase<ProcessArticle>(repositoryContext), IProcessArticleRepository
{
    public async Task<IEnumerable<ProcessArticle>?> GetAllAsync(bool trackChanges, ProcessArticleSearchParam searchParam)
    {
        if(trackChanges)
            return await RepositoryContext.ProcessArticles.SetSearchParam(searchParam).ToListAsync();
        else
            return await RepositoryContext.ProcessArticles.SetSearchParam(searchParam).AsNoTracking().ToListAsync();
    }

    public async Task<ProcessArticle?> GetByNumberAsync(long number, bool trackChanges)
    {
        return await FindByCondition(c => c.Number.Equals(number), trackChanges).FirstOrDefaultAsync();
    }

    public async Task<ProcessArticle?> GetByIdAsync(long id, bool trackChanges)
    {
        return await FindByCondition(c => c.Id == id, trackChanges).FirstOrDefaultAsync();
    }

    public async Task<ProcessArticle> Create(ProcessArticle processArticle, string userName)
    {
        await Task.Delay(100);
        
        var nowDateTime = DateTime.UtcNow;
        
        processArticle.Number = GetMaxNumber() + 1;
        processArticle.CreatedAt = nowDateTime;
        processArticle.CreationUser = userName;
        processArticle.UpdatedAt = nowDateTime;
        processArticle.UpdateUser = userName;

        Create(processArticle);
        
        await SaveChangesAsync();
        var entry = RepositoryContext.Entry(processArticle);
        
        auditLogRepository.AddLogChanges(entry, "Create", userName);
        
        entry.State = EntityState.Detached;
        
        return processArticle;
    }

    public async Task<ProcessArticle> Update(ProcessArticle processArticle, string userName)
    {
        await Task.Delay(100);
        
        var oldObject = await GetByIdAsync(processArticle.Id, false);
        
        if(oldObject is null)
            throw new Exception($"ProcessArticle with Id ({processArticle.Id})not found");

        if (oldObject == processArticle)
            return processArticle;
        
        var oldEntry = RepositoryContext.Entry(oldObject);
        
        processArticle.UpdatedAt = DateTime.UtcNow;
        processArticle.UpdateUser = userName;
        
        Update(processArticle);
        
        var newEntry = RepositoryContext.Entry(processArticle);
        
        auditLogRepository.AddLogChanges(oldEntry, newEntry, userName);

        return processArticle;
    }

    public async Task Delete(long processArticleId)
    {
        var objSearchResults = await GetAllAsync(false, new ProcessArticleSearchParam() with{ Id = processArticleId});
        var objSearchResult = objSearchResults?.FirstOrDefault();
        if(objSearchResult is null)
            return;
        
        Delete(objSearchResult);
        
        var currentEntry = RepositoryContext.Entry(objSearchResult);
        
        auditLogRepository.AddLogChanges(currentEntry, "");
    }
    
    public long GetMaxNumber()
    {
        return RepositoryContext.ProcessArticles.Any() ?  RepositoryContext.ProcessArticles.Max(c => c.Number) : 0;
    }
}