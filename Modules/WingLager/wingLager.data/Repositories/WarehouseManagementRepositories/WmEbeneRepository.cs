using Microsoft.EntityFrameworkCore;
using wingLager.application.Contracts;
using wingLager.application.Contracts.WarehouseManagementRepositories;
using wingLager.application.IServices.WarehouseManagementIServices;
using wingLager.domain.WarehouseManagementModels;

namespace wingLager.data.Repositories.WarehouseManagementRepositories;

public class WmEbeneRepository(WingLagerDbContext dbContext, IWmStellplatzService wmStellplatzService, IWmStellplatzRepository wmStellplatzRepository,
    IAuditLogRepository auditLogRepository)
    : WmBaseRepository<WmEbene>(dbContext), IWmEbeneRepository
{
    public async Task<ICollection<WmEbene>> GetByFachIdAsync(Guid fachId, CancellationToken cancellationToken)
    {
       return await dbContext.Set<WmEbene>().Where(z => z.WmFeldId == fachId).OrderBy(z => z.Number).AsNoTracking().ToListAsync(cancellationToken: cancellationToken);
    }

    public async Task DeleteTreeAsync(Guid ebeneId, CancellationToken cancellationToken)
    {
        var ebene = await GetAsync(ebeneId);
        if (ebene is null)
            return;
        ebene.StellplatzList = (await wmStellplatzService.GetWmStellplaetzeByEbenenIddAsync(ebeneId)).ToList();
        foreach (var stellplatz in ebene.StellplatzList)
            await wmStellplatzRepository.DeleteAsync(stellplatz, cancellationToken);
        await DeleteAsync(ebene, cancellationToken);
    }
}