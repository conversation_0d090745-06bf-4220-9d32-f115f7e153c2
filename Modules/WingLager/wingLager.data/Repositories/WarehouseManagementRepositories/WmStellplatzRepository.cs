using wingLager.application.Contracts;
using wingLager.application.Contracts.WarehouseManagementRepositories;
using wingLager.application.IServices.WarehouseManagementIServices;
using wingLager.domain.WarehouseManagementModels;
using wingLager.domain.WarehouseManagementModels.Status;

namespace wingLager.data.Repositories.WarehouseManagementRepositories;

public class WmStellplatzRepository(WingLagerDbContext wingLagerDbContext, IAuditLogRepository auditLogRepository, IWmStellplatzService wmStellplatzService)
    : WmBaseRepository<WmStellplatz>(wingLagerDbContext), IWmStellplatzRepository
{
    public async Task BlockEvenStellplatzOfEbeneAsync(Guid ebeneId, CancellationToken cancellationToken)
    {
        if (ebeneId == Guid.Empty)
            return;
        var stellplatzList = (await wmStellplatzService.GetWmStellplaetzeByEbenenIddAsync(ebeneId)).ToList().Where(s => s.Status == WmStellplatzStatus.EmptyStellplatz);
        var evenStellplaetze = stellplatzList.Where(s => int.TryParse(s.Number, out var number) && (number & 1) == 0).ToList();
        foreach (var stellplatz in evenStellplaetze)
        {
            stellplatz.Status = WmStellplatzStatus.BlockedStellplatz;
            await UpdateAsync(stellplatz, cancellationToken);
        }
    }

    public async Task BlockOddStellplatzOfEbeneAsync(Guid ebeneId, CancellationToken cancellationToken)
    {
        if (ebeneId == Guid.Empty)
            return;
        var stellplatzList = (await wmStellplatzService.GetWmStellplaetzeByEbenenIddAsync(ebeneId)).ToList().Where(s => s.Status == WmStellplatzStatus.EmptyStellplatz);
        var evenStellplaetze = stellplatzList.Where(s => int.TryParse(s.Number, out var number) && (number & 1) != 0).ToList();
        foreach (var stellplatz in evenStellplaetze)
        {
            stellplatz.Status = WmStellplatzStatus.BlockedStellplatz;
            await UpdateAsync(stellplatz, cancellationToken);
        }
    }
}