using Microsoft.EntityFrameworkCore;
using wingLager.application.Contracts;
using wingLager.application.Contracts.WarehouseManagementRepositories;
using wingLager.domain.WarehouseManagementModels;

namespace wingLager.data.Repositories.WarehouseManagementRepositories;

public class WmHallRepository(WingLagerDbContext repositoryContext, IWmReiheRepository wmReiheRepository,
    IAuditLogRepository auditLogRepository)
    : WmBaseRepository<WmHall>(repositoryContext), IWmHallRepository
{
    public async Task DeleteTreeAsync(Guid hallId, CancellationToken token = default)
    {
        var hall = await GetAsync(hallId);
        if (hall is null)
            return;
        hall.Reihen = (await wmReiheRepository.GetByHallIdAsync(hallId, token)).ToList();
        foreach (var reihe in hall.Reihen)
        {
            await wmReiheRepository.DeleteTreeAsync(reihe.Id, token);
        }
        await DeleteAsync(hall, token);
    }
}