using Dapper;
using Microsoft.EntityFrameworkCore;
using WingCore.application.Contract.IModels;
using wingLager.application.Contracts;
using wingLager.application.SearchParams;
using wingLager.data.Repositories.Helper;
using wingLager.data.Services;
using wingLager.domain.PaletLagers;
using wingLager.domain.ProcessOrders;
using wingLager.domain.WarehouseManagementModels;

namespace wingLager.data.Repositories;

public class PaletLagerRepository(WingLagerDbContext repositoryContext,
                                  INumberSetRepository numberSetRepository,
                                  IAuditLogRepository auditLogRepository, IWingLagerDbConnectionBuilder connectionBuilder)
    : RepositoryBase<PaletLager>(repositoryContext), IPaletLagerRepository
{
    public async Task<long> Create(PaletLager paletLager, string username)
    {
        paletLager.CreatedAt = DateTime.Now;
        paletLager.CreationUser = username;
        
        Create(paletLager);
        await RepositoryContext.SaveChangesAsync();
        return paletLager.Id;
    }

    public async Task<IEnumerable<PaletLager>?> GetAllAsync(bool trackChanges, PaletLagerSearchParam searchParam)
    {
        if(trackChanges)
            return await RepositoryContext.PaletLagers.SetSearchParam(searchParam).ToListAsync();
        else
            return await RepositoryContext.PaletLagers.SetSearchParam(searchParam).AsNoTracking().ToListAsync();
    }

    public async Task<IEnumerable<PaletLager>?> GetByIdAsync(long id, bool trackChanges)
    {
        return await FindByCondition(c => c.Id.Equals(id), trackChanges).ToListAsync();
    }

    public Task<PaletLager?> GetByIdWithoutTrackingAsync(long id)
    {
        return repositoryContext.PaletLagers.Where(c => c.Id == id).AsNoTracking().FirstOrDefaultAsync();
    }

    public async Task<ProcessOrderHeader> GetProcessOrderHeaderOfPaletLager(long id)
    {
        await using var connection = connectionBuilder.GetConnection();
        var query = """
                    SELECT
                    ph.*
                    FROM [PaletLager] p
                    JOIN [ProcessOrderPosition] po on p.[process_order_position_id] = po.[ID]
                    JOIN [ProcessOrderHeader] ph on po.[ProcessOrderHeaderNumber] = ph.[Number]
                    WHERE p.[ID] = @id
                    """;
        var parameter = new { id };
        var result = await connection.QueryFirstAsync<ProcessOrderHeader>(query, parameter);
        if (result == null)
            throw new InvalidOperationException($"Kein Prozessauftrag für PaletLager mit ID {id} gefunden");
        return result;
    }

    public async Task<WmStellplatz> GetStellplatzOfPalletLager(long number)
    {
        await using var connection = connectionBuilder.GetConnection();
        var query = """
                    SELECT
                    s.*
                    FROM [stellplaetze] s
                    WHERE s.[pallet_lager_id] = @id
                    """;
        var parameter = new { id = number };
        var result = await connection.QueryFirstAsync<WmStellplatz>(query, parameter);
        if(result is null)
            throw new Exception("Diese Palette steht auf keinen Stellplatz");
        return result;
    }

    public async Task<PaletLager?> SetFlagPrintedStartPaletLager(long id)
    {
        var paletLager = await GetByIdWithoutTrackingAsync(id);
        
        //var paletLager = paletLagers?.FirstOrDefault();
        if(paletLager is null)
            return null;
        var testlager = paletLager with { };
        
        paletLager.FlagsWorker.SetPrintStartedFlag();
        await UpdateWithoutTracking(testlager, "");
        
        return paletLager;
    }

    public async Task<PaletLager?> RemoveFlagPrintedStartPaletLager(long id)
    {
        var paletLagers = await GetByIdAsync(id, false);
        var paletLager = paletLagers?.FirstOrDefault();
        if(paletLager is null)
            return null;
        
        paletLager.FlagsWorker.RemovePrintStartedFlag();
        Update(paletLager);
        
        return paletLager;
    }

    public async Task<string?> GetPalletTypeOfPaletLager(long id)
    {
        await using var connection =  connectionBuilder.GetConnection();
        var query = """
                    SELECT
                        [ProcessArticle].[PalettenTyp]
                    FROM [PaletLager]
                    JOIN [ProcessOrderPosition] on [PaletLager].[process_order_position_id] = [ProcessOrderPosition].[ID]
                    JOIN [ProcessArticle] on [ProcessOrderPosition].[ProcessArticleId] = [ProcessArticle].[ID]
                    WHERE [PaletLager].[ID] = @id
                    """;
        var parameter = new { id };
        var result = (await connection.QueryAsync<string>(query, parameter)).FirstOrDefault();
        return result;
    }

    private static readonly SemaphoreSlim Semaphore = new SemaphoreSlim(1, 1);

    public async Task SetNextSsccNrUpdateAndSave(PaletLager paletLager, string userName)
    {
        await Semaphore.WaitAsync();
        try
        {
            var numberSet = await numberSetRepository.GetNextPaletNumberSetAsync();
            var existNumbersetInPaletLager = false;
            do
            {
                var count = await RepositoryContext.PaletLagers.CountAsync(p => p.NVENr == numberSet.CurrentCompleteNumber);
                existNumbersetInPaletLager = count > 0;
                if(existNumbersetInPaletLager)
                    numberSet.GetNextCompleteNumberAndSetCurrent();
            }
            while (existNumbersetInPaletLager);
           
            paletLager.NVENr = numberSet.CurrentCompleteNumber;
            await UpdateWithoutTracking(paletLager, userName);
            await numberSetRepository.SaveChangesAsync();
            await SaveChangesAsync();
            numberSetRepository.ClearChangeTracker();
        }
        finally
        {
            Semaphore.Release();
        }
    }

    public async Task Update(PaletLager paletLager, string userName)
    {
        await Task.Delay(100);
        
        var oldObjects = await GetByIdAsync(paletLager.Id, false);
        var oldObject = oldObjects?.FirstOrDefault();
        
        if(oldObject is null)
            throw new Exception($"PaletLager with Id ({paletLager.Id}) not found");
        
        if (oldObject == paletLager)
            return;
        
        var oldEntry = RepositoryContext.Entry(oldObject);
        
        paletLager.UpdatedAt = DateTime.UtcNow;
        paletLager.UpdateUser = userName;
        
        Update(paletLager);
        
        var newEntry = RepositoryContext.Entry(paletLager);
        
        auditLogRepository.AddLogChanges(oldEntry, newEntry, userName);
    }

    public async Task UpdateWithoutTracking(PaletLager paletLager, string userName, CancellationToken token = default)
    {
        RepositoryContext.Update(paletLager);
        await RepositoryContext.SaveChangesAsync(token);
        RepositoryContext.Entry(paletLager).State = EntityState.Detached;
    }
    
    public async Task Delete(long paletLagerId)
    {
        var objSearchResults = await GetAllAsync(false, new PaletLagerSearchParam(){ Id = paletLagerId});
        var objSearchResult = objSearchResults?.FirstOrDefault();
        if(objSearchResult is null)
            return;
        
        Delete(objSearchResult);
        
        var currentEntry = RepositoryContext.Entry(objSearchResult);
        
        auditLogRepository.AddLogChanges(currentEntry, "");
    }

    public Task CreatePaletLagerWithoutTracking(PaletLager paletLager, string userName)
    {
        RepositoryContext.Set<PaletLager>().Add(paletLager);
        RepositoryContext.SaveChanges();
        RepositoryContext.Entry(paletLager).State = EntityState.Detached;
        return Task.CompletedTask;
    }
}