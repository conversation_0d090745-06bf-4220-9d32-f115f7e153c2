using Microsoft.EntityFrameworkCore;
using wingLager.application.SearchParams;
using wingLager.domain.ProcessOrders;

namespace wingLager.data.Repositories.Helper;

public static class ProcessOrderHeaderSearchHelper
{
    public static IQueryable<ProcessOrderHeader> SetSearchParam(this DbSet<ProcessOrderHeader> query, OrderProcessHeaderSearchParam searchParam)
    {
        var searchOverStatus = searchParam is { StatusList: not null, StatusList.Count: > 0 };
        var withNumber = searchParam is { Number: not null, Number: > 0 };
        var searchOverId = searchParam is { Id: not null, Id: > 0 };

        var sqlQuery = $"""
                       SELECT * 
                          FROM ProcessOrderHeader
                       """;
        var whereAdded = false;               
        if (searchOverStatus)
        {
            var statusListString = string.Join(",", searchParam.StatusList!.Select(s => $"'{s.Value}'").Distinct());
            sqlQuery += $" WHERE Status IN ({statusListString})";
            
            whereAdded = true;
        }
        
        if (withNumber)
        {
            sqlQuery += whereAdded ? " AND" : " WHERE";
            sqlQuery += $" Number = {searchParam.Number}";
            
            whereAdded = true;
        }
        
        if (searchOverId)
        {
            sqlQuery += whereAdded ? " AND" : " WHERE";
            sqlQuery += $" ID = {searchParam.Id}";
            
            whereAdded = true;
        }

        if (searchParam.WithAllData)
            return query
                .FromSqlRaw(sqlQuery)
                .Include(processOrderHeader => processOrderHeader.Positionen!.OrderBy(p => p.Position))
                    .ThenInclude(p => p.PaletLager);
        return query
            .FromSqlRaw(sqlQuery);
    }
}