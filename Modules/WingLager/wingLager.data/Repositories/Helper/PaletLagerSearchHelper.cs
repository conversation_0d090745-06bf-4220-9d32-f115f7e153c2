using Microsoft.EntityFrameworkCore;
using wingLager.application.SearchParams;
using wingLager.domain.PaletLagers;

namespace wingLager.data.Repositories.Helper;

public static class PaletLagerSearchHelper
{
    public static IQueryable<PaletLager> SetSearchParam(this DbSet<PaletLager> query, PaletLagerSearchParam searchParam)
    {
        IQueryable<PaletLager> internQuery = query;

        if(searchParam.Id.HasValue && searchParam.Id > 0)
            internQuery = internQuery.Where(p => p.Id == searchParam.Id);
        if(searchParam.OrderPositionIds is not null &&
           searchParam.OrderPositionIds.Any())
            internQuery = internQuery.Where(p => searchParam.OrderPositionIds.Contains(p.ProcessOrderPositionId));

        return internQuery;
    }
}