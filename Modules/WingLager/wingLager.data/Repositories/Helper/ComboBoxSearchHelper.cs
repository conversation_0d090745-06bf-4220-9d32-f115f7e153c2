using Microsoft.EntityFrameworkCore;
using wingLager.application.SearchParams;
using wingLager.domain.ComboBoxs;

namespace wingLager.data.Repositories.Helper;

public static class ComboBoxSearchHelper
{
    public static IQueryable<Combobox> SetSearchParam(this DbSet<Combobox> query, ComboBoxSearchParam searchParam)
    {
        var sqlQuery = $"""
                       SELECT * 
                          FROM Combobox
                       """;
        var whereAdded = false;

        if (searchParam.Id is not null)
        {
            sqlQuery += $" WHERE ID = '{searchParam.Id}'";
            whereAdded = true;
        }
        
        if (searchParam.Type is not null)
        {
            sqlQuery += whereAdded ? " AND" : " WHERE";
            sqlQuery += $" Type = '{searchParam.Type}'";
            whereAdded = true;
        }
        
        if (searchParam.Key is not null)
        {
            sqlQuery += whereAdded ? " AND" : " WHERE";
            sqlQuery += $" [Key] = '{searchParam.Key}'";
            whereAdded = true;
        }
        
        return query
            .FromSqlRaw(sqlQuery);
    }
}