
using Microsoft.EntityFrameworkCore;
using wingLager.application.IServices.WarehouseManagementIServices;
using wingLager.domain.Api.DTO;
using wingLager.domain.PaletLagers;
using wingLager.domain.WarehouseManagementModels;
using wingLager.domain.WarehouseManagementModels.SearchParams;
using wingLager.domain.WarehouseManagementModels.Status;


namespace wingLager.data.Services.WarehouseManagementServices;

public class WmBestandService(WingLagerDbContext dbContext) :IWmBestandService
{
    public async Task<ICollection<WmBestand>> GetWmBestandAsync(WmBestandSearchParams searchParams)
    {
        var query = from p in dbContext.Set<PaletLager>()
            join s in dbContext.Set<WmStellplatz>() on p.Id equals s.PalletLagerId into StellplatzGroup
            from s in StellplatzGroup.DefaultIfEmpty()
            join e in dbContext.Set<WmEbene>() on s.WmEbenenId equals e.Id into EbeneGroup
            from e in EbeneGroup.DefaultIfEmpty()
            join f in dbContext.Set<WmFeld>() on e.WmFeldId equals f.Id into FeldGroup
            from f in FeldGroup.DefaultIfEmpty()
            join r in dbContext.Set<WmReihe>() on f.WmReiheId equals r.Id into ReiheGroup
            from r in ReiheGroup.DefaultIfEmpty()
            join h in dbContext.Set<WmHall>() on r.WmHallId equals h.Id into HalleGroup
            from h in HalleGroup.DefaultIfEmpty()
            select new WmBestand
            {
                PLagerId = p.Id,
                NveNumber = p.NVENr,
                ProcessOrderPosition = p.ProcessOrderPositionId,
                Article = new ArticleResponse
                {
                    ArticleNumber = p.ArtikelNr,
                    ArticleName1 = p.ArtBez1,
                    ArticleName2 = p.ArtBez2,
                    ArticleName3 = p.ArtBez3,
                    ArticleName4 = p.ArtBez4,
                    ArticleEAN = p.ArtEAN,
                    ArticleUnit = p.ArtBezGr,
                    ArticleNumberForSupplier = p.ArtNrLieferant
                },
                ChargeNr = p.ChargNr ?? string.Empty,
                OrderNumber = p.AuftrNr ?? 0,
                ProductionDateTime = p.JobDatum,
                ExpiryDate = p.MHD.Date,
                Weight = p.Gewicht ?? 0,
                LoadedAtDateTime = p.AbgDatum ?? DateTime.MinValue,
                Category = s.LoadType.Value ?? string.Empty,
                Flags = p.Flags,
                CustomerNumber = p.KundNrWE ?? 0,
                StellplatzBeschreibung = s.PalletDescription ?? string.Empty,
                HallenPosition = new HallenPositionInfo
                {
                    HallenId = h != null ? h.Id : Guid.Empty,
                    HallenNumber = h != null && h.Number != null ? h.Number : "nan",
                    ReiheId = r != null ? r.Id : Guid.Empty,
                    ReiheNumber = r != null && r.Number != null ? r.Number : "nan",
                    FeldId = f != null ? f.Id : Guid.Empty,
                    FeldNumber = f != null && f.Number != null ? f.Number : "nan",
                    EbeneId = e != null ? e.Id : Guid.Empty,
                    EbeneNumber = e != null && e.Number != null ? e.Number : "nan",
                    StellplatzId = s != null ? s.Id : Guid.Empty,
                    StellplatzNumber = s != null && s.Number != null ? s.Number : "nan"
                }
            };
        if (searchParams.ChargeNr is not null)
            query = query.Where(p => p.ChargeNr == searchParams.ChargeNr);
        if (searchParams.OrderNumber is not null)
            query = query.Where(p => p.OrderNumber == searchParams.OrderNumber);
        if (searchParams.KundenNrWe is not null)
            query = query.Where(p => p.CustomerNumber == long.Parse(searchParams.KundenNrWe));
        if (searchParams.ProcessOrderPosition is not null)
            query = query.Where(p => p.ProcessOrderPosition == searchParams.ProcessOrderPosition);
        if (searchParams.ProductionDateFrom is not null)
            query = query.Where(p => p.ProductionDateTime >= searchParams.ProductionDateFrom);
        if (searchParams.ProductionDateTo is not null)
            query = query.Where(p => p.ProductionDateTime <= searchParams.ProductionDateTo);
        /*if (!string.IsNullOrWhiteSpace(searchParams.Category))
            query = query.Where(p => p.Category == searchParams.Category);*/
        
        var result = await query.AsNoTracking().ToListAsync();

        if (!string.IsNullOrWhiteSpace(searchParams.Category))
            result = result.Where(p => p.Category == searchParams.Category).ToList();

        return result;
        
    }
    
    
    
}