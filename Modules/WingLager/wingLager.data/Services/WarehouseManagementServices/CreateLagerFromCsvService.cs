using System.Globalization;
using CsvHelper;
using CsvHelper.Configuration;
using wingLager.application.IServices.WarehouseManagementIServices;
using wingLager.application.WarehouseManagement.Commands;
using wingLager.application.WarehouseManagement.Queries;
using wingLager.domain.WarehouseManagementModels;
using wingLager.domain.WarehouseManagementModels.Status;
using IMediator = MediatR.IMediator;

namespace wingLager.data.Services.WarehouseManagementServices;

public record CreateLagerFromCsvService(IMediator Mediator) : ICreateLagerFromCsv
{
    public List<LagerCsvRow> ReadCsvRows(string base64)
    {
        var cleanedString = base64.Split(",");
        var reader = new StreamReader(new MemoryStream(Convert.FromBase64String(cleanedString[1])));
        var config = new CsvConfiguration(CultureInfo.GetCultureInfo("de-DE"))
        {
            Delimiter = ";",
            HeaderValidated = null,
            MissingFieldFound = null,
            BadDataFound = null,
        };

        var csv = new CsvReader(reader, config);
        csv.Context.TypeConverterOptionsCache.GetOptions<int>().NullValues.Add(string.Empty);

        var records = csv.GetRecords<LagerCsvRow>().ToList();
        return records.Where(r => !string.IsNullOrWhiteSpace(r.Name) && r.Exist).ToList();
    }

    public async Task CreateLager(List<LagerCsvRow> records, CancellationToken cancellationToken = default)
    {
        var keyedRows = records.Select(KeyedLagerRow.FromCsvRow).ToList();


        var importHallList = keyedRows
            .GroupBy(e => e.HallenNr, e => WmHall.Create(null, e.HallenNr, string.Empty))
            .ToDictionary(e => e.Key, e => e.First());
        var importRowList = keyedRows
            .GroupBy(e => new KeyRow(e.HallenNr, e.RegalNr), e => WmReihe.Create(null, e.RegalNr, Guid.Empty))
            .ToDictionary(e => e.Key, e => e.First());
        var importFieldList = keyedRows
            .GroupBy(e => new KeyFeld(e.HallenNr, e.RegalNr, e.FeldNr), e => WmFeld.Create(null, e.FeldNr, Guid.Empty))
            .ToDictionary(e => e.Key, e => e.First());
        var importLevelList = keyedRows
            .GroupBy(e => new KeyEbene(e.HallenNr, e.RegalNr, e.FeldNr, e.EbenenNr),
                e => WmEbene.Create(null, e.EbenenNr, Guid.Empty, 0,
                    e.EbenenNr is "1" or "01"
                        ? WmEbenenRestriction.OverLoadRestriction
                        : WmEbenenRestriction.NoRestriction))
            .ToDictionary(e => e.Key, e => e.First());
        var importSpaceList = keyedRows
            .GroupBy(e => new KeyStellplatz(e.HallenNr, e.RegalNr, e.FeldNr, e.EbenenNr, e.StellplatzNr),
                e => WmStellplatz.Create(e.Description, e.StellplatzNr, Guid.Empty, null, null, e.PalettType,
                    WmStellplatzStatus.EmptyStellplatz, e.Length, e.Width, e.Height, e.MaxWeight, e.Automatic))
            .ToDictionary(e => e.Key, e => e.First());

        foreach (var (_, value) in importSpaceList)
        {
            value.FlagWorker.SetStellplatzFlag();
        }


        var hallIds = await UpdateOrCreateWmHalls(importHallList);

        var rowIds = await UpdateOrCreateRow(importRowList, hallIds);

        var feldIds = await UpdateOrCreateField(importFieldList, rowIds);

        var levelIds = await UpdateOrCreateLevel(importLevelList, feldIds);

        var storeSpaceIds = await UpdateOrCreateStellplatz(importSpaceList, levelIds);
    }

    private async Task<Dictionary<string, Guid>> UpdateOrCreateWmHalls(Dictionary<string, WmHall> importHallList)
    {
        var existingHalls = await Mediator.Send(new GetAllWmHallQuery());
        var createImportHallList = importHallList;
        List<WmHall> needsUpdateHallList = [];
        Dictionary<string, Guid> hallNumberToId = [];
        foreach (var (key, hallObject) in createImportHallList)
        {
            var dbHallEntry = existingHalls.FirstOrDefault(x => x.Number == hallObject.Number);


            if (dbHallEntry is null)
            {
                hallNumberToId.TryAdd(hallObject.Number, hallObject.Id);
                continue;
            }

            hallNumberToId.TryAdd(dbHallEntry.Number, dbHallEntry.Id);
            createImportHallList.Remove(key);

            if (hallObject.Description == dbHallEntry.Description &&
                hallObject.Location == dbHallEntry.Location)
            {
                continue;
            }

            var updatedHallObject = dbHallEntry with
            {
                Description = hallObject.Description,
                Location = hallObject.Location,
            };

            needsUpdateHallList.Add(updatedHallObject);
        }

        await Mediator.Send(new CreateWmHallFromListCommand(createImportHallList.Values.ToList()));
        await Mediator.Send(new UpdateWmHallFromListCommand(needsUpdateHallList));

        return hallNumberToId;
    }

    private async Task<Dictionary<KeyRow, Guid>> UpdateOrCreateRow(Dictionary<KeyRow, WmReihe> importRowList,
        Dictionary<string, Guid> hallIds)
    {
        var existingRowList = await Mediator.Send(new GetAllWmReiheQuery());

        List<WmReihe> needsUpdateReiheList = [];
        List<WmReihe> needCreateReiheList = [];
        Dictionary<KeyRow, Guid> rowNumberToId = [];

        foreach (var (key, value) in importRowList)
        {
            if (!hallIds.TryGetValue(key.HallNr, out var hallId))
                continue; //TODO LOG

            var dbRowEntry = existingRowList.FirstOrDefault(x => x.Number == value.Number && x.WmHallId == hallId);

            if (dbRowEntry is null)
            {
                needCreateReiheList.Add(value with { WmHallId = hallId });
                rowNumberToId.TryAdd(key, value.Id);
                continue;
            }

            rowNumberToId.TryAdd(key, dbRowEntry.Id);

            if (value.Description == dbRowEntry.Description)
                continue;

            var updatedRowObject = dbRowEntry with
            {
                Description = value.Description,
            };

            needsUpdateReiheList.Add(updatedRowObject);
        }

        await Mediator.Send(new CreateWmReiheFromListCommand(needCreateReiheList));
        await Mediator.Send(new UpdateWmReiheFromListCommand(needsUpdateReiheList));

        return rowNumberToId;
    }

    private async Task<Dictionary<KeyFeld, Guid>> UpdateOrCreateField(Dictionary<KeyFeld, WmFeld> importFieldList,
        Dictionary<KeyRow, Guid> rowIds)
    {
        var existingFieldList = await Mediator.Send(new GetAllWmFeldQuery());

        List<WmFeld> needsUpdateFeldList = [];
        List<WmFeld> needCreateFeldList = [];
        Dictionary<KeyFeld, Guid> fieldNumberToId = [];

        foreach (var field in importFieldList)
        {
            if (!rowIds.TryGetValue(new KeyRow(field.Key.HallNr, field.Key.RegalNr), out var rowId))
                continue; //TODO LOG

            var dbFieldEntry =
                existingFieldList.FirstOrDefault(x => x.Number == field.Value.Number && x.WmReiheId == rowId);

            if (dbFieldEntry is null)
            {
                needCreateFeldList.Add(field.Value with { WmReiheId = rowId });
                fieldNumberToId.TryAdd(field.Key, field.Value.Id);
                continue;
            }

            fieldNumberToId.TryAdd(field.Key, dbFieldEntry.Id);

            if (field.Value.Description == dbFieldEntry.Description)
                continue;

            var updatedRowObject = dbFieldEntry with
            {
                Description = field.Value.Description,
            };

            needsUpdateFeldList.Add(updatedRowObject);
        }

        await Mediator.Send(new CreateWmFeldFromListCommand(needCreateFeldList));
        await Mediator.Send(new UpdateWmFeldFromListCommand(needsUpdateFeldList));

        return fieldNumberToId;
    }

    private async Task<Dictionary<KeyEbene, Guid>> UpdateOrCreateLevel(Dictionary<KeyEbene, WmEbene> importLevelList,
        Dictionary<KeyFeld, Guid> feldIds)
    {
        var existingLevelList = await Mediator.Send(new GetAllWmEbeneQuery());
        List<WmEbene> needsUpdateEbeneList = [];
        List<WmEbene> needCreateEbeneList = [];
        Dictionary<KeyEbene, Guid> levelNumberToId = [];

        foreach (var level in importLevelList)
        {
            if (!feldIds.TryGetValue(new KeyFeld(level.Key.HallNr, level.Key.RegalNr, level.Key.FeldNr),
                    out var fieldId))
                continue; //TODO LOG

            var dbLevelEntry =
                existingLevelList.FirstOrDefault(x => x.Number == level.Value.Number && x.WmFeldId == fieldId);

            if (dbLevelEntry is null)
            {
                needCreateEbeneList.Add(level.Value with { WmFeldId = fieldId });
                levelNumberToId.TryAdd(level.Key, level.Value.Id);
                continue;
            }

            levelNumberToId.TryAdd(level.Key, dbLevelEntry.Id);
            if (level.Value.Description == dbLevelEntry.Description)
                continue;
            var updatedRowObject = dbLevelEntry with
            {
                Description = level.Value.Description,
            };
            needsUpdateEbeneList.Add(updatedRowObject);
        }

        await Mediator.Send(new CreateWmEbeneFromListCommand(needCreateEbeneList));
        await Mediator.Send(new UpdateWmEbeneFromListCommand(needsUpdateEbeneList));
        return levelNumberToId;
    }

    private async Task<Dictionary<KeyStellplatz, Guid>> UpdateOrCreateStellplatz(
        Dictionary<KeyStellplatz, WmStellplatz> importSpaceList,
        Dictionary<KeyEbene, Guid> levelIds)
    {
        var existingSpaceList = await Mediator.Send(new GetAllWmStellplaetzeQuery());

        List<WmStellplatz> needsUpdateStellplatzList = [];
        List<WmStellplatz> needCreateStellplatzList = [];
        Dictionary<KeyStellplatz, Guid> spaceNumberToId = [];

        foreach (var space in importSpaceList)
        {
            if (!levelIds.TryGetValue(
                    new KeyEbene(space.Key.HallNr, space.Key.RegalNr, space.Key.FeldNr, space.Key.EbeneNr),
                    out var levelId))
                continue; //TODO LOG

            var dbSpaceEntry =
                existingSpaceList.FirstOrDefault(x => x.Number == space.Value.Number && x.WmEbenenId == levelId);

            if (dbSpaceEntry is null)
            {
                space.Value.FlagWorker.SetStellplatzFlag();
                needCreateStellplatzList.Add(space.Value with { WmEbenenId = levelId });
                spaceNumberToId.TryAdd(space.Key, space.Value.Id);
                continue;
            }

            spaceNumberToId.TryAdd(space.Key, dbSpaceEntry.Id);

            if (space.Value.Description == dbSpaceEntry.Description)
                continue;

            var updatedRowObject = dbSpaceEntry with
            {
                Description = space.Value.Description,
                PossiblePalletType = space.Value.PossiblePalletType,
            };

            needsUpdateStellplatzList.Add(updatedRowObject);
        }

        await Mediator.Send(new CreateWmStellplatzFromListCommand(needCreateStellplatzList));
        await Mediator.Send(new UpdateWmStellplatzFromListCommand(needsUpdateStellplatzList));

        return spaceNumberToId;
    }

    private record KeyRow(string HallNr, string RegalNr);

    private record KeyFeld(string HallNr, string RegalNr, string FeldNr);

    private record KeyEbene(string HallNr, string RegalNr, string FeldNr, string EbeneNr);

    private record KeyStellplatz(string HallNr, string RegalNr, string FeldNr, string EbeneNr, string StellplatzNr);
}