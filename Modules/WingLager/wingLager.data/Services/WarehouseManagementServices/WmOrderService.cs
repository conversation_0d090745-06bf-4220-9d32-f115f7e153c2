using Dapper;
using wingLager.application.IServices.WarehouseManagementIServices;
using wingLager.domain.Common;
using wingLager.domain.PaletLagers;
using wingLager.domain.ProcessOrders;
using wingLager.domain.WarehouseManagementModels;

namespace wingLager.data.Services.WarehouseManagementServices;

public class WmOrderService(IWingLagerDbConnectionBuilder connectionBuilder) : IWmOrderService
{
    public async Task<ICollection<WmOrder>> GetAllAsync()
    {
        await using var connection = connectionBuilder.GetConnection();
        var query = """
                    SELECT 
                        processOrderHeader.*,
                        processOrderPosition.*,
                        paletLager.*
                    FROM [ProcessOrderHeader] processOrderHeader
                    JOIN [ProcessOrderPosition] processOrderPosition 
                        ON processOrderHeader.[Number] = processOrderPosition.[ProcessOrderHeaderNumber]
                    JOIN [PaletLager] paletLager 
                        ON paletLager.[process_order_position_id] = processOrderPosition.[ID]
                    JOIN [stellplaetze] s on paletLager.[ID] = s.[pallet_lager_id]
                    WHERE processOrderHeader.[Status] LIKE @Status
                    """;

        var parameter = new { Status = ProcessOrderHeaderStatus.InWareHouseString };
        var kommissionierungDict = new Dictionary<long, WmOrder>();
        
        await connection.QueryAsync<ProcessOrderHeader, ProcessOrderPosition, PaletLager, WmOrder>(
            query,
            (header, position, paletLager) =>
            {
                if (!kommissionierungDict.TryGetValue(header.Id, out var kommissionierung))
                {
                    kommissionierung = new WmOrder
                    {
                        ProcessOrderHeader = header,
                        ProcessOrderPositions = new List<ProcessOrderPosition>(),
                        PaletLager = new List<PaletLager>()
                    };
                    kommissionierungDict.Add(header.Id, kommissionierung);
                }

                kommissionierung.ProcessOrderPositions.Add(position);
                kommissionierung.PaletLager.Add(paletLager);
                if(kommissionierung.OrderNumber == 0)
                    kommissionierung.OrderNumber = kommissionierung.ProcessOrderPositions.First().OrderNumber;
                if(string.IsNullOrEmpty(kommissionierung.WarenEmpfaenger))
                    kommissionierung.WarenEmpfaenger = kommissionierung.ProcessOrderPositions.FirstOrDefault()?.WarenEmpfaengerName ?? string.Empty;
                return kommissionierung;
            },
            parameter,
            splitOn: "ID,ID"
        );
        return kommissionierungDict.Values.ToList();
    }
}