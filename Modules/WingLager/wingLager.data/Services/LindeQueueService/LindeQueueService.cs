using Microsoft.EntityFrameworkCore;
using wingLager.application.IServices.LindeQueueIService;
using wingLager.domain.LindeQueue;
using wingLager.domain.LindeQueueModel;

namespace wingLager.data.Services.LindeQueueService;

public class LindeQueueService(WingLagerDbContext dbContext) : ILindeQueueService
{
    public Task<ICollection<LindeQueueEntry>> GetAllAsync()
    {
        var result = dbContext.Set<LindeQueueEntry>().OrderBy(l => l.CreatedAt).ToList();
        return Task.FromResult<ICollection<LindeQueueEntry>>(result);
    }

    public Task<LindeQueueEntry?> GetByIdAsync(Guid requestId)
    {
        var result = dbContext.Set<LindeQueueEntry>().Find(requestId);
        return Task.FromResult(result);
    }

    public Task<ICollection<LindeQueueEntry>> GetByStatusAsync(LindeQueueStatus status)
    {
        var result = dbContext.Set<LindeQueueEntry>().Where(l => l.Status == status).AsNoTracking().ToList();
        return Task.FromResult<ICollection<LindeQueueEntry>>(result);
    }

    public Task<ICollection<LindeQueueEntry>> GetAllOpenEntries()
    {
        var result = dbContext.Set<LindeQueueEntry>()
            .Where(l => l.Status == LindeQueueStatus.Created || l.Status == LindeQueueStatus.Waiting).AsNoTracking()
            .ToList();
        return Task.FromResult<ICollection<LindeQueueEntry>>(result);
    }
}