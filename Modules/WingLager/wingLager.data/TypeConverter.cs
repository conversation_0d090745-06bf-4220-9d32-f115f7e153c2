using Commons.ComboBoxs;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using wingLager.domain.Common;
using wingLager.domain.LindeQueue;
using wingLager.domain.ProcessArticles;
using wingLager.domain.WarehouseManagementModels.Status;


namespace wingLager.data;


internal class ProcessOrderHeaderStatusConverter()
    : ValueConverter<ProcessOrderHeaderStatus, string>(s => s.Value, c => new ProcessOrderHeaderStatus(c));
internal class ProcessOrderPositionStatusConverter()
    : ValueConverter<ProcessOrderPositionStatus, string>(s => s.Value, c => new ProcessOrderPositionStatus(c));
internal class ComboboxTypeConverter()
    : ValueConverter<ComboboxType, string>(s => s.Value, c => new ComboboxType(c));

public class DimensionInMilliMeterConverter : ValueConverter<DimensionInMilliMeter, string>
{
    public DimensionInMilliMeterConverter()
        : base(
            v => v.ToString(),
            v => DimensionInMilliMeter.Parse(v))
    {
    }
}
internal class WmStellplatzStatusConverter()
    : ValueConverter<WmStellplatzStatus, string>(s => s.value, c => new WmStellplatzStatus(c));
    
internal class WmStellplatzLoadTypeConverter()
    : ValueConverter<WmStellplatzLoadType, string>(s => s.Value, c => new WmStellplatzLoadType(c));

internal class WmEbeneRestrictionTypeConverter()
    : ValueConverter<WmEbenenRestriction, string>(s => s.value, c => new WmEbenenRestriction(c));
    
internal class LindeQueueStatusTypeConverter()
    : ValueConverter<LindeQueueStatus, string>(s => s.value, c => new LindeQueueStatus(c));