using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using wingLager.domain.ProcessOrders;

namespace wingLager.data.EntityConfigurations;

public class ProcessOrderLockConfiguration : IEntityTypeConfiguration<ProcessOrderLock>
{
    public void Configure(EntityTypeBuilder<ProcessOrderLock> builder)
    {
        builder
            .HasNoKey()
            .ToTable("ProcessOrderLock");

        builder.Property(e => e.LockStart);
        builder.Property(e => e.LockUser).HasMaxLength(100);
    }
}