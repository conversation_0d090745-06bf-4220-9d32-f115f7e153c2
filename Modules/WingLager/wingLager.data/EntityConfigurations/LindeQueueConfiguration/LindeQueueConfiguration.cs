using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using wingLager.domain.LindeQueue;
using wingLager.domain.LindeQueueModel;

namespace wingLager.data.EntityConfigurations.LindeQueueConfiguration;

public class LindeQueueConfiguration : IEntityTypeConfiguration<LindeQueueEntry>
{
    public void Configure(EntityTypeBuilder<LindeQueueEntry> builder)
    {
        builder.ToTable("LindeQueue");

        builder.HasKey(q => q.Id);

        builder.Property(q => q.PalletLagerId)
            .IsRequired();

        builder.Property(q => q.CreatedAt)
            .IsRequired();

        builder.Property(q => q.Destination)
            .IsRequired()
            .HasMaxLength(100); 

        builder.Property(q => q.ExecutedAt);

        builder.Property(q => q.RobotAnswer)
            .IsRequired()
            .HasColumnType("nvarchar(max)");
        builder.Property(q => q.SessionId);
        builder.Property(q => q.MissionId);

        builder.Property(q => q.Status)
            .HasConversion<LindeQueueStatusTypeConverter>()
            .IsRequired();
    }
}
