using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using wingLager.domain.WarehouseManagementModels;
using wingLager.domain.WarehouseManagementModels.Status;

namespace wingLager.data.EntityConfigurations.WareHouseManagement;

public class WmEbeneConfiguration : IEntityTypeConfiguration<WmEbene>
{
    public void Configure(EntityTypeBuilder<WmEbene> builder)
    {
        builder.HasKey(e => e.Id);
        
        builder.Property(e => e.Description)
            .HasMaxLength(255);
        builder.Property(e => e.Number)
            .HasMaxLength(255);
        builder.Property<WmEbenenRestriction>(e => e.Restrictions)
            .HasConversion<WmEbeneRestrictionTypeConverter>();
    }
}