using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using wingLager.domain.WarehouseManagementModels;

namespace wingLager.data.EntityConfigurations.WareHouseManagement;

public class WmHallConfiguration : IEntityTypeConfiguration<WmHall>
{
    public void Configure(EntityTypeBuilder<WmHall> builder)
    {
        builder.ToTable("Halls");

        builder.<PERSON><PERSON><PERSON>(h => h.Id);

        builder.HasMany(h => h.<PERSON><PERSON><PERSON>)
            .WithOne()
            .HasForeign<PERSON>ey(r => r.WmHallId);
    }
}
