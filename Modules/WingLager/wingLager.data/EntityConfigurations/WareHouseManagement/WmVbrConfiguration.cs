using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using wingLager.domain.WarehouseManagementModels;

namespace wingLager.data.EntityConfigurations.WareHouseManagement;

public class WmVbrConfiguration : IEntityTypeConfiguration<WmVbr>
{
    public void Configure(EntityTypeBuilder<WmVbr> builder)
    {
        builder.ToTable("Vbrs");
        builder.<PERSON><PERSON><PERSON>(v => v.Id);
        builder.HasMany(h => h.VbrStellplatze).WithOne().HasForeignKey(r => r.WmVbr);
    }
}