using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using StateMachineMasstransit;
using wingLager.domain.AduitLogs;
using wingLager.domain.ProcessArticles;

namespace wingLager.data.EntityConfigurations;

public class BackgroundStateConfiguration : IEntityTypeConfiguration<BackgroundState>
{
    public void Configure(EntityTypeBuilder<BackgroundState> builder)
    {
        builder.ToTable("BackgroundState");
        builder.<PERSON><PERSON>ey(x => x.CorrelationId);
        builder.Property(e => e.CreationDateTimeUtc).HasColumnName("CreationDateTimeUtc");
        builder.Property(e => e.CorrelationId).HasColumnName("CorrelationId");
        builder.Property(e => e.CurrentState).HasColumnName("CurrentState");
        builder.Property(e => e.ObjectName).HasColumnName("ObjectName");
        builder.Property(e => e.Counter).HasColumnName("Counter");
        builder.Property(e => e.InfoMessage).HasColumnName("InfoMessage");
        builder.Property(e => e.Data).HasColumnName("Data");
    }
}