using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using wingLager.domain.PaletLagers;

namespace wingLager.data.EntityConfigurations;

public class PaletLagerConfiguration : IEntityTypeConfiguration<PaletLager>
{
    public void Configure(EntityTypeBuilder<PaletLager> builder)
    {
        builder.HasKey(e => e.Id).HasName("PaletLager$ID");

        builder.ToTable("PaletLager");

        builder.Property(e => e.Id).HasColumnName("ID");
        builder.Property(e => e.NVEIndexNr).HasColumnName("NVEIndexNr");
        builder.Property(e => e.NVENr).HasColumnName("NVENr").HasMaxLength(50);
        builder.Property(e => e.ArtikelNr).HasColumnName("ArtikelNr");
        builder.Property(e => e.ArtBez1).HasColumnName("ArtBez1").HasMaxLength(120).IsRequired();
        builder.Property(e => e.ArtBez2).HasColumnName("ArtBez2").HasMaxLength(120);
        builder.Property(e => e.ArtBez3).HasColumnName("ArtBez3").HasMaxLength(120);
        builder.Property(e => e.ArtBez4).HasColumnName("ArtBez4").HasMaxLength(120);
        builder.Property(e => e.ArtEAN).HasColumnName("ArtEAN").HasMaxLength(15);
        builder.Property(e => e.ArtBezGr).HasColumnName("ArtBezGr").HasMaxLength(10);
        builder.Property(e => e.ChargNr).HasColumnName("ChargNr").HasMaxLength(25);
        builder.Property(e => e.JobDatum).HasColumnName("JobDatum").IsRequired();
        builder.Property(e => e.MHD).HasColumnName("MHD").IsRequired();
        builder.Property(e => e.Gewicht).HasColumnName("Gewicht").HasPrecision(18,2);
        builder.Property(e => e.AnzEtik).HasColumnName("AnzEtik");
        builder.Property(e => e.Zutaten).HasColumnName("Zutaten");
        builder.Property(e => e.LagerBest).HasColumnName("LagerBest").IsRequired();
        builder.Property(e => e.AuftrNr).HasColumnName("AuftrNr");
        builder.Property(e => e.AbgDatum).HasColumnName("AbgDatum");
        builder.Property(e => e.FremdUeberg).HasColumnName("FremdUeberg");
        builder.Property(e => e.JobUhrzeit).HasColumnName("JobUhrzeit");
        builder.Property(e => e.LagerNr).HasColumnName("LagerNr");
        builder.Property(e => e.ExportDatum).HasColumnName("ExportDatum");
        builder.Property(e => e.ExportUhrzeit).HasColumnName("ExportUhrzeit");
        builder.Property(e => e.EnthEAN).HasColumnName("enthEAN").HasMaxLength(15);
        builder.Property(e => e.KolliAnz).HasColumnName("KolliAnz").HasMaxLength(10);
        builder.Property(e => e.KolliInh).HasColumnName("KolliInh").HasMaxLength(15);
        builder.Property(e => e.EANKonsEinh).HasColumnName("EANKonsEinh").HasMaxLength(15);
        builder.Property(e => e.MaschNr).HasColumnName("MaschNr");
        builder.Property(e => e.ProtNr).HasColumnName("ProtNr").HasMaxLength(255);
        builder.Property(e => e.Status).HasColumnName("Status").HasMaxLength(10);
        builder.Property(e => e.AnzSack).HasColumnName("AnzSack");
        builder.Property(e => e.FWare).HasColumnName("FWare");
        builder.Property(e => e.SEtikett).HasColumnName("SEtikett");
        builder.Property(e => e.Version).HasColumnName("Version").HasMaxLength(20);
        builder.Property(e => e.PalNr).HasColumnName("PalNr");
        builder.Property(e => e.MandNr).HasColumnName("MandNr");
        builder.Property(e => e.PalAnz).HasColumnName("PalAnz");
        builder.Property(e => e.CreationUser).HasColumnName("CreationUser").HasMaxLength(50).IsRequired();
        builder.Property(e => e.CreatedAt).HasColumnName("CreatedAt").IsRequired();
        builder.Property(e => e.UpdateUser).HasColumnName("UpdateUser").HasMaxLength(50).IsRequired();
        builder.Property(e => e.UpdatedAt).HasColumnName("UpdatedAt").IsRequired();
        builder.Property(e => e.BestellNr).HasColumnName("BestellNr").HasMaxLength(30);
        builder.Property(e => e.ArtNrLieferant).HasColumnName("ArtNrLieferant").HasMaxLength(30);
        builder.Property(e => e.KundNrWE).HasColumnName("KundNrWE");
        builder.Property(e => e.KundName1).HasColumnName("KundName1").HasMaxLength(30);
        builder.Property(e => e.KundName2).HasColumnName("KundName2").HasMaxLength(30);
        builder.Property(e => e.KundStrasse).HasColumnName("KundStrasse").HasMaxLength(30);
        builder.Property(e => e.KundPLZ).HasColumnName("KundPLZ").HasMaxLength(10);
        builder.Property(e => e.KundOrt).HasColumnName("KundOrt").HasMaxLength(30);
        builder.Property(e => e.LieferDatum).HasColumnName("LieferDatum");
        builder.Property(e => e.SackNumbersWasPrinted);
        builder.Property(e => e.Flags).HasColumnName("Flags");
     }
}