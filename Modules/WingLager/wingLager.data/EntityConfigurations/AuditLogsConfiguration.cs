using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using wingLager.domain.AduitLogs;
using wingLager.domain.ProcessArticles;

namespace wingLager.data.EntityConfigurations;

public class AuditLogsConfiguration : IEntityTypeConfiguration<AuditEntry>
    {
        public void Configure(EntityTypeBuilder<AuditEntry> builder)
        {
            builder.HasKey(e => e.Id).HasName("AuditLogs$ID");

            builder.ToTable("AuditLogs");

            builder.Property(e => e.Id).HasColumnName("ID");
            builder.Property(e => e.Metadata).HasColumnName("Metadata").HasColumnType("VARCHAR(MAX)");
            builder.Property(e => e.ModifiedData).HasColumnName("ModifiedData").HasColumnType("VARCHAR(MAX)");
            builder.Property(e => e.Action).HasColumnName("Action");
            builder.Property(e => e.EntityType).HasColumnName("EntityType");
            builder.Property(e => e.EntityId).HasColumnName("EntityId");
            builder.Property(e => e.CratedAt).HasColumnName("CratedAt");
            builder.Property(e => e.ErrorMessage).HasColumnName("ErrorMessage").HasMaxLength(255);
            builder.Property(e => e.Succeeded).HasColumnName("Succeeded");
        }
    }