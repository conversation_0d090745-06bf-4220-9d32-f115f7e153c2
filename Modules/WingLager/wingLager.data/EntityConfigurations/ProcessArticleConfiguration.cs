using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using wingLager.domain.ProcessArticles;

namespace wingLager.data.EntityConfigurations;

public class ProcessArticleConfiguration : IEntityTypeConfiguration<ProcessArticle>
    {
        public void Configure(EntityTypeBuilder<ProcessArticle> builder)
        {
            builder.HasKey(e => e.Id).HasName("ProcessArticle$ID");

            builder.ToTable("ProcessArticle");

            builder.Property(e => e.Id).HasColumnName("ID");
            builder.Property(e => e.Number).HasColumnName("Number");
            builder.Property(e => e.Description).HasColumnName("Description").HasMaxLength(120).IsRequired();
            builder.Property(e => e.Description2).HasColumnName("Description2").HasMaxLength(120);
            builder.Property(e => e.Description3).HasColumnName("Description3").HasMaxLength(120);
            builder.Property(e => e.Description4).HasColumnName("Description4").HasMaxLength(120);
            builder.Property(e => e.BasisArticleId).HasColumnName("BasisArticleId");
            builder.Property(e => e.BasisArticleNumber).HasColumnName("BasisArticleNumber");
            builder.Property(e => e.CustomerId).HasColumnName("CustomerId");
            builder.Property(e => e.CustomerNumber).HasColumnName("CustomerNumber");
            builder.Property(e => e.EtikTyp).HasColumnName("EtikTyp");
            builder.Property(e => e.PalettenTyp).HasColumnName("PalettenTyp");
            builder.Property(e => e.EanSack).HasColumnName("EanSack").IsRequired();
            builder.Property(e => e.EanPalette).HasColumnName("EanPalette").IsRequired();
            builder.Property(e => e.PalettiererRezept).HasColumnName("PalettiererRezept");
            builder.Property(e => e.RezeptWickler).HasColumnName("RezeptWickler");
            builder.Property(e => e.ChargeNr).HasColumnName("ChargeNr");
            builder.Property(e => e.MhdTimeSpan).HasColumnName("MhdTimeSpan");
            builder.Property(e => e.SackTyp).HasColumnName("SackTyp");
            builder.Property(e => e.SackAnzahl).HasColumnName("SackAnzahl");
            builder.Property(e => e.SackInhalt).HasColumnName("SackInhalt");
            builder.Property(e => e.LayoutPalEtik).HasColumnName("LayoutPalEtik");
            builder.Property(e => e.LayoutSackEtik).HasColumnName("LayoutSackEtik");
            builder.Property(e => e.Note).HasColumnName("Note").HasMaxLength(512);
            builder.Property(e => e.CustomerReference).HasColumnName("CustomerReference").HasMaxLength(512);
            builder.Property(e => e.NumberOfPalletEtikettToPrint).HasColumnName("NumberOfPalletEtikettToPrint");
            
            builder.Property(e => e.CreationUser).HasColumnName("CreationUser").IsRequired();
            builder.Property(e => e.CreatedAt).HasColumnName("CreatedAt").IsRequired();
            builder.Property(e => e.UpdateUser).HasColumnName("UpdateUser").IsRequired();
            builder.Property(e => e.UpdatedAt).HasColumnName("UpdatedAt").IsRequired();
            
            builder.Property(e => e.DetailDescription).HasColumnName("DetailDescription").HasMaxLength(250);
            
            builder.Property(e => e.DimensionInMilliMeter)
                .HasConversion(new DimensionInMilliMeterConverter())
                .HasColumnName("DimensionInMilliMeter");
        }
    }