using wingLager.domain.PaletLagers;

namespace wingLager.domain.Api.DTO;

public record PaletLagerResponse
{
    public long Id { get; set; }
    public long? NVEIndexNumber { get; set; }
    public string? NVENumber { get; set; }
    
    public ArticleResponse Article { get; set; }
    public string? BatchNumber { get; set; }
    public DateTime JobDate { get; set; }
    public DateTime BestBeforeDate { get; set; }
    public string? Ingredients { get; set; }
    public bool Stock { get; set; }
    public long? JobNumber { get; set; }
    public DateTime? AbgDatum { get; set; }
    public DateTime? JobTime { get; set; }
    public DateTime? ExportDate { get; set; }
    public DateTime? ExportTime { get; set; }
    public string? EnthEan { get; set; }
    public string? KolliAnz { get; set; }
    public string? KolliInh { get; set; }
    public string? EankonsEinh { get; set; }
    public string? ProtNr { get; set; }
    public string? State { get; set; }
    public bool? Fware { get; set; }
    public bool? Setikett { get; set; }
    public string? Version { get; set; }
    public string SysUser { get; set; } = string.Empty;
    public DateTime SysTime { get; set; }
    public string? OrderNumber { get; set; }
    public CustomerResponse CustomerResponse { get; set; }
    public DateTime? LieferDatum { get; set; }

    public PaletLagerResponse(PaletLager plager)
    {
        Id = plager.Id;
            NVEIndexNumber = plager.Id;
            NVENumber = plager.NVENr;
            Article = new ArticleResponse
            {
                ArticleNumber = plager.ArtikelNr,
                ArticleName1 = plager.ArtBez1,
                ArticleName2 = plager.ArtBez2,
                ArticleName3 = plager.ArtBez3,
                ArticleName4 = plager.ArtBez4,
                ArticleEAN = plager.EnthEAN,
                ArticleUnit = plager.EANKonsEinh,
                ArticleNumberForSupplier = plager.ArtNrLieferant
            };
            BatchNumber = plager.ChargNr;
            JobDate = plager.JobDatum;
            BestBeforeDate = plager.MHD;
            Ingredients = plager.Zutaten;
            Stock = plager.LagerBest;
            JobNumber = plager.AuftrNr;
            AbgDatum = plager.AbgDatum;
            JobTime = plager.JobUhrzeit;
            ExportDate = plager.ExportDatum;
            ExportTime = plager.ExportUhrzeit;
            EnthEan = plager.EnthEAN;
            KolliAnz = plager.KolliAnz;
            KolliInh = plager.KolliInh;
            EankonsEinh = plager.EANKonsEinh;
            ProtNr = plager.ProtNr;
            State = plager.Status;
            Fware = plager.FWare;
            Setikett = plager.SEtikett;
            Version = plager.Version;
            SysUser = plager.UpdateUser;
            SysTime = plager.UpdatedAt;
            OrderNumber = plager.BestellNr;
            CustomerResponse = new CustomerResponse
            {
                CustomerNumberGoodsReciever = plager.KundNrWE,
                CustomerName1 = plager.KundName1,
                CustomerName2 = plager.KundName2,
                CustomerStreet = plager.KundStrasse,
                CustomerPostcode = plager.KundPLZ,
                CustomerCity = plager.KundOrt
            };
            LieferDatum = plager.LieferDatum;
    }
}