using System.Globalization;
using Microsoft.EntityFrameworkCore;

namespace wingLager.domain.ProcessArticles;

public class DimensionInMilliMeter
{
    public double Width { get; set; }
    public double Height { get; set; }
    public double Depth { get; set; }

    private const int FactorMmToCm = 10;
    private const int DecimalPlaces = 2;

    public double WidthInCm
    {
        get => Math.Round(Width / FactorMmToCm, DecimalPlaces);
        set => Width = Math.Round(Math.Round(value, DecimalPlaces) * FactorMmToCm, DecimalPlaces);
    }

    public double HeightInCm
    {
        get => Math.Round(Height / FactorMmToCm, DecimalPlaces);
        set => Height = Math.Round(Math.Round(value, DecimalPlaces) * FactorMmToCm, DecimalPlaces);
    }

    public double DepthInCm
    {
        get => Math.Round(Depth / FactorMmToCm, DecimalPlaces);
        set => Depth = Math.Round(Math.Round(value, DecimalPlaces) * FactorMmToCm, DecimalPlaces);
    }

    
    public override string ToString()
    {
        return $"{Width.ToString("F2", CultureInfo.InvariantCulture)}|{Height.ToString("F2", CultureInfo.InvariantCulture)}|{Depth.ToString("F2", CultureInfo.InvariantCulture)}";
    }
    
    public static DimensionInMilliMeter Parse(string value)
    {
        if(string.IsNullOrEmpty(value))
            return new DimensionInMilliMeter();
        
        var parts = value.Split('|');
        var culture = CultureInfo.InvariantCulture;
        return new DimensionInMilliMeter
        {
            Width = double.Parse(parts[0], culture),
            Height = double.Parse(parts[1], culture),
            Depth = double.Parse(parts[2], culture)
        };
    }
    
}