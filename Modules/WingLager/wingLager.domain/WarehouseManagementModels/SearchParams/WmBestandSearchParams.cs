using wingLager.domain.ProcessOrders;

namespace wingLager.domain.WarehouseManagementModels.SearchParams;

public record WmBestandSearchParams
{
    public string? ChargeNr { get; set; }
    public long? OrderNumber { get; set; }
    public string? KundenNrWe { get; set; }
    public long? ProcessOrderPosition { get; set; }
    public DateTime? ProductionDateFrom { get; set; }
    public DateTime? ProductionDateTo { get; set; }
    public string Category { get; set; } = string.Empty;
    public bool IsInWarehouse { get; set; } = true;
}