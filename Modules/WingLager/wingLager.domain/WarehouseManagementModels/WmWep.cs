using wingLager.domain.Common;

namespace wingLager.domain.WarehouseManagementModels;

public record WmWep : BaseData
{
    public Guid Id { get; set; }
    public string Description { get; set; } = string.Empty;
    public string Number { get; set; } = string.Empty;
    public List<WmStellplatz> Stellplaetze { get; set; } = [];
    
    public static WmWep Create(string description, string number)
    {
        return new WmWep
        {
            Id = Guid.NewGuid(),
            Description = description,
            Number = number
        };
    }
}