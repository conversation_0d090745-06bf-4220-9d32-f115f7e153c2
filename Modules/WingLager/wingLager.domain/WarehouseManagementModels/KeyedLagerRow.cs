namespace wingLager.domain.WarehouseManagementModels;

public record KeyedLagerRow
{
        
        public string HallenNr { get; set; } = string.Empty;
        public string RegalNr { get; set; } = string.Empty;
        public string FeldNr { get; set; } = string.Empty;
        public string EbenenNr { get; set; } = string.Empty;
        public string StellplatzNr { get; set; } = string.Empty;
        public bool Exist { get; set; } = false;
        public bool Automatic { get; set; } = false;
        public List<string> PalettType { get; set; } = [];
        public string Description { get; set; } = string.Empty;
        public string ChangeNeeded { get; set; } = string.Empty;
        public int MaxWeight { get; set; } = 0;
        public int Length { get; set; } = 0;
        public int Width { get; set; } = 0;
        public int Height { get; set; } = 0;

        public static KeyedLagerRow FromCsvRow(LagerCsvRow csvRow)
        {
                var cleanName = csvRow.Name.Replace("H", "");
            
                var parts = cleanName.Split('-');
                if(parts.Length < 5) throw new Exception("Invalid Lager Name");
            
                var tempHallNumber = parts[0];
                var tempReiheNumber = parts[1];
                var tempFeldNumber = parts[2];
                var tempEbeneNumber = parts[3];
                var tempStellplatzNumber = parts[4];

                return new KeyedLagerRow()
                {
                        HallenNr = tempHallNumber,
                        RegalNr = tempReiheNumber,
                        FeldNr = tempFeldNumber,
                        EbenenNr = tempEbeneNumber,
                        StellplatzNr = tempStellplatzNumber,
                        Exist = csvRow.Exist,
                        Automatic = csvRow.Automatic,
                        PalettType = csvRow.PalettType,
                        Description = csvRow.Description,
                        ChangeNeeded = csvRow.ChangeNeeded,
                        MaxWeight = csvRow.MaxWeight,
                        Length = csvRow.Length,
                        Width = csvRow.Width,
                        Height = csvRow.Height,
                };
        }
}