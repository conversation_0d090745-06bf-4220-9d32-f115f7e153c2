using wingLager.domain.Common;

namespace wingLager.domain.WarehouseManagementModels;

public record WmVbr : BaseData
{
    public Guid Id { get; set; }
    public string Description { get; set; } = string.Empty;
    public string Number { get; set; } = string.Empty;
    public List<WmStellplatz> VbrStellplatze { get; set; } = [];
    public int StellplatzCount { get; set; } = 1;
    
    public static WmVbr Create(string description, string number)
    {
        return new WmVbr
        {
            Id = Guid.NewGuid(),
            Description = description,
            Number = number
        };
    }
}