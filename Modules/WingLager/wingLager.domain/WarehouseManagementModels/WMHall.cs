using wingLager.domain.Common;

namespace wingLager.domain.WarehouseManagementModels;

public record WmHall : BaseData
{
    public Guid Id { get; set; }
    public string Description { get; set; } = string.Empty;
    public string Number { get; set; } = string.Empty;
    public string Location { get; set; } = string.Empty;
    
    public List<WmReihe> Re<PERSON>en { get; set; } = [];
    
    public static WmHall Create(string? description, string number, string location)
    {
        return new WmHall
        {
            Id = Guid.CreateVersion7(),
            Description = description ?? string.Empty,
            Number = number,
            Location = location,
            CreatedAt = DateTime.Now
        };
    }
}