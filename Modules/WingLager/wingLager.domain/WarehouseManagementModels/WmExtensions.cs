using System.Text.RegularExpressions;

namespace wingLager.domain.WarehouseManagementModels;

public static class WmExtensions
{
    private static readonly Regex HallPositionRegex = new(@"^H\d{2}-\d{2}-\d{2}-\d{2}-\d{2}$");

    public static HallenPositionInfo ToHallPosition(this string value)
    {
        if (string.IsNullOrEmpty(value))
            throw new ArgumentNullException(nameof(value));
            
        if (!HallPositionRegex.IsMatch(value))
            throw new ArgumentException("Ungültiges Format für Hallposition. Format muss H##-##-##-##-## sein.");
        
        var parts = value[1..].Split('-');
        
        return new HallenPositionInfo
        {
            HallenNumber = parts[0],
            ReiheNumber = parts[1],
            FeldNumber = parts[2],
            EbeneNumber = parts[3],
            StellplatzNumber = parts[4],
        };
    }
}
