using WingCore.domain.Models;

namespace wingLager.domain.WarehouseManagementModels;

public class WmCustomerMini
{
    public string Number { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
}
public static class CustomerExtensions
{
    public static WmCustomerMini ToWmCustomerMini(this Kunden kunde)
    {
        return new WmCustomerMini
        {
            Number = kunde.Kdnummer.ToString(),
            Name = kunde.Kdname1 ?? string.Empty
        };
    }
    
    // Optional: Eine Version für IEnumerable/Listen
    public static IEnumerable<WmCustomerMini> ToWmCustomerMini(this IEnumerable<Kunden> kunden)
    {
        return kunden.Select(k => k.ToWmCustomerMini());
    }
}
