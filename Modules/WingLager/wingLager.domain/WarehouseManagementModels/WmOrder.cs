using WingCore.domain.Models;
using wingLager.domain.PaletLagers;
using wingLager.domain.ProcessOrders;

namespace wingLager.domain.WarehouseManagementModels;

public record WmOrder()
{
    public long OrderNumber { get; set; } = 0;
    public string WarenEmpfaenger { get; set; } = string.Empty;
    public ProcessOrderHeader? ProcessOrderHeader { get; set; } = null;
    public List<PaletLager> PaletLager { get; set; } = [];
    public ICollection<ProcessOrderPosition> ProcessOrderPositions { get; set; } = [];
    public Auftragskopf? Auftragskopf { get; set; } = null;
    public List<Auftragspo>? AuftragsposList {get; set; } = null;
}