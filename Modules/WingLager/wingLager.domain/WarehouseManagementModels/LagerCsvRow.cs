using CsvHelper;
using CsvHelper.Configuration;
using CsvHelper.Configuration.Attributes;
using CsvHelper.TypeConversion;

namespace wingLager.domain.WarehouseManagementModels;
 [Delimiter(";")]
 [CultureInfo("de-DE")]
 public record LagerCsvRow
 {
     [Name("Bezeichnung")]
     public string Name { get; set; } = string.Empty;
 
     [Name("Gibt’s?")]
     [BooleanTrueValues("y")]
     [BooleanFalseValues("n", "")]
     public bool Exist { get; set; } = false;
     [Name("manuell/auto")]
     [BooleanTrueValues("auto")]
     [BooleanFalseValues("manuell", "")]
     public bool Automatic { get; set; } = false;
     [Name("Stellplatz")]
     [TypeConverter(typeof(StringListConverter))]
     public List<string> PalettType { get; set; } = [];
     [Name("Beschreibung")]
     public string Description { get; set; } = string.Empty;
     [Name("Änderung benötigt")]
     public string ChangeNeeded { get; set; } = string.Empty;
     [Name("Maximales Gewicht")]
     [TypeConverter(typeof(IntWithDefaultZeroConverter))]
     public int MaxWeight { get; set; } = 0;
     [Name("Länge")]
     [TypeConverter(typeof(IntWithDefaultZeroConverter))]
     public int Length { get; set; } = 0;
     [Name("Breite")]
     [TypeConverter(typeof(IntWithDefaultZeroConverter))]
     public int Width { get; set; } = 0;
     [Name("Höhe")]
     [TypeConverter(typeof(IntWithDefaultZeroConverter))]
     public int Height { get; set; } = 0;
 }
 
 public class IntWithDefaultZeroConverter : Int32Converter
 {
     public override object ConvertFromString(string? text, IReaderRow row, MemberMapData memberMapData)
     {
         if (string.IsNullOrWhiteSpace(text))
         {
             return 0;
         }
         var result = base.ConvertFromString(text, row, memberMapData);
         return result ?? 0;
     }
 }
 public class StringListConverter : DefaultTypeConverter
 {
     public override object ConvertFromString(string? text, IReaderRow row, MemberMapData memberMapData)
     {
         if (string.IsNullOrWhiteSpace(text))
             return new List<string>();
            
         return text.Split(',').Select(x => x.Trim()).ToList();
     }
 }