namespace wingLager.domain.WarehouseManagementModels.Status;

public record WmStellplatzLoadType(string Value): IComparable<WmStellplatzLoadType>
{
    private const string FinishedProductLoadTypeString = "Fertigware";
    private const string PackagingMaterialLoadTypeString = "Verpackungsmaterial";
    private const string SparePartsLoadTypeString = "Ersatzteile";
    
    public static WmStellplatzLoadType FinishedProductLoadType => new(FinishedProductLoadTypeString);
    public static WmStellplatzLoadType PackagingMaterialLoadType => new(PackagingMaterialLoadTypeString);
    public static WmStellplatzLoadType SparePartsLoadType => new(SparePartsLoadTypeString);
    
    public static List<WmStellplatzLoadType> GetAll()
    {
        return new List<WmStellplatzLoadType>
        {
            FinishedProductLoadType,
            PackagingMaterialLoadType,
            SparePartsLoadType
        };
    }
    
    public int CompareTo(WmStellplatzLoadType? other)
    {
        return string.Compare(Value, other?.Value, StringComparison.Ordinal);
    }

    public override string ToString()
    {
        return Value;
    }
}