using System.Data;
using Dapper;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using WingCore.domain.Common;

namespace wingLager.domain.WarehouseManagementModels.Status;

public record WmStellplatzStatus(string value) : IComparable<WmStellplatzStatus>
{
    private const string EmptyStellplatzString = "Frei";
    private const string OccupiedStellplatzString = "Belegt";
    private const string BlockedStellplatzString = "Gesperrt";
    
    public static WmStellplatzStatus EmptyStellplatz => new(EmptyStellplatzString);
    public static WmStellplatzStatus OccupiedStellplatz => new(OccupiedStellplatzString);
    public static WmStellplatzStatus BlockedStellplatz => new(BlockedStellplatzString);
    
    
    public bool IsEmptyStellplatz => value == EmptyStellplatzString;

    public static List<WmStellplatzStatus> GetAll()
    {
        return
        [
            EmptyStellplatz,
            OccupiedStellplatz,
            BlockedStellplatz
        ];
    }
    
    
    public int CompareTo(WmStellplatzStatus? other)
    {
        if (ReferenceEquals(this, other)) return 0;
        if (other is null) return 1;
        return string.Compare(value, other.value, StringComparison.Ordinal);
    }
}

public class WmStellplatzStatusHandler : SqlMapper.TypeHandler<WmStellplatzStatus>
{
    public override void SetValue(IDbDataParameter parameter, WmStellplatzStatus? status)
    {
        parameter.Value = status?.value ?? "Error";
    }

    public override WmStellplatzStatus Parse(object value)
    {
        return new WmStellplatzStatus(value.ToString()!);
    }
}