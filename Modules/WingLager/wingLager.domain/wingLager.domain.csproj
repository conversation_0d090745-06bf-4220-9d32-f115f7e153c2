<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
      <ProjectReference Include="..\..\..\libs\ExternalInterfaces\LindeFahrerlosTransportsystem\LindeFahrerlosTransportsystem.csproj" />
      <ProjectReference Include="..\..\WingCore\WingCore.domain\WingCore.domain.csproj" />
      <ProjectReference Include="..\..\WingPrinterListLabel\wingPrinterListLabel.domain\wingPrinterListLabel.domain.csproj" />
    </ItemGroup>

    <ItemGroup>
      <PackageReference Include="CsvHelper" Version="33.1.0" />
      <PackageReference Include="Dapper" Version="2.1.66" />
    </ItemGroup>

</Project>
