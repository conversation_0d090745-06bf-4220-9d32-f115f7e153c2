namespace licenses.application.Contracts;

public interface ICheckTenantData
{
    ValueTask CheckCanCreateTenant(string email, CancellationToken cancellationToken);
    ValueTask CheckName(string name);
    ValueTask CheckEmail(string email);
    ValueTask CheckWartungsDatum(DateTime wartungsDatumBeginn, DateTime wartungsDatumEnde);
    ValueTask CheckMaxSeats(short maxSeats);
    ValueTask CheckCanDeleteData(Guid licenceId);
}