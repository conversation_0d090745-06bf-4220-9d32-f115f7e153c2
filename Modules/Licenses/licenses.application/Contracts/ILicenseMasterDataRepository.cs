using licenses.domain.LicenseMasterDatas;

namespace licenses.application.Contracts;

public interface ILicenseMasterDataRepository
{
    Task AddAsync(LicenseMasterData licenseMasterData);
    Task UpdateAsync(LicenseMasterData licenseMasterData);
    Task DeleteAsync(Guid id);
    ValueTask<LicenseMasterData?> GetAsync(Guid id, CancellationToken cancellationToken);
    Task<IEnumerable<LicenseMasterData>> GetAllAsync();
    Task<LicenseMasterData?> GetByKuerzelAsync(string licenceMasterKuerzel);
}
