using licenses.application.Contracts;
using licenses.domain.Models;

namespace licenses.application.Licenses.Handlers;

public static class LicenseQueryHandler
{
    public static async Task<License?> Handle(LicenseQuery query,
                                              ILicenseRepository repository)
    {
        return await repository.GetAsync(query.LicenseId);
    }
    
    public static async Task<IEnumerable<License>> Handle(LicensesQuery query,
                                                          ILicenseRepository repository)
    {
        return await repository.GetAllAsync(query.WithAllData);
    }
    
    public static async Task<IEnumerable<License>> Handle(LicensesFromIdsQuery query,
                                                          ILicenseRepository repository)
    {
        return await repository.GetAllAsync(query.LicenseIds,query.WithAllData);
    }
    
    public static async Task<IEnumerable<License>> Handle(LicensesWithLicenseMasterDataIdQuery query, ILicenseRepository repository)
    {
        return await repository.GetAllWithLicenseMasterDataIdAsync(query.LicenseMasterDataId);
    }
}