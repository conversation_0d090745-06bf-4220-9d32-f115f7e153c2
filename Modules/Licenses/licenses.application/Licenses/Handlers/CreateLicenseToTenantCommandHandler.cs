using Commons;
using licenses.application.Contracts;
using licenses.application.LicenseMasterDatas;
using licenses.application.Tenants;
using licenses.domain.LicenseMasterDatas;
using licenses.domain.Licenses.Events;
using licenses.domain.Models;
using licenses.domain.Tenants;
using Wolverine;

namespace licenses.application.Licenses.Handlers;

public static class CreateLicenseToTenantCommandHandler
{
    public static async Task<License> Handle(CreateLicenseToTenantCommand command, 
                                            IUUidFactory idFactory,
                                            ILicenseRepository repository,
                                            IMessageBus messageBus,
                                            CancellationToken cancellationToken)
    {
        var tenant = await messageBus.InvokeAsync<Tenant?>(new TenantOverIdQuery(command.TenantId), cancellationToken);
        
        if (tenant is null)
        {
            throw new Exception($"Kein Tenant mit ID({command.TenantId}) gefunden");
        }
        
        var licenseMasterData = await messageBus.InvokeAsync<LicenseMasterData?>(new LicenseMasterDataQuery(command.LicenseMasterDataId), cancellationToken);
        
        if (licenseMasterData is null)
        {
            throw new Exception($"Keine Lizenzstammdaten mit der ID({command.LicenseMasterDataId}) gefunden");
        }
        
        var id = idFactory.CreateNewUuid();
        var @event = new CreateLicenseEvent(id,
                                            tenant.Id, 
                                            licenseMasterData.Id, 
                                            licenseMasterData.Claims, 
                                            command.BeginDateTime.ToUniversalTime(),
                                            command.BeginDateTime.AddDays(tenant.DayValidAfterCreation).ToUniversalTime(),
                                            licenseMasterData.Name,
                                            licenseMasterData.UseOnlyWhenInUse,
                                            tenant.MaximalSeats,
                                            "");
        
        var newLicenses = new License(@event);
        await repository.AddAsync(newLicenses, cancellationToken);

        return newLicenses;
    }
}