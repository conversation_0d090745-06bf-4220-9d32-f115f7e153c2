using licenses.application.Contracts;
using licenses.domain.Models;
using Wolverine;

namespace licenses.application.Licenses.Handlers;

public static class ActivateLicenseCommandHandler
{
    public static async Task Handle(ActivateLicenseCommand command,
                                    ILicenseRepository repository,
                                    IMessageBus messageBus,
                                    CancellationToken cancellationToken)
    {
        var license = await messageBus.InvokeAsync<License?>(new LicenseQuery(command.LicenseId), cancellationToken);
        
        if (license is null)
        {
            throw new Exception($"Keine Lizenz mit der ID({command.LicenseId}) gefunden");
        }
        
        if (license.State == LicenseState.Active) return;
         
        license.State = LicenseState.Active;
        await repository.UpdateAsync(license);
    }
    
    public static async Task Handle(InActivateLicenseCommand command,
                                    ILicenseRepository repository,
                                    IMessageBus messageBus,
                                    CancellationToken cancellationToken)
    {
        var license = await messageBus.InvokeAsync<License?>(new LicenseQuery(command.LicenseId), cancellationToken);
        
        if (license is null)
        {
            throw new Exception($"Keine Lizenz mit der ID({command.LicenseId}) gefunden");
        }
        
         if (license.State == LicenseState.InActive) return;
         
        license.State = LicenseState.InActive;
        await repository.UpdateAsync(license);
    }
}