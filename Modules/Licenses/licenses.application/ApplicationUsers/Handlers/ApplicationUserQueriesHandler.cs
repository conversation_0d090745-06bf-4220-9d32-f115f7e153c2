using licenses.application.Contracts;
using licenses.application.Tenants;
using licenses.domain.ApplicationUsers;
using Marten;
using Wolverine;

namespace licenses.application.ApplicationUsers.Handlers;

public static class ApplicationUserQueriesHandler
{
    public static async Task<IEnumerable<ApplicationUser>> Handle(ApplicationUsersQuery _,
                                                                  IApplicationUserRepository applicationUserRepository,
                                                                  CancellationToken cancellationToken)
    {
        return await applicationUserRepository.GetAllAsync(cancellationToken);
    }
    
    public static async Task<ApplicationUser?> <PERSON>le(ApplicationUserQuery query,
                                                        IApplicationUserRepository applicationUserRepository,
                                                        CancellationToken cancellationToken)
    {
        return await applicationUserRepository.GetAsync(query.ApplicationUserId, cancellationToken);
    }
        
    public static async Task<ApplicationUser?> Handle(ApplicationUserOverEmailQuery query,
                                                        IApplicationUserRepository applicationUserRepository,
                                                        CancellationToken cancellationToken)
    {
        return await applicationUserRepository.GetOverEmailAsync(query.Email);
    }
    
    public static async Task<List<string>> <PERSON><PERSON>(ApplicationUseClaimsQuery query,
                                                        IMessageBus messageBus,
                                                        IApplicationUserRepository applicationUserRepository,
                                                        CancellationToken cancellationToken)
    {
        var appUser =await applicationUserRepository.GetOverEmailAsync(query.Email);
        if(appUser is null || appUser.Id == Guid.Empty)
        {
            return [];
        }
        
        if(appUser.IsOnline)
        {
            return [];
        }
        
        return [];
    }
}