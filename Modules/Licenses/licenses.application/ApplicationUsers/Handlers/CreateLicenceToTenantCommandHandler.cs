using Commons;
using licenses.application.Contracts;
using licenses.application.Tenants;
using licenses.domain.ApplicationUsers;
using licenses.domain.ApplicationUsers.Event;
using licenses.domain.Tenants;
using Marten;
using Wolverine;

namespace licenses.application.ApplicationUsers.Handlers;

public static class CreateApplicationUserForTenantCommandHandler
{
    public static async Task<Guid> Handle(CreateApplicationUserForTenantCommand command,
                                            IUUidFactory idFactory, 
                                            IMessageBus messageBus,
                                            IApplicationUserRepository applicationUserRepository,
                                            CancellationToken cancellationToken)
    {
        var tenant = await messageBus.InvokeAsync<Tenant?>(new TenantOverIdQuery(command.TenantId), cancellationToken);
        
        if (tenant is null)
        {
            throw new Exception($"Kein Tenant mit ID({command.TenantId}) gefunden");
        }
        
        var id = idFactory.CreateNewUuid();
        var @event = new CreateApplicationUserEvent(id, tenant.Id, command.Name, command.Email, "");
        var applicationUser = new ApplicationUser(@event);
        
        await applicationUserRepository.AddAsync(applicationUser, cancellationToken);
        
        return id;
    }
}