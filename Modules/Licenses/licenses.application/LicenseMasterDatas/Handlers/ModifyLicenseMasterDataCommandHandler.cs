using licenses.application.Contracts;
using licenses.application.Licenses;
using licenses.domain.LicenseMasterDatas;
using Marten;
using Wolverine;

namespace licenses.application.LicenseMasterDatas.Handlers;

public static class ModifyLicenseMasterDataCommandHandler
{
    public static async Task Handle(ModifyLicenseMasterDataCommand command,
                                    ICheckLicenseMasterData checkLicenceMasterData,
                                    IMessageBus messageBus,
                                    CancellationToken cancellationToken,
                                    ILicenseMasterDataRepository repository)
    {
        await checkLicenceMasterData.CheckName(command.Body.Name);
        await checkLicenceMasterData.CheckClaims(command.Body.Claims);
        
        var licenseMasterData = await messageBus.InvokeAsync<LicenseMasterData?>(new LicenseQuery(command.LicenseMasterDataId), cancellationToken);
                
        if (licenseMasterData is null)
        {
            throw new Exception($"Keine Lizenz mit der ID({command.LicenseMasterDataId}) gefunden");
        }
                
        licenseMasterData.Apply(command.Body);
        
        await repository.UpdateAsync(licenseMasterData);
    }
}
