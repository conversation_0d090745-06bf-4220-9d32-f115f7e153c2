using Licencing;
using licenses.domain.LicenseMasterDatas.Events;

namespace licenses.application.LicenseMasterDatas;

public sealed record CreateLicenseMasterDataCommand(string Kuerzel, string Name, IEnumerable<string> Claims, bool UseOnlyWhenInUse);

public interface ILicenseMasterDataCommand
{
    Guid LicenseMasterDataId { get; init; }
}

public sealed record ModifyLicenseMasterDataCommand(Guid LicenseMasterDataId, ModifyLicenseMasterDataEvent Body) : ILicenseMasterDataCommand;
public sealed record DeleteLicenseMasterDataCommand(Guid LicenseMasterDataId) : ILicenseMasterDataCommand;
