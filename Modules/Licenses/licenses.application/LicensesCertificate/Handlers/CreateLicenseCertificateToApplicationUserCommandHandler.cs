using Commons;
using Licencing;
using licenses.application.ApplicationUsers;
using licenses.application.Contracts;
using licenses.application.Tenants;
using licenses.domain.ApplicationUsers;
using licenses.domain.LicenseCertificates.Events;
using licenses.domain.Licenses.Events;
using licenses.domain.Models;
using licenses.domain.Tenants;
using Wolverine;

namespace licenses.application.LicensesCertificate.Handlers;

public static class CreateLicenseCertificateToApplicationUserCommandHandler
{
    public static async Task<LicenseCertificate?> Handle(CreateLicenseCertificateToApplicationUserCommand command, 
                                                        IUUidFactory idFactory,
                                                        ILicenseRepository licenseRepository,
                                                        ILicenseCertificateRepository repository,
                                                        IMessageBus messageBus,
                                                        CancellationToken cancellationToken)
    {
        var applicationUser = await messageBus.InvokeAsync<ApplicationUser?>(new ApplicationUserQuery(command.ApplicationUserId), cancellationToken);
        
        if (applicationUser is null)
        {
            throw new Exception($"Kein User mit der ID({command.ApplicationUserId}) gefunden");
        }
        
        var tenant = await messageBus.InvokeAsync<Tenant?>(new TenantOverIdQuery(applicationUser.TenantId), cancellationToken);
        if (tenant is null)
        {
            throw new Exception($"Kein Tenant mit ID({applicationUser.TenantId}) gefunden");
        }

        if (tenant.Licenses is null) return null;
        
        Dictionary<string,string> canUseLicenseClaims = [];
        List<Guid> usedLicenses = [];
        
        foreach (var license in tenant.Licenses)
        {
            if(!license.CanUse()) continue;

            var claimWasAdd = false;
            foreach (var claim in license.Claims)
            {
                if(canUseLicenseClaims.TryAdd(claim, claim)) claimWasAdd = true;
            }
            
            if(!claimWasAdd) continue;
                
            license.Apply(new AddCurrentUesedEvent());
            usedLicenses.Add(license.Id);
            await licenseRepository.UpdateAsync(license);
        }

        var expireAt = DateTime.Now.AddDays(tenant.DayValidAfterCreation);
        var licenseData = LicenseCreationAndValidation.Create(tenant.Name,
                                                              tenant.Email,
                                                              tenant.MaximalSeats,
                                                              expireAt.ToUniversalTime(),
                                                              canUseLicenseClaims.Values.ToList());
         
        // licenseData.Validate();

        var id = idFactory.CreateNewUuid();
        var @event = new CreateLicenseCertificateEvent(id,
                        applicationUser.Id, 
                        expireAt.ToUniversalTime(), 
                        licenseData.PrivateKey,
                        licenseData.PublicKey,
                        licenseData.LicenseDataAsXmlToBase64,
                        usedLicenses,
                        "");
        
        var newLicenseCertificate = new LicenseCertificate(@event);
        await repository.AddAsync(newLicenseCertificate, cancellationToken);

        return newLicenseCertificate;
    }
}