using licenses.domain.Tenants.Events;

namespace licenses.application.Tenants;

public sealed record CreateTenantCommand(string TenantName, string TenantEmail, int MaximalSeats);

public interface ITenantCommand
{
    Guid TenantId { get; init; }
}

public sealed record DeleteTenantCommand(Guid TenantId) : ITenantCommand;
public sealed record ModifyTenantCommand(Guid TenantId, ModifyTenantEvent Body) : ITenantCommand;

