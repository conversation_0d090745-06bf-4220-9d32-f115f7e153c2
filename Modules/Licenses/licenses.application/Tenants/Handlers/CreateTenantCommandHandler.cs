using Commons;
using licenses.application.Contracts;
using licenses.domain.ApplicationUsers;
using licenses.domain.ApplicationUsers.Event;
using licenses.domain.Tenants;
using licenses.domain.Tenants.Events;

namespace licenses.application.Tenants.Handlers;

public static class CreateTenantCommandHandler
{
    public static async Task<Guid> Handle(CreateTenantCommand command,
                                          ITenantRepository tenantRepository,
                                          IApplicationUserRepository applicationUserRepository,
                                          IUUidFactory idFactory,
                                          ICheckTenantData checkTenantData,
                                          CancellationToken cancellationToken)
    {
        await checkTenantData.CheckCanCreateTenant(command.TenantEmail, cancellationToken);
        await checkTenantData.CheckName(command.TenantName);
        
        var idTenant = idFactory.CreateNewUuid();
        var newTenant = new Tenant(new CreateTenantEvent(idTenant, command.TenantName, command.TenantEmail,command.MaximalSeats, ""));
        
        await tenantRepository.AddTenantAsync(newTenant, cancellationToken);
     
        var idApplicationUser = idFactory.CreateNewUuid();
        var @event = new CreateApplicationUserEvent(idApplicationUser, idTenant, command.TenantName, command.TenantEmail, "");
        var applicationUser = new ApplicationUser(@event);
        
        await applicationUserRepository.AddAsync(applicationUser, cancellationToken);
        
        return idTenant;
    }
}
