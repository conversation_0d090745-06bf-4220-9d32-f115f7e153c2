using licenses.application.Contracts;
using licenses.domain.Models;
using Microsoft.EntityFrameworkCore;

namespace licenses.data;

public class LicenseCertificateRepository(LicenseDbContext dataDbContext) : ILicenseCertificateRepository
{
    public ValueTask<LicenseCertificate?> GetAsync(Guid id)
    {
        return ValueTask.FromResult(dataDbContext.Set<LicenseCertificate>()
            .AsNoTracking()
            .Include(l => l.ApplicationUserData)
                .ThenInclude(a => a!.TenantData)
            .FirstOrDefault(l=> l.Id == id));
    }

    public async Task AddAsync(LicenseCertificate licenseCertificate, CancellationToken cancellationToken)
    {
        await dataDbContext.AddAsync(licenseCertificate, cancellationToken: cancellationToken);
    }

    public Task UpdateAsync(LicenseCertificate licenseCertificate)
    {
        dataDbContext.Update(licenseCertificate);
        return Task.CompletedTask;
    }

    public Task DeleteAsync(Guid id)
    {
        dataDbContext.Remove(new LicenseCertificate()
        {
            Id = id
        });
        return Task.CompletedTask;
    }

    public Task<IEnumerable<LicenseCertificate>> GetLicencesFromApplicationUserAsync(Guid applicationUserId)
    {
        return Task.FromResult<IEnumerable<LicenseCertificate>>(dataDbContext.Set<LicenseCertificate>()
                            .AsNoTracking()
                            .Where(license => license.ApplicationUserId == applicationUserId)
                            .OrderByDescending(license => license.CreationDateTime )
                            .ToList());
    }

    public Task<LicenseCertificate?> GetByPublicKeyAndLicenseDataAsBase64(string publicKey, string licenseDataAsBase64)
    {
        return Task.FromResult(dataDbContext
                        .Set<LicenseCertificate>()
                        .AsNoTracking()
                        .Include(l => l.ApplicationUserData)
                            .ThenInclude(a => a!.TenantData)
                        .FirstOrDefault(license => license.PublicKey == publicKey &&
                                                      license.LicenseDataAsBase64 == licenseDataAsBase64));
        
    }

    public Task<IEnumerable<LicenseCertificate>> GetAllAsync(bool withAllData)
    {
        if (!withAllData)
        {
            return Task.FromResult<IEnumerable<LicenseCertificate>>(dataDbContext.Set<LicenseCertificate>()
                                        .AsNoTracking()
                                        .OrderByDescending(license => license.CreationDateTime)
                                        .ToList());
        }
        
        return Task.FromResult<IEnumerable<LicenseCertificate>>(dataDbContext.Set<LicenseCertificate>()
                            .AsNoTracking()
                            .Include(license => license.ApplicationUserData)
                            .OrderByDescending(license => license.CreationDateTime)
                            .ToList());
    }
    
}