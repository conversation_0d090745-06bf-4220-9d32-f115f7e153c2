using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;
using Microsoft.Extensions.Configuration;

namespace licenses.data;


public class LicenseDbContextFactory(IConfiguration configuration) : IDesignTimeDbContextFactory<LicenseDbContext>
{
    public LicenseDbContextFactory() : this(new ConfigurationRoot([]))
    {
    }
    
    public LicenseDbContext CreateDbContext(string[] args)
    {
        var optionsBuilder = new DbContextOptionsBuilder<LicenseDbContext>();
        optionsBuilder.EnableSensitiveDataLogging()
                        .EnableServiceProviderCaching()
                        .UseNpgsql(configuration.GetConnectionString("license-db") ?? "", options =>
                        {
                        });
       
        return new LicenseDbContext(optionsBuilder.Options);
    }
}