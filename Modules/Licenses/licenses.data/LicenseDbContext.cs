using licenses.domain.ApplicationUsers;
using licenses.domain.LicenseMasterDatas;
using Microsoft.EntityFrameworkCore;
using licenses.domain.Models;
using licenses.domain.Tenants;

namespace licenses.data;

public class LicenseDbContext(DbContextOptions<LicenseDbContext> dbContextOptions) : DbContext(dbContextOptions), IUnityOfWork
{
    private const int DEFAULT_STRING_LENGTH = 250;
    
    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
    {
        base.OnConfiguring(optionsBuilder);
        optionsBuilder.UseSnakeCaseNamingConvention();
    }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        
        modelBuilder.Entity<LicenseMasterData>(builder =>
        {
            builder.HasKey(e => e.Id);
  
            builder.Property(e => e.Name).IsRequired().HasMaxLength(DEFAULT_STRING_LENGTH);
            builder.Property(e => e.<PERSON>zel).IsRequired().HasMaxLength(DEFAULT_STRING_LENGTH);
            builder.Property(e => e.Claims);
            builder.Property(e => e.UseOnlyWhenInUse);
            builder.Property(e => e.CreationDateTime).IsRequired();
            builder.Property(e => e.CreationUserName).IsRequired();
            builder.Property(e => e.UpdateUserName).IsRequired();
        });

        modelBuilder.Entity<License>(builder =>
        {
            builder.HasKey(e => e.Id);
            
            builder.Property(e => e.Name).IsRequired().HasMaxLength(DEFAULT_STRING_LENGTH);
            builder.Property(e => e.TenantId).IsRequired();
            builder.Property(e => e.LicenseMasterDataId).IsRequired();
            builder.Property(e => e.Claims).IsRequired();
            builder.Property(e => e.State).IsRequired();
            builder.Property(e => e.BeginnDateTime).IsRequired();
            builder.Property(e => e.EndDateTime).IsRequired();
            builder.Property(e => e.UseOnlyWhenInUse).IsRequired();
            builder.Property(e => e.MaximalUse).IsRequired();
            builder.Property(e => e.CurrentUsed).IsRequired();
            builder.Property(e => e.CreationDateTime).IsRequired();
            builder.Property(e => e.CreationUserName).IsRequired();
            builder.Property(e => e.UpdateUserName).IsRequired();
            builder.HasOne(e => e.TenantData)
                   .WithMany(t => t.Licenses)
                   .HasForeignKey(e => e.TenantId);
        });

        modelBuilder.Entity<LicenseCertificate>(builder =>
        {
            builder.HasKey(e => e.Id);
            
            builder.Property(e => e.ApplicationUserId).IsRequired();
            builder.Property(e => e.PrivateKey).IsRequired();
            builder.Property(e => e.PublicKey).IsRequired();
            builder.Property(e => e.LicenseDataAsBase64).IsRequired();
            builder.Property(e => e.ExpireAt).IsRequired();
            builder.Property(e => e.CreationDateTime).IsRequired();
            builder.Property(e => e.CreationUserName).IsRequired();
            builder.Property(e => e.UpdateUserName).IsRequired();
            builder.Property(e => e.UsedLicensesId);
            builder.HasOne(e => e.ApplicationUserData)
                   .WithMany(t => t.LicenseCertificatesList)
                   .HasForeignKey(e => e.ApplicationUserId);
        });
                
        modelBuilder.Entity<Tenant>(builder =>
        {
            builder.HasKey(e => e.Id);

            builder.Property(e => e.Name).IsRequired().HasMaxLength(DEFAULT_STRING_LENGTH);
            builder.Property(e => e.Email).IsRequired().HasMaxLength(DEFAULT_STRING_LENGTH);
            builder.Property(e => e.NormalizedInviterEmail).IsRequired().HasMaxLength(DEFAULT_STRING_LENGTH);
            builder.Property(e => e.DayValidAfterCreation).IsRequired();
            builder.Property(e => e.WartungsDatumBeginn).IsRequired();
            builder.Property(e => e.WartungsDatumEnde).IsRequired();
            builder.Property(e => e.CreationDateTime).IsRequired();
            builder.Property(e => e.CreationUserName).IsRequired();
            builder.Property(e => e.UpdateUserName).IsRequired();
            builder.HasMany(e => e.ApplicationUserDataList)
                   .WithOne(t => t.TenantData)
                   .HasForeignKey(e => e.TenantId);
            builder.HasMany(e => e.Licenses)
                   .WithOne(t => t.TenantData)
                   .HasForeignKey(e => e.TenantId);
        });

        modelBuilder.Entity<ApplicationUser>(builder =>
        {
            builder.HasKey(e => e.Id);
            builder.Property(e => e.TenantId).IsRequired();
            builder.Property(e => e.Name).IsRequired().HasMaxLength(DEFAULT_STRING_LENGTH);
            builder.Property(e => e.Email).IsRequired().HasMaxLength(DEFAULT_STRING_LENGTH);
            builder.Property(e => e.NormalizedInviterEmail).IsRequired().HasMaxLength(DEFAULT_STRING_LENGTH);
            builder.Property(e => e.CreationDateTime).IsRequired();
            builder.Property(e => e.CreationUserName).IsRequired();
            builder.Property(e => e.UpdateUserName).IsRequired();
            builder.Property(e => e.IsOnline).IsRequired();
            builder.HasMany(e => e.LicenseCertificatesList)
                   .WithOne(t => t.ApplicationUserData)
                   .HasForeignKey(e => e.ApplicationUserId);
            builder.HasOne(e => e.TenantData)
                   .WithMany(t => t.ApplicationUserDataList)
                   .HasForeignKey(e => e.TenantId);
        });
    }
    
    protected override void ConfigureConventions(ModelConfigurationBuilder configurationBuilder)
    {
    }
    
}
