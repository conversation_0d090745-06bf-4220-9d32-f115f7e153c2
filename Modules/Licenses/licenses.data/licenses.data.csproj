<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
      <PackageReference Include="EFCore.NamingConventions" Version="9.0.0" />
      <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.8" />
      <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="9.0.8" />
      <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="9.0.8" />
      <PackageReference Include="Microsoft.Extensions.Logging.Debug" Version="9.0.8" />
      <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="9.0.4" />
      <PackageReference Include="System.Drawing.Common" Version="9.0.8" />
      <PackageReference Include="WolverineFx.Postgresql" Version="4.8.0" />
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\licenses.application\licenses.application.csproj" />
      <ProjectReference Include="..\licenses.domain\licenses.domain.csproj" />
    </ItemGroup>

</Project>
