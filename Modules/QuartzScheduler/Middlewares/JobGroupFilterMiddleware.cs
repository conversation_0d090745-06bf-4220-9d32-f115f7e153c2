
using Microsoft.Extensions.Configuration;
using Quartz;
using QuartzScheduler.Extensions;

namespace QuartzScheduler.Middlewares;

public class JobGroupFilterListener(IConfiguration config) : IJobListener
{
    private readonly string? _serverName = config["Quartz:ServerName"];

    public string Name => "JobGroupFilter";

    public Task JobExecutionVetoed(IJobExecutionContext context, CancellationToken cancellationToken = default)
        => Task.CompletedTask;

    public Task JobToBeExecuted(IJobExecutionContext context, CancellationToken cancellationToken = default)
    {
        var serverNameFromJob = context.JobDetail.Key.GetServerName();

        if (string.IsNullOrWhiteSpace(serverNameFromJob))
            return Task.CompletedTask;
  
        if (string.Equals(_serverName, serverNameFromJob, StringComparison.OrdinalIgnoreCase))
            return Task.CompletedTask;
        
        Console.WriteLine($"[Quartz] Job '{context.JobDetail.Key}' wird auf Server '{_serverName}' übersprungen (gehört zu '{serverNameFromJob}').");

        throw new JobExecutionException("Dieser Server ist für diesen Job nicht zuständig.")
        {
            RefireImmediately = false
        };
    }

    public Task JobWasExecuted(IJobExecutionContext context, JobExecutionException? jobException, CancellationToken cancellationToken = default)
        => Task.CompletedTask;

}