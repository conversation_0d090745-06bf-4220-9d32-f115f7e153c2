using MediatR;
using Quartz;
using Microsoft.Extensions.Logging;
using ObjectDeAndSerialize;
using QuartzScheduler.Jobs.BaseJobs;
using WingCore.application.Wiegeschein;
using wingPrinterListLabel.application.Invoices;

namespace QuartzScheduler.Jobs;

[DisallowConcurrentExecution]
[PersistJobDataAfterExecution]
public class SendEWgsPerMailJob(ILogger<SendEWgsPerMailJob> logger, ISender mediatr) : BaseJob(logger)
{
    protected override async Task<string> ExecuteJob(IJobExecutionContext context)
    {
        var wgsToSends = await mediatr.Send(new WgsToSendEasiAdfinityQuery());
        List<LogInfoForEWgs> logInformations = [];
        
        foreach (var wgsToSend in wgsToSends)
        {
            var logInformation = new LogInfoForEWgs
            {
                WgsNumber = wgsToSend.WgsNumber,
                Success = false
            };
            
            try
            {
                // Note: This would need to be implemented with actual Wgs email sending logic
                // For now, using a placeholder command that would need to be created
                // var emailData = await mediatr.Send(new WgsSendEmailCommand(wgsToSend.WgsNumber, ""));
                // logInformation.Email = emailData?.EmailParameter.To ?? string.Empty;
                logInformation.Success = true;
                logInformation.Email = "<EMAIL>";
            }
            catch (Exception e)
            {
                logInformation.Success = false;
                logInformation.ErrMsg = e.InnerException?.Message ?? e.Message;
            }
            
            logInformations.Add(logInformation);
        }

        return logInformations.SerializeToJson();
    }
}

public record LogInfoForEWgs
{
    public long WgsNumber { get; set; }
    public string Email { get; set; } = string.Empty;
    public bool Success { get; set; }
    public string? ErrMsg { get; set; }
}
