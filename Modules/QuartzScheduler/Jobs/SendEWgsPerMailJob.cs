using MediatR;
using Quartz;
using Microsoft.Extensions.Logging;
using ObjectDeAndSerialize;
using QuartzScheduler.Jobs.BaseJobs;
using WingCore.application.Wiegeschein;
using wingPrinterListLabel.application.Invoices;
using wingPrinterListLabel.application.Wgs;
using PrinterService.Helper;

namespace QuartzScheduler.Jobs;

[DisallowConcurrentExecution]
[PersistJobDataAfterExecution]
public class SendEWgsPerMailJob(ILogger<SendEWgsPerMailJob> logger, ISender mediatr) : BaseJob(logger)
{
    protected override async Task<string> ExecuteJob(IJobExecutionContext context)
    {
        var wgsToSends = await mediatr.Send(new WgsToSendEmailQuery());
        List<LogInfoForEWgs> logInformations = [];

        foreach (var wgsToSend in wgsToSends)
        {
            var logInformation = new LogInfoForEWgs
            {
                WgsNumber = wgsToSend.WgsNumber,
                Success = false
            };

            try
            {
                // Send WGS email using the actual command
                var emailData = await mediatr.Send(new WgsSendEmailCommand(wgsToSend.WgsNumber, "EN"));

                if (emailData != null)
                {
                    // Send the email
                    SendEmailHelper.SendEmailWithSetData(emailData);

                    logInformation.Success = true;
                    logInformation.Email = emailData.EmailParameter.To ?? string.Empty;
                }
                else
                {
                    logInformation.Success = false;
                    logInformation.ErrMsg = "Failed to create email data";
                }
            }
            catch (Exception e)
            {
                logInformation.Success = false;
                logInformation.ErrMsg = e.InnerException?.Message ?? e.Message;
            }

            logInformations.Add(logInformation);
        }

        return logInformations.SerializeToJson();
    }
}

public record LogInfoForEWgs
{
    public long WgsNumber { get; set; }
    public string Email { get; set; } = string.Empty;
    public bool Success { get; set; }
    public string? ErrMsg { get; set; }
}
