using System.Text.RegularExpressions;
using Quartz;

namespace QuartzScheduler.Extensions;

public static partial class TriggerKeyExtensions
{
    public static string GetServerName(this TriggerKey triggerKey)
    {
        return ExtractServerName(triggerKey.Group);
    }
    
    public static string GetServerName(this JobKey jobKey)
    {
        return ExtractServerName(jobKey.Group);
    }
    

    private static string ExtractServerName(string? jobGroupName)
    {
        if(string.IsNullOrWhiteSpace(jobGroupName))
            return string.Empty;
        
        var match = ServerRegex().Match(jobGroupName);
        if (!match.Success)
            return string.Empty;

        if (match.Groups.Count < 1)
            return string.Empty;

        var target = match.Groups[1].Value;
        return target;
    }

    [GeneratedRegex("(.*?)_(.*)")]
    private static partial Regex ServerRegex();
}