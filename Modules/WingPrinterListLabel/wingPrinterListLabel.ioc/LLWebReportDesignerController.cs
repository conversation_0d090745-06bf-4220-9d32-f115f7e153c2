using combit.Reporting;
using combit.Reporting.Web.WebReportDesigner.Server;
using PrinterService.Helper;
using wingPrinterListLabel.application;

namespace wingPrinterListLabel.ioc;
public sealed class LlWebReportDesignerController() : WebReportDesignerController
{
    public override void OnProvideListLabel(ProvideListLabelContext provideListLabelContext)
    {
        var instanceId = string.IsNullOrWhiteSpace(provideListLabelContext.InstanceId) ? provideListLabelContext.ClientCustomData : provideListLabelContext.InstanceId ;
        instanceId ??= string.Empty;
        
        provideListLabelContext.NewInstance = ListLabelFactory.GetListLabel(instanceId);
    }

    public override void OnProvideRepository(ProvideRepositoryContext provideRepositoryContext)
    {
        var instanceId = string.IsNullOrWhiteSpace(provideRepositoryContext.InstanceId) ? provideRepositoryContext.ClientCustomData : provideRepositoryContext.InstanceId ;
        instanceId ??= string.Empty;

        provideRepositoryContext.FileRepository = ListLabelFactory.GetDataBase(instanceId);
    }
    
    public override void OnProvideWebReportDesignerSessionOptions(ProvideWebReportDesignerSessionOptionsContext provideWebReportDesignerSessionOptionsContext)
    {
        provideWebReportDesignerSessionOptionsContext.Options = new WebReportDesignerSessionOptions()
        {
            MaxIterations = 5
        };
        //provideWebReportDesignerSessionOptionsContext.Options.SetLogger();
        base.OnProvideWebReportDesignerSessionOptions(provideWebReportDesignerSessionOptionsContext);
    }
    
    public override void OnProvideProhibitedActions(ProvideProhibitedActionsContext provideProhibitedActionsContext)
    {
        /*
        foreach (WebReportDesignerAction action in DefaultSettings.GetProhibitedActions())
        {
            provideProhibitedActionsContext.ProhibitedActions.Add(action);
        }
        */
    }
		
    public override void OnProvideFileUploadExtensions(ProvideFileUploadExtensions provideFileUploadExtensions)
    {
        var generalTypes = ".pdf";
        var imageTypes = ".jpg,.jpeg,.png,.gif,.svg,.bmp,.emf,.tif,.tiff";

        if (DesigneHelper.GetStaticListLabel().Language == LlLanguage.German)
        {
            string projectTypes = ".blg,.brf,.crd,.dfm,.gtc,.gtx,.idx,.lbl,.loc,.lsr,.lst,.toc";
            provideFileUploadExtensions.FileExtensions = generalTypes + "," + imageTypes + "," + projectTypes;
        }
        else if (DesigneHelper.GetStaticListLabel().Language == LlLanguage.English)
        {
            string projectTypes = ".crd,.gtc,.gtx,.idx,.inv,.lab,.loc,.rpt,.srt,.toc,.ufm";
            provideFileUploadExtensions.FileExtensions = generalTypes + "," + imageTypes + "," + projectTypes;                
        }
    }
}
