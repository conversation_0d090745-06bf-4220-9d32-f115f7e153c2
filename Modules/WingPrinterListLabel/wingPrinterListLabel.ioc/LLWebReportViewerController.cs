using System.Runtime.Versioning;
using combit.Reporting.Web.WebReportViewer;
using PrinterService.Helper;
using wingPrinterListLabel.application;
using wingPrinterListLabel.application.Contracts;
using wingPrinterListLabel.data;
using wingPrinterListLabel.data.Repositories;

namespace wingPrinterListLabel.ioc;

[SupportedOSPlatform("windows")]
public class LlWebReportViewerController() : WebReportViewerController
{
    public override void OnProvideListLabel(ProvideListLabelContext provideListLabelContext)
    {
        var instanceId = string.IsNullOrWhiteSpace(provideListLabelContext.InstanceId) ? provideListLabelContext.ClientCustomData : provideListLabelContext.InstanceId ;
        instanceId ??= string.Empty;
        
        var ll = ListLabelFactory.GetListLabel(instanceId);
        // D:   <PERSON>se <PERSON>omment<PERSON>, wenn verschiedene Zeilendefinitionen verwendet werden und diese im Browser nicht exakt bündig gerendert werden
        // US:  Uncomment this line if different line definitions are used and they are not rendered exactly flush in the browser
        // ll.Core.LlXSetParameter(LlExtensionType.Export, "HTML5", "XHTML.ForceTableLineOneHundredPercentageWidth", "1");

        // D:   Der WebReportViewer benötigt ein Verzeichnis für temporäre Dateien. Diese werden einige Minuten nach Schließen eines WebReportViewer automatisch gelöscht.
        // US:  The WebReportViewer requires a directory for temporary files. Some minutes after a WebReportViewer is closed, these files will be deleted automatically.
        var fileStorage = "~/App_Data/TempFiles";
        provideListLabelContext.ExportPath =fileStorage.Replace("~", Directory.GetCurrentDirectory()); //use the same tempDir (Demo)

        provideListLabelContext.NewInstance = ll;
    }


    public override void OnProvideRepository(ProvideRepositoryContext provideRepositoryContext)
    {
        var instanceId = string.IsNullOrWhiteSpace(provideRepositoryContext.InstanceId) ? provideRepositoryContext.ClientCustomData : provideRepositoryContext.InstanceId ;
        instanceId ??= string.Empty;

        provideRepositoryContext.FileRepository = ListLabelFactory.GetDataBase(instanceId);
    }
}