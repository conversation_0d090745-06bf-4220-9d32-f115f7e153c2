using System.Runtime.Versioning;
using MediatR;
using PrinterService;
using PrinterService.Models;
using WingCore.application.Contract;
using WingCore.application.Contract.IModels;
using WingCore.application.Contract.Services;
using WingCore.application.PrintableDtos;
using WingCore.application.Wiegeschein;
using WingCore.domain.Common.Configurations;
using WingCore.domain.Common.Flags;
using WingCore.domain.Models;
using wingPrinterListLabel.application.Contracts;

namespace wingPrinterListLabel.application.Wgs.Handler.CommandHandlers;


public class WgsSendEmailCommandHandler(ISender mediatr,
                                        IWgsRepository wgsRepository,
                                        IApplicationPath applicationPath,
                                        IConfigurationService configurationService,
                                        IListLabelRepository lListLabelRepository,
                                        IUnitOfWork iUnitOfWork) : IRequestHandler<WgsSendEmailCommand, EmailDataToCreateEmail?>
{
    [SupportedOSPlatform("windows")]
    public async Task<EmailDataToCreateEmail?> Handle(WgsSendEmailCommand command, CancellationToken cancellationToken)
    {
        var wgs = await mediatr.Send(new WgsWithDataQuery(command.WgsNumber, true), cancellationToken);

        if(wgs is null)
            return null;

        var (eWgsConfig,emailBodyParameter) = await GetEWgsEmailConfig(command.LanguageToUse);
        var eWgsEmail = ""; // TODO: Get email from supplier data if available

        var emailDataToCreateEmail = new EmailDataToCreateEmail
        {
            PrintableObject = LabelWiegeschein.Create(wgs),
            LayoutFileWithPath = emailBodyParameter.LayoutForPrint,
            AppFolder = applicationPath.GetPath(),
            EmailParameter = new EmailParameter()
                                {
                                    BodyTemplate = emailBodyParameter.EmailBodyHtml,
                                    FileName = emailBodyParameter.FileName,
                                    Subject = emailBodyParameter.Subject,

                                    SmtpSecureConnection = eWgsConfig.SmtpSecureConnection,
                                    SmtpSenderAddress = eWgsConfig.SmtpSenderAddress,
                                    SmtpSenderName = eWgsConfig.SmtpSenderName,
                                    SmtpServerAddress = eWgsConfig.SmtpServerAddress,
                                    SmtpServerPort = eWgsConfig.SmtpServerPort,
                                    SmtpServerUser = eWgsConfig.SmtpServerUser,
                                    SmtpServerPassword = eWgsConfig.SmtpServerPassword,
                                    SmtpSocketTimeout = eWgsConfig.SmtpSocketTimeout,
                                    To = eWgsEmail
                                }

        };

        // Just attach as PDF, no XML needed for WGS
        PrinterCreator.SetEmailDataForSendWithPdf(ref emailDataToCreateEmail,
                                            "",
                                            "",
                                            lListLabelRepository);

        // Set WGS flag for email sent success
        wgsRepository.SetWgsFlag([wgs.Id], WgsFlags.WgsMailSendSuccess);
        await iUnitOfWork.SaveChangesAsync(cancellationToken);

        return emailDataToCreateEmail;
    }
    
    private async Task<(ConfigurationElectricWgs,LanguageAndEmailFields)> GetEWgsEmailConfig(string languageToUse)
    {
        var eWgsConfig = await configurationService.GetConfigurationElectricWgsAsync();
        if(eWgsConfig is null)
            throw new Exception("Es existiert keine E-Mail konfiguration für E-Wiegescheine");

        LanguageAndEmailFields? emailBodyParameter = null;
        var usedCountry = new List<string>();
        if (!string.IsNullOrWhiteSpace(languageToUse))
        {
            usedCountry.Add(languageToUse);
            eWgsConfig.LanguageToEmailFields.TryGetValue(languageToUse, out emailBodyParameter);
        }
        
        if (emailBodyParameter is null)
        {
            usedCountry.Add("EN");
            eWgsConfig.LanguageToEmailFields.TryGetValue("EN", out emailBodyParameter);
        }

        if (emailBodyParameter is null)
            throw new Exception($"Es existiert keine E-Mail konfiguration für die Länder '{string.Join(",", usedCountry)}'");

        return (eWgsConfig,emailBodyParameter);
    }
}
