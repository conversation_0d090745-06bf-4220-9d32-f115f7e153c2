using System.Runtime.Versioning;
using MediatR;
using PrinterService;
using PrinterService.Models;
using WingCore.application.Contract;
using WingCore.application.Contract.IModels;
using WingCore.application.Contract.Services;
using WingCore.application.PrintableDtos;
using WingCore.application.Wiegeschein;
using WingCore.domain.Common.Configurations;
using WingCore.domain.Common.Flags;
using WingCore.domain.Models;
using wingPrinterListLabel.application.Contracts;

namespace wingPrinterListLabel.application.Wgs.Handler.CommandHandlers;


public class WgsManualSendEmailCommandHandler(ISender mediatr,
                                        IWgsRepository wgsRepository,
                                        IApplicationPath applicationPath,
                                        IConfigurationService configurationService,
                                        IListLabelRepository lListLabelRepository,
                                        IUnitOfWork iUnitOfWork) : IRequestHandler<WgsManualSendEmailCommand, EmailDataToCreateEmail?>
{
    [SupportedOSPlatform("windows")]
    public async Task<EmailDataToCreateEmail?> Handle(WgsManualSendEmailCommand command, CancellationToken cancellationToken)
    {
        var wgs = await mediatr.Send(new WgsWithDataQuery(command.WgsNumber, true), cancellationToken);

        if(wgs is null)
            return null;

        var (eWgsConfig,emailBodyParameter) = await GetEWgsEmailConfig(command.LanguageToUse);
        var eWgsEmail = ""; // TODO: Get email from supplier data if available

        var emailDataToCreateEmail = new EmailDataToCreateEmail()
        {
            PrintableObject = new WgsPrintableDto(wgs),
            EmailParameter = new EmailParameter()
                                {
                                    BodyTemplate = emailBodyParameter.BodyTemplate,
                                    Subject = emailBodyParameter.Subject,
                                    FileNameTemplate = emailBodyParameter.FileNameTemplate,
                                    
                                    SmtpSecureConnection = eWgsConfig.SmtpSecureConnection,
                                    SmtpSenderAddress = eWgsConfig.SmtpSenderAddress,
                                    SmtpSenderName = eWgsConfig.SmtpSenderName,
                                    SmtpServerAddress = eWgsConfig.SmtpServerAddress,
                                    SmtpServerPort = eWgsConfig.SmtpServerPort,
                                    SmtpServerUser = eWgsConfig.SmtpServerUser,
                                    SmtpServerPassword = eWgsConfig.SmtpServerPassword,
                                    SmtpSocketTimeout = eWgsConfig.SmtpSocketTimeout,
                                    To = eWgsEmail
                                }

        };

        // Just attach as PDF, no XML needed for WGS
        PrinterCreator.SetEmailDataForSendWithPdf(ref emailDataToCreateEmail,
                                            "",
                                            "",
                                            lListLabelRepository);

        // Set WGS flag for email sent success (manual send also sets the flag)
        wgsRepository.SetWgsFlag([wgs.Id], WgsFlags.WgsMailSendSuccess);
        await iUnitOfWork.SaveChangesAsync(cancellationToken);

        return emailDataToCreateEmail;
    }
    
    private async Task<(ConfigurationElectricWgs,LanguageAndEmailFields)> GetEWgsEmailConfig(string languageToUse)
    {
        var eWgsConfig = await configurationService.GetConfigurationElectricWgsAsync();
        if(eWgsConfig is null)
            throw new Exception("Es existiert keine E-Mail konfiguration für E-Wiegescheine");

        LanguageAndEmailFields? emailBodyParameter = null;
        var usedCountry = new List<string>();
        if (!string.IsNullOrWhiteSpace(languageToUse))
        {
            usedCountry.Add(languageToUse);
            eWgsConfig.LanguageToEmailFields.TryGetValue(languageToUse, out emailBodyParameter);
        }
        
        if (emailBodyParameter is null)
        {
            usedCountry.Add("EN");
            eWgsConfig.LanguageToEmailFields.TryGetValue("EN", out emailBodyParameter);
        }
        
        if (emailBodyParameter is null)
        {
            usedCountry.Add("DE");
            eWgsConfig.LanguageToEmailFields.TryGetValue("DE", out emailBodyParameter);
        }
        
        if (emailBodyParameter is null)
            throw new Exception($"Es existiert keine E-Mail konfiguration für E-Wiegescheine für die Sprachen: {string.Join(", ", usedCountry)}");

        return (eWgsConfig, emailBodyParameter);
    }
}
