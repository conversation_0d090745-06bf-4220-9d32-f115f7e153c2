using MediatR;
using PrinterService.Models;
using WingCore.domain.Common.Configurations;

namespace wingPrinterListLabel.application.Wgs;


public sealed record DummyWgsSendEmailCommand(string Email,
                                              string LanguageToUse,
                                              ConfigurationElectricWgs ConfigurationElectricWgs) : IRequest<EmailDataToCreateEmail?>;
public sealed record WgsSendEmailCommand(long WgsNumber, string LanguageToUse) : IRequest<EmailDataToCreateEmail?>;
public sealed record WgsManualSendEmailCommand(long WgsNumber, string LanguageToUse) : IRequest<EmailDataToCreateEmail?>;
public sealed record WgsAsPdfBase64Command(long WgsNumber, string LanguageToUse) : IRequest<string>;
