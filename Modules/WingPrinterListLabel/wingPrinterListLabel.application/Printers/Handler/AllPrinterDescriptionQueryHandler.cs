using MediatR;
using PrinterService;

namespace wingPrinterListLabel.application.Printers.Handler;

public class AllPrinterDescriptionQueryHandler : IRequestHandler<AllPrinterDescriptionQuery, List<string>>
{
    public Task<List<string>> Handle(AllPrinterDescriptionQuery request, CancellationToken cancellationToken)
    {
        
#if WINDOWS
    var printers = PrinterCreator.GetAllPrinter();
    return Task.FromResult(printers);
#else
        return Task.FromResult(new List<string>());
#endif


    }
}