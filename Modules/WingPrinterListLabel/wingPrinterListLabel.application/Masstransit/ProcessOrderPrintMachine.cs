using MassTransit;
using MediatR;
using Microsoft.Extensions.DependencyInjection;
using ObjectDeAndSerialize;
using StateMachineMasstransit;
using wingPrinterListLabel.application.ProcessOrders.Positions;

namespace wingPrinterListLabel.application.Masstransit;

public class ProcessOrderPrintMachine : MassTransitStateMachine<BackgroundState>
{
    private const int MaxRetryCount = 3;
    public State StartetPrint { get; private set; } = null!;
    public State ProcessPrint { get; private set; } = null!;
    public State EndPrint { get; private set; } = null!;
    public State Error { get; private set; } = null!;
    public State Rejected { get; private set; } = null!;

    public Event<StarPrinterForProcessOrderHeaderEvent> StarPrinterForProcessOrderHeaderEvent { get; private set; } = null!;
    public Event<ProcessPrinterForProcessOrderHeaderEvent> ProcessPrinterForProcessOrderHeaderEvent { get; private set; } = null!;
    public Event<StopBackgroundWorkEvent> StopBackgroundWorkEvent { get; private set; } = null!;
    public Event<ErrorBackgroundWorkEvent> ErrorBackgroundWorkEvent { get; private set; } = null!;
    //public Event<OrderRejected> OrderRejectedEvent { get; private set; }

    
    public ProcessOrderPrintMachine()
    {
        InstanceState(x => x.CurrentState);

        Event(() => StarPrinterForProcessOrderHeaderEvent, x => x.CorrelateById(m => m.Message.Id));
        Event(() => ProcessPrinterForProcessOrderHeaderEvent, x => x.CorrelateById(m => m.Message.Id));
        Event(() => StopBackgroundWorkEvent, x => x.CorrelateById(m => m.Message.Id));
        Event(() => ErrorBackgroundWorkEvent, x => x.CorrelateById(m => m.Message.Id));

        Initially(
            When(StarPrinterForProcessOrderHeaderEvent)
                .ThenAsync(async context =>
                {
                    context.Saga.ObjectName = "PrintProcessPositionCommand";
                    context.Saga.InfoMessage = "start print";
                    context.Saga.Data = context.Message.Data;

                    await context.Publish(new ProcessPrinterForProcessOrderHeaderEvent(context.Message.Id, context.Message.Data));
                })
                .TransitionTo(StartetPrint));

        During(StartetPrint,
            When(ProcessPrinterForProcessOrderHeaderEvent)
                .ThenAsync(async context =>
                {
                    context.Saga.Counter++;
                    context.Saga.InfoMessage = "print in progress";
                    context.Saga.Data = context.Message.Data;

                    try
                    {
                        if (string.IsNullOrEmpty(context.Message.Data))
                            throw new Exception("no Data found");
                        
                        var printProcessPositionCommand = context.Message.Data.DeserializeJsonObject<PrintProcessPositionCommand>();

                        if (printProcessPositionCommand is null)
                            throw new Exception("printProcessPositionCommand is null");
                        
                        using var scope = context.GetPayload<IServiceProvider>().CreateScope();
                        var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();
                        await mediator.Send(printProcessPositionCommand);
                        await context.Publish(new StopBackgroundWorkEvent(context.Message.Id));
                    }
                    catch (Exception e)
                    {
                        context.Saga.InfoMessage += $"Error: {e.InnerException?.Message ?? e.Message}";
                        if (context.Saga.Counter < MaxRetryCount)
                        {
                            await context.ScheduleSend(TimeSpan.FromSeconds(10), context.Message with{Id = Guid.NewGuid()});
                        }
                        else
                        {
                            context.Saga.InfoMessage += " Max retries reached";
                            await context.Publish(new ErrorBackgroundWorkEvent(context.Message.Id));
                        }
                    }
                })
                .TransitionTo(EndPrint),
            When(StopBackgroundWorkEvent)
                .Then(context =>
                {
                    context.Saga.InfoMessage = "print end";
                })
                .TransitionTo(EndPrint),
            When(ErrorBackgroundWorkEvent)
                .Then(_ =>
                {
                })
                .TransitionTo(Error));
    }
}