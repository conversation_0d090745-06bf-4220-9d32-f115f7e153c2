using combit.Reporting.Repository;

namespace wingPrinterListLabel.domain;

public class CustomizedRepositoryItem : RepositoryItem
{
    public CustomizedRepositoryItem() : base(string.Empty, string.Empty, string.Empty, DateTime.Now)
    {
        Author = string.Empty;
        OriginalFileName = string.Empty;
        Language = string.Empty;
        DisplayName = string.Empty;
    }
    
    public CustomizedRepositoryItem(string itemId,
                                    string descriptor,
                                    string type,
                                    DateTime lastModified,
                                    string author,
                                    bool showInToolbar,
                                    string originalFileName,
                                    string language,
                                    string displayName) : base(itemId, descriptor, type, lastModified)
    {
        Author = author;
        ShowInToolbar = showInToolbar;
        OriginalFileName = originalFileName;
        Language = language;
        DisplayName = displayName;
    }
    
    public Guid Id { get; private set; } = Guid.NewGuid();
    public string Author { get; set; }
    public bool ShowInToolbar { get; set; }
    public string OriginalFileName { get; set; }
    public string Language { get; set; }
    public string DisplayName { get; set; }
    public byte[]? FileContent { get; set; }
}