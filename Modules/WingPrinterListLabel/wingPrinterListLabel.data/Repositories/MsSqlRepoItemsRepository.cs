using System.Data;
using combit.Reporting.Repository;
using Dapper;
using Microsoft.Data.SqlClient;
using PrinterService;
using PrinterService.Helper;
using wingPrinterListLabel.application.Contracts;
using wingPrinterListLabel.domain;

namespace wingPrinterListLabel.data.Repositories;

public class MsSqlRepoItemsRepository: IListLabelRepository
{
    private readonly IDbConnection _dbConnection;
    private readonly string _connectionString;
    private readonly string _reportLanguage = "de";
    private string PrintType { get; set; } = string.Empty;
    private string RepoId { get; set; } = string.Empty;

    public MsSqlRepoItemsRepository(WingPrinterListLabelDbContextFactory factory)
    : this(factory.CreateDbConnect(), factory.ConnectionString())
    {
        
    }
    
    public MsSqlRepoItemsRepository(IDbConnection dbConnection, string? connectionString)
    {
        _dbConnection = dbConnection;
        _connectionString = connectionString ?? dbConnection.ConnectionString;
        
        EnsureDatabaseAndTable();
    }
    
    private void EnsureDatabaseAndTable()
    {
        const string createTableQuery = @"
            IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='RepositoryItems' AND xtype='U')
            CREATE TABLE RepositoryItems (
                InternalID NVARCHAR(255) PRIMARY KEY,
                Descriptor NVARCHAR(MAX),
                Type NVARCHAR(255),
                LastModificationUTC DATETIME,
                FileContent VARBINARY(MAX),
                Author NVARCHAR(255),
                ShowInToolbar BIT,
                OriginalFileName NVARCHAR(255),
                UIName NVARCHAR(255),
                Language NVARCHAR(50),
                DisplayName NVARCHAR(255),
                FolderId NVARCHAR(255),
                FolderPath NVARCHAR(255)
            )";
        
        ExecuteDoDatabase(createTableQuery);
    }
    
    #region DropDownFunctions
    
    public Task<IEnumerable<CustomizedRepositoryItem>> GetAllCustomizedItems(string selectedPrintType)
    {
        const string query = @"SELECT * FROM RepositoryItems";
        if(string.IsNullOrWhiteSpace(selectedPrintType))
            return Task.FromResult(QueryDoDatabase<CustomizedRepositoryItem>(query));
        
        const string queryWithType = @"SELECT * FROM RepositoryItems
                                            WHERE UIName LIKE @PrintType + '%' 
                                            OR  Descriptor LIKE @PrintType + '%'
                                            OR ShowInToolbar=0";
        return Task.FromResult(QueryDoDatabase<CustomizedRepositoryItem>(queryWithType, new { PrintType = selectedPrintType}));
    }
    #endregion
    
    #region IRepositoryFucntion
    public bool ContainsItem(string id)
    {
        const string query = "SELECT COUNT(1) FROM RepositoryItems WHERE InternalID = @Id";
        
        return ExecuteScalarDoDatabase<bool>(query, new { Id = id });
    }

    public void LoadItem(string id, Stream destinationStream, CancellationToken cancelToken)
    {
        const string query = "SELECT FileContent FROM RepositoryItems WHERE InternalID = @Id";
        
        var fileContent = QuerySingleOrDefaultDoDatabase<byte[]>(query, new { Id = id });
        
        if (fileContent is not null)
        {
            destinationStream.Write(fileContent, 0, fileContent.Length);
        }
    }

    public IEnumerable<CustomizedRepositoryItem> GetAllCustomizedItems()
    {
        const string query = @"SELECT * FROM RepositoryItems";
        if(string.IsNullOrWhiteSpace(PrintType))
            return QueryDoDatabase<CustomizedRepositoryItem>(query);
        
        if (!string.IsNullOrEmpty(RepoId))
        {
            var queryWithType = $"SELECT * FROM RepositoryItems WHERE InternalID = '{RepoId}'";
            return QueryDoDatabase<CustomizedRepositoryItem>(queryWithType);
        }
        else
        {
            var queryWithType = @"SELECT * FROM RepositoryItems
                                            WHERE UIName LIKE @PrintType + '%' 
                                            OR  Descriptor LIKE @PrintType + '%'
                                            OR ShowInToolbar=0";
            return QueryDoDatabase<CustomizedRepositoryItem>(queryWithType, new { PrintType = PrintType});
        }
    }

    public IEnumerable<RepositoryItem> GetAllItems()
    {
        return GetAllCustomizedItems();
    }

    public RepositoryItem? GetItem(string id)
    {
        const string query = "SELECT * FROM RepositoryItems WHERE InternalID = @Id";
        return QuerySingleOrDefaultDoDatabase<CustomizedRepositoryItem>(query, new { Id = id });
    }

    public CustomizedRepositoryItem? GetRepoItem(string id)
    {
        const string query = "SELECT * FROM RepositoryItems WHERE InternalID = @Id";
        return QuerySingleOrDefaultDoDatabase<CustomizedRepositoryItem>(query, new { Id = id });
    }

    public void DeleteItem(string id)
    {
        const string query = "DELETE FROM RepositoryItems WHERE InternalID = @Id";
        
        ExecuteDoDatabase(query, new { Id = id });
    }

    public void CreateOrUpdateItem(RepositoryItem item, string importUserData, Stream? sourceStream)
    {
        var currentUser = "[Anonymous User]";
        
        byte[]? fileContent = null;
        bool setMetadataOnly;
        if (sourceStream is not null)
        {
            using var memStream = new MemoryStream();
            sourceStream.CopyTo(memStream);
            fileContent = memStream.ToArray();
            setMetadataOnly = false;
        }
        else
        {
            setMetadataOnly = true;
        }

        var isUpdate = ContainsItem(item.InternalID);
        var itemToInsert = isUpdate ? GetRepoItem(item.InternalID) : null;
        
        if (itemToInsert is not null)
        {
            itemToInsert.Descriptor = item.Descriptor;
            itemToInsert.LastModificationUTC = item.LastModificationUTC;
            itemToInsert.FolderId = item.FolderId;
            itemToInsert.FolderPath = item.FolderPath;
            itemToInsert.Author = currentUser;
            itemToInsert.DisplayName = item.ExtractDisplayName();
        }
        else
        {
            itemToInsert = new CustomizedRepositoryItem(item.InternalID,
                                                        item.Descriptor, 
                                                        item.Type, 
                                                        item.LastModificationUTC, 
                                                        currentUser,
                                                        RepositoryItemType.IsProjectType(item.Type, false),
                                                        item.UIName,
                                                        _reportLanguage,
                                                        item.ExtractDisplayName()
                                                        )
            {
                FolderId = item.FolderId,
                FolderPath = item.FolderPath
            };
            
            itemToInsert.UIName = string.IsNullOrWhiteSpace(itemToInsert.UIName) ? PrintType : itemToInsert.UIName;
            itemToInsert.DisplayName = string.IsNullOrWhiteSpace(itemToInsert.DisplayName) ? PrintType : itemToInsert.DisplayName;
        }
        /*
        var query = isUpdate
            ? @"UPDATE RepositoryItems SET Descriptor = @Descriptor, LastModificationUTC = @LastModificationUTC,
                FolderId = @FolderId, FolderPath = @FolderPath, Author = @Author, FileContent = @FileContent
                WHERE InternalID = @InternalID"
            : @"INSERT INTO RepositoryItems (InternalID, Descriptor, Type, LastModificationUTC, Author, ShowInToolbar,
                UIName, Language, DisplayName, FileContent) VALUES (@InternalID, @Descriptor, @Type, @LastModificationUTC,
                @Author, @ShowInToolbar, @UIName, @Language, @DisplayName, @FileContent)";
*/
        string query;

        if (isUpdate) // UPDATE
        {
            if (setMetadataOnly)
            {
                query = @"UPDATE RepositoryItems 
                             SET Descriptor = @Descriptor, 
                                 LastModificationUTC = @LastModificationUTC, 
                                 Author = @Author, 
                                 ShowInToolbar = @ShowInToolbar, 
                                 OriginalFileName = @OriginalFileName, 
                                 DisplayName = @DisplayName,
                                 UIName = @UIName,
                                 Language = @Language,
                                 FolderId = @FolderId,
                                 FolderPath = @FolderPath
                             WHERE InternalID = @InternalID";
            }
            else
            {
                query = @"UPDATE RepositoryItems 
                             SET Descriptor = @Descriptor, 
                                 LastModificationUTC = @LastModificationUTC, 
                                 Author = @Author, 
                                 ShowInToolbar = @ShowInToolbar, 
                                 OriginalFileName = @OriginalFileName, 
                                 FileContent = @FileContent, 
                                 DisplayName = @DisplayName,
                                 UIName = @UIName,
                                 Language = @Language,
                                 FolderId = @FolderId,
                                 FolderPath = @FolderPath
                             WHERE InternalID = @InternalID";
            }
            }
            else // INSERT
            {
            if (setMetadataOnly)
            {
                query = @"INSERT INTO RepositoryItems 
                                (InternalID, Type, Descriptor, LastModificationUTC, Author, ShowInToolbar, OriginalFileName, Language, FolderId,FolderPath) 
                             VALUES 
                                (@InternalID, @Type, @Descriptor, @LastModificationUTC, @Author, @ShowInToolbar, @OriginalFileName, @Language, @FolderId, @FolderPath)";
            }
            else
            {
                query = @"INSERT INTO RepositoryItems 
                                (InternalID, Descriptor, Type, LastModificationUTC, Author, ShowInToolbar, UIName, OriginalFileName, Language, DisplayName, FileContent, FolderId,FolderPath)
                             VALUES 
                                (@InternalID, @Descriptor, @Type, @LastModificationUTC, @Author, @ShowInToolbar, @UIName, @OriginalFileName, @Language, @DisplayName, @FileContent, @FolderId, @FolderPath)";
            }
        }

        // Dapper-Ausführung
        var parameters = new
        {
            itemToInsert.InternalID,
            itemToInsert.Descriptor,
            itemToInsert.Type,
            itemToInsert.LastModificationUTC,
            Author = itemToInsert.Author,
            ShowInToolbar = itemToInsert.ShowInToolbar,
            UIName = itemToInsert.DisplayName,
            itemToInsert.OriginalFileName,
            Language = _reportLanguage,
            DisplayName = itemToInsert.DisplayName,
            FileContent = fileContent,
            itemToInsert.FolderId,
            FolderPath = itemToInsert.FolderPath ?? ""
        };
        
        ExecuteDoDatabase(query, parameters);
    }

    public bool LockItem(string id)
    {
        return true;
    }

    public void UnlockItem(string id)
    {
    }
    
    private void ExecuteDoDatabase(string query, object? parameters = null)
    {
        try
        {
            if (_dbConnection.State == ConnectionState.Closed)
            {
                _dbConnection.Open();
            }
        
            _dbConnection.Execute(query, parameters);
        }
        finally
        {
            if (_dbConnection.State == ConnectionState.Open)
            {
                _dbConnection.Close();
            }
        }
    }
    
    private T? ExecuteScalarDoDatabase<T>(string query, object parameters )
    {
        using var connectDb = new SqlConnection(_connectionString);
        return connectDb.ExecuteScalar<T>(query, parameters);
    }
    
    private T? QuerySingleOrDefaultDoDatabase<T>(string query, object parameters )
    {
        using var connectDb = new SqlConnection(_connectionString);
        return connectDb.QuerySingleOrDefault<T>(query, parameters);
    }
    
    private T? QuerySingleOrDefaultDoDatabase<T>(string query)
    {
        using var connectDb = new SqlConnection(_connectionString);
        return connectDb.QuerySingleOrDefault<T>(query);
    }
    
    private IEnumerable<T> QueryDoDatabase<T>(string query, object parameters)
    {
        using var connectDb = new SqlConnection(_connectionString);
        return connectDb.Query<T>(query,parameters);
    }
    
    private IEnumerable<T> QueryDoDatabase<T>(string query)
    {
        using var connectDb = new SqlConnection(_connectionString);
        return connectDb.Query<T>(query);
    }

    public void AddPrintType(string printType)
    {
        PrintType = printType ?? "";
    }

    public void AddRepoId(string repoId)
    {
        if(BasePrinter.UseRepository(repoId))
            RepoId = repoId;
    }

    #endregion
}