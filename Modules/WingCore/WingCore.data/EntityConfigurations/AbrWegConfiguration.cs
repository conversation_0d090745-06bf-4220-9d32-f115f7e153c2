using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WingCore.domain.Models;

namespace WingCore.data.EntityConfigurations;

public class AbrWegConfiguration : IEntityTypeConfiguration<AbrWeg>
{
    public void Configure(EntityTypeBuilder<AbrWeg> builder)
    {
        builder.HasKey(e => e.Id).HasName("AbrWeg$ID");

        builder.ToTable("AbrWeg");

        builder.Property(e => e.Id).HasColumnName("ID");
        builder.Property(e => e.AbrWeg1)
            .HasMaxLength(60)
            .HasColumnName("AbrWeg");
        builder.Property(e => e.AbrWegNr)
            .HasDefaultValue((short)0)
            .HasColumnName("AbrWegNR");
    }
}