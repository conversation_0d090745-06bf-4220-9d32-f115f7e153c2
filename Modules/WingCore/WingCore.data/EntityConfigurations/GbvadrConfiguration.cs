using WingCore.domain.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;


namespace WingCore.data.EntityConfigurations;

public class GbvadrConfiguration : IEntityTypeConfiguration<Gbvadr>
{
    public void Configure(EntityTypeBuilder<Gbvadr> builder)
    {

            builder.HasKey(e => e.Id).HasName("GBVADR$ID");

            builder.ToTable("GBVADR");

            builder.Property(e => e.Id).HasColumnName("ID");
            builder.Property(e => e.AnzBrutto).HasColumnType("money");
            builder.Property(e => e.Anz<PERSON>to).HasColumnType("money");
            builder.Property(e => e.AnzRestOffen).HasColumnType("money");
            builder.Property(e => e.AnzStartBrutto).HasColumnType("money");
            builder.Property(e => e.AnzStartNetto).HasColumnType("money");
            builder.Property(e => e.BioNr).HasMaxLength(50);
            builder.Property(e => e.Gbabfall)
                .HasColumnType("money")
                .HasColumnName("GBAbfall");
            // builder.Property(e => e.GbabrGew)
            //     .HasColumnType("money")
            //     .HasColumnName("GBAbrGew");
            builder.Property(e => e.GbabzgBetr1)
                .HasColumnType("money")
                .HasColumnName("GBAbzgBetr1");
            builder.Property(e => e.GbabzgBetr2)
                .HasColumnType("money")
                .HasColumnName("GBAbzgBetr2");
            builder.Property(e => e.GbabzgBetr3)
                .HasColumnType("money")
                .HasColumnName("GBAbzgBetr3");
            builder.Property(e => e.GbabzgBetr4)
                .HasColumnType("money")
                .HasColumnName("GBAbzgBetr4");
            builder.Property(e => e.GbabzgBetr5)
                .HasColumnType("money")
                .HasColumnName("GBAbzgBetr5");
            builder.Property(e => e.GbabzgText1)
                .HasMaxLength(30)
                .HasColumnName("GBAbzgText1");
            builder.Property(e => e.GbabzgText2)
                .HasMaxLength(30)
                .HasColumnName("GBAbzgText2");
            builder.Property(e => e.GbabzgText3)
                .HasMaxLength(30)
                .HasColumnName("GBAbzgText3");
            builder.Property(e => e.GbabzgText4)
                .HasMaxLength(30)
                .HasColumnName("GBAbzgText4");
            builder.Property(e => e.GbabzgText5)
                .HasMaxLength(30)
                .HasColumnName("GBAbzgText5");
            builder.Property(e => e.GbabzgVz1)
                .HasMaxLength(1)
                .HasColumnName("GBAbzgVZ1");
            builder.Property(e => e.GbabzgVz2)
                .HasMaxLength(1)
                .HasColumnName("GBAbzgVZ2");
            builder.Property(e => e.GbabzgVz3)
                .HasMaxLength(1)
                .HasColumnName("GBAbzgVZ3");
            builder.Property(e => e.GbabzgVz4)
                .HasMaxLength(1)
                .HasColumnName("GBAbzgVZ4");
            builder.Property(e => e.GbabzgVz5)
                .HasMaxLength(1)
                .HasColumnName("GBAbzgVZ5");
            builder.Property(e => e.Gbabzug)
                .HasColumnType("money")
                .HasColumnName("GBAbzug");
            builder.Property(e => e.Gbaltnr).HasColumnName("GBAltnr");
            builder.Property(e => e.GbangAbfall)
                .HasColumnType("money")
                .HasColumnName("GBAngAbfall");
            builder.Property(e => e.Gbanlfnr).HasColumnName("GBANLFNR");
            builder.Property(e => e.Gbanrede)
                .HasMaxLength(30)
                .HasColumnName("GBAnrede");
            builder.Property(e => e.Gbart).HasColumnName("GBArt");
            builder.Property(e => e.Gbasld).HasColumnName("GBASLD");
            builder.Property(e => e.Gbbank)
                .HasMaxLength(30)
                .HasColumnName("GBBank");
            builder.Property(e => e.Gbbld)
                .HasMaxLength(30)
                .HasColumnName("GBBLD");
            builder.Property(e => e.Gbbldkng).HasColumnName("GBBLDKng");
            builder.Property(e => e.Gbblz)
                .HasMaxLength(10)
                .HasColumnName("GBBLZ");
            builder.Property(e => e.Gbbrutto)
                .HasColumnType("money")
                .HasColumnName("GBBrutto");
            builder.Property(e => e.Gbdatum)
                .HasColumnType("datetime")
                .HasColumnName("GBDatum");
            builder.Property(e => e.GbdruckKz)
                .HasMaxLength(1)
                .HasColumnName("GBDruckKz");
            builder.Property(e => e.GbentfKl)
                .HasMaxLength(2)
                .HasColumnName("GBEntfKL");
            builder.Property(e => e.GbentfKlzuEur)
                .HasColumnType("money")
                .HasColumnName("GBEntfKLZuEUR");
            builder.Property(e => e.Gbezg)
                .HasDefaultValue(false)
                .HasColumnName("GBEZG");
            builder.Property(e => e.Gbezgbetr)
                .HasColumnType("money")
                .HasColumnName("GBEZGBetr");
            builder.Property(e => e.Gbfax)
                .HasMaxLength(20)
                .HasColumnName("GBFax");
            builder.Property(e => e.Gbfibu2)
                .HasDefaultValue(false)
                .HasColumnName("GBFibu2");
            builder.Property(e => e.Gbfiliale)
                .HasMaxLength(2)
                .HasColumnName("GBFiliale");
            builder.Property(e => e.GbgesMenge)
                .HasColumnType("money")
                .HasColumnName("GBGesMenge");
            builder.Property(e => e.Gbhaendler)
                .HasDefaultValue(false)
                .HasColumnName("GBHaendler");
            builder.Property(e => e.Gbiban)
                .HasMaxLength(30)
                .HasColumnName("GBIBAN");
            builder.Property(e => e.Gbkonto)
                .HasMaxLength(20)
                .HasColumnName("GBKonto");
            builder.Property(e => e.Gbktnr).HasColumnName("GBKTNr");
            builder.Property(e => e.GbktrNrLf)
                .HasMaxLength(10)
                .HasColumnName("GBKtrNrLF");
            builder.Property(e => e.Gbland)
                .HasMaxLength(30)
                .HasColumnName("GBLand");
            builder.Property(e => e.Gblfnr).HasColumnName("GBLFNr");
            builder.Property(e => e.Gblfstatus)
                .HasMaxLength(15)
                .HasColumnName("GBLFStatus");
            builder.Property(e => e.GbmitText1)
                .HasMaxLength(60)
                .HasColumnName("GBMitText1");
            builder.Property(e => e.GbmitText2)
                .HasMaxLength(60)
                .HasColumnName("GBMitText2");
            builder.Property(e => e.Gbmwst)
                .HasColumnType("money")
                .HasColumnName("GBMwst");
            builder.Property(e => e.Gbnachzhlg)
                .HasMaxLength(1)
                .HasColumnName("GBNachzhlg");
            builder.Property(e => e.Gbname1)
                .HasMaxLength(30)
                .HasColumnName("GBName1");
            builder.Property(e => e.Gbname2)
                .HasMaxLength(30)
                .HasColumnName("GBName2");
            builder.Property(e => e.Gbname3)
                .HasMaxLength(30)
                .HasColumnName("GBName3");
            builder.Property(e => e.Gbnetto)
                .HasColumnType("money")
                .HasColumnName("GBNetto");
            builder.Property(e => e.Gbnr).HasColumnName("GBNr");
            builder.Property(e => e.Gbort)
                .HasMaxLength(30)
                .HasColumnName("GBOrt");
            builder.Property(e => e.Gbplz)
                .HasMaxLength(10)
                .HasColumnName("GBPLZ");
            builder.Property(e => e.GbreNrLf)
                .HasMaxLength(25)
                .HasColumnName("GBReNrLF");
            builder.Property(e => e.Gbsbg)
                .HasMaxLength(10)
                .HasColumnName("GBSBG");
            builder.Property(e => e.GbstNr)
                .HasMaxLength(20)
                .HasColumnName("GBStNr");
            builder.Property(e => e.GbstProz)
                .HasColumnType("money")
                .HasColumnName("GBStProz");
            builder.Property(e => e.GbstProz2).HasColumnName("GBStProz2");
            builder.Property(e => e.GbstSchl).HasColumnName("GBStSchl");
            builder.Property(e => e.Gbstorno)
                .HasMaxLength(1)
                .HasColumnName("GBStorno");
            builder.Property(e => e.Gbstrasse)
                .HasMaxLength(30)
                .HasColumnName("GBStrasse");
            builder.Property(e => e.Gbswift)
                .HasMaxLength(30)
                .HasColumnName("GBSWIFT");
            builder.Property(e => e.Gbtelefon)
                .HasMaxLength(20)
                .HasColumnName("GBTelefon");
            builder.Property(e => e.Gbustidnr)
                .HasMaxLength(18)
                .HasColumnName("GBUSTIDNR");
            builder.Property(e => e.Gbvaluta)
                .HasMaxLength(60)
                .HasColumnName("GBValuta");
            builder.Property(e => e.GbvalutaDatum)
                .HasColumnType("datetime")
                .HasColumnName("GBValutaDatum");
            builder.Property(e => e.Gbvwhrg)
                .HasMaxLength(4)
                .HasColumnName("GBVWhrg");
            builder.Property(e => e.GbvwhrgKurs)
                .HasColumnType("money")
                .HasColumnName("GBVWhrgKurs");
            builder.Property(e => e.GbzahlBetrag)
                .HasColumnType("money")
                .HasColumnName("GBZahlBetrag");
            builder.Property(e => e.GbzuschlEur)
                .HasColumnType("money")
                .HasColumnName("GBZuschlEUR");
            builder.Property(e => e.GbzuschlSumme)
                .HasColumnType("money")
                .HasColumnName("GBZuschlSumme");
            builder.Property(e => e.Send).HasDefaultValue(false);
            builder.Property(e => e.SysTime).HasColumnType("datetime");
            builder.Property(e => e.SysUser).HasMaxLength(50);
            builder.Property(e => e.Vdatum).HasColumnType("datetime");
            builder.Property(e => e.Flags).HasColumnName("Flags");
                    
            builder.HasMany(e => e.Positionen)
                .WithOne()
                .HasForeignKey(p => p.Gbnr)
                .HasPrincipalKey(p => p.Gbnr)
                .OnDelete(DeleteBehavior.SetNull);
            
            builder.HasOne(e => e.InvoiceSupplierCurrentData)
                .WithMany()
                .HasForeignKey(p => p.Gblfnr)
                .HasPrincipalKey(p => p.Lfnummer);
    }
}