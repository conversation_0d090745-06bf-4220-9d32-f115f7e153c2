using WingCore.domain.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace WingCore.data.EntityConfigurations;

public class ArtLagerConfiguration : IEntityTypeConfiguration<ArtLager>
{
    public void Configure(EntityTypeBuilder<ArtLager> builder)
    {
        builder.HasKey(e => e.Id).HasName("ArtLager$ID");

        builder.ToTable("ArtLager");

        builder.Property(e => e.Id).HasColumnName("ID");
        builder.Property(e => e.ArtAkabaltDp).HasColumnName("ArtAKABaltDP");
        builder.Property(e => e.ArtAkabaltKg).HasColumnName("ArtAKABaltKG");
        builder.Property(e => e.ArtAkabaltStck)
            .HasDefaultValue(0m)
            .HasColumnType("money")
            .HasColumnName("ArtAKABaltStck");
        builder.Property(e => e.ArtAkabaltWert).HasColumnName("ArtAKABaltWert");
        builder.Property(e => e.ArtDatum).HasColumnType("datetime");
        builder.Property(e => e.ArtDla).HasColumnName("ArtDLA");
        builder.Property(e => e.ArtGdp)
            .HasColumnType("money")
            .HasColumnName("ArtGDP");
        builder.Property(e => e.ArtLgm).HasColumnName("ArtLGM");
        builder.Property(e => e.ArtLgs).HasColumnName("ArtLGS");
        builder.Property(e => e.ArtLgstck)
            .HasDefaultValue(0m)
            .HasColumnType("money")
            .HasColumnName("ArtLGStck");
        builder.Property(e => e.ArtLgw)
            .HasColumnType("money")
            .HasColumnName("ArtLGW");
        builder.Property(e => e.ArtMla).HasColumnName("ArtMLA");
        builder.Property(e => e.ArtNetto).HasColumnType("money");
        builder.Property(e => e.ArtOpt1).HasMaxLength(50);
        builder.Property(e => e.ArtSelP).HasColumnType("money");
        builder.Property(e => e.ArtStckLa)
            .HasDefaultValue(0m)
            .HasColumnType("money")
            .HasColumnName("ArtStckLA");
        builder.Property(e => e.SysTime).HasColumnType("datetime");
        builder.Property(e => e.SysUser).HasMaxLength(50);
    }
}