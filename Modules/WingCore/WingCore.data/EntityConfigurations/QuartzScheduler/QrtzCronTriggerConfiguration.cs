using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WingCore.domain.Models.Quartz;

namespace WingCore.data.EntityConfigurations.QuartzScheduler;

public class QrtzCronTriggerConfiguration : IEntityTypeConfiguration<QrtzCronTrigger>
{
    public void Configure(EntityTypeBuilder<QrtzCronTrigger> builder)
    {
        builder.<PERSON>Key(e => new { e.SchedName, e.TriggerName, e.TriggerGroup });

        builder.ToTable("QRTZ_CRON_TRIGGERS");

        builder.Property(e => e.SchedName)
            .HasColumnType("NVARCHAR(120)")
            .HasColumnName("SCHED_NAME");

        builder.Property(e => e.TriggerName)
            .HasColumnType("NVARCHAR(150)")
            .HasColumnName("TRIGGER_NAME");

        builder.Property(e => e.TriggerGroup)
            .HasColumnType("NVARCHAR(150)")
            .HasColumnName("TRIGGER_GROUP");

        builder.Property(e => e.CronExpression)
            .HasColumnType("NVARCHAR(250)")
            .HasColumnName("CRON_EXPRESSION");

        builder.Property(e => e.TimeZoneId)
            .HasColumnType("NVARCHAR(80)")
            .HasColumnName("TIME_ZONE_ID");

        builder.HasOne(d => d.QrtzTrigger)
            .WithOne(p => p.QrtzCronTrigger)
            .HasForeignKey<QrtzCronTrigger>(d => new { d.SchedName, d.TriggerName, d.TriggerGroup });
    }
}