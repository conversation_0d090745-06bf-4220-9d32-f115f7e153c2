using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WingCore.domain.Models.Quartz;

namespace WingCore.data.EntityConfigurations.QuartzScheduler;

public class QrtzSimpropTriggerConfiguration : IEntityTypeConfiguration<QrtzSimpropTrigger>
{
    public void Configure(EntityTypeBuilder<QrtzSimpropTrigger> builder)
    {
        builder.<PERSON><PERSON><PERSON>(e => new { e.SchedName, e.TriggerName, e.TriggerGroup });

        builder.ToTable("QRTZ_SIMPROP_TRIGGERS");

        builder.Property(e => e.SchedName)
            .HasColumnType("NVARCHAR (120)")
            .HasColumnName("SCHED_NAME");

        builder.Property(e => e.TriggerName)
            .HasColumnType("NVARCHAR (150)")
            .HasColumnName("TRIGGER_NAME");

        builder.Property(e => e.TriggerGroup)
            .HasColumnType("NVARCHAR (150)")
            .HasColumnName("TRIGGER_GROUP");

        builder.Property(e => e.Bo<PERSON>Prop1)
            .HasColumnName("BOOL_PROP_1");

        builder.Property(e => e.BoolProp2)
            .HasColumnName("BOOL_PROP_2");

        builder.Property(e => e.DecProp1)
            .HasColumnName("DEC_PROP_1");

        builder.Property(e => e.DecProp2)
            .HasColumnName("DEC_PROP_2");

        builder.Property(e => e.IntProp1)
            .HasColumnType("INT")
            .HasColumnName("INT_PROP_1");

        builder.Property(e => e.IntProp2)
            .HasColumnType("INT")
            .HasColumnName("INT_PROP_2");

        builder.Property(e => e.LongProp1)
            .HasColumnType("BIGINT")
            .HasColumnName("LONG_PROP_1");

        builder.Property(e => e.LongProp2)
            .HasColumnType("BIGINT")
            .HasColumnName("LONG_PROP_2");

        builder.Property(e => e.StrProp1)
            .HasColumnType("NVARCHAR (512)")
            .HasColumnName("STR_PROP_1");

        builder.Property(e => e.StrProp2)
            .HasColumnType("NVARCHAR (512)")
            .HasColumnName("STR_PROP_2");

        builder.Property(e => e.StrProp3)
            .HasColumnType("NVARCHAR (512)")
            .HasColumnName("STR_PROP_3");

        builder.Property(e => e.TimeZoneId)
            .HasColumnType("NVARCHAR(80)")
            .HasColumnName("TIME_ZONE_ID");

        builder.HasOne(d => d.QrtzTrigger)
            .WithOne(p => p.QrtzSimpropTrigger)
            .HasForeignKey<QrtzSimpropTrigger>(d => new { d.SchedName, d.TriggerName, d.TriggerGroup });
    }
}