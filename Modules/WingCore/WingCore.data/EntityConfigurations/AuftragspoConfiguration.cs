using WingCore.domain.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace WingCore.data.EntityConfigurations;

public class AuftragspoConfiguration : IEntityTypeConfiguration<Auftragspo>
{
    public void Configure(EntityTypeBuilder<Auftragspo> builder)
    {
        builder.HasKey(e => e.Id).HasName("Auftragspos$ID");

        builder.Property(e => e.Id).HasColumnName("ID");
        builder.Property(e => e.AlteMenge).HasColumnType("money");
        builder.Property(e => e.ArtEinhVp)
            .HasColumnType("money")
            .HasColumnName("ArtEinhVP");
        builder.Property(e => e.<PERSON>).HasMaxLength(10);
        builder.Property(e => e.BestNr).HasMaxLength(25);
        builder.Property(e => e.<PERSON>z<PERSON>).HasMaxLength(10);
        builder.Property(e => e.ChargenNr).HasMaxLength(20);
        builder.Property(e => e.Eannr)
            .HasMaxLength(14)
            .HasColumnName("EANNr");
        builder.Property(e => e.EdiQalifier).HasDefaultValue(0);
        builder.Property(e => e.Epreis)
            .HasDefaultValue(0m)
            .HasColumnType("money")
            .HasColumnName("EPreis");
        builder.Property(e => e.Erfassungsschema).HasMaxLength(3);
        builder.Property(e => e.Faktor).HasMaxLength(4);
        builder.Property(e => e.Fbneu)
            .HasMaxLength(1)
            .HasColumnName("FBNeu");
        builder.Property(e => e.Fbzgnr).HasColumnName("FBZGNr");
        builder.Property(e => e.GesMenge)
            .HasDefaultValue(0m)
            .HasColumnType("money");
        builder.Property(e => e.GesStck)
            .HasDefaultValue(0m)
            .HasColumnType("money");
        builder.Property(e => e.Gpreis)
            .HasDefaultValue(0m)
            .HasColumnType("money")
            .HasColumnName("GPreis");
        builder.Property(e => e.GpreisRab)
            .HasColumnType("money")
            .HasColumnName("GPreisRab");
        builder.Property(e => e.KdtxTcheck)
            .HasDefaultValue(false)
            .HasColumnName("KDTxTCheck");
        builder.Property(e => e.KolliInh).HasDefaultValue(0L);
        builder.Property(e => e.Lkwkammer)
            .HasMaxLength(15)
            .HasColumnName("LKWKammer");
        builder.Property(e => e.LoeschKng).HasDefaultValue(false);
        builder.Property(e => e.LsdepotNr).HasColumnName("LSDepotNr");
        builder.Property(e => e.Mhd)
            .HasMaxLength(15)
            .HasColumnName("MHD");
        builder.Property(e => e.Muehlenzelle).HasMaxLength(15);
        builder.Property(e => e.Mwst).HasColumnType("money");
        builder.Property(e => e.Mwst2).HasColumnType("money");
        builder.Property(e => e.NuebTexte)
            .HasMaxLength(255)
            .HasColumnName("NUebTexte");
        builder.Property(e => e.Nvo)
            .HasDefaultValue(false)
            .HasColumnName("NVO");
        builder.Property(e => e.NvobilanzNr)
            .HasMaxLength(30)
            .HasColumnName("NVOBilanzNr");
        builder.Property(e => e.Nvobilanziert)
            .HasDefaultValue(false)
            .HasColumnName("NVOBilanziert");
        builder.Property(e => e.Nvonr)
            .HasMaxLength(30)
            .HasColumnName("NVONr");
        builder.Property(e => e.PakGewicht).HasColumnType("money");
        builder.Property(e => e.PalArt).HasMaxLength(30);
        builder.Property(e => e.PalGew).HasColumnType("money");
        builder.Property(e => e.Pfracht)
            .HasColumnType("money")
            .HasColumnName("PFracht");
        builder.Property(e => e.PosNrBest).HasMaxLength(25);
        builder.Property(e => e.ProduktionsvorgangId).HasColumnName("ProduktionsvorgangID");
        builder.Property(e => e.Rabatt).HasMaxLength(255);
        builder.Property(e => e.Selektionskennung).HasMaxLength(1);
        builder.Property(e => e.Status).HasMaxLength(1);
        builder.Property(e => e.Wgsnr).HasColumnName("WGSNr");
        builder.Property(e => e.ZtxtKng)
            .HasDefaultValue(false)
            .HasColumnName("ZTxtKng");
        builder.Property(e => e.Flags).HasColumnName("Flags");
    }
}