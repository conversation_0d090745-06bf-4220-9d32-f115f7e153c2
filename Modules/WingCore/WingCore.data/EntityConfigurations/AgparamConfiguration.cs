using WingCore.domain.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace WingCore.data.EntityConfigurations;

public class AgparamConfiguration : IEntityTypeConfiguration<Agparam>
{
    public void Configure(EntityTypeBuilder<Agparam> builder)
    {
        builder.HasKey(e => e.Id).HasName("AGParam$ID");

        builder.ToTable("AGParam");

        builder.Property(e => e.Id).HasColumnName("ID");
        builder.Property(e => e.Abrgesp).HasDefaultValue(false);
        builder.Property(e => e.Bezeichnung).HasMaxLength(18);
        builder.Property(e => e.Kennung).HasMaxLength(3);
        builder.Property(e => e.Wgs)
            .HasMaxLength(4)
            .HasColumnName("WGS");
        builder.Property(e => e.WgsBezeichnung1).HasMaxLength(18);
        builder.Property(e => e.WgsBezeichnung10).HasMaxLength(18);
        builder.Property(e => e.WgsBezeichnung11).HasMaxLength(18);
        builder.Property(e => e.WgsBezeichnung12).HasMaxLength(18);
        builder.Property(e => e.WgsBezeichnung2).HasMaxLength(18);
        builder.Property(e => e.WgsBezeichnung3).HasMaxLength(18);
        builder.Property(e => e.WgsBezeichnung4).HasMaxLength(18);
        builder.Property(e => e.WgsBezeichnung5).HasMaxLength(18);
        builder.Property(e => e.WgsBezeichnung6).HasMaxLength(18);
        builder.Property(e => e.WgsBezeichnung7).HasMaxLength(18);
        builder.Property(e => e.WgsBezeichnung8).HasMaxLength(18);
        builder.Property(e => e.WgsBezeichnung9).HasMaxLength(18);
        builder.Property(e => e.WgsEinh1).HasMaxLength(3);
        builder.Property(e => e.WgsEinh10).HasMaxLength(3);
        builder.Property(e => e.WgsEinh11).HasMaxLength(3);
        builder.Property(e => e.WgsEinh12).HasMaxLength(3);
        builder.Property(e => e.WgsEinh2).HasMaxLength(3);
        builder.Property(e => e.WgsEinh3).HasMaxLength(3);
        builder.Property(e => e.WgsEinh4).HasMaxLength(3);
        builder.Property(e => e.WgsEinh5).HasMaxLength(3);
        builder.Property(e => e.WgsEinh6).HasMaxLength(3);
        builder.Property(e => e.WgsEinh7).HasMaxLength(3);
        builder.Property(e => e.WgsEinh8).HasMaxLength(3);
        builder.Property(e => e.WgsEinh9).HasMaxLength(3);
        builder.Property(e => e.Wgskennung1)
            .HasMaxLength(4)
            .HasColumnName("WGSKennung1");
        builder.Property(e => e.Wgskennung10)
            .HasMaxLength(4)
            .HasColumnName("WGSKennung10");
        builder.Property(e => e.Wgskennung11)
            .HasMaxLength(4)
            .HasColumnName("WGSKennung11");
        builder.Property(e => e.Wgskennung12)
            .HasMaxLength(4)
            .HasColumnName("WGSKennung12");
        builder.Property(e => e.Wgskennung2)
            .HasMaxLength(4)
            .HasColumnName("WGSKennung2");
        builder.Property(e => e.Wgskennung3)
            .HasMaxLength(4)
            .HasColumnName("WGSKennung3");
        builder.Property(e => e.Wgskennung4)
            .HasMaxLength(4)
            .HasColumnName("WGSKennung4");
        builder.Property(e => e.Wgskennung5)
            .HasMaxLength(4)
            .HasColumnName("WGSKennung5");
        builder.Property(e => e.Wgskennung6)
            .HasMaxLength(4)
            .HasColumnName("WGSKennung6");
        builder.Property(e => e.Wgskennung7)
            .HasMaxLength(4)
            .HasColumnName("WGSKennung7");
        builder.Property(e => e.Wgskennung8)
            .HasMaxLength(4)
            .HasColumnName("WGSKennung8");
        builder.Property(e => e.Wgskennung9)
            .HasMaxLength(4)
            .HasColumnName("WGSKennung9");
    }
}