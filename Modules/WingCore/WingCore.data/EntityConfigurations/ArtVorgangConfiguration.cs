using WingCore.domain.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace WingCore.data.EntityConfigurations;

public class ArtVorgangConfiguration : IEntityTypeConfiguration<ArtVorgang>
{
    public void Configure(EntityTypeBuilder<ArtVorgang> builder)
    {
        builder.HasKey(e => e.Id).HasName("ArtVorgang$ID");

        builder.ToTable("ArtVorgang");

        builder.Property(e => e.Id).HasColumnName("ID");
        builder.Property(e => e.Abschl).HasDefaultValue(false);
        builder.Property(e => e.Bmenge)
            .HasDefaultValue(0m)
            .HasColumnType("money")
            .HasColumnName("BMenge");
        builder.Property(e => e.Bstueck)
            .HasDefaultValue(0m)
            .HasColumnType("money")
            .HasColumnName("BStueck");
        builder.Property(e => e.Datum).HasColumnType("datetime");
        builder.Property(e => e.<PERSON>).HasDefaultValue((short)0);
        builder.Property(e => e.LscheinNr)
            .HasDefaultValue(0L)
            .HasColumnName("LScheinNr");
        builder.Property(e => e.Menge).HasColumnType("money");
        builder.Property(e => e.Stueck)
            .HasDefaultValue(0m)
            .HasColumnType("money");
        builder.Property(e => e.SysTime).HasColumnType("datetime");
        builder.Property(e => e.SysUser).HasMaxLength(50);
        builder.Property(e => e.VgArt).HasMaxLength(50);
    }
}