using WingCore.domain.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace WingCore.data.EntityConfigurations;

public class ArtGruppeConfiguration : IEntityTypeConfiguration<ArtGruppe>
{
    public void Configure(EntityTypeBuilder<ArtGruppe> builder)
    {
        builder.HasKey(e => e.Id).HasName("ArtGruppe$ID");

        builder.ToTable("ArtGruppe");

        builder.Property(e => e.Id).HasColumnName("ID");
        builder.Property(e => e.GrpText).HasMaxLength(30);
    }
}