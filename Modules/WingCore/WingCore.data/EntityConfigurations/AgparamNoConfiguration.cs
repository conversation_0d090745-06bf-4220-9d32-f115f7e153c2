using WingCore.domain.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace WingCore.data.EntityConfigurations;

public class AgparamNoConfiguration : IEntityTypeConfiguration<AgparamNo>
{
    public void Configure(EntityTypeBuilder<AgparamNo> builder)
    {
        builder.HasKey(e => e.Id).HasName("AGParamNO$ID");

        builder.ToTable("AGParamNO");

        builder.Property(e => e.Id).HasColumnName("ID");
        builder.Property(e => e.Faktor1).HasColumnType("money");
        builder.Property(e => e.Faktor10).HasColumnType("money");
        builder.Property(e => e.Faktor11).HasColumnType("money");
        builder.Property(e => e.Faktor12).HasColumnType("money");
        builder.Property(e => e.Faktor13).HasColumnType("money");
        builder.Property(e => e.Faktor14).HasColumnType("money");
        builder.Property(e => e.Faktor15).HasColumnType("money");
        builder.Property(e => e.Faktor16).HasColumnType("money");
        builder.Property(e => e.Faktor17).HasColumnType("money");
        builder.Property(e => e.Faktor18).HasColumnType("money");
        builder.Property(e => e.Faktor19).HasColumnType("money");
        builder.Property(e => e.Faktor2).HasColumnType("money");
        builder.Property(e => e.Faktor20).HasColumnType("money");
        builder.Property(e => e.Faktor21).HasColumnType("money");
        builder.Property(e => e.Faktor22).HasColumnType("money");
        builder.Property(e => e.Faktor23).HasColumnType("money");
        builder.Property(e => e.Faktor24).HasColumnType("money");
        builder.Property(e => e.Faktor25).HasColumnType("money");
        builder.Property(e => e.Faktor26).HasColumnType("money");
        builder.Property(e => e.Faktor27).HasColumnType("money");
        builder.Property(e => e.Faktor28).HasColumnType("money");
        builder.Property(e => e.Faktor29).HasColumnType("money");
        builder.Property(e => e.Faktor3).HasColumnType("money");
        builder.Property(e => e.Faktor30).HasColumnType("money");
        builder.Property(e => e.Faktor31).HasColumnType("money");
        builder.Property(e => e.Faktor32).HasColumnType("money");
        builder.Property(e => e.Faktor33).HasColumnType("money");
        builder.Property(e => e.Faktor34).HasColumnType("money");
        builder.Property(e => e.Faktor35).HasColumnType("money");
        builder.Property(e => e.Faktor36).HasColumnType("money");
        builder.Property(e => e.Faktor4).HasColumnType("money");
        builder.Property(e => e.Faktor5).HasColumnType("money");
        builder.Property(e => e.Faktor6).HasColumnType("money");
        builder.Property(e => e.Faktor7).HasColumnType("money");
        builder.Property(e => e.Faktor8).HasColumnType("money");
        builder.Property(e => e.Faktor9).HasColumnType("money");
        builder.Property(e => e.Ft)
            .HasMaxLength(2)
            .HasColumnName("FT");
        builder.Property(e => e.Laborpos1).HasMaxLength(4);
        builder.Property(e => e.Laborpos10).HasMaxLength(4);
        builder.Property(e => e.Laborpos11).HasMaxLength(4);
        builder.Property(e => e.Laborpos12).HasMaxLength(4);
        builder.Property(e => e.Laborpos2).HasMaxLength(4);
        builder.Property(e => e.Laborpos3).HasMaxLength(4);
        builder.Property(e => e.Laborpos4).HasMaxLength(4);
        builder.Property(e => e.Laborpos5).HasMaxLength(4);
        builder.Property(e => e.Laborpos6).HasMaxLength(4);
        builder.Property(e => e.Laborpos7).HasMaxLength(4);
        builder.Property(e => e.Laborpos8).HasMaxLength(4);
        builder.Property(e => e.Laborpos9).HasMaxLength(4);
        builder.Property(e => e.Preis).HasColumnType("money");
        builder.Property(e => e.Tk)
            .HasMaxLength(2)
            .HasColumnName("TK");
    }
}