using WingCore.domain.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace WingCore.data.EntityConfigurations;

public class ArtIdStammConfiguration : IEntityTypeConfiguration<ArtIdStamm>
{
    public void Configure(EntityTypeBuilder<ArtIdStamm> builder)
    {
        builder.HasKey(e => e.Id).HasName("ArtIdStamm$ID");

        builder.ToTable("ArtIdStamm");

        builder.Property(e => e.Id).HasColumnName("ID");
        builder.Property(e => e.ArtIdBem).HasMaxLength(255);
        builder.Property(e => e.ArtIdBez1).HasMaxLength(30);
        builder.Property(e => e.ArtIdBez2).HasMaxLength(30);
        builder.Property(e => e.ArtIdBez3).HasMaxLength(30);
        builder.Property(e => e.ArtIdBez4).HasMaxLength(30);
        builder.Property(e => e.ArtIdDatum).HasColumnType("datetime");
        builder.Property(e => e.ArtIdMatch).HasMaxLength(10);
        builder.Property(e => e.SysTime).HasColumnType("datetime");
        builder.Property(e => e.SysUser).HasMaxLength(50);
    }
}