using WingCore.domain.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace WingCore.data.EntityConfigurations;

public class ArtIdProzConfiguration : IEntityTypeConfiguration<ArtIdProz>
{
    public void Configure(EntityTypeBuilder<ArtIdProz> builder)
    {
        builder.HasKey(e => e.Id).HasName("ArtIdProz$ID");

        builder.ToTable("ArtIdProz");

        builder.Property(e => e.Id).HasColumnName("ID");
        builder.Property(e => e.ArtIdBez1).HasMaxLength(30);
        builder.Property(e => e.ArtIdBez2).HasMaxLength(30);
        builder.Property(e => e.ArtIdChgNr).HasMaxLength(15);
        builder.Property(e => e.ArtIdEh).HasMaxLength(10);
        builder.Property(e => e.ArtIdSatz).HasColumnType("money");
    }
}