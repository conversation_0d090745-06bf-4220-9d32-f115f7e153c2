using WingCore.domain.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace WingCore.data.EntityConfigurations;

public class ArtKtoAbschlConfiguration : IEntityTypeConfiguration<ArtKtoAbschl>
{
    public void Configure(EntityTypeBuilder<ArtKtoAbschl> builder)
    {
        builder.HasKey(e => e.Id).HasName("ArtKtoAbschl$ID");

        builder.ToTable("ArtKtoAbschl");

        builder.Property(e => e.Id).HasColumnName("ID");
        builder.Property(e => e.AkabaktKg).HasColumnName("AKABAktKG");
        builder.Property(e => e.AkabaltDp)
            .HasColumnType("money")
            .HasColumnName("AKABaltDP");
        builder.Property(e => e.AkabaltKg).HasColumnName("AKABaltKG");
        builder.Property(e => e.AkabaltWert)
            .HasColumnType("money")
            .HasColumnName("AKABaltWert");
        builder.Property(e => e.AkabartNr).HasColumnName("AKABArtNr");
        builder.Property(e => e.AkabfilNr).HasColumnName("AKABFilNr");
        builder.Property(e => e.AkabneuDp)
            .HasColumnType("money")
            .HasColumnName("AKABneuDP");
        builder.Property(e => e.AkabneuWert)
            .HasColumnType("money")
            .HasColumnName("AKABneuWert");
        builder.Property(e => e.Akabper)
            .HasMaxLength(7)
            .HasColumnName("AKABPer");
        builder.Property(e => e.ArtKtoAbschl1)
            .HasColumnType("money")
            .HasColumnName("ArtKtoAbschl");
    }
}