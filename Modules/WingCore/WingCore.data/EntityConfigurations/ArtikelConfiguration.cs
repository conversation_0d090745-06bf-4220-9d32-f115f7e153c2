using WingCore.domain.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace WingCore.data.EntityConfigurations;

public class ArtikelConfiguration : IEntityTypeConfiguration<Artikel>
{
    public void Configure(EntityTypeBuilder<Artikel> builder)
    {
        builder.HasKey(e => new { e.Artikelnr, e.Id }).HasName("Artikel$ArtNr");

        builder.ToTable("Artikel", tb => tb.<PERSON><PERSON><PERSON>ger("Trigger"));
        builder.Property(e => e.Id)
            .ValueGeneratedOnAdd()
            .HasColumnName("ID");
        builder.Property(e => e.AktDtBis).HasColumnType("datetime");
        builder.Property(e => e.AktDtVon).HasColumnType("datetime");
        builder.Property(e => e.AktPreis).HasColumnType("money");
        builder.Property(e => e.ArtBestellNr).HasMaxLength(30);
        builder.Property(e => e.ArtBezText1).HasMaxLength(30);
        builder.Property(e => e.ArtBezText2).HasMaxLength(30);
        builder.Property(e => e.ArtBezText3).HasMaxLength(30);
        builder.Property(e => e.ArtBezText4).HasMaxLength(30);
        builder.Property(e => e.ArtBezugsgr).HasMaxLength(10);
        builder.Property(e => e.ArtChargenNr).HasMaxLength(20);
        builder.Property(e => e.ArtEan)
            .HasMaxLength(15)
            .HasColumnName("ArtEAN");
        builder.Property(e => e.ArtEinhVerp1).HasColumnType("money");
        builder.Property(e => e.ArtEinhVp)
            .HasColumnType("money")
            .HasColumnName("ArtEinhVP");
        builder.Property(e => e.ArtEkpreis)
            .HasColumnType("money")
            .HasColumnName("ArtEKPreis");
        builder.Property(e => e.ArtEkpreisKolli)
            .HasColumnType("money")
            .HasColumnName("ArtEKPreisKolli");
        builder.Property(e => e.ArtFrachtZuschlag).HasColumnType("money");
        builder.Property(e => e.ArtHaltbarkeit).HasMaxLength(255);
        builder.Property(e => e.ArtKassBrutto).HasColumnType("money");
        builder.Property(e => e.ArtKolliInhalt).HasDefaultValue(0L);
        builder.Property(e => e.ArtKonto1).HasMaxLength(10);
        builder.Property(e => e.ArtKonto2).HasMaxLength(10);
        builder.Property(e => e.ArtKonto3).HasMaxLength(10);
        builder.Property(e => e.ArtKonto4).HasMaxLength(10);
        builder.Property(e => e.ArtKonto5).HasMaxLength(10);
        builder.Property(e => e.ArtKonto6).HasMaxLength(10);
        builder.Property(e => e.ArtKtoZug).HasDefaultValue(false);
        builder.Property(e => e.ArtLagerstelle).HasMaxLength(25);
        builder.Property(e => e.ArtLetzteÄnderung).HasColumnType("datetime");
        builder.Property(e => e.ArtLkwtype).HasColumnName("ArtLKWType");
        builder.Property(e => e.ArtMwSt).HasColumnType("money");
        builder.Property(e => e.ArtPakGewicht).HasColumnType("money");
        builder.Property(e => e.ArtPfand).HasColumnType("money");
        builder.Property(e => e.ArtRabattfähig).HasDefaultValue(false);
        builder.Property(e => e.ArtSbg)
            .HasMaxLength(10)
            .HasColumnName("ArtSBG");
        builder.Property(e => e.ArtSkontierbar).HasDefaultValue(false);
        builder.Property(e => e.ArtSperr).HasDefaultValue(false);
        builder.Property(e => e.ArtText).HasMaxLength(255);
        builder.Property(e => e.ArtVkbrutto)
            .HasColumnType("money")
            .HasColumnName("ArtVKBrutto");
        builder.Property(e => e.ArtVkpreis)
            .HasColumnType("money")
            .HasColumnName("ArtVKPreis");
        builder.Property(e => e.ArtWnr)
            .HasMaxLength(10)
            .HasColumnName("ArtWNR");
        builder.Property(e => e.ArtZbem1)
            .HasMaxLength(30)
            .HasColumnName("ArtZBem1");
        builder.Property(e => e.ArtZbem2)
            .HasMaxLength(30)
            .HasColumnName("ArtZBem2");
        builder.Property(e => e.ArtZbem3)
            .HasMaxLength(30)
            .HasColumnName("ArtZBem3");
        builder.Property(e => e.ArtZbem4)
            .HasMaxLength(30)
            .HasColumnName("ArtZBem4");
        builder.Property(e => e.ArtZbem5)
            .HasMaxLength(30)
            .HasColumnName("ArtZBem5");
        builder.Property(e => e.ArtikelIdent).HasDefaultValue(0L);
        builder.Property(e => e.DisplayArtikel).HasDefaultValue(false);
        builder.Property(e => e.DruckForm).HasMaxLength(255);
        builder.Property(e => e.DruckFormSack).HasMaxLength(255);
        builder.Property(e => e.EmpfBruttoVk)
            .HasColumnType("money")
            .HasColumnName("EmpfBruttoVK");
        builder.Property(e => e.EnthEan)
            .HasMaxLength(15)
            .HasColumnName("EnthEAN");
        builder.Property(e => e.EtDruck).HasDefaultValue(false);
        builder.Property(e => e.Hartikel).HasColumnName("HArtikel");
        builder.Property(e => e.Hlgewicht).HasColumnName("HLGewicht");
        builder.Property(e => e.InhFeld).HasMaxLength(10);
        builder.Property(e => e.K)
            .HasDefaultValue(0m)
            .HasColumnType("money");
        builder.Property(e => e.KolliAnzPal).HasMaxLength(10);
        builder.Property(e => e.KonsEan)
            .HasMaxLength(15)
            .HasColumnName("KonsEAN");
        builder.Property(e => e.Lgfaktor)
            .HasMaxLength(2)
            .HasColumnName("LGFaktor");
        builder.Property(e => e.Lgstatistik)
            .HasColumnType("money")
            .HasColumnName("LGStatistik");
        builder.Property(e => e.LieferantIln)
            .HasMaxLength(10)
            .HasColumnName("LieferantILN");
        builder.Property(e => e.MindBestandMge).HasDefaultValue(0L);
        builder.Property(e => e.N)
            .HasDefaultValue(0m)
            .HasColumnType("money");
        builder.Property(e => e.NettoEk)
            .HasColumnType("money")
            .HasColumnName("NettoEK");
        builder.Property(e => e.P)
            .HasDefaultValue(0m)
            .HasColumnType("money");
        builder.Property(e => e.P2o5)
            .HasDefaultValue(0m)
            .HasColumnType("money")
            .HasColumnName("P2O5");
        builder.Property(e => e.Palette).HasDefaultValue(false);
        builder.Property(e => e.RezNrAbsMasch).HasDefaultValue(0L);
        builder.Property(e => e.RezNrPalettierer).HasDefaultValue(0L);
        builder.Property(e => e.RezNrStretcher).HasDefaultValue(0L);
        builder.Property(e => e.SchwerGetr).HasDefaultValue(false);
        builder.Property(e => e.Send).HasDefaultValue(false);
        builder.Property(e => e.SysTime).HasColumnType("datetime");
        builder.Property(e => e.SysUser).HasMaxLength(255);
        builder.Property(e => e.VglMgeEh).HasMaxLength(10);
        builder.Property(e => e.VglPreis).HasColumnType("money");
        
        builder.Ignore(e => e.ArtikelnrAsString);
    }
}