using WingCore.domain.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace WingCore.data.EntityConfigurations;

public class AwgConfiguration : IEntityTypeConfiguration<Awg>
{
    public void Configure(EntityTypeBuilder<Awg> builder)
    {
        builder.HasKey(e => e.Id).HasName("AWGS$ID");

        builder.ToTable("AWGS");

        builder.Property(e => e.Id).HasColumnName("ID");
        builder.Property(e => e.GegBlgNr).HasMaxLength(15);
        builder.Property(e => e.Lkw2)
            .HasMaxLength(15)
            .HasColumnName("LKW2");
        builder.Property(e => e.LockLast).HasColumnType("datetime");
        builder.Property(e => e.LockStart).HasColumnType("datetime");
        builder.Property(e => e.LockUser).HasMaxLength(50);
        builder.Property(e => e.LoeschKng).HasDefaultValue(false);
        builder.Property(e => e.Nvo)
            .HasDefaultValue(false)
            .HasColumnName("NVO");
        builder.Property(e => e.NvobilanzNr)
            .HasMaxLength(30)
            .HasColumnName("NVOBilanzNr");
        builder.Property(e => e.Nvobilanziert)
            .HasDefaultValue(false)
            .HasColumnName("NVOBilanziert");
        builder.Property(e => e.Nvonr)
            .HasMaxLength(30)
            .HasColumnName("NVONr");
        builder.Property(e => e.Send).HasDefaultValue(false);
        builder.Property(e => e.SysTime).HasColumnType("datetime");
        builder.Property(e => e.SysUser).HasMaxLength(50);
        builder.Property(e => e.Verwieger).HasMaxLength(15);
        builder.Property(e => e.WgsabrNr).HasColumnName("WGSAbrNr");
        builder.Property(e => e.WgsanlGewicht)
            .HasColumnType("money")
            .HasColumnName("WGSAnlGewicht");
        builder.Property(e => e.WgsanlName2)
            .HasMaxLength(30)
            .HasColumnName("WGSAnlName2");
        builder.Property(e => e.WgsanlName3)
            .HasMaxLength(30)
            .HasColumnName("WGSAnlName3");
        builder.Property(e => e.WgsanlNr).HasColumnName("WGSAnlNr");
        builder.Property(e => e.WgsanlOrt)
            .HasMaxLength(30)
            .HasColumnName("WGSAnlOrt");
        builder.Property(e => e.WgsanlPlz)
            .HasMaxLength(6)
            .HasColumnName("WGSAnlPLZ");
        builder.Property(e => e.WgsartNr).HasColumnName("WGSArtNr");
        builder.Property(e => e.Wgsasld).HasColumnName("WGSAsld");
        builder.Property(e => e.Wgsbemerkung)
            .HasMaxLength(255)
            .HasColumnName("WGSBemerkung");
        builder.Property(e => e.WgschargNr)
            .HasMaxLength(15)
            .HasColumnName("WGSChargNr");
        builder.Property(e => e.Wgsdatum)
            .HasColumnType("datetime")
            .HasColumnName("WGSDatum");
        builder.Property(e => e.Wgselnr)
            .HasMaxLength(4)
            .HasColumnName("WGSELNR");
        builder.Property(e => e.Wgsempfänger)
            .HasMaxLength(20)
            .HasColumnName("WGSEmpfänger");
        builder.Property(e => e.Wgsfeuchte)
            .HasColumnType("money")
            .HasColumnName("WGSFeuchte");
        builder.Property(e => e.WgsfilialNr).HasColumnName("WGSFilialNr");
        builder.Property(e => e.Wgsga)
            .HasMaxLength(1)
            .HasColumnName("WGSGA");
        builder.Property(e => e.WgsgetrArt)
            .HasMaxLength(18)
            .HasColumnName("WGSGetrArt");
        builder.Property(e => e.Wgsgkennung)
            .HasMaxLength(3)
            .HasColumnName("WGSGKennung");
        builder.Property(e => e.WgskontraktNr).HasColumnName("WGSKontraktNr");
        builder.Property(e => e.WgsktNrHandPartn)
            .HasMaxLength(15)
            .HasColumnName("WGSKtNrHandPartn");
        builder.Property(e => e.WgsktNrHdP)
            .HasMaxLength(20)
            .HasColumnName("WGSKtNrHdP");
        builder.Property(e => e.WgslagerortSkp)
            .HasMaxLength(10)
            .HasColumnName("WGSLagerortSKP");
        builder.Property(e => e.Wgsland)
            .HasMaxLength(30)
            .HasColumnName("WGSLand");
        builder.Property(e => e.Wgslfnr).HasColumnName("WGSLFNr");
        builder.Property(e => e.Wgslfstatus)
            .HasMaxLength(10)
            .HasColumnName("WGSLFStatus");
        builder.Property(e => e.Wgslkw2)
            .HasMaxLength(15)
            .HasColumnName("WGSLKW2");
        builder.Property(e => e.Wgslkwkennz)
            .HasMaxLength(10)
            .HasColumnName("WGSLKWKennz");
        builder.Property(e => e.WgslsNr)
            .HasMaxLength(10)
            .HasColumnName("WGSLsNr");
        builder.Property(e => e.WgsmaklerNr).HasColumnName("WGSMaklerNr");
        builder.Property(e => e.Wgsmenge)
            .HasColumnType("money")
            .HasColumnName("WGSMenge");
        builder.Property(e => e.Wgsname1)
            .HasMaxLength(30)
            .HasColumnName("WGSName1");
        builder.Property(e => e.Wgsname2)
            .HasMaxLength(30)
            .HasColumnName("WGSName2");
        builder.Property(e => e.Wgsnr).HasColumnName("WGSNr");
        builder.Property(e => e.Wgsort)
            .HasMaxLength(30)
            .HasColumnName("WGSOrt");
        builder.Property(e => e.WgspartieBlenr)
            .HasMaxLength(20)
            .HasColumnName("WGSPartieBLENr");
        builder.Property(e => e.Wgsplz)
            .HasMaxLength(30)
            .HasColumnName("WGSPLZ");
        builder.Property(e => e.Wgspreis)
            .HasColumnType("money")
            .HasColumnName("WGSPreis");
        builder.Property(e => e.Wgsrmnr).HasColumnName("WGSRMNr");
        builder.Property(e => e.WgssammelNr).HasColumnName("WGSSammelNr");
        builder.Property(e => e.Wgssbg)
            .HasMaxLength(10)
            .HasColumnName("WGSSBG");
        builder.Property(e => e.Wgsselektion)
            .HasMaxLength(1)
            .HasColumnName("WGSSelektion");
        builder.Property(e => e.Wgssorte)
            .HasMaxLength(20)
            .HasColumnName("WGSSorte");
        builder.Property(e => e.WgsspedFax)
            .HasMaxLength(20)
            .HasColumnName("WGSSpedFax");
        builder.Property(e => e.WgsspedName1)
            .HasMaxLength(30)
            .HasColumnName("WGSSpedName1");
        builder.Property(e => e.WgsspedName2)
            .HasMaxLength(30)
            .HasColumnName("WGSSpedName2");
        builder.Property(e => e.WgsspedNr).HasColumnName("WGSSpedNr");
        builder.Property(e => e.WgsspedOrt)
            .HasMaxLength(30)
            .HasColumnName("WGSSpedOrt");
        builder.Property(e => e.WgsspedPlz)
            .HasMaxLength(6)
            .HasColumnName("WGSSpedPLZ");
        builder.Property(e => e.WgsspedSbg)
            .HasMaxLength(10)
            .HasColumnName("WGSSpedSBG");
        builder.Property(e => e.WgsspedStrasse)
            .HasMaxLength(30)
            .HasColumnName("WGSSpedStrasse");
        builder.Property(e => e.WgsspedTelefon)
            .HasMaxLength(20)
            .HasColumnName("WGSSpedTelefon");
        builder.Property(e => e.Wgssperr).HasColumnName("WGSSperr");
        builder.Property(e => e.Wgsstrasse)
            .HasMaxLength(30)
            .HasColumnName("WGSStrasse");
        builder.Property(e => e.WgsstreckenNr).HasColumnName("WGSStreckenNr");
        builder.Property(e => e.Wgstelefon)
            .HasMaxLength(20)
            .HasColumnName("WGSTelefon");
        builder.Property(e => e.WgsvonGbvgesp)
            .HasMaxLength(1)
            .HasColumnName("WGSVonGBVgesp");
        builder.Property(e => e.Wgsvz)
            .HasColumnType("money")
            .HasColumnName("WGSVZ");
        builder.Property(e => e.Wgsvzdatum)
            .HasColumnType("datetime")
            .HasColumnName("WGSVZDatum");
        builder.Property(e => e.Wgsvznr).HasColumnName("WGSVZNr");
        builder.Property(e => e.WgswgNr)
            .HasMaxLength(15)
            .HasColumnName("WGSWgNr");
        builder.Property(e => e.WgszellenNr).HasColumnName("WGSZellenNr");
        builder.Property(e => e.Wiegezeit).HasColumnType("datetime");
    }
}