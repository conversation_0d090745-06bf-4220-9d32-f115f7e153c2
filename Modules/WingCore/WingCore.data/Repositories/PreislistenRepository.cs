using WingCore.application.Contract.IModels;
using WingCore.data.WaWiContext;
using WingCore.domain.Models;

namespace WingCore.data.Repositories;

public class PreislistenRepository(RepositoryContext repositoryContext) :
     RepositoryBase<Preislisten>(repositoryContext), IPreislistenRepository
{
    public IEnumerable<Preislisten> GetPreislistenById(long? id)
    {
        return FindByCondition(c =>c.Prlnr == id, false);
    }
}