using WingCore.domain.Models;
using WingCore.application.Contract.IModels;
using WingCore.data.WaWiContext;
using WingCore.domain.Common.Configurations;

namespace WingCore.data.Repositories;

public class ConfigurationRepository(RepositoryContext repositoryContext)
    : RepositoryBase<Config>(repositoryContext), IConfigurationRepository
{
    public ValueTask<Config?> GetAsync(long id, bool trackChanges = false)
        => ValueTask.FromResult(FindByCondition(c=> c.Id == id, false).FirstOrDefault());

    public ValueTask<Config?> GetAsync(ConfigurationType type, bool trackChanges = false) 
        => ValueTask.FromResult(FindByCondition(c => c.Type == type, trackChanges).FirstOrDefault());
}