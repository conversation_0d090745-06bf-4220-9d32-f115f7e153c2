using WingCore.domain.Models;
using Microsoft.EntityFrameworkCore;
using WingCore.application.Contract.IModels;
using WingCore.application.Contract.IModels.Helper;
using WingCore.data.Repositories.Helper;
using WingCore.data.WaWiContext;

namespace WingCore.data.Repositories;

public class GbvhptRepository(RepositoryContext repositoryContext)
    : RepositoryBase<Gbvhpt>(repositoryContext), IGbvhptRepository
{
    public Task<IEnumerable<Gbvhpt>> GetGbvPositionsByNumber(long number)
    {
        return Task.FromResult<IEnumerable<Gbvhpt>>(FindByCondition(g => g.Gbnr == number, false));
    }

    public void AddGbvhpt(Gbvhpt gbvhpt)
    {
        Create(gbvhpt);
        SaveChanges();
    }

    public Task<bool> DeleteGbvPosition(long number, long position)
    {
        var gbvhpt = FindByCondition(g => g.Gbnr == number && g.GbgridPos == position, false).FirstOrDefault();
        if (gbvhpt != null)
        {
            Delete(gbvhpt);
            SaveChanges();
            return Task.FromResult(true);
        }
        return Task.FromResult(false);
    }

    public Task<bool> EditGbvPosition(long number, long position, Gbvhpt newValue)
    {
        var gbvhpt = FindByCondition(g => g.Gbnr == number && g.GbgridPos == position, false).FirstOrDefault();
        if (gbvhpt != null)
        {
            gbvhpt.Gbtext = newValue.Gbtext;
            gbvhpt.Gblbwert = newValue.Gblbwert;
            gbvhpt.Gblbeh = newValue.Gblbeh;
            gbvhpt.GbanlGew = newValue.GbanlGew;
            gbvhpt.GbabzGew = newValue.GbabzGew;
            gbvhpt.GbabrGew = newValue.GbabrGew;
            gbvhpt.Gbep = newValue.Gbep;
            gbvhpt.Gblbeh = newValue.Gblbeh;
            gbvhpt.GbgesNetto = newValue.GbgesNetto;
            gbvhpt.GbartNr = newValue.GbartNr;
            gbvhpt.BuchMenge = newValue.BuchMenge;

            Update(gbvhpt);
            SaveChanges();
            return Task.FromResult(true);
        }
        return Task.FromResult(false);
    }
}