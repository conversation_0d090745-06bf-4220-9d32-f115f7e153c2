using WingCore.domain.Models;

namespace WingCore.data.Repositories.Mapper;

public static class AradressdatenMapper
{
    public static IQueryable<Aradressdaten> MapToEdifactAradressdaten(this IQueryable<Aradressdaten> source)
    {
        return source.Select(a => new Aradressdaten
                {
                    Flags = a.Flags,
                    Arnummer = a.<PERSON>rn<PERSON>,
                    Ardatum = a.Ardatum,
                    ArkdnrRe = a.ArkdnrRe,
                    Arname1Re = a.Arname1Re,
                    ArstrasseRe = a.ArstrasseRe,
                    ArustIdNrRe = a.ArustIdNrRe,
                    Arplzre = a.Arplzre,
                    ArortRe = a.ArortRe,
                    ArkdnrWe = a.ArkdnrWe,
                    Arname1We = a.Arname1We,
                    ArstrasseWe = a.ArstrasseWe,
                    Arplzwe = a.Arplzwe,
                    ArortWe = a.ArortWe,
                    ArgesMenge = a.ArgesMenge,
                    Arnetto1 = a.Arnetto1,
                    Arnetto2 = a.Arnetto2,
                    Arnetto3 = a.Arnetto3,
                    ArgesNetto = a.<PERSON>rges<PERSON>to,
                    Armwst1 = a.Armwst1,
                    Armwst2 = a.Armwst2,
                    Armwst3 = a.Armwst3,
                    ArgesMwst = a.ArgesMwst,
                    ArgesBrutto1 = a.ArgesBrutto1,
                    ArgesBrutto2 = a.ArgesBrutto2,
                    ArgesBrutto3 = a.ArgesBrutto3,
                    ArgesBrutto = a.ArgesBrutto,
                    ArmwSt1Proz = a.ArmwSt1Proz,
                    ArmwSt2Proz = a.ArmwSt2Proz,
                    ArmwSt3Proz = a.ArmwSt3Proz,
                    Arskt1Betrag = a.Arskt1Betrag,
                    Arskt2Betrag = a.Arskt2Betrag,
                    Arskt3Betrag = a.Arskt3Betrag,
                    Arskto1P = a.Arskto1P,
                    Arskto1T = a.Arskto1T,
                    Arskto2P = a.Arskto2P,
                    Arskto2T = a.Arskto2T,
                    ArnettoTage = a.ArnettoTage,
                    ArgesRabatt = a.ArgesRabatt,
                    Arwhrg = a.Arwhrg,
                    ArwhrgKurs = a.ArwhrgKurs,
                    ArstatusRechnung = a.ArstatusRechnung,
                    ArstatusGutschrift = a.ArstatusGutschrift,
                    ArstatusStorno = a.ArstatusStorno,
                    Arwashout = a.Arwashout,
                    Arkorrektur = a.Arkorrektur,
                    WertGut = a.WertGut,
                    Arfibu2 = a.Arfibu2,
                    Send = a.Send,
                    Edifact = a.Edifact,
                    Factoring = a.Factoring,
                    Factoringv2 = a.Factoringv2,
                    ArstornoKng = a.ArstornoKng,
                    AraltNr = a.AraltNr,
                    ArstreckenNr = a.ArstreckenNr,
                    Arbearbeiter = a.Arbearbeiter,
                    ArbestellNr = a.ArbestellNr,
                    Aruhrzeit = a.Aruhrzeit,
                    UhrzeitTxt = a.UhrzeitTxt,
                    Arkdsbgre = a.Arkdsbgre,
                    AranredeRe = a.AranredeRe,
                    Arname2Re = a.Arname2Re,
                    Arname3Re = a.Arname3Re,
                    Arbldre = a.Arbldre,
                    ArlandRe = a.ArlandRe,
                    IlnnrRe = a.IlnnrRe,
                    Arkdsbgwe = a.Arkdsbgwe,
                    AranredeWe = a.AranredeWe,
                    Arname2We = a.Arname2We,
                    Arname3We = a.Arname3We,
                    Arbldwe = a.Arbldwe,
                    ArlandWe = a.ArlandWe,
                    IlnnrWe = a.IlnnrWe,
                    VvonrWe = a.VvonrWe,
                    ArregioNr = a.ArregioNr,
                    ArregioName = a.ArregioName,
                    ArregioName2 = a.ArregioName2,
                    ArregioStrasse = a.ArregioStrasse,
                    ArregioPlz = a.ArregioPlz,
                    ArregioOrt = a.ArregioOrt,
                    ArregioStatus = a.ArregioStatus,
                    ArregIln = a.ArregIln,
                    ArvertrNr = a.ArvertrNr,
                    ArvertrName = a.ArvertrName,
                    ArvertrOrt = a.ArvertrOrt,
                    ArzahlZiel = a.ArzahlZiel,
                    Arbez = a.Arbez,
                    Arbank = a.Arbank,
                    Arblz = a.Arblz,
                    ArkontoNr = a.ArkontoNr,
                    ArzhlDruck = a.ArzhlDruck,
                    ArzhlDruck02 = a.ArzhlDruck02,
                    ArzhlDruck03 = a.ArzhlDruck03,
                    ArkdgrabattP = a.ArkdgrabattP,
                    ArkdgrabattD = a.ArkdgrabattD,
                    ArreBetrag = a.ArreBetrag,
                    Arasld = a.Arasld,
                    Argblnr = a.Argblnr,
                    ArkdlsEz = a.ArkdlsEz,
                    ArkdnrCl = a.ArkdnrCl,
                    ArfbzgKng = a.ArfbzgKng,
                    Arlkw = a.Arlkw,
                    Skontosatz = a.Skontosatz,
                    Vdatum = a.Vdatum,
                    Vdok = a.Vdok,
                    ArbankZuOrdn = a.ArbankZuOrdn,
                    Opre = a.Opre,
                    FilialNr = a.FilialNr,
                    ArpfachRe = a.ArpfachRe,
                    ArpfachWe = a.ArpfachWe,
                    KdliefNr = a.KdliefNr,
                    StornoBemerkung = a.StornoBemerkung,
                    MandatNr = a.MandatNr,
                    SysUser = a.SysUser,
                    SysTime = a.SysTime,
                    Id = a.Id,
                    ArlfnrWe = a.ArlfnrWe,
                    InvoiceHolderCustomerData = new Kunden()
                    {
                         Id = a.InvoiceHolderCustomerData.Id,
                        Kdnummer = a.InvoiceHolderCustomerData.Kdnummer,
                        Kdsbg = a.InvoiceHolderCustomerData.Kdsbg,
                        KdansprPartner = a.InvoiceHolderCustomerData.KdansprPartner,
                        Kdanrede = a.InvoiceHolderCustomerData.Kdanrede,
                        Kdname1 = a.InvoiceHolderCustomerData.Kdname1,
                        Kdname2 = a.InvoiceHolderCustomerData.Kdname2,
                        Kdname3 = a.InvoiceHolderCustomerData.Kdname3,
                        Kdstrasse = a.InvoiceHolderCustomerData.Kdstrasse,
                        Kdplz = a.InvoiceHolderCustomerData.Kdplz,
                        Kdort = a.InvoiceHolderCustomerData.Kdort,
                        Kdpostfach = a.InvoiceHolderCustomerData.Kdpostfach,
                        Kdplzpostfach = a.InvoiceHolderCustomerData.Kdplzpostfach,
                        Kdland = a.InvoiceHolderCustomerData.Kdland,
                        KdvertrNr = a.InvoiceHolderCustomerData.KdvertrNr,
                        Kdstaffelnr = a.InvoiceHolderCustomerData.Kdstaffelnr,
                        Kdbundesland = a.InvoiceHolderCustomerData.Kdbundesland,
                        KdustIdnr = a.InvoiceHolderCustomerData.KdustIdnr,
                        KdbranchenKng1 = a.InvoiceHolderCustomerData.KdbranchenKng1,
                        KdbranchenKng2 = a.InvoiceHolderCustomerData.KdbranchenKng2,
                        KdbranchenKng3 = a.InvoiceHolderCustomerData.KdbranchenKng3,
                        KdbranchenKng4 = a.InvoiceHolderCustomerData.KdbranchenKng4,
                        KdbranchenKng5 = a.InvoiceHolderCustomerData.KdbranchenKng5,
                        KdbranchenKng6 = a.InvoiceHolderCustomerData.KdbranchenKng6,
                        Kdtelefon1 = a.InvoiceHolderCustomerData.Kdtelefon1,
                        Kdtelefon2 = a.InvoiceHolderCustomerData.Kdtelefon2,
                        Kdtelefax = a.InvoiceHolderCustomerData.Kdtelefax,
                        Kdmobil = a.InvoiceHolderCustomerData.Kdmobil,
                        Kdemail = a.InvoiceHolderCustomerData.Kdemail,
                        Kdinternet = a.InvoiceHolderCustomerData.Kdinternet,
                        KdpreislNr1 = a.InvoiceHolderCustomerData.KdpreislNr1,
                        KdpreislNr2 = a.InvoiceHolderCustomerData.KdpreislNr2,
                        KdpreislNr3 = a.InvoiceHolderCustomerData.KdpreislNr3,
                        KdpreislNr4 = a.InvoiceHolderCustomerData.KdpreislNr4,
                        KdpreislNr5 = a.InvoiceHolderCustomerData.KdpreislNr5,
                        KdpreislNr6 = a.InvoiceHolderCustomerData.KdpreislNr6,
                        Kdblz = a.InvoiceHolderCustomerData.Kdblz,
                        KdkontoNr = a.InvoiceHolderCustomerData.KdkontoNr,
                        Kdbank = a.InvoiceHolderCustomerData.Kdbank,
                        KdbankEinzug = a.InvoiceHolderCustomerData.KdbankEinzug,
                        Kdzahlungsziel = a.InvoiceHolderCustomerData.Kdzahlungsziel,
                        Kdskt1Proz = a.InvoiceHolderCustomerData.Kdskt1Proz,
                        Kdskt1Tage = a.InvoiceHolderCustomerData.Kdskt1Tage,
                        Kdskt2Proz = a.InvoiceHolderCustomerData.Kdskt2Proz,
                        Kdskt2Tage = a.InvoiceHolderCustomerData.Kdskt2Tage,
                        KdtageNetto = a.InvoiceHolderCustomerData.KdtageNetto,
                        KdgesRabatt = a.InvoiceHolderCustomerData.KdgesRabatt,
                        KdabrStelleNr = a.InvoiceHolderCustomerData.KdabrStelleNr,
                        KdabrStelleName = a.InvoiceHolderCustomerData.KdabrStelleName,
                        KdabrStelleOrt = a.InvoiceHolderCustomerData.KdabrStelleOrt,
                        KdabrStelleStatus = a.InvoiceHolderCustomerData.KdabrStelleStatus,
                        Kdnotiz = a.InvoiceHolderCustomerData.Kdnotiz,
                        KddatLetztRechn = a.InvoiceHolderCustomerData.KddatLetztRechn,
                        KdbankZuOrdnung = a.InvoiceHolderCustomerData.KdbankZuOrdnung,
                        Kdlieferrythmus = a.InvoiceHolderCustomerData.Kdlieferrythmus,
                        KdtourenNr = a.InvoiceHolderCustomerData.KdtourenNr,
                        KdlieferSatz = a.InvoiceHolderCustomerData.KdlieferSatz,
                        KdreAusdrAnzahl = a.InvoiceHolderCustomerData.KdreAusdrAnzahl,
                        KdsollZins = a.InvoiceHolderCustomerData.KdsollZins,
                        KdhabenZins = a.InvoiceHolderCustomerData.KdhabenZins,
                        Kdkreditlimit = a.InvoiceHolderCustomerData.Kdkreditlimit,
                        Kdmitteilungstext = a.InvoiceHolderCustomerData.Kdmitteilungstext,
                        Kdgruppe = a.InvoiceHolderCustomerData.Kdgruppe,
                        Kdhänger = a.InvoiceHolderCustomerData.Kdhänger,
                        Kdentsorger = a.InvoiceHolderCustomerData.Kdentsorger,
                        Kdgeburtstag = a.InvoiceHolderCustomerData.Kdgeburtstag,
                        KdfilialNr = a.InvoiceHolderCustomerData.KdfilialNr,
                        Kdasld = a.InvoiceHolderCustomerData.Kdasld,
                        Kdrabatt = a.InvoiceHolderCustomerData.Kdrabatt,
                        Kdintra1 = a.InvoiceHolderCustomerData.Kdintra1,
                        Kdintra2 = a.InvoiceHolderCustomerData.Kdintra2,
                        Kdle = a.InvoiceHolderCustomerData.Kdle,
                        Kdleh = a.InvoiceHolderCustomerData.Kdleh,
                        KdLfNr = a.InvoiceHolderCustomerData.KdLfNr,
                        KdrabText = a.InvoiceHolderCustomerData.KdrabText,
                        Kdlfr1 = a.InvoiceHolderCustomerData.Kdlfr1,
                        Kdlfr2 = a.InvoiceHolderCustomerData.Kdlfr2,
                        Kdlfr3 = a.InvoiceHolderCustomerData.Kdlfr3,
                        Kdlfr4 = a.InvoiceHolderCustomerData.Kdlfr4,
                        Kdanalyse = a.InvoiceHolderCustomerData.Kdanalyse,
                        Kdmtl = a.InvoiceHolderCustomerData.Kdmtl,
                        Kdhmtl = a.InvoiceHolderCustomerData.Kdhmtl,
                        Kdwtl = a.InvoiceHolderCustomerData.Kdwtl,
                        Kddekade = a.InvoiceHolderCustomerData.Kddekade,
                        Kdlfstopp = a.InvoiceHolderCustomerData.Kdlfstopp,
                        Send = a.InvoiceHolderCustomerData.Send,
                        Mvanalyse = a.InvoiceHolderCustomerData.Mvanalyse,
                        Edifact = a.InvoiceHolderCustomerData.Edifact,
                        Kdgaw = a.InvoiceHolderCustomerData.Kdgaw,
                        Kdsperr = a.InvoiceHolderCustomerData.Kdsperr,
                        EigLager = a.InvoiceHolderCustomerData.EigLager,
                        EdifactDatum = a.InvoiceHolderCustomerData.EdifactDatum,
                        Druckanzahl = a.InvoiceHolderCustomerData.Druckanzahl,
                        KdletztAend = a.InvoiceHolderCustomerData.KdletztAend,
                        KdmitTour = a.InvoiceHolderCustomerData.KdmitTour,
                        Kilometer = a.InvoiceHolderCustomerData.Kilometer,
                        Zfeld1 = a.InvoiceHolderCustomerData.Zfeld1,
                        Zfeld2 = a.InvoiceHolderCustomerData.Zfeld2,
                        Zfeld3 = a.InvoiceHolderCustomerData.Zfeld3,
                        KdfacFlowSend = a.InvoiceHolderCustomerData.KdfacFlowSend,
                        FactSend = a.InvoiceHolderCustomerData.FactSend,
                        Gedifact = a.InvoiceHolderCustomerData.Gedifact,
                        LieferAvis = a.InvoiceHolderCustomerData.LieferAvis,
                        LieferAvisDatum = a.InvoiceHolderCustomerData.LieferAvisDatum,
                        Kdswift = a.InvoiceHolderCustomerData.Kdswift,
                        Kdiban = a.InvoiceHolderCustomerData.Kdiban,
                        Iln = a.InvoiceHolderCustomerData.Iln,
                        Anzahlungsbetrag = a.InvoiceHolderCustomerData.Anzahlungsbetrag,
                        Nvo = a.InvoiceHolderCustomerData.Nvo,
                        Nvojahr = a.InvoiceHolderCustomerData.Nvojahr,
                        Nvonr = a.InvoiceHolderCustomerData.Nvonr,
                        Nvoscheinegeschrieben = a.InvoiceHolderCustomerData.Nvoscheinegeschrieben,
                        Vvonr = a.InvoiceHolderCustomerData.Vvonr,
                        DruckForm1 = a.InvoiceHolderCustomerData.DruckForm1,
                        DruckForm2 = a.InvoiceHolderCustomerData.DruckForm2,
                        ArdruckForm = a.InvoiceHolderCustomerData.ArdruckForm,
                        KdktrStelleNr = a.InvoiceHolderCustomerData.KdktrStelleNr,
                        Katnr = a.InvoiceHolderCustomerData.Katnr,
                        VerbNr = a.InvoiceHolderCustomerData.VerbNr,
                        Hebebuehne = a.InvoiceHolderCustomerData.Hebebuehne,
                        KreditBem = a.InvoiceHolderCustomerData.KreditBem,
                        MandatNr = a.InvoiceHolderCustomerData.MandatNr,
                        DruckRform = a.InvoiceHolderCustomerData.DruckRform,
                        EmailVersand = a.InvoiceHolderCustomerData.EmailVersand,
                        SachKuNachweis = a.InvoiceHolderCustomerData.SachKuNachweis,
                        Ramail = a.InvoiceHolderCustomerData.Ramail,
                        Mvmail = a.InvoiceHolderCustomerData.Mvmail,
                        SammelEmail = a.InvoiceHolderCustomerData.SammelEmail,
                        MvmailVersand = a.InvoiceHolderCustomerData.MvmailVersand,
                        OrdersBest = a.InvoiceHolderCustomerData.OrdersBest,
                        SysUser = a.InvoiceHolderCustomerData.SysUser,
                        SysTime = a.InvoiceHolderCustomerData.SysTime,
                        AlsdruckForm = a.InvoiceHolderCustomerData.AlsdruckForm,
                        Mvformular = a.InvoiceHolderCustomerData.Mvformular,
                        Mvetikett = a.InvoiceHolderCustomerData.Mvetikett,
                    },
                    GoodsRecipientsData = new Kunden()
                    {
                         Id = a.GoodsRecipientsData.Id,
                        Kdnummer = a.GoodsRecipientsData.Kdnummer,
                        Kdsbg = a.GoodsRecipientsData.Kdsbg,
                        KdansprPartner = a.GoodsRecipientsData.KdansprPartner,
                        Kdanrede = a.GoodsRecipientsData.Kdanrede,
                        Kdname1 = a.GoodsRecipientsData.Kdname1,
                        Kdname2 = a.GoodsRecipientsData.Kdname2,
                        Kdname3 = a.GoodsRecipientsData.Kdname3,
                        Kdstrasse = a.GoodsRecipientsData.Kdstrasse,
                        Kdplz = a.GoodsRecipientsData.Kdplz,
                        Kdort = a.GoodsRecipientsData.Kdort,
                        Kdpostfach = a.GoodsRecipientsData.Kdpostfach,
                        Kdplzpostfach = a.GoodsRecipientsData.Kdplzpostfach,
                        Kdland = a.GoodsRecipientsData.Kdland,
                        KdvertrNr = a.GoodsRecipientsData.KdvertrNr,
                        Kdstaffelnr = a.GoodsRecipientsData.Kdstaffelnr,
                        Kdbundesland = a.GoodsRecipientsData.Kdbundesland,
                        KdustIdnr = a.GoodsRecipientsData.KdustIdnr,
                        KdbranchenKng1 = a.GoodsRecipientsData.KdbranchenKng1,
                        KdbranchenKng2 = a.GoodsRecipientsData.KdbranchenKng2,
                        KdbranchenKng3 = a.GoodsRecipientsData.KdbranchenKng3,
                        KdbranchenKng4 = a.GoodsRecipientsData.KdbranchenKng4,
                        KdbranchenKng5 = a.GoodsRecipientsData.KdbranchenKng5,
                        KdbranchenKng6 = a.GoodsRecipientsData.KdbranchenKng6,
                        Kdtelefon1 = a.GoodsRecipientsData.Kdtelefon1,
                        Kdtelefon2 = a.GoodsRecipientsData.Kdtelefon2,
                        Kdtelefax = a.GoodsRecipientsData.Kdtelefax,
                        Kdmobil = a.GoodsRecipientsData.Kdmobil,
                        Kdemail = a.GoodsRecipientsData.Kdemail,
                        Kdinternet = a.GoodsRecipientsData.Kdinternet,
                        KdpreislNr1 = a.GoodsRecipientsData.KdpreislNr1,
                        KdpreislNr2 = a.GoodsRecipientsData.KdpreislNr2,
                        KdpreislNr3 = a.GoodsRecipientsData.KdpreislNr3,
                        KdpreislNr4 = a.GoodsRecipientsData.KdpreislNr4,
                        KdpreislNr5 = a.GoodsRecipientsData.KdpreislNr5,
                        KdpreislNr6 = a.GoodsRecipientsData.KdpreislNr6,
                        Kdblz = a.GoodsRecipientsData.Kdblz,
                        KdkontoNr = a.GoodsRecipientsData.KdkontoNr,
                        Kdbank = a.GoodsRecipientsData.Kdbank,
                        KdbankEinzug = a.GoodsRecipientsData.KdbankEinzug,
                        Kdzahlungsziel = a.GoodsRecipientsData.Kdzahlungsziel,
                        Kdskt1Proz = a.GoodsRecipientsData.Kdskt1Proz,
                        Kdskt1Tage = a.GoodsRecipientsData.Kdskt1Tage,
                        Kdskt2Proz = a.GoodsRecipientsData.Kdskt2Proz,
                        Kdskt2Tage = a.GoodsRecipientsData.Kdskt2Tage,
                        KdtageNetto = a.GoodsRecipientsData.KdtageNetto,
                        KdgesRabatt = a.GoodsRecipientsData.KdgesRabatt,
                        KdabrStelleNr = a.GoodsRecipientsData.KdabrStelleNr,
                        KdabrStelleName = a.GoodsRecipientsData.KdabrStelleName,
                        KdabrStelleOrt = a.GoodsRecipientsData.KdabrStelleOrt,
                        KdabrStelleStatus = a.GoodsRecipientsData.KdabrStelleStatus,
                        Kdnotiz = a.GoodsRecipientsData.Kdnotiz,
                        KddatLetztRechn = a.GoodsRecipientsData.KddatLetztRechn,
                        KdbankZuOrdnung = a.GoodsRecipientsData.KdbankZuOrdnung,
                        Kdlieferrythmus = a.GoodsRecipientsData.Kdlieferrythmus,
                        KdtourenNr = a.GoodsRecipientsData.KdtourenNr,
                        KdlieferSatz = a.GoodsRecipientsData.KdlieferSatz,
                        KdreAusdrAnzahl = a.GoodsRecipientsData.KdreAusdrAnzahl,
                        KdsollZins = a.GoodsRecipientsData.KdsollZins,
                        KdhabenZins = a.GoodsRecipientsData.KdhabenZins,
                        Kdkreditlimit = a.GoodsRecipientsData.Kdkreditlimit,
                        Kdmitteilungstext = a.GoodsRecipientsData.Kdmitteilungstext,
                        Kdgruppe = a.GoodsRecipientsData.Kdgruppe,
                        Kdhänger = a.GoodsRecipientsData.Kdhänger,
                        Kdentsorger = a.GoodsRecipientsData.Kdentsorger,
                        Kdgeburtstag = a.GoodsRecipientsData.Kdgeburtstag,
                        KdfilialNr = a.GoodsRecipientsData.KdfilialNr,
                        Kdasld = a.GoodsRecipientsData.Kdasld,
                        Kdrabatt = a.GoodsRecipientsData.Kdrabatt,
                        Kdintra1 = a.GoodsRecipientsData.Kdintra1,
                        Kdintra2 = a.GoodsRecipientsData.Kdintra2,
                        Kdle = a.GoodsRecipientsData.Kdle,
                        Kdleh = a.GoodsRecipientsData.Kdleh,
                        KdLfNr = a.GoodsRecipientsData.KdLfNr,
                        KdrabText = a.GoodsRecipientsData.KdrabText,
                        Kdlfr1 = a.GoodsRecipientsData.Kdlfr1,
                        Kdlfr2 = a.GoodsRecipientsData.Kdlfr2,
                        Kdlfr3 = a.GoodsRecipientsData.Kdlfr3,
                        Kdlfr4 = a.GoodsRecipientsData.Kdlfr4,
                        Kdanalyse = a.GoodsRecipientsData.Kdanalyse,
                        Kdmtl = a.GoodsRecipientsData.Kdmtl,
                        Kdhmtl = a.GoodsRecipientsData.Kdhmtl,
                        Kdwtl = a.GoodsRecipientsData.Kdwtl,
                        Kddekade = a.GoodsRecipientsData.Kddekade,
                        Kdlfstopp = a.GoodsRecipientsData.Kdlfstopp,
                        Send = a.GoodsRecipientsData.Send,
                        Mvanalyse = a.GoodsRecipientsData.Mvanalyse,
                        Edifact = a.GoodsRecipientsData.Edifact,
                        Kdgaw = a.GoodsRecipientsData.Kdgaw,
                        Kdsperr = a.GoodsRecipientsData.Kdsperr,
                        EigLager = a.GoodsRecipientsData.EigLager,
                        EdifactDatum = a.GoodsRecipientsData.EdifactDatum,
                        Druckanzahl = a.GoodsRecipientsData.Druckanzahl,
                        KdletztAend = a.GoodsRecipientsData.KdletztAend,
                        KdmitTour = a.GoodsRecipientsData.KdmitTour,
                        Kilometer = a.GoodsRecipientsData.Kilometer,
                        Zfeld1 = a.GoodsRecipientsData.Zfeld1,
                        Zfeld2 = a.GoodsRecipientsData.Zfeld2,
                        Zfeld3 = a.GoodsRecipientsData.Zfeld3,
                        KdfacFlowSend = a.GoodsRecipientsData.KdfacFlowSend,
                        FactSend = a.GoodsRecipientsData.FactSend,
                        Gedifact = a.GoodsRecipientsData.Gedifact,
                        LieferAvis = a.GoodsRecipientsData.LieferAvis,
                        LieferAvisDatum = a.GoodsRecipientsData.LieferAvisDatum,
                        Kdswift = a.GoodsRecipientsData.Kdswift,
                        Kdiban = a.GoodsRecipientsData.Kdiban,
                        Iln = a.GoodsRecipientsData.Iln,
                        Anzahlungsbetrag = a.GoodsRecipientsData.Anzahlungsbetrag,
                        Nvo = a.GoodsRecipientsData.Nvo,
                        Nvojahr = a.GoodsRecipientsData.Nvojahr,
                        Nvonr = a.GoodsRecipientsData.Nvonr,
                        Nvoscheinegeschrieben = a.GoodsRecipientsData.Nvoscheinegeschrieben,
                        Vvonr = a.GoodsRecipientsData.Vvonr,
                        DruckForm1 = a.GoodsRecipientsData.DruckForm1,
                        DruckForm2 = a.GoodsRecipientsData.DruckForm2,
                        ArdruckForm = a.GoodsRecipientsData.ArdruckForm,
                        KdktrStelleNr = a.GoodsRecipientsData.KdktrStelleNr,
                        Katnr = a.GoodsRecipientsData.Katnr,
                        VerbNr = a.GoodsRecipientsData.VerbNr,
                        Hebebuehne = a.GoodsRecipientsData.Hebebuehne,
                        KreditBem = a.GoodsRecipientsData.KreditBem,
                        MandatNr = a.GoodsRecipientsData.MandatNr,
                        DruckRform = a.GoodsRecipientsData.DruckRform,
                        EmailVersand = a.GoodsRecipientsData.EmailVersand,
                        SachKuNachweis = a.GoodsRecipientsData.SachKuNachweis,
                        Ramail = a.GoodsRecipientsData.Ramail,
                        Mvmail = a.GoodsRecipientsData.Mvmail,
                        SammelEmail = a.GoodsRecipientsData.SammelEmail,
                        MvmailVersand = a.GoodsRecipientsData.MvmailVersand,
                        OrdersBest = a.GoodsRecipientsData.OrdersBest,
                        SysUser = a.GoodsRecipientsData.SysUser,
                        SysTime = a.GoodsRecipientsData.SysTime,
                        AlsdruckForm = a.GoodsRecipientsData.AlsdruckForm,
                        Mvformular = a.GoodsRecipientsData.Mvformular,
                        Mvetikett = a.GoodsRecipientsData.Mvetikett
                    },
                    Positionen = a.Positionen,
                });
    }
}