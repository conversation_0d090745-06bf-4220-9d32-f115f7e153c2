using Microsoft.EntityFrameworkCore;
using WingCore.domain.Models;
using WingCore.application.Contract.IModels;
using WingCore.application.DataTransferObjects.Wgs;
using WingCore.application.Wiegeschein.Helper;
using WingCore.data.WaWiContext;

using WingCore.domain.Common.Flags;

namespace WingCore.data.Repositories;

public class WgsRepository(RepositoryContext repositoryContext)
    : RepositoryBase<Wg>(repositoryContext), IWgsRepository
{
    public async Task<IEnumerable<Wg>> GetOpenWgs(bool trackBack, long? supplierId)
    {
        IEnumerable<Wg>? wgs = null;
        if (supplierId is null)
        {
            wgs = FindByCondition(w => ((w.WgsabrNr ?? 0) == 0) && w.Wgssperr != true, trackBack);
        }
        else
        {
            wgs = FindByCondition(w => ((w.WgsabrNr ?? 0) == 0) && w.Wgssperr != true && w.Wgslfnr == supplierId, trackBack);
        }
        
        var singleWgs = wgs?.ToList();

        return singleWgs ?? [];
    }

    public Task<IEnumerable<Wg>> GetWgsByNumbers(IEnumerable<long> wgsNumbers)
    {
        var wgsList = new List<Wg>();
        foreach (var number in wgsNumbers)
        {
            // Execute a raw SQL query
            var sql = "SELECT * FROM WGS WHERE Wgsnr = @p0";
            var wgs = RepositoryContext.Set<Wg>().FromSqlRaw(sql, number).AsNoTracking().ToList();
        
            if (wgs.Count > 0)
            {
                wgsList.AddRange(wgs);
            }
        }
    
        return Task.FromResult<IEnumerable<Wg>>(wgsList);
    }

    public async Task<IEnumerable<WgAdfinityJobDto>> GetAllOpenWgsDtoForAdfinity(WiegescheinSearchParam searchParam)
    {
       var query = from w in RepositoryContext.Set<Wg>()
           select w;

       // Only apply filtering if any of the search parameters have values
       if (searchParam.FromWgsNumber.HasValue && searchParam.FromWgsNumber.Value != 0 && 
           searchParam.ToWgsNumber.HasValue && searchParam.ToWgsNumber.Value != 0
          )
       {
           // Filter by WGS number range if specified
           query = query.Where(w => w.Wgsnr >= searchParam.FromWgsNumber);
           query = query.Where(w => w.Wgsnr <= searchParam.ToWgsNumber);
       }
       else if (searchParam.FromDate.HasValue && searchParam.ToDate.HasValue)
       {
           // Filter by date range if specified and no WGS numbers provided
           query = query.Where(w => w.SysTime >= searchParam.FromDate);
           query = query.Where(w => w.SysTime <= searchParam.ToDate);
       }
       // If all parameters are null, no additional filtering is applied

       var allSearchedEntity = from w in query
           where (searchParam.WithOutFlags == (long)WgsFlags.WgsMailSendSuccess
                     ? (w.Flags & (long)WgsFlags.WgsMailSendSuccess) == 0
                     : (w.Flags & ((long)WgsFlags.EasiAdfinityReadyToSend | (long)WgsFlags.EasiAdfinitySendSuccess)) == 0
                       || searchParam.WithOutFlags == (long)WgsFlags.EasiAdfinityReadyToSend)
           where w.SysTime >= searchParam.FromSystemDate
           where w.Wgsselektion == "X"
           select new WgAdfinityJobDto()
           {
               Id = w.Id,
               WgsNumber = w.Wgsnr,
               WgsLfNr = w.Wgslfnr,
               WgsArtNr = w.WgsartNr,
               WgsDatum = w.Wgsdatum,
               WgsPreis = w.Wgspreis,
               WgsMenge = w.Wgsmenge,
               WgsGegBlgNr = w.GegBlgNr ?? string.Empty
           };
    
       return await allSearchedEntity.ToListAsync();
    }

    public void SetAsUsed(long wgsNumber, long abrNumber)
    {
        var wgs = FindByCondition(w => w.Wgsnr == wgsNumber, false).FirstOrDefault();
        if (wgs != null)
        {
            wgs.WgsabrNr = abrNumber;
            Update(wgs);
            SaveChanges();
        }
        else
        {
            throw new Exception($"WGS with number {wgsNumber} not found.");
        }
    }
    
    public void SetWgsFlag(List<long> wgsIds, WgsFlags flag)
    {
        if (wgsIds.Count == 0)
            return;

        const int batchSize = 200;
        for (var i = 0; i < wgsIds.Count; i += batchSize)
        {
            var batch = wgsIds.Skip(i).Take(batchSize).ToList();
            foreach (var invoiceNumber in batch)
            {
                var allWg = FindAll(true)
                    .FirstOrDefault(wg => wg.Id == invoiceNumber);
                
                if (allWg is null)
                    continue;

                allWg.FlagsWorker.SetFlag(flag);
            }

            RepositoryContext.SaveChanges();
            RepositoryContext.ChangeTracker.Clear();
        }
    }
    
    public void RemoveWgsFlag(List<long> wgsIds, WgsFlags flag)
    {
        if (wgsIds.Count == 0)
            return;
        
        var wgs = from wg in RepositoryContext.Wgs
            where wgsIds.Contains(wg.Id)
            select wg;

        foreach (var wg in wgs)
        {
            wg.FlagsWorker.RemoveFlag(flag);
        }

        RepositoryContext.SaveChanges();
        RepositoryContext.ChangeTracker.Clear();
    }
}