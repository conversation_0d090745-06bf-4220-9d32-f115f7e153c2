using System.Security.Cryptography;
using Microsoft.EntityFrameworkCore;
using WingCore.application.Contract.IModels.StammDb;
using WingCore.data.StammContext;
using WingCore.domain.Models.StammDbModels;

namespace WingCore.data.Repositories.StammDB;

public class ApiKeyRepository(StammDbContext stammDbContext) : StammDbRepositoryBase<ApiKey>(stammDbContext), IApiKeyRepository
{
    public async ValueTask<ApiKey> Create(string description)
    {
        var key = new byte[32];
        using var rng = RandomNumberGenerator.Create();
        rng.GetBytes(key);

        var base64Key = Convert.ToBase64String(key);
        if(string.IsNullOrWhiteSpace(base64Key))
            throw new Exception("Failed to generate a new API key");
        
        var newApiKey = new ApiKey
        {
            Key = base64Key,
            Description = description
        };
        
        await RepositoryContext.ApiKeys.AddAsync(newApiKey);
        return newApiKey;
    }

    public async ValueTask<ApiKey?> GetApiKey(string apiKey) => await FindByCondition(a => a.Key == apiKey, false).FirstOrDefaultAsync();
    public async ValueTask<ApiKey?> GetApiKeyById(long id) => await FindByCondition(a => a.Id == id, false).FirstOrDefaultAsync();

    public async Task<IEnumerable<ApiKey>> GetAllApiKeysMasked()
    {
        return await FindAll(false).Select(a => new ApiKey
        {
            Id = a.Id,
            Description = a.Description,
            IpAddress = a.IpAddress,
            Key = a.GetMaskedKey()
        }).ToListAsync();
    }
    

    public async ValueTask<ApiKey?> GetApiKeyByIp(string ip) => await FindByCondition(a => a.IpAddress == ip, false).FirstOrDefaultAsync();

    public Task SaveChangesAsync(CancellationToken cancellationToken) => RepositoryContext.SaveChangesAsync(cancellationToken);
    public void Delete(long id)
    {
        var entity = RepositoryContext.ApiKeys.Find(id);
        if(entity is not null)
            RepositoryContext.ApiKeys.Remove(entity);
    }

    public void Detach<TEntity>(TEntity entity) where TEntity : class
    {
        var entry = RepositoryContext.Entry(entity);
        entry.State = EntityState.Detached;
    }
}