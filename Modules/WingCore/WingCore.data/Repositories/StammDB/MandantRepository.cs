using WingCore.domain.Models.StammDbModels;
using Microsoft.EntityFrameworkCore;
using WingCore.application.Contract.IModels.StammDb;
using WingCore.data.StammContext;

namespace WingCore.data.Repositories.StammDB;

public class MandantRepository(StammDbContext stammDbContext)
    : IMandantRepository
{
    public ValueTask<Mandant?> GetByNumber(string mandantNumber) =>
        ValueTask.FromResult(stammDbContext.Mandants.SingleOrDefault(m => m.Mnr == mandantNumber));

    public ValueTask<IEnumerable<Mandant>> GetAll() =>
        ValueTask.FromResult(stammDbContext.Mandants.AsEnumerable());

    public void Update(Mandant newMandant)
    {
        var mandant = stammDbContext.Mandants.SingleOrDefault(m => m.Mnr == newMandant.Mnr);
        
        if(mandant is null) return;
        
        stammDbContext.Mandants.Where(m => m.Mnr == newMandant.Mnr)
            .ExecuteUpdate(propertyCalls => propertyCalls
                .SetProperty(mOld => mOld.Mandantenliste, newMandant.Mandantenliste)
                .SetProperty(mOld => mOld.Name, newMandant.Name)
                .SetProperty(mOld => mOld.Branche, newMandant.Branche)
                .SetProperty(mOld => mOld.Straße, newMandant.Straße)
                .SetProperty(mOld => mOld.Lkz, newMandant.Lkz)
                .SetProperty(mOld => mOld.Plz, newMandant.Plz)
                .SetProperty(mOld => mOld.Ort, newMandant.Ort)
                .SetProperty(mOld => mOld.Mnr, newMandant.Mnr)
                .SetProperty(mOld => mOld.Datenpfad, newMandant.Datenpfad)
                .SetProperty(mOld => mOld.FibuJaNein, newMandant.FibuJaNein)
                .SetProperty(mOld => mOld.Lagerstellen, newMandant.Lagerstellen)
                .SetProperty(mOld => mOld.LagerBez, newMandant.LagerBez)
                .SetProperty(mOld => mOld.Nko, newMandant.Nko)
                .SetProperty(mOld => mOld.Gdistd1, newMandant.Gdistd1)
                .SetProperty(mOld => mOld.Gdistd2, newMandant.Gdistd2)
                .SetProperty(mOld => mOld.Gdibrutto, newMandant.Gdibrutto)
                .SetProperty(mOld => mOld.BackColor, newMandant.BackColor)
                .SetProperty(mOld => mOld.Sqldbname, newMandant.Sqldbname)
                .SetProperty(mOld => mOld.Sqldbpfad, newMandant.Sqldbpfad)
                .SetProperty(mOld => mOld.Sqluser, newMandant.Sqluser)
                .SetProperty(mOld => mOld.Ilnnr, newMandant.Ilnnr)
                .SetProperty(mOld => mOld.Nkokg, newMandant.Nkokg)
            );
    }
}