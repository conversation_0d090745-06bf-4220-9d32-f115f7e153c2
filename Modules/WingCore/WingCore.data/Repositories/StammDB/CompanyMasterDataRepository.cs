using WingCore.domain.Models.StammDbModels;
using Microsoft.EntityFrameworkCore;
using WingCore.application.Contract.IModels.StammDb;
using WingCore.data.StammContext;

namespace WingCore.data.Repositories.StammDB;

public class CompanyMasterDataRepository(StammDbContext stammDbContext)
    : ICompanyMasterDataRepository
{
    public async ValueTask<CompanyMasterData?> GetAsync(string mandantNr) => await stammDbContext.FirmenStamms.AsNoTracking().Where(company => company.Mnr == mandantNr).FirstOrDefaultAsync();

    public async void SetOrUpdate(CompanyMasterData newCompanyMasterData)
    {
        if (string.IsNullOrEmpty(newCompanyMasterData.Mnr)  || newCompanyMasterData.Mnr == "0")
            return;
        
        var oldFirst = await GetAsync(newCompanyMasterData.Mnr);
        if (oldFirst is null)
        {
            await stammDbContext.Database.ExecuteSqlRawAsync($"INSERT INTO FirmenStamm (Mnr) VALUES ('{newCompanyMasterData.Mnr}')");
            
            oldFirst = await GetAsync(newCompanyMasterData.Mnr);
        }

        if (oldFirst is null)
            return;
        
        stammDbContext.FirmenStamms.Where(oldCompany => oldCompany.Mnr == newCompanyMasterData.Mnr)
            .ExecuteUpdate(propertyCalls => propertyCalls
                .SetProperty(oldCompany => oldCompany.FName1, newCompanyMasterData.FName1)
                .SetProperty(oldCompany => oldCompany.FName2, newCompanyMasterData.FName2)
                .SetProperty(oldCompany => oldCompany.FName3, newCompanyMasterData.FName3)
                .SetProperty(oldCompany => oldCompany.FName4, newCompanyMasterData.FName4)
                .SetProperty(oldCompany => oldCompany.FStrasse, newCompanyMasterData.FStrasse)
                .SetProperty(oldCompany => oldCompany.Fplz, newCompanyMasterData.Fplz)
                .SetProperty(oldCompany => oldCompany.FOrt, newCompanyMasterData.FOrt)
                .SetProperty(oldCompany => oldCompany.FTelefon, newCompanyMasterData.FTelefon)
                .SetProperty(oldCompany => oldCompany.FTelefax, newCompanyMasterData.FTelefax)
                .SetProperty(oldCompany => oldCompany.FEmail, newCompanyMasterData.FEmail)
                .SetProperty(oldCompany => oldCompany.FInternet, newCompanyMasterData.FInternet)
                .SetProperty(oldCompany => oldCompany.FIdentNr, newCompanyMasterData.FIdentNr)
                .SetProperty(oldCompany => oldCompany.FBundesland, newCompanyMasterData.FBundesland)
                .SetProperty(oldCompany => oldCompany.FkdNrVon, newCompanyMasterData.FkdNrVon)
                .SetProperty(oldCompany => oldCompany.FkdNrBis, newCompanyMasterData.FkdNrBis)
                .SetProperty(oldCompany => oldCompany.FlfNrVon, newCompanyMasterData.FlfNrVon)
                .SetProperty(oldCompany => oldCompany.FlfNrBis, newCompanyMasterData.FlfNrBis)
                .SetProperty(oldCompany => oldCompany.FFibuPfad, newCompanyMasterData.FFibuPfad)
                .SetProperty(oldCompany => oldCompany.FFibuLw, newCompanyMasterData.FFibuLw)
                .SetProperty(oldCompany => oldCompany.FDruckerAnz, newCompanyMasterData.FDruckerAnz)
                .SetProperty(oldCompany => oldCompany.Fnko, newCompanyMasterData.Fnko)
                .SetProperty(oldCompany => oldCompany.Registriert, newCompanyMasterData.Registriert)
                .SetProperty(oldCompany => oldCompany.Fkunr, newCompanyMasterData.Fkunr)
                .SetProperty(oldCompany => oldCompany.IlnNummer, newCompanyMasterData.IlnNummer)
            );
           
        //stammDbContext.Entry((object)oldFirst).CurrentValues.SetValues(newCompanyMasterData);
    }
}