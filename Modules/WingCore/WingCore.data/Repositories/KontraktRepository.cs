using Microsoft.EntityFrameworkCore;
using WingCore.domain.Models;
using WingCore.application.Contract.IModels;
using WingCore.data.WaWiContext;

namespace WingCore.data.Repositories;

public class KontraktRepository(RepositoryContext repositoryContext)
    : RepositoryBase<Kontrakt>(repositoryContext), IKontraktRepository
{
    public async Task<IEnumerable<Kontrakt>> GetKontraktByNumber(int kontraktNumber, bool trackChanges)
    {
        return await FindByCondition(c => c.Ktnr == kontraktNumber, trackChanges)
            .ToListAsync();
    }
    
    public IEnumerable<Kontrakt> GetKontrakteByKundeId(long kundeId, bool trackChanges)
    {
        return FindByCondition(c => c.KtliefNr == kundeId, trackChanges)
            .Where(c => c.Ktek == false)
            .Where(c => c.Kterledigt == false)
            .ToList();
    }

    public void ChangeKontraktAmount(Kontrakt kontrakt, int amount)
    {
        kontrakt.KtabgerMenge += amount;
        kontrakt.KtrestMenge -= amount;
        Update(kontrakt);
        RepositoryContext.SaveChanges();
    }
}