using WingCore.domain.Models;
using WingCore.application.Contract.IModels;
using WingCore.data.WaWiContext;
using WingCore.domain.Common;

namespace WingCore.data.Repositories;

public class IncomingInvoicePostingsRepository(RepositoryContext repositoryContext)
    : RepositoryBase<Erhauptdatei>(repositoryContext), IIncomingInvoicePostingsRepository
{
    public IEnumerable<Erhauptdatei> GetAllInvoicePostingsFromInvoice(bool trackChanges,
        long invoiceNumber,
        PostingsSchema? schema)
        => FindAll(trackChanges)
            .Where(p => p.Renummer == invoiceNumber && 
                        (schema == null || p.ErfSchema == schema));
}