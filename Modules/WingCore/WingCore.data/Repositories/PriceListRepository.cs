using Microsoft.EntityFrameworkCore;
using WingCore.domain.Models;
using WingCore.data.WaWiContext;
using WingCore.application.PriceList.IModels;

namespace WingCore.data.Repositories;

public class PriceListRepository(RepositoryContext repositoryContext)
    : RepositoryBase<Preislisten>(repositoryContext), IPriceListRepository
{
    public async Task<IEnumerable<Preislisten>> GetAllPriceLists(bool trackChanges)
    {
        return await FindAll(trackChanges).OrderByDescending(preislisten => preislisten.SysTime).ToListAsync();
    }

    public async Task<Preislisten?> GetPriceList(bool trackChanges, long id)
    {
        return await FindByCondition(p => p.Id == id, trackChanges).FirstOrDefaultAsync();
    }
    
    public async Task<Preislisten> AddPriceList(Preislisten preislisten)
    {
        Create(preislisten);
        await SaveChangesAsync();
        
        repositoryContext.Entry(preislisten).State = EntityState.Detached;

        return preislisten;
    }
    
    public async Task UpdatePriceList(Preislisten preislisten)
    {
        Update(preislisten);
        await SaveChangesAsync();
    }
    
    public async Task DeletePriceList(Preislisten preislisten)
    {
        Delete(preislisten);
        await SaveChangesAsync();
    }
}