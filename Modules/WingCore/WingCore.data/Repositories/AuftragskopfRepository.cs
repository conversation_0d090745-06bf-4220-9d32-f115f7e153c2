using WingCore.domain.Models;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using WingCore.application.Contract.IModels;
using WingCore.data.Repositories.Mapper;
using WingCore.data.WaWiContext;

namespace WingCore.data.Repositories;

public class AuftragskopfRepository(RepositoryContext repositoryContext)
    : RepositoryBase<Auftragskopf>(repositoryContext), IAuftragskopfRepository
{
    public async Task WriteOrderToDb(bool trackChanges, Auftragskopf order)
    {
        if (trackChanges)
        {
            Update(order);
        }
        else
        {
            Create(order);
        }

        await SaveChangesAsync();
    }

    public async Task<IEnumerable<Auftragskopf>> GetAll(bool trackChanges) => await FindAll(trackChanges).ToListAsync();
    
    public List<long?> GetAllAuftragNummer()
    {
        return FindAll(false)
            .Select(x => x.Auftragsnummer)
            .ToList();
    }
    

    public async Task<IEnumerable<Auftragskopf>?> GetAuftragskopfByOrderNumber(long orderId, bool trackChanges)
    {
        return await FindByCondition(c => c.Auftragsnummer.Equals(orderId), trackChanges)
            .ToListAsync();
    }

    public Task<ICollection<Auftragskopf>> GetAuftragskopfByOrderNumberList(List<long> orderId)
    {
        if (!orderId.Any())
            return Task.FromResult<ICollection<Auftragskopf>>(new List<Auftragskopf>());

        var result = RepositoryContext.Set<Auftragskopf>()
            .Where(a => a.Auftragsnummer.HasValue && orderId.Contains(a.Auftragsnummer.Value))
            .AsNoTracking()
            .ToList();

        return Task.FromResult<ICollection<Auftragskopf>>(result);
    }

    public IEnumerable<Auftragskopf> GetAuftragskopfsByNumbers(List<long> orderId, bool trackChanges)
    {
        var orderIdsString = string.Join(",", orderId);

        var query = $@"
        SELECT * 
        FROM Auftragskopf
        WHERE Auftragsnummer IN ({orderIdsString})
    ";

        if (!trackChanges)
        {
            return RepositoryContext.Auftragskopfs.FromSqlRaw(query).AsNoTracking().ToList();
        }
        else
        {
            return RepositoryContext.Auftragskopfs.FromSqlRaw(query).ToList();
        }
    }

    public Auftragskopf? GetOverInvoiceNumberForEdifact(long invoiceNumber)
    {
        return RepositoryContext.Auftragskopfs
                            .AsNoTracking()
                            .Where(a => a.Arvnummer == invoiceNumber)
                            .MapToEdifactAuftragskopf()
                            .FirstOrDefault();
    }

    public async Task UpdateWithoutTracking(Auftragskopf auftragskopf, string userName, CancellationToken token = default)
    {
        var sql = """
                  UPDATE Auftragskopf 
                  SET LoeschKng = @LoeschKng, 
                      Lieferschein = @Lieferschein, 
                      Auftrag = @Auftrag
                  WHERE Auftragsnummer = @Auftragsnummer
                  """;

        await RepositoryContext.Database.ExecuteSqlRawAsync(
            sql, [
                new SqlParameter("@LoeschKng", auftragskopf.LoeschKng),
                new SqlParameter("@Lieferschein", auftragskopf.Lieferschein),
                new SqlParameter("@Auftrag", auftragskopf.Auftrag),
                new SqlParameter("@Auftragsnummer", auftragskopf.Auftragsnummer),
            ],
            cancellationToken: token);

    }

    public Task SetSend(List<long> sendOrderheaderList)
    {
        if (!sendOrderheaderList.Any())
            return Task.CompletedTask;

        var orderIdsString = string.Join(",", sendOrderheaderList);
        var sql = $"UPDATE Auftragskopf SET Send = 1 WHERE Auftragsnummer IN ({orderIdsString})";

        return RepositoryContext.Database.ExecuteSqlRawAsync(sql);
    }

    public async Task DisableInsertTrigger()
    {
        await RepositoryContext.Database.ExecuteSqlRawAsync(
            "DISABLE TRIGGER trg_Auftragskopf_Insert ON Auftragskopf;");

    }

    public async Task EnableInsertTrigger()
    {
        await RepositoryContext.Database.ExecuteSqlRawAsync(
            "ENABLE TRIGGER trg_Auftragskopf_Insert ON Auftragskopf;");

    }


    public async Task SetLockToCreateNumberAsync(string userName)
    {
        while (RepositoryContext.AuftragsSps.Any())
            await Task.Delay(50);
        
        RepositoryContext.Database.ExecuteSqlRaw("INSERT INTO AuftragsSP (LockStart, LockUser) VALUES (@LockStart, @LockUser)",
                                                    new SqlParameter("@LockStart", DateTime.Now.ToString("yyyy-MM-dd")),
                                                    new SqlParameter("@LockUser", userName));
        SaveChanges();
    }
    
    public void SetUnLockToCreateNumber()
    {
        if(!RepositoryContext.AuftragsSps.Any())
            return;

        RepositoryContext.Database.ExecuteSqlRaw("DELETE FROM AuftragsSP");
        
        SaveChanges();
    }
    
    public long? GetMaxAuftragsnummer()
    {
        return RepositoryContext.Auftragskopfs.Max(c => c.Auftragsnummer);
    }

    public Auftragskopf? GetOverBestellNumber(string number, bool trackChanges)  => FindByCondition(c => !string.IsNullOrEmpty(c.Bestellnr) && c.Bestellnr.Equals(number), trackChanges).FirstOrDefault();
}