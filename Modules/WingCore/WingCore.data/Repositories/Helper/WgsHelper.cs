namespace WingCore.data.Repositories.Helper;

public class WgsHelper
{
    public static void SetDefaults<T>(T obj)
    {
        if (obj == null) return;

        // Get all the properties of the object
        var properties = obj.GetType().GetProperties();

        foreach (var property in properties)
        {
            // Check if the property is writable
            if (!property.CanWrite) continue;

            var propertyType = property.PropertyType;

            // Handle nullable types
            var underlyingType = Nullable.GetUnderlyingType(propertyType) ?? propertyType;

            // Check the property type and set default value
            if (underlyingType == typeof(int) || underlyingType == typeof(long) || 
                underlyingType == typeof(float) || underlyingType == typeof(double) || 
                underlyingType == typeof(decimal))
            {
                property.SetValue(obj, 0); // Default numeric value to 0
            }
            else if (underlyingType == typeof(string))
            {
                property.SetValue(obj, string.Empty); // Default string to ""
            }
            else if (underlyingType.IsClass)
            {
                // Recursively set defaults for nested objects
                var nestedObject = property.GetValue(obj);
                if (nestedObject == null)
                {
                    // Initialize nested object if null
                    nestedObject = Activator.CreateInstance(propertyType);
                    property.SetValue(obj, nestedObject);
                }
                SetDefaults(nestedObject);
            }
            else if (underlyingType.IsGenericType && underlyingType.GetGenericTypeDefinition() == typeof(IEnumerable<>))
            {
                // Skip IEnumerable properties
                continue;
            }
        }
    }
}