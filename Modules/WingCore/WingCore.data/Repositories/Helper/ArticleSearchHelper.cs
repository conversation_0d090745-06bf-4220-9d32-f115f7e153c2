using WingCore.domain.Models;
using Microsoft.EntityFrameworkCore;
using WingCore.application.Contract.IModels.Helper;

namespace WingCore.data.Repositories.Helper;

public static class ArticleSearchHelper
{
    public static IQueryable<Artikel> SetSearchParam(this DbSet<Artikel> query, ArticleSearchParam searchParam)
    {
        var searchOverLastChange = searchParam is { LastChange: not null };
        
        IQueryable<Artikel> tmpQuery = query;
            
        if (searchParam is { Number: not null, Number: > 0 })
        {
            tmpQuery = tmpQuery.Where(a => a.Artikelnr == searchParam.Number);
        }
        
        if (!string.IsNullOrEmpty(searchParam.Name))
        {
            tmpQuery = tmpQuery.Where(a => a.ArtBezText1 != null && a.ArtBezText1.ToUpper().Contains(searchParam.Name.ToUpper()));
        }
        
        if (!string.IsNullOrEmpty(searchParam.Ean))
        {
            tmpQuery = tmpQuery.Where(a => a.ArtEan == searchParam.Ean);
        }
        
        if (searchParam.MainArticleNumber is not null)
        {
            tmpQuery = tmpQuery.Where(a => a.Hartikel == searchParam.MainArticleNumber);
        }
        
        if (searchParam.ArticleNumbers is not null &&
            searchParam.ArticleNumbers.Count > 0)
        {
            tmpQuery = tmpQuery.Where(a => searchParam.ArticleNumbers.Distinct().Contains(a.Artikelnr));
        }
        
        if (searchParam.LastChange.HasValue)
        {
            tmpQuery = tmpQuery.Where(a =>
                a.SysTime.HasValue &&
                a.SysTime.Value.Date >= searchParam.LastChange.Value.Date);
        }
        
        return tmpQuery;
        
//         if (searchOverArticleNumbers && searchParam.ArticleNumbers!.Count > 0)
//         {
//             var articleNumbers = string.Join(",", searchParam.ArticleNumbers.Distinct());
//             sqlQuery = $"""
//                           SELECT * 
//                              FROM Artikel
//                              WHERE Artikelnr IN ({articleNumbers})
//                           """;
//         }
//
//         
//         return query
//             .FromSqlRaw(sqlQuery)
//             .Where(a => 
//             (!searchOverNumber || a.Artikelnr == searchParam.Number) &&
//             (!searchOverKdname1 || a.ArtBezText1!.ToUpperInvariant().Contains(searchParam.Name!.ToUpperInvariant())) &&
//             (!searchOverEan || a.ArtEan == searchParam.Ean) && 
//             (!searchOverMainArticleNumber || a.Hartikel == searchParam.MainArticleNumber) 
//         );
    }
}