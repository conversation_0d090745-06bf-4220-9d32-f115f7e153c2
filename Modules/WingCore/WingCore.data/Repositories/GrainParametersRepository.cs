using Microsoft.EntityFrameworkCore;
using WingCore.domain.Models;
using WingCore.application.Contract.IModels;
using WingCore.data.WaWiContext;
using WingCore.data.Repositories.Helper;

namespace WingCore.data.Repositories
{
    public class GrainParametersRepository(RepositoryContext repositoryContext)
        : RepositoryBase<GrainParameter>(repositoryContext), IGrainParametersRepository
    {
        public async Task<List<GrainParameter>> GetAllGrainParameters()
        {
            // Assuming FindAll(true) returns IQueryable<GrainParameter>
            return await FindAll(true).ToListAsync();
        }

        public void Add(GrainParameter grainParameter)
        {
            Create(grainParameter);
            SaveChanges();
        }

        public void Remove(GrainParameter parameter)
        {
            Delete(parameter);
            SaveChanges();
        }
        
        public void UpdateParameters(GrainParameter parameter)
        {
            Update(parameter);
            SaveChanges();
        }
    }
}