using WingCore.domain.Models;
using WingCore.application.Contract.IModels;
using WingCore.data.WaWiContext;
using WingCore.domain.Common;

namespace WingCore.data.Repositories;

public class GrainSettlementInvoicePostingsRepository(RepositoryContext repositoryContext)
    : RepositoryBase<Gbvhpt>(repositoryContext), IGrainSettlementInvoicePostingsRepository
{
    public IEnumerable<Gbvhpt> GetAllInvoicePostingsFromInvoice(bool trackChanges,
                                                                long invoiceNumber,
                                                                PostingsSchema? schema)
        => FindAll(trackChanges)
            .Where(p => p.Gbnr == invoiceNumber && 
                        (schema == null || p.ErfSchema == schema));
}