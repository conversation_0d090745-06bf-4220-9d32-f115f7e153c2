using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace WingCore.data.WaWiContext.Migrations
{
    /// <inheritdoc />
    public partial class AddGrainParameters : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "GrainParameters",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    MaskNr = table.Column<long>(type: "bigint", nullable: false),
                    FixedType = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    Description = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    Unit = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    BaseType = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    BaseValueFrom = table.Column<double>(type: "float", nullable: false),
                    BaseValueTo = table.Column<double>(type: "float", nullable: false),
                    ModifierValueStart = table.Column<double>(type: "float", nullable: false),
                    ModifierValueStep = table.Column<double>(type: "float", nullable: false),
                    Factor = table.Column<double>(type: "float", nullable: false, defaultValue: 0.10000000000000001),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: true, defaultValueSql: "getdate()")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_GrainParameters", x => x.Id);
                });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "GrainParameters");
        }
    }
}
