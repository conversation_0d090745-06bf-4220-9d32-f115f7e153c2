using Commons.ComboBoxs;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using WingCore.domain.Common;
using WingCore.domain.Common.Configurations;
using WingCore.domain.Models;

namespace WingCore.data;

internal class PostingsSchemaConverter()
    : ValueConverter<PostingsSchema, string>(s => s.Value, c => new PostingsSchema(c));


internal class ConfigurationTypeConverter()
    : ValueConverter<ConfigurationType, string>(s => s.Value, c => new ConfigurationType(c));

internal class ComboboxTypeConverter()
    : ValueConverter<ComboboxType, string>(s => s.Value, c => new ComboboxType(c));

internal class NumberSetTypeConverter()
    : ValueConverter<NumberSetType, string>(s => s.Value, c => new NumberSetType(c));
