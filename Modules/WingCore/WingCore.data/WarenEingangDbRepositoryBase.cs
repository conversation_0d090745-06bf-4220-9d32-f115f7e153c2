using System.Linq.Expressions;
using Microsoft.EntityFrameworkCore;
using WingCore.application.Contract;
using WingCore.data.WarenEingangContext;

namespace WingCore.data;

public abstract class WarenEingangDbRepositoryBase<T> : IRepositoryBas<T> where T : class
{
    protected WarenEingangDBContext RepositoryContext; 
    
    public WarenEingangDbRepositoryBase(WarenEingangDBContext repositoryContext)
        => RepositoryContext = repositoryContext;
    public IQueryable<T> FindAll(bool trackChanges) =>
        !trackChanges
            ? RepositoryContext.Set<T>()
                .AsNoTracking()
            : RepositoryContext.Set<T>();

    public IQueryable<T> FindByCondition(Expression<Func<T, bool>> expression, bool trackChanges) =>
        !trackChanges
            ? RepositoryContext.Set<T>()
                .Where(expression)
                .AsNoTracking()
            : RepositoryContext.Set<T>()
                .Where(expression);

    public void Create(T entity)
    {
        throw new NotImplementedException();
    }

    public void Update(T entity)
    {
        throw new NotImplementedException();
    }

    public void Delete(T entity)
    {
        throw new NotImplementedException();
    }

    public Task SaveChangesAsync()
    {
        throw new NotImplementedException();
    }

    public int SaveChanges()
    {
        throw new NotImplementedException();
    }
}

