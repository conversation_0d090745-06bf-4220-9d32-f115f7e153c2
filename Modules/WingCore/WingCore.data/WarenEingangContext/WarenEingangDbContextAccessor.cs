using System.Xml;
using Microsoft.Data.SqlClient;
using ObjectDeAndSerialize;
using WingCore.application;
using WingCore.application.Contract;
using WingCore.application.Contract.IModels;

namespace WingCore.data.WarenEingangContext;

public class WarenEingangDbContextAccessor(IApplicationPath applicationPath,
                                               ILoggerManager logger) : IWarenEingangDbContextAccessor
{
    private string ConnectionString { get; set; } = string.Empty;
    private string WorkingMandantNumber { get; set; } = string.Empty;
    private string WorkingConfigFileDirectory { get; set; } = applicationPath.GetStammDbPath();
    public string GetConnectionString()
    {
        LoadConnectionStringFromFileAndSet();
        return ConnectionString;
    }

    public (bool,string) TestConnection()
    {
        try
        {
            var testConnection = new SqlConnection(GetConnectionString());
            testConnection.Open();
            return (true,"");
        }
        catch (Exception e)
        {
            logger.LogError(e.Message);
            return (false,e.Message);
        }
    }

    public string GetWorkingMandant()
    {
        if(string.IsNullOrWhiteSpace(WorkingMandantNumber))
            LoadWorkingMandantFromFileAndSet();
        return WorkingMandantNumber;
    }

    public void SetConnectionString(string dbPath, string dbName, string dbUserName, string dbPassword)
    {
        ConnectionString =
            $"Data Source={dbPath};Initial Catalog={dbName};TrustServerCertificate=True;User={dbUserName};Password={dbPassword}";
    }

    public ValueTask<IDbParameter?> LoadConnectionObjectFromFile()
    {
        var configFileDirectory = GetDbConnectionDataFilePath();

        if (!File.Exists(configFileDirectory))
        {
            ConnectionString = string.Empty;

            return ValueTask.FromResult<IDbParameter?>(null);
        }

        var xmlDocument = new XmlDocument();
        xmlDocument.Load(configFileDirectory);

        return ValueTask.FromResult<IDbParameter?>(xmlDocument.DeserializeObject<DbParameter>());
    }

    public async Task SaveDbConnectionData(IDbParameter dbParameter)
    {
        WorkingMandantNumber = dbParameter.MandantNumber;
        if (!dbParameter.IsValid(true))
            return;

        var xmlDocument = dbParameter.SerializeToXml();
        await File.WriteAllTextAsync(GetDbConnectionDataFilePath(), xmlDocument);
    }
    
    private async void LoadConnectionStringFromFileAndSet()
    {
        var param = await LoadConnectionObjectFromFile();

        if (param is null) return;

        SetConnectionString(param.DbPath, param.DbName, param.DbUsername, param.DbPassword);
    }

    private async void LoadWorkingMandantFromFileAndSet()
    {
        var param = await LoadConnectionObjectFromFile();

        if (param is null) return;

        WorkingMandantNumber = param.MandantNumber;
    }
    
    private string GetDbConnectionDataFilePath()
    {
        var strConfigPath = WorkingConfigFileDirectory;

        if (string.IsNullOrEmpty(strConfigPath))
            return string.Empty;
        
        try
        {
            if (!Directory.Exists(strConfigPath))
            {
                Directory.CreateDirectory(strConfigPath);
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine(ex.Message);
        }
        

        strConfigPath = Path.Combine(strConfigPath, "FW71SQLConfigData");

        return strConfigPath;
    }
}