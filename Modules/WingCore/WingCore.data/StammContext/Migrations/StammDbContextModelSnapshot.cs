// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using WingCore.data.StammContext;

#nullable disable

namespace WingCore.data.StammContext
{
    [DbContext(typeof(StammDbContext))]
    partial class StammDbContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.1")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRole", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ConcurrencyStamp")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("NormalizedName")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("Roles");
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<string>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("ClaimType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RoleId")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("RoleClaims");
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<string>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("ClaimType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("UserId")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("UserClaims");
                });

            modelBuilder.Entity("WingCore.domain.ApplicationUser", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<int>("AccessFailedCount")
                        .HasColumnType("int");

                    b.Property<string>("ConcurrencyStamp")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Email")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("EmailConfirmed")
                        .HasColumnType("bit");

                    b.Property<bool>("LockoutEnabled")
                        .HasColumnType("bit");

                    b.Property<DateTimeOffset?>("LockoutEnd")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("NormalizedEmail")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("NormalizedUserName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PasswordHash")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("PhoneNumberConfirmed")
                        .HasColumnType("bit");

                    b.Property<string>("SecurityStamp")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("TwoFactorEnabled")
                        .HasColumnType("bit");

                    b.Property<string>("UserName")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("Users");
                });

            modelBuilder.Entity("WingCore.domain.ApplicationUserLogin", b =>
                {
                    b.Property<string>("UserId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("LoginProvider")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ProviderDisplayName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ProviderKey")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("UserId");

                    b.ToTable("UserLogins");
                });

            modelBuilder.Entity("WingCore.domain.ApplicationUserRole", b =>
                {
                    b.Property<string>("UserId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("RoleId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("UserId");

                    b.ToTable("UserRoles");
                });

            modelBuilder.Entity("WingCore.domain.ApplicationUserToken", b =>
                {
                    b.Property<string>("UserId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("LoginProvider")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Value")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("UserId", "LoginProvider", "Name");

                    b.ToTable("UserTokens");
                });

            modelBuilder.Entity("WingCore.domain.Models.StammDbModels.AktMand", b =>
                {
                    b.Property<string>("AktDatenpfad")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("AktMandName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("AktMandNr")
                        .HasColumnType("nvarchar(max)");

                    b.ToTable("AktMand");
                });

            modelBuilder.Entity("WingCore.domain.Models.StammDbModels.ApiKey", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasColumnName("CreatedAt")
                        .HasDefaultValueSql("GETDATE()");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("Description");

                    b.Property<string>("Endpoint")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("Endpoint");

                    b.Property<string>("IpAddress")
                        .HasMaxLength(45)
                        .HasColumnType("nvarchar(45)")
                        .HasColumnName("IpAdress");

                    b.Property<string>("Key")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Key");

                    b.Property<DateTime?>("LastUsingAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("LastUsingAt");

                    b.Property<string>("Mandanten")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("Mandanten");

                    b.HasKey("Id")
                        .HasName("ID");

                    b.ToTable("ApiKey", (string)null);
                });

            modelBuilder.Entity("WingCore.domain.Models.StammDbModels.Blz", b =>
                {
                    b.Property<string>("Blz1")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("BLZ");

                    b.Property<string>("Bname")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("BName");

                    b.Property<string>("Bort")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("BOrt");

                    b.Property<string>("Bplz")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("BPlz");

                    b.Property<string>("Kennummer")
                        .HasColumnType("nvarchar(max)");

                    b.ToTable("Blz");
                });

            modelBuilder.Entity("WingCore.domain.Models.StammDbModels.BundLand", b =>
                {
                    b.Property<string>("BlName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BlNr")
                        .HasColumnType("nvarchar(max)");

                    b.ToTable("BundLand");
                });

            modelBuilder.Entity("WingCore.domain.Models.StammDbModels.CompanyMasterData", b =>
                {
                    b.Property<string>("FBundesland")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("FBundesland");

                    b.Property<string>("FDruckerAnz")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("FDruckerAnz");

                    b.Property<string>("FEmail")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("FEmail");

                    b.Property<string>("FFibuLw")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("FFibuLW");

                    b.Property<string>("FFibuPfad")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("FFibuPfad");

                    b.Property<string>("FIdentNr")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("FIdentNr");

                    b.Property<string>("FInternet")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("FInternet");

                    b.Property<string>("FName1")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("FName1");

                    b.Property<string>("FName2")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("FName2");

                    b.Property<string>("FName3")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("FName3");

                    b.Property<string>("FName4")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("FName4");

                    b.Property<string>("FOrt")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("FOrt");

                    b.Property<string>("FStrasse")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("FStrasse");

                    b.Property<string>("FTelefax")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("FTelefax");

                    b.Property<string>("FTelefon")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("FTelefon");

                    b.Property<string>("FkdNrBis")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("FKDNrBis");

                    b.Property<string>("FkdNrVon")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("FKDNrVon");

                    b.Property<string>("Fkunr")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("FKUNR");

                    b.Property<string>("FlfNrBis")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("FLFNrBis");

                    b.Property<string>("FlfNrVon")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("FLFNrVon");

                    b.Property<string>("Fnko")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("FNKO");

                    b.Property<string>("Fplz")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("FPLZ");

                    b.Property<string>("IlnNummer")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ILNNummer");

                    b.Property<string>("Mnr")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("MNr");

                    b.Property<string>("Registriert")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Registriert");

                    b.ToTable("FirmenStamm", (string)null);
                });

            modelBuilder.Entity("WingCore.domain.Models.StammDbModels.ConfigForAllMandant", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("ID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<byte[]>("ConfigData")
                        .HasColumnType("varbinary(max)");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id")
                        .HasName("Config$ID");

                    b.ToTable("Config", (string)null);
                });

            modelBuilder.Entity("WingCore.domain.Models.StammDbModels.Deutschland", b =>
                {
                    b.Property<string>("Bundesland")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ID");

                    b.Property<string>("Kng")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Ort")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Plz")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Vorwahl")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Zusatz")
                        .HasColumnType("nvarchar(max)");

                    b.ToTable("Deutschland");
                });

            modelBuilder.Entity("WingCore.domain.Models.StammDbModels.Dimport", b =>
                {
                    b.Property<string>("DimportDevice")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("DImportDevice");

                    b.Property<string>("DimportPath")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("DImportPath");

                    b.ToTable("DImport");
                });

            modelBuilder.Entity("WingCore.domain.Models.StammDbModels.Faktor", b =>
                {
                    b.Property<string>("FaktNr")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FaktText")
                        .HasColumnType("nvarchar(max)");

                    b.ToTable("Faktor");
                });

            modelBuilder.Entity("WingCore.domain.Models.StammDbModels.Getreideart", b =>
                {
                    b.Property<string>("Art")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Nr")
                        .HasColumnType("nvarchar(max)");

                    b.ToTable("Getreideart");
                });

            modelBuilder.Entity("WingCore.domain.Models.StammDbModels.Kfzkennzeichen", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ID");

                    b.Property<string>("Kennzeichen")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Nuts2code")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("NUTS2Code");

                    b.Property<string>("Nuts2name")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("NUTS2Name");

                    b.ToTable("KFZKennzeichen");
                });

            modelBuilder.Entity("WingCore.domain.Models.StammDbModels.LandKennung", b =>
                {
                    b.Property<string>("Auslandskennung")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ID");

                    b.Property<string>("IntraKennung")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Kfzkennz")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("KFZKennz");

                    b.Property<string>("Landesbezeichnung")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Landeswährung")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Umrechnungsfaktor")
                        .HasColumnType("nvarchar(max)");

                    b.ToTable("Land_Kennung");
                });

            modelBuilder.Entity("WingCore.domain.Models.StammDbModels.Mandant", b =>
                {
                    b.Property<string>("BackColor")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Branche")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Datenpfad")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FibuJaNein")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Gdibrutto")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("GDIBrutto");

                    b.Property<string>("Gdistd1")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("GDIStd1");

                    b.Property<string>("Gdistd2")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("GDIStd2");

                    b.Property<string>("Ilnnr")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ILNNr");

                    b.Property<string>("LagerBez")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Lagerstellen")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Lkz")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Mandantenliste")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Mnr")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("MNr");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Nko")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("NKO");

                    b.Property<string>("Nkokg")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("NKOKg");

                    b.Property<string>("Ort")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Plz")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("PLZ");

                    b.Property<string>("Sqldbname")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("SQLDBName");

                    b.Property<string>("Sqldbpfad")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("SQLDBPfad");

                    b.Property<string>("Sqluser")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("SQLUser");

                    b.Property<string>("Straße")
                        .HasColumnType("nvarchar(max)");

                    b.ToTable("mandant");
                });

            modelBuilder.Entity("WingCore.domain.Models.StammDbModels.MwSt", b =>
                {
                    b.Property<string>("MwSt1")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("MwSt");

                    b.Property<string>("Schlüssel")
                        .HasColumnType("nvarchar(max)");

                    b.ToTable("MwSt");
                });

            modelBuilder.Entity("WingCore.domain.Models.StammDbModels.Optionen", b =>
                {
                    b.Property<string>("Absender")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("AlsanzAusdr")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ALSAnzAusdr");

                    b.Property<string>("Alslagerakt")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ALSLagerakt");

                    b.Property<string>("Alsnr")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ALSNr");

                    b.Property<string>("AlsoptAuftr")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ALSOptAuftr");

                    b.Property<string>("AlsoptLiefer")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ALSOptLiefer");

                    b.Property<string>("AutoBuch")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("EndText")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Fuß")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Idx")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("idx");

                    b.ToTable("Optionen");
                });

            modelBuilder.Entity("WingCore.domain.Models.StammDbModels.PlzOrt", b =>
                {
                    b.Property<string>("Feld5")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Kennummer")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Ort")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Plz")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("PLZ");

                    b.Property<string>("Vorwahl")
                        .HasColumnType("nvarchar(max)");

                    b.ToTable("PlzOrt");
                });

            modelBuilder.Entity("WingCore.domain.Models.StammDbModels.Sicherung", b =>
                {
                    b.Property<string>("Datum")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MandantNam")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MandantNr")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Sicdir")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Sicdrv")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Sicherungspfad")
                        .HasColumnType("nvarchar(max)");

                    b.ToTable("sicherung");
                });

            modelBuilder.Entity("WingCore.domain.Models.StammDbModels.Steuer", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ID");

                    b.Property<string>("KontoAnzVerkauf")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("KontoAnzVerkaufDritt")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("KontoAnzVerkaufEg")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("KontoAnzVerkaufEG");

                    b.Property<string>("KontoDrek")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("KontoDREK");

                    b.Property<string>("KontoDrvk")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("KontoDRVK");

                    b.Property<string>("KontoEgek")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("KontoEGEK");

                    b.Property<string>("KontoEgvk")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("KontoEGVK");

                    b.Property<string>("KontoEk")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("KontoEK");

                    b.Property<string>("KontoVk")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("KontoVK");

                    b.Property<string>("MwSt")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Schlüssel")
                        .HasColumnType("nvarchar(max)");

                    b.ToTable("Steuer");
                });

            modelBuilder.Entity("WingCore.domain.Models.StammDbModels.Treibhausgasemissionen", b =>
                {
                    b.Property<string>("ArtBez")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ArtNr")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ID");

                    b.Property<string>("Nuts2code")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("NUTS2Code");

                    b.Property<string>("Nuts2gebiet")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("NUTS2Gebiet");

                    b.Property<string>("Wert")
                        .HasColumnType("nvarchar(max)");

                    b.ToTable("Treibhausgasemissionen");
                });

            modelBuilder.Entity("WingCore.domain.Models.StammDbModels.TriggerLog", b =>
                {
                    b.Property<int>("LogId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("LogID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("LogId"));

                    b.Property<string>("Action")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<bool?>("Done")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<int?>("EntryId")
                        .HasColumnType("int")
                        .HasColumnName("EntryID");

                    b.Property<string>("TableAffected")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime?>("Timestamp")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime")
                        .HasDefaultValueSql("(getdate())");

                    b.Property<string>("TriggerName")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.HasKey("LogId")
                        .HasName("PK__TriggerL__5E5499A82E1E7D06");

                    b.ToTable("TriggerLog");
                });

            modelBuilder.Entity("WingCore.domain.Models.StammDbModels.User", b =>
                {
                    b.Property<string>("GeneralUserOption")
                        .HasColumnType("nvarchar(max)");

                    b.ToTable("User");
                });

            modelBuilder.Entity("WingCore.domain.Models.StammDbModels.UserCode", b =>
                {
                    b.Property<string>("UserCode1")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("UserCode");

                    b.Property<string>("UserDatum")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("UserFreigabe")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("UserName")
                        .HasColumnType("nvarchar(max)");

                    b.ToTable("UserCode");
                });

            modelBuilder.Entity("WingCore.domain.Models.StammDbModels.VasToVbf", b =>
                {
                    b.Property<string>("ArtikelBezeichnung")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("Artikel Bezeichnung");

                    b.Property<string>("ArtikelNummer")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("Artikel Nummer");

                    b.Property<string>("ArtikelWGrp")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("Artikel W.-Grp.");

                    b.Property<string>("KommentarIntern")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("Kommentar Intern");

                    b.Property<string>("KommentarLieferschein")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("Kommentar Lieferschein");

                    b.Property<string>("KundenEntladestelle")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("Kunden-Entladestelle");

                    b.Property<string>("KundenName1")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("Kunden-Name 1");

                    b.Property<string>("KundenName2")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("Kunden-Name 2");

                    b.Property<int?>("KundenNr")
                        .HasColumnType("int")
                        .HasColumnName("Kunden-Nr.");

                    b.Property<string>("KundenOrt")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("Kunden-Ort");

                    b.Property<string>("KundenPlz")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)")
                        .HasColumnName("Kunden-PLZ");

                    b.Property<string>("KundenStraße")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("Kunden-Straße");

                    b.Property<DateTime?>("Ladedatum")
                        .HasColumnType("datetime");

                    b.Property<DateTime?>("Lieferdatum")
                        .HasColumnType("datetime");

                    b.Property<double?>("SollmengeKg")
                        .HasColumnType("float")
                        .HasColumnName("Sollmenge kg");

                    b.Property<string>("VasBestellReferenz")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("VAS-Bestell-Referenz");

                    b.ToTable("VAS_to_VBF");
                });

            modelBuilder.Entity("WingCore.domain.Models.StammDbModels.VbfToVa", b =>
                {
                    b.Property<string>("AnhängerKennzeichen")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Anhänger-Kennzeichen");

                    b.Property<string>("ArtikelBezeichnung")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("Artikel Bezeichnung");

                    b.Property<string>("ArtikelNummer")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("Artikel Nummer");

                    b.Property<string>("ArtikelWGrp")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("Artikel W.-Grp.");

                    b.Property<DateTime?>("DatumWägung1")
                        .HasColumnType("datetime")
                        .HasColumnName("Datum Wägung 1");

                    b.Property<DateTime?>("DatumWägung2")
                        .HasColumnType("datetime")
                        .HasColumnName("Datum Wägung 2");

                    b.Property<string>("Entladestelle")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<double?>("GewichtWägung1")
                        .HasColumnType("float")
                        .HasColumnName("Gewicht Wägung 1");

                    b.Property<double?>("GewichtWägung2")
                        .HasColumnType("float")
                        .HasColumnName("Gewicht Wägung 2");

                    b.Property<string>("Kommentar")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("KundenName1")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("Kunden-Name 1");

                    b.Property<string>("KundenName2")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("Kunden-Name 2");

                    b.Property<int?>("KundenNr")
                        .HasColumnType("int")
                        .HasColumnName("Kunden-Nr.");

                    b.Property<string>("KundenOrt")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("Kunden-Ort");

                    b.Property<string>("KundenPlz")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)")
                        .HasColumnName("Kunden-PLZ");

                    b.Property<string>("KundenStraße")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("Kunden-Straße");

                    b.Property<int?>("Lagerstätte1")
                        .HasColumnType("int")
                        .HasColumnName("Lagerstätte 1");

                    b.Property<int?>("Lagerstätte2")
                        .HasColumnType("int")
                        .HasColumnName("Lagerstätte 2");

                    b.Property<int?>("Lagerstätte3")
                        .HasColumnType("int")
                        .HasColumnName("Lagerstätte 3");

                    b.Property<string>("LkwFahrer")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("LKW-Fahrer");

                    b.Property<string>("LkwKennzeichen")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("LKW-Kennzeichen");

                    b.Property<double?>("Menge1Kg")
                        .HasColumnType("float")
                        .HasColumnName("Menge 1 kg");

                    b.Property<double?>("Menge2Kg")
                        .HasColumnType("float")
                        .HasColumnName("Menge 2 kg");

                    b.Property<double?>("Menge3Kg")
                        .HasColumnType("float")
                        .HasColumnName("Menge 3 kg");

                    b.Property<string>("PartieNr")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("Partie-Nr.");

                    b.Property<string>("SpeditionName1")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("Spedition-Name 1");

                    b.Property<string>("SpeditionName2")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("Spedition-Name 2");

                    b.Property<int?>("SpeditionNr")
                        .HasColumnType("int")
                        .HasColumnName("Spedition-Nr.");

                    b.Property<string>("SpeditionOrt")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("Spedition-Ort");

                    b.Property<string>("SpeditionPlz")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)")
                        .HasColumnName("Spedition-PLZ");

                    b.Property<string>("SpeditionStraße")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("Spedition-Straße");

                    b.Property<string>("VasBestellReferenz")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("VAS-Bestell-Referenz");

                    b.Property<int?>("VbfBestellId")
                        .HasColumnType("int")
                        .HasColumnName("VBF-Bestell-ID");

                    b.Property<int?>("WarenbewegungId")
                        .HasColumnType("int")
                        .HasColumnName("Warenbewegung-ID");

                    b.Property<int?>("WarenbewegungLinie")
                        .HasColumnType("int")
                        .HasColumnName("Warenbewegung-Linie");

                    b.Property<string>("WarenbewegungTyp")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("Warenbewegung-Typ");

                    b.ToTable("VBF_to_VAS");
                });

            modelBuilder.Entity("WingCore.domain.Models.StammDbModels.Wschluessel", b =>
                {
                    b.Property<string>("Asld")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ASLD");

                    b.Property<string>("Landesbezeichnung")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Ldkennung")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("LDKennung");

                    b.Property<string>("UmrFaktor")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Waehrung")
                        .HasColumnType("nvarchar(max)");

                    b.ToTable("WSchluessel");
                });
#pragma warning restore 612, 618
        }
    }
}
