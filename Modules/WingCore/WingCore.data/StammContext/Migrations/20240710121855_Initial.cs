using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace WingCore.data.StammContext
{
    /// <inheritdoc />
    public partial class Initial : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "AktMand",
                columns: table => new
                {
                    AktMandNr = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    AktMandName = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    AktDatenpfad = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                });

            migrationBuilder.CreateTable(
                name: "Blz",
                columns: table => new
                {
                    Kennummer = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    BLZ = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    BName = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    BOrt = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    BPlz = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                });

            migrationBuilder.CreateTable(
                name: "BundLand",
                columns: table => new
                {
                    BlNr = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    BlName = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                });

            migrationBuilder.CreateTable(
                name: "Deutschland",
                columns: table => new
                {
                    ID = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Ort = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Zusatz = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Plz = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Vorwahl = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Bundesland = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Kng = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                });

            migrationBuilder.CreateTable(
                name: "DImport",
                columns: table => new
                {
                    DImportDevice = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    DImportPath = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                });

            migrationBuilder.CreateTable(
                name: "Faktor",
                columns: table => new
                {
                    FaktNr = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    FaktText = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                });
            
            migrationBuilder.CreateTable(
                name: "FirmenStamm",
                columns: table => new
                {
                    FName1 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    FName2 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    FName3 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    FName4 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    FStrasse = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    FPLZ = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    FOrt = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    FTelefon = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    FTelefax = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    FEmail = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    FInternet = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    FIdentNr = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    FBundesland = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    FKDNrVon = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    FKDNrBis = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    FLFNrVon = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    FLFNrBis = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    FFibuPfad = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    FFibuLW = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    FDruckerAnz = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    FNKO = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Registriert = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    FKUNR = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ILNNummer = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                });
 
            migrationBuilder.CreateTable(
                name: "Getreideart",
                columns: table => new
                {
                    Nr = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Art = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                });

            migrationBuilder.CreateTable(
                name: "KFZKennzeichen",
                columns: table => new
                {
                    NUTS2Code = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    NUTS2Name = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Kennzeichen = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ID = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                });

            migrationBuilder.CreateTable(
                name: "Land_Kennung",
                columns: table => new
                {
                    Landesbezeichnung = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Landeswährung = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Umrechnungsfaktor = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Auslandskennung = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    IntraKennung = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    KFZKennz = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ID = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                });

            migrationBuilder.CreateTable(
                name: "mandant",
                columns: table => new
                {
                    Mandantenliste = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Name = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Branche = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Straße = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Lkz = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    PLZ = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Ort = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    MNr = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Datenpfad = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    FibuJaNein = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Lagerstellen = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    LagerBez = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    NKO = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    GDIStd1 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    GDIStd2 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    GDIBrutto = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    BackColor = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    SQLDBName = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    SQLDBPfad = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    SQLUser = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ILNNr = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    NKOKg = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                });

            migrationBuilder.CreateTable(
                name: "MwSt",
                columns: table => new
                {
                    Schlüssel = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    MwSt = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                });

            migrationBuilder.CreateTable(
                name: "Optionen",
                columns: table => new
                {
                    idx = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Fuß = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    AutoBuch = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Absender = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    EndText = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ALSOptLiefer = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ALSOptAuftr = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ALSNr = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ALSAnzAusdr = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ALSLagerakt = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                });

            migrationBuilder.CreateTable(
                name: "PlzOrt",
                columns: table => new
                {
                    Kennummer = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Vorwahl = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    PLZ = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Feld5 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Ort = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                });

            migrationBuilder.CreateTable(
                name: "sicherung",
                columns: table => new
                {
                    MandantNam = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    MandantNr = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Sicherungspfad = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Datum = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Sicdrv = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Sicdir = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                });

            migrationBuilder.CreateTable(
                name: "Steuer",
                columns: table => new
                {
                    ID = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Schlüssel = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    MwSt = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    KontoEK = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    KontoVK = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    KontoEGEK = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    KontoEGVK = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    KontoDREK = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    KontoDRVK = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    KontoAnzVerkauf = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    KontoAnzVerkaufEG = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    KontoAnzVerkaufDritt = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                });

            migrationBuilder.CreateTable(
                name: "Treibhausgasemissionen",
                columns: table => new
                {
                    NUTS2Code = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    NUTS2Gebiet = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ArtNr = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ArtBez = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Wert = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ID = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                });

            migrationBuilder.CreateTable(
                name: "TriggerLog",
                columns: table => new
                {
                    LogID = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    TriggerName = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: true),
                    Action = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    TableAffected = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    Timestamp = table.Column<DateTime>(type: "datetime", nullable: true, defaultValueSql: "(getdate())"),
                    EntryID = table.Column<int>(type: "int", nullable: true),
                    Done = table.Column<bool>(type: "bit", nullable: true, defaultValue: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK__TriggerL__5E5499A82E1E7D06", x => x.LogID);
                });

            migrationBuilder.CreateTable(
                name: "User",
                columns: table => new
                {
                    GeneralUserOption = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                });

            migrationBuilder.CreateTable(
                name: "UserCode",
                columns: table => new
                {
                    UserCode = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    UserName = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    UserFreigabe = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    UserDatum = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                });

            migrationBuilder.CreateTable(
                name: "VAS_to_VBF",
                columns: table => new
                {
                    VASBestellReferenz = table.Column<string>(name: "VAS-Bestell-Referenz", type: "nvarchar(255)", maxLength: 255, nullable: true),
                    KundenNr = table.Column<int>(name: "Kunden-Nr.", type: "int", nullable: true),
                    KundenName1 = table.Column<string>(name: "Kunden-Name 1", type: "nvarchar(255)", maxLength: 255, nullable: true),
                    KundenName2 = table.Column<string>(name: "Kunden-Name 2", type: "nvarchar(255)", maxLength: 255, nullable: true),
                    KundenStraße = table.Column<string>(name: "Kunden-Straße", type: "nvarchar(255)", maxLength: 255, nullable: true),
                    KundenPLZ = table.Column<string>(name: "Kunden-PLZ", type: "nvarchar(10)", maxLength: 10, nullable: true),
                    KundenOrt = table.Column<string>(name: "Kunden-Ort", type: "nvarchar(255)", maxLength: 255, nullable: true),
                    KundenEntladestelle = table.Column<string>(name: "Kunden-Entladestelle", type: "nvarchar(255)", maxLength: 255, nullable: true),
                    Sollmengekg = table.Column<double>(name: "Sollmenge kg", type: "float", nullable: true),
                    ArtikelWGrp = table.Column<string>(name: "Artikel W.-Grp.", type: "nvarchar(255)", maxLength: 255, nullable: true),
                    ArtikelNummer = table.Column<string>(name: "Artikel Nummer", type: "nvarchar(255)", maxLength: 255, nullable: true),
                    ArtikelBezeichnung = table.Column<string>(name: "Artikel Bezeichnung", type: "nvarchar(255)", maxLength: 255, nullable: true),
                    Ladedatum = table.Column<DateTime>(type: "datetime", nullable: true),
                    Lieferdatum = table.Column<DateTime>(type: "datetime", nullable: true),
                    KommentarLieferschein = table.Column<string>(name: "Kommentar Lieferschein", type: "nvarchar(255)", maxLength: 255, nullable: true),
                    KommentarIntern = table.Column<string>(name: "Kommentar Intern", type: "nvarchar(255)", maxLength: 255, nullable: true)
                },
                constraints: table =>
                {
                });

            migrationBuilder.CreateTable(
                name: "VBF_to_VAS",
                columns: table => new
                {
                    WarenbewegungID = table.Column<int>(name: "Warenbewegung-ID", type: "int", nullable: true),
                    WarenbewegungTyp = table.Column<string>(name: "Warenbewegung-Typ", type: "nvarchar(255)", maxLength: 255, nullable: true),
                    WarenbewegungLinie = table.Column<int>(name: "Warenbewegung-Linie", type: "int", nullable: true),
                    DatumWägung1 = table.Column<DateTime>(name: "Datum Wägung 1", type: "datetime", nullable: true),
                    GewichtWägung1 = table.Column<double>(name: "Gewicht Wägung 1", type: "float", nullable: true),
                    DatumWägung2 = table.Column<DateTime>(name: "Datum Wägung 2", type: "datetime", nullable: true),
                    GewichtWägung2 = table.Column<double>(name: "Gewicht Wägung 2", type: "float", nullable: true),
                    Lagerstätte1 = table.Column<int>(name: "Lagerstätte 1", type: "int", nullable: true),
                    Menge1kg = table.Column<double>(name: "Menge 1 kg", type: "float", nullable: true),
                    Lagerstätte2 = table.Column<int>(name: "Lagerstätte 2", type: "int", nullable: true),
                    Menge2kg = table.Column<double>(name: "Menge 2 kg", type: "float", nullable: true),
                    Lagerstätte3 = table.Column<int>(name: "Lagerstätte 3", type: "int", nullable: true),
                    Menge3kg = table.Column<double>(name: "Menge 3 kg", type: "float", nullable: true),
                    ArtikelWGrp = table.Column<string>(name: "Artikel W.-Grp.", type: "nvarchar(255)", maxLength: 255, nullable: true),
                    ArtikelNummer = table.Column<string>(name: "Artikel Nummer", type: "nvarchar(255)", maxLength: 255, nullable: true),
                    ArtikelBezeichnung = table.Column<string>(name: "Artikel Bezeichnung", type: "nvarchar(255)", maxLength: 255, nullable: true),
                    KundenNr = table.Column<int>(name: "Kunden-Nr.", type: "int", nullable: true),
                    KundenName1 = table.Column<string>(name: "Kunden-Name 1", type: "nvarchar(255)", maxLength: 255, nullable: true),
                    KundenName2 = table.Column<string>(name: "Kunden-Name 2", type: "nvarchar(255)", maxLength: 255, nullable: true),
                    KundenStraße = table.Column<string>(name: "Kunden-Straße", type: "nvarchar(255)", maxLength: 255, nullable: true),
                    KundenPLZ = table.Column<string>(name: "Kunden-PLZ", type: "nvarchar(10)", maxLength: 10, nullable: true),
                    KundenOrt = table.Column<string>(name: "Kunden-Ort", type: "nvarchar(255)", maxLength: 255, nullable: true),
                    SpeditionNr = table.Column<int>(name: "Spedition-Nr.", type: "int", nullable: true),
                    SpeditionName1 = table.Column<string>(name: "Spedition-Name 1", type: "nvarchar(255)", maxLength: 255, nullable: true),
                    SpeditionName2 = table.Column<string>(name: "Spedition-Name 2", type: "nvarchar(255)", maxLength: 255, nullable: true),
                    SpeditionStraße = table.Column<string>(name: "Spedition-Straße", type: "nvarchar(255)", maxLength: 255, nullable: true),
                    SpeditionPLZ = table.Column<string>(name: "Spedition-PLZ", type: "nvarchar(10)", maxLength: 10, nullable: true),
                    SpeditionOrt = table.Column<string>(name: "Spedition-Ort", type: "nvarchar(255)", maxLength: 255, nullable: true),
                    Kommentar = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: true),
                    PartieNr = table.Column<string>(name: "Partie-Nr.", type: "nvarchar(255)", maxLength: 255, nullable: true),
                    Entladestelle = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: true),
                    LKWKennzeichen = table.Column<string>(name: "LKW-Kennzeichen", type: "nvarchar(50)", maxLength: 50, nullable: true),
                    AnhängerKennzeichen = table.Column<string>(name: "Anhänger-Kennzeichen", type: "nvarchar(50)", maxLength: 50, nullable: true),
                    LKWFahrer = table.Column<string>(name: "LKW-Fahrer", type: "nvarchar(255)", maxLength: 255, nullable: true),
                    VBFBestellID = table.Column<int>(name: "VBF-Bestell-ID", type: "int", nullable: true),
                    VASBestellReferenz = table.Column<string>(name: "VAS-Bestell-Referenz", type: "nvarchar(255)", maxLength: 255, nullable: true)
                },
                constraints: table =>
                {
                });

            migrationBuilder.CreateTable(
                name: "WSchluessel",
                columns: table => new
                {
                    ASLD = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Landesbezeichnung = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Waehrung = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    LDKennung = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    UmrFaktor = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "AktMand");

            migrationBuilder.DropTable(
                name: "Blz");

            migrationBuilder.DropTable(
                name: "BundLand");

            migrationBuilder.DropTable(
                name: "Deutschland");

            migrationBuilder.DropTable(
                name: "DImport");

            migrationBuilder.DropTable(
                name: "Faktor");
            
            migrationBuilder.DropTable(
                name: "FirmenStamm");

            migrationBuilder.DropTable(
                name: "Getreideart");

            migrationBuilder.DropTable(
                name: "KFZKennzeichen");

            migrationBuilder.DropTable(
                name: "Land_Kennung");

            migrationBuilder.DropTable(
                name: "mandant");

            migrationBuilder.DropTable(
                name: "MwSt");

            migrationBuilder.DropTable(
                name: "Optionen");

            migrationBuilder.DropTable(
                name: "PlzOrt");

            migrationBuilder.DropTable(
                name: "sicherung");

            migrationBuilder.DropTable(
                name: "Steuer");

            migrationBuilder.DropTable(
                name: "Treibhausgasemissionen");

            migrationBuilder.DropTable(
                name: "TriggerLog");

            migrationBuilder.DropTable(
                name: "User");

            migrationBuilder.DropTable(
                name: "UserCode");

            migrationBuilder.DropTable(
                name: "VAS_to_VBF");

            migrationBuilder.DropTable(
                name: "VBF_to_VAS");

            migrationBuilder.DropTable(
                name: "WSchluessel");
        }
    }
}
