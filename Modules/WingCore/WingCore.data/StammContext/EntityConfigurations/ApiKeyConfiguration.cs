using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WingCore.domain.Models.StammDbModels;

namespace WingCore.data.StammContext.EntityConfigurations;

public class ApiKeyConfiguration : IEntityTypeConfiguration<ApiKey>
{
    public void Configure(EntityTypeBuilder<ApiKey> builder)
    {
        builder.ToTable("ApiKey");
        builder.Has<PERSON>ey(e => e.Id).HasName("ID");
        builder.Property(e => e.Key).HasColumnName("Key").IsRequired();
        builder.Property(e => e.CreatedAt)
            .HasColumnName("CreatedAt")
            .IsRequired()
            .ValueGeneratedOnAdd()
            .HasDefaultValueSql("GETDATE()");
        builder.Property(e => e.LastUsingAt).HasColumnName("LastUsingAt");
        builder.Property(e => e.IpAddress).HasColumnName("IpAdress").HasMaxLength(45);
        builder.Property(e => e.Description).HasColumnName("Description").HasMaxLength(255);
        builder.Property(e => e.Endpoint).HasColumnName("Endpoint").HasMaxLength(255);
        builder.Property(e => e.Mandanten).HasColumnName("Mandanten").HasMaxLength(255);
    }
}