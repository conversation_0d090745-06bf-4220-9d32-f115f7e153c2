using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;
using Microsoft.Extensions.Configuration;
using WingCore.application.Contract;

namespace WingCore.data.StammContext;

public class StammDbContextFactory(
    IStammDbContextAccessor? stammDbContextAccessor,
    IConfiguration? configuration,
    ILoggerManager? logger)
    : IDesignTimeDbContextFactory<StammDbContext>
{
    public StammDbContextFactory() : this(default, default, default)
    {
    }

    public StammDbContext CreateDbContext(params string[] args)
    {
        var connectionString = stammDbContextAccessor?.GetConnectionString() ?? string.Empty;
            
        connectionString = string.IsNullOrEmpty(connectionString)
            ? configuration?.GetConnectionString("DefaultStammDbConnection")
            : connectionString;

        var timeOut = 360;

        if (stammDbContextAccessor is not null && !stammDbContextAccessor.TestConnection().Item1)
        {
            timeOut = 5;
        }
        
        var optionsBuilder = new DbContextOptionsBuilder<StammDbContext>();
        optionsBuilder.UseLoggerFactory(logger?.LoggerFactory);
        optionsBuilder.UseSqlServer(connectionString,
                                      providerOptions =>
                                      {
                                          providerOptions.CommandTimeout(timeOut);
                                      });
        
        return new StammDbContext(optionsBuilder.Options);
    }
}