using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using WingCore.data.EntityConfigurations;
using WingCore.data.StammContext.EntityConfigurations;
using WingCore.domain;
using WingCore.domain.Common.Configurations;
using WingCore.domain.Models.StammDbModels;
using Optionen = WingCore.domain.Models.StammDbModels.Optionen;

namespace WingCore.data.StammContext;

public sealed partial class StammDbContext 
    : IdentityDbContext<ApplicationUser,
                        IdentityRole,
                        string,
                        IdentityUserClaim<string>,
                        ApplicationUserRole,
                        ApplicationUserLogin,
                        IdentityRoleClaim<string>,
                        ApplicationUserToken>
{
    public StammDbContext(DbContextOptions<StammDbContext> options, bool withMigrations = true)
        : base(options)
    {
        try
        {
            if(!withMigrations)
                return;
            if(!Database.CanConnect())
                return;
            if (!Database.GetPendingMigrations().Any())
                 return;
        }
        catch (Exception e)
        {
            Console.WriteLine(e);
            return;
        }


        try
        {
            Database.ExecuteSql($"""
                                 IF EXISTS (SELECT * FROM sys.tables WHERE name = 'BundLand')
                                 BEGIN
                                     IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = '__EFMigrationsHistory')
                                     BEGIN
                                         CREATE TABLE __EFMigrationsHistory
                                         (
                                             MigrationId nvarchar(150) NOT NULL,
                                             ProductVersion nvarchar(32) NOT NULL
                                         )
                                         INSERT INTO __EFMigrationsHistory (MigrationId, ProductVersion)
                                         VALUES ('20240710121855_Initial', '8.0.6')
                                     END
                                     ELSE IF NOT EXISTS (SELECT * FROM __EFMigrationsHistory WHERE MigrationId = '20240710121855_Initial')
                                     BEGIN
                                         INSERT INTO __EFMigrationsHistory (MigrationId, ProductVersion)
                                         VALUES ('20240710121855_Initial', '8.0.6')
                                     END
                                 END
                                 """);

        }
        catch (Exception e)
        {
            Console.WriteLine(e);
        }
        

        Database.MigrateAsync().GetAwaiter().GetResult();

    }
    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
    {
        //optionsBuilder.UseSqlServer("Data Source=**********\\VASEXPRESS;Initial Catalog=ACCESS;TrustServerCertificate=True;User=***;Password=***;Integrated Security=False;MultipleActiveResultSets=True");
    }
    
    public DbSet<AktMand> AktMands { get; set; }

    public DbSet<Blz> Blzs { get; set; }

    public DbSet<BundLand> BundLands { get; set; }

    public DbSet<Deutschland> Deutschlands { get; set; }

    public DbSet<Dimport> Dimports { get; set; }

    public DbSet<Faktor> Faktors { get; set; }
    public DbSet<CompanyMasterData> FirmenStamms { get; set; }

    public DbSet<Getreideart> Getreidearts { get; set; }

    public DbSet<Kfzkennzeichen> Kfzkennzeichens { get; set; }

    public DbSet<LandKennung> LandKennungs { get; set; }

    public DbSet<Mandant> Mandants { get; set; }

    public DbSet<MwSt> MwSts { get; set; }

    public DbSet<Optionen> Optionens { get; set; }

    public DbSet<PlzOrt> PlzOrts { get; set; }

    public DbSet<Sicherung> Sicherungs { get; set; }

    public DbSet<Steuer> Steuers { get; set; }

    public DbSet<Treibhausgasemissionen> Treibhausgasemissionens { get; set; }

    public DbSet<TriggerLog> TriggerLogs { get; set; }

    public DbSet<User> User { get; set; }

    public DbSet<UserCode> UserCodes { get; set; }

    public DbSet<VasToVbf> VasToVbfs { get; set; }

    public DbSet<VbfToVa> VbfToVas { get; set; }

    public DbSet<Wschluessel> Wschluessels { get; set; }
    public DbSet<ConfigForAllMandant> Configs { get; set; }
    public DbSet<ApiKey> ApiKeys { get; set; }

    // protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
    //     => optionsBuilder.UseSqlServer("Data Source=********** \\VASEXPRESS;Initial Catalog=ACCESS;TrustServerCertificate=True;User=***;Password=***");

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<TriggerLog>(entity =>
        {
            entity.HasKey(e => e.LogId).HasName("PK__TriggerL__5E5499A82E1E7D06");

            entity.Property(e => e.Done).HasDefaultValue(false);
            entity.Property(e => e.Timestamp).HasDefaultValueSql("(getdate())");
        });

        modelBuilder.Entity<AktMand>().HasNoKey();
        modelBuilder.Entity<Blz>().HasNoKey();
        modelBuilder.Entity<BundLand>().HasNoKey();
        modelBuilder.Entity<Deutschland>().HasNoKey();
        modelBuilder.Entity<Dimport>().HasNoKey();
        modelBuilder.Entity<Faktor>().HasNoKey();
        modelBuilder.Entity<Getreideart>().HasNoKey();
        modelBuilder.Entity<Kfzkennzeichen>().HasNoKey();
        modelBuilder.Entity<LandKennung>().HasNoKey();
        modelBuilder.Entity<Mandant>().HasNoKey();
        modelBuilder.Entity<MwSt>().HasNoKey();
        modelBuilder.Entity<Optionen>().HasNoKey();
        modelBuilder.Entity<PlzOrt>().HasNoKey();
        modelBuilder.Entity<Sicherung>().HasNoKey();
        modelBuilder.Entity<Steuer>().HasNoKey();
        modelBuilder.Entity<Treibhausgasemissionen>().HasNoKey();
        modelBuilder.Entity<User>().HasNoKey();
        modelBuilder.Entity<UserCode>().HasNoKey();
        modelBuilder.Entity<VasToVbf>().HasNoKey();
        modelBuilder.Entity<VbfToVa>().HasNoKey();
        modelBuilder.Entity<Wschluessel>().HasNoKey();
        
        modelBuilder.Entity<ConfigForAllMandant>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("Config$ID");
        
            entity.ToTable("Config");
        
            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.ConfigData);
        });
        
        new FirmenStammConfiguration().Configure(modelBuilder.Entity<CompanyMasterData>());
        modelBuilder.Entity<ApplicationUserToken>()
            .HasKey(t => new { t.UserId, t.LoginProvider, t.Name });
        
        new ApiKeyConfiguration().Configure(modelBuilder.Entity<ApiKey>());
    }
    
   protected override void ConfigureConventions(ModelConfigurationBuilder configurationBuilder)
    {
        configurationBuilder
            .Properties<ConfigurationType>()
            .HaveConversion<ConfigurationTypeConverter>();
    }
}
