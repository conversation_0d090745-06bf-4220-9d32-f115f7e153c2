using WingCore.domain.Models.StammDbModels;
using WingCore.application.Contract.IModels.StammDb;
using WingCore.application.Contract.Services;

namespace WingCore.ioc;

public class CompanyMasterDataService(ICompanyMasterDataRepository companyMasterDataRepository)
    : ICompanyMasterDataService
{
    public async ValueTask<CompanyMasterData?> GetMasterData(string mandantNr) => await companyMasterDataRepository.GetAsync(mandantNr);

    public void SetOrUpdate(CompanyMasterData newCompanyMasterData) => companyMasterDataRepository.SetOrUpdate(newCompanyMasterData);
}