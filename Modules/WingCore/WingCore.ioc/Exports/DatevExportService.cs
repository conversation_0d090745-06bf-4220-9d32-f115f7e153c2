using DatevExport;
using Languages.Resources;
using Microsoft.Extensions.Localization;
using WingCore.application.Contract;
using WingCore.application.Contract.Services;
using WingCore.domain.Common.Configurations;

namespace WingCore.ioc.Exports;

internal sealed record DatevExportService(IOutgoingInvoiceHeaderService OutgoingInvoiceHeaderService,
                                          IIncomingInvoiceHeaderService IncomingInvoiceHeaderHeaderService,
                                          IGrainSettlementInvoiceHeaderService GrainSettlementInvoiceHeaderService,
                                          IRapeSettlementInvoiceHeaderService RapeSettlementInvoiceHeaderService,
                                          ILoggerManager Logger,
                                          IStringLocalizer<Resource> Localizer)
    : IDatevExportService
{
    public long NumberOfExportInvoices { get; set; }
    public string FileName { get; set; } = string.Empty;
    
    
    public async Task<MemoryStream> ExportInvoicesAsZip(DateTime? fromDate,
                                                        DateTime? toDate,
                                                        int beraternummer,
                                                        int mandantennummer,
                                                        long fromInvoiceNumber,
                                                        long toInvoiceNumber,
                                                        short sachKontenLaenge,
                                                        string suffixForKundenkonto,
                                                        DateTime wirtschaftsjahresbeginn,
                                                        DateTime beginingExportDate,
                                                        bool exportOutgoingInvoices,
                                                        bool exportIncomingInvoices,
                                                        bool exportGrainSettlementInvoices,
                                                        bool withExportedInvoices)
    {
        ConfigurationDatev configuration = new()
        {
            FromDate = fromDate,
            ToDate = toDate,
            BeraterNummer = beraternummer,
            MandantenNummer = mandantennummer,
            FromInvoiceNumber = fromInvoiceNumber,
            ToInvoiceNumber = toInvoiceNumber,
            SachKontenLaenge = sachKontenLaenge,
            SuffixForKundenkonto = suffixForKundenkonto,
            Wirtschaftsjahresbeginn = wirtschaftsjahresbeginn,
            BeginingExportDate = beginingExportDate,
            WithOutgoingInvoice = exportOutgoingInvoices,
            WithIncomingInvoice = exportIncomingInvoices,
            WithGrainSettlementInvoice = exportGrainSettlementInvoices,
            WithExportedInvoices = withExportedInvoices
        };

        return await ExportInvoicesAsZip(configuration);
    }



    public async Task<MemoryStream> ExportInvoicesAsZip(ConfigurationDatev configuration)
    {
        var datevExport = new DatevExportToCsv(OutgoingInvoiceHeaderService,
                                                IncomingInvoiceHeaderHeaderService,
                                                GrainSettlementInvoiceHeaderService,
                                                RapeSettlementInvoiceHeaderService,
                                                Logger,
                                                Localizer);
    

        var zipByteArray = await datevExport.ExportInvoicesAsZip(configuration);
        NumberOfExportInvoices = datevExport.NumberOfExportInvoices;
        FileName = datevExport.FileName;
        return zipByteArray;
    }

}