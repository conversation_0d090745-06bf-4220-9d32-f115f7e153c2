using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Routing;
using WingCore.application.Api;

namespace WingCore.ioc.Endpoints;

public static class IncomingGoodsEndpoints
{
    public static IEndpointRouteBuilder MapIncomingGoodsEndpoints(this IEndpointRouteBuilder app)
    {
        var incomingGoods = app.MapGroup("/IncomingGoods").WithTags("IncomingGoods");
        incomingGoods.MapGet("/GetAll", GetAllIncomingGoods);
        /*incomingGoods.MapGet("/GetByNumber/{IncomingGoodsNumber}", GetIncomingGoodsByNumber);*/
        return app;
    }

    private static Task<IResult> GetIncomingGoodsByNumber(HttpContext context)
    {
        throw new NotImplementedException();
    }

    private static async Task<IResult> GetAllIncomingGoods([FromServices] IMediator mediator, CancellationToken cancellationToken)
    {
        var incomingGoods = await mediator.Send(new GetIncomingGoodsQuery());
        return Results.Ok(incomingGoods);
    }
}