using FluentValidation;
using WingCore.application.ApiDtos.OrderData;
using WingCore.application.ApiDtos.OrderData.Put;

namespace WingCore.ioc.Endpoints.Validators.Put;

public class OrderHeaderPutDtoV1Validator : AbstractValidator<OrderHeaderPutDtoV1>
{
    public OrderHeaderPutDtoV1Validator()
    {
        RuleFor(x => x.OrderNumber).NotEmpty();
        RuleFor(x => x.Status).NotEmpty().Must(e => OrderHeaderStatusV1.All.Contains(e))
            .WithMessage("Invalid value for 'Status'. Allowed values are: " + 
                         string.Join(", ", OrderHeaderStatusV1.All));
    }
}