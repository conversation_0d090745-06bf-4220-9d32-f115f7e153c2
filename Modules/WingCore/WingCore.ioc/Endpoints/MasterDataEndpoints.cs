using System.Collections;
using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Routing;
using WingCore.application.Api;
using WingCore.application.ApiDtos.MasterData.V1;
using WingCore.application.Contract.IModels.Helper;
using WingCore.domain.Models;

namespace WingCore.ioc.Endpoints;

public static class MasterDataEndpoints
{
    public static IEndpointRouteBuilder MapMasterDataEndpoints(this IEndpointRouteBuilder app)
    {
        var masterData = app.MapGroup("/MasterData").WithTags("MasterData");
        masterData.MapGet("/GetAllCustomers/{lastChange:datetime}", GetAllCustomer)
            .Produces<IEnumerable<CustomerMasterDataDtoV1>>(StatusCodes.Status200OK);
        masterData.MapGet("/GetCustomerByNumber/{Number}", GetCustomerByNumber)
            .Produces<CustomerMasterDataDtoV1>(StatusCodes.Status200OK);
        masterData.MapGet("/GetAllArticles/{lastChange:datetime}", GetAllArticles)
            .Produces<IEnumerable<ArticelDtoV1>>(StatusCodes.Status200OK);
        masterData.MapGet("/GetArticleByNumber/{Number}", GetArticleByNumber)
            .Produces<ArticelDtoV1>(StatusCodes.Status200OK);
        masterData.MapGet("/GetAllSupplier/{lastChange:datetime}", GetAllSupplier).Produces<ArticelDtoV1>(StatusCodes.Status200OK);
        masterData.MapGet("/GetAllSupplier/{Number}", GetAllSupplierByNumber)
            .Produces<ArticelDtoV1>(StatusCodes.Status200OK);

        return app;
    }

    private static async Task<IResult> GetAllSupplier([FromServices] IMediator mediator, DateTime lastChange,
        CancellationToken cancellationToken)
    {
        try
        {
            var supplierList = await mediator.Send(new GetAllSupplierDtoV1Query(lastChange), cancellationToken);
            return Results.Ok(supplierList);
        }
        catch (Exception e)
        {
            return Results.BadRequest(e.InnerException?.Message ?? e.Message);
        }
    }

    private static async Task<IResult> GetAllSupplierByNumber([FromServices] IMediator mediator, long number,
        CancellationToken cancellationToken)
    {
        try
        {
            var supplier = await mediator.Send(new GetSupplierDtoV1ByNumberQuery(number), cancellationToken);
            return Results.Ok(supplier);
        }
        catch (Exception e)
        {
            return Results.BadRequest(e.InnerException?.Message ?? e.Message);
        }
    }

    private static async Task<IResult> GetAllCustomer([FromServices] IMediator mediator,
        DateTime lastChange,
        CancellationToken cancellationToken)
    {
        var customerSearchParam = new CustomerSearchParam { LastChange = lastChange };
        var customerList =
            await mediator.Send(new GetCustomerBySearchParamQuery(customerSearchParam), cancellationToken);
        var customerDto = customerList.Select(x => (CustomerMasterDataDtoV1)x);
        return Results.Ok(customerDto);
    }

    private static async Task<IResult> GetCustomerByNumber([FromServices] IMediator mediator, long number,
        CancellationToken cancellationToken)
    {
        var customerSearchParam = new CustomerSearchParam { Number = number };
        var customerList =
            (await mediator.Send(new GetCustomerBySearchParamQuery(customerSearchParam), cancellationToken)).ToList();
        if (customerList.Count == 0)
            return Results.NotFound($"Kunde mit Nummer {number} wurde nicht gefunden");
        var customerDto =
            await mediator.Send(new AddPriceListToCustomerQuery(customerList), cancellationToken);
        return Results.Ok(customerDto);
    }

    private static async Task<IResult> GetAllArticles([FromServices] IMediator mediator,
        DateTime? lastChange,
        CancellationToken cancellationToken)
    {
        var articleSearchParam = new ArticleSearchParam { LastChange = lastChange };
        var articles = await mediator.Send(new GetArticleBySearchParamQuery(articleSearchParam), cancellationToken);
        return Results.Ok(articles.Select(ArticelDtoV1.Create));
    }

    private static async Task<IResult> GetArticleByNumber([FromServices] IMediator mediator, long number,
        CancellationToken cancellationToken)
    {
        var articleSearchParam = new ArticleSearchParam { Number = number };
        var articles = await mediator.Send(new GetArticleBySearchParamQuery(articleSearchParam), cancellationToken);
        if (!articles.Any())
            return Results.NotFound($"Artikel mit Nummer {number} wurde nicht gefunden");
        var article = articles.Select(ArticelDtoV1.Create).FirstOrDefault();

        return Results.Ok(article);
    }
}