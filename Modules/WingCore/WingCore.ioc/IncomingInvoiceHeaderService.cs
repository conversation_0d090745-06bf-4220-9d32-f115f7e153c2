using WingCore.domain.Models;
using WingCore.application.Contract.IModels;
using WingCore.application.Contract.Services;
using WingCore.application.DataTransferObjects.Invoices;

namespace WingCore.ioc;

internal sealed class IncomingInvoiceHeaderService(IIncomingInvoiceRepository incomingInvoiceRepository)
    : BaseInvoiceHeaderService<IIncomingInvoiceRepository,Erv, IncomingInvoiceOverviewDto>(incomingInvoiceRepository),
        IIncomingInvoiceHeaderService;