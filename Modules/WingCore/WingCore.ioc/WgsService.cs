using WingCore.domain.Models;
using WingCore.application.Contract.IModels;
using WingCore.application.Contract.Services;

namespace WingCore.ioc;

public class WgsService(IWgsRepository wgsRepository)
    : IWgsService
{
    public async ValueTask<IEnumerable<Wg>> GetOpenWgs(bool trackBack, long? supplierId)
    {
        try
        {
            return await wgsRepository.GetOpenWgs(trackBack, supplierId);
        }
        catch (Exception ex)
        {
            throw new Exception("Error while getting open Wgs", ex);
        }
    }
}