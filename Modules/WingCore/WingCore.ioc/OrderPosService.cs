using WingCore.domain.Models;
using WingCore.application.Contract.IModels;
using WingCore.application.Contract.Services;
using WingCore.domain.Common.Flags;

namespace WingCore.ioc;

public class OrderPosService(IAuftragsposRepository auftragsposRepository)
    : IOrderPosService
{
    public IEnumerable<Auftragspo> GetAllOrderPos(bool trackChanges)
    {
        throw new NotImplementedException();
    }

    public async Task<Auftragspo?> GetOrderPosAsync(bool trackChanges, long posId) => await auftragsposRepository.GetAuftragsposById(trackChanges, posId);

    public IEnumerable<Auftragspo> GetOrderPosByArtikels(bool trackChanges, List<long> articleNumbers) => auftragsposRepository.GetAuftragsposByArtikels(trackChanges, articleNumbers);

    public async Task<IEnumerable<Auftragspo>> GetOrderPosWithArticlesHaveSameMainArticleAsync(long mainArticleNumber, OrderPosFlags withOutflag, bool palettenware) => await auftragsposRepository.GetOrderPosWithArticlesHaveSameMainArticleAsync( mainArticleNumber, withOutflag,palettenware);
    public async Task SetFlagAsync(List<long> allUsedOrderPos, OrderPosFlags processOrderCreated) => await auftragsposRepository.SetFlagAsync(allUsedOrderPos, processOrderCreated);
    public async Task RemoveFlagAsync(List<long> allUsedOrderPos, OrderPosFlags processOrderCreated) => await auftragsposRepository.RemoveFlagAsync(allUsedOrderPos, processOrderCreated);
    public async Task SetFlagProcessOrderCreatedAsync(List<long> orderPosIds) => await SetFlagAsync(orderPosIds, OrderPosFlags.ProcessOrderCreated);
    public async Task RemoveFlagProcessOrderCreatedAsync(List<long> orderPosIds) => await RemoveFlagAsync(orderPosIds, OrderPosFlags.ProcessOrderCreated);
}