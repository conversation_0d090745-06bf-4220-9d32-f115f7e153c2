using WingCore.application.Contract.IModels;
using WingCore.application.Contract.Services;
using WingCore.application.DataTransferObjects.Invoices;
using WingCore.application.Extensions;
using WingCore.domain.Common;

namespace WingCore.ioc;

internal sealed class RapeSettlementInvoicePostingsService(
    IRapeSettlementInvoicePostingsRepository rapeSettlementInvoicePostingsRepository,
    IRapeSettlementInvoiceRepository rapeSettlementInvoiceRepository)
    : IRapeSettlementInvoicePostingsService
{
    public async Task<IEnumerable<GrainAndRapeSettlementInvoicePostingDto>> GetAllPostingsFromInvoice(bool trackChanges, long invoiceNumber, PostingsSchema? schema = null)
    {
        var listPostings = rapeSettlementInvoicePostingsRepository.GetAllInvoicePostingsFromInvoice(trackChanges, invoiceNumber,schema).ToList();
        
        if (listPostings.Count == 0)
            return [];
        
        var invoice = await rapeSettlementInvoiceRepository.GetInvoiceHeaderDataAsync(trackChanges, invoiceNumber);
        
        return invoice is null ? [] : listPostings.Select(x => GrainAndRapeSettlementInvoicePostingDto.Create(x,invoice));
    }

    public async Task<IEnumerable<GrainAndRapeSettlementInvoicePostingDto>> GetAllPostingsFromInvoiceAndCompress(bool trackChanges, long invoiceNumber, InvoicePostingCompressKeyOption compressKeyOption)
    {
        var listPostings = rapeSettlementInvoicePostingsRepository.GetAllInvoicePostingsFromInvoice(trackChanges, invoiceNumber,PostingsSchema.Po).ToList();
        if (listPostings.Count == 0)
            return [];

        var invoice = await rapeSettlementInvoiceRepository.GetInvoiceHeaderDataAsync(trackChanges, invoiceNumber);

        return invoice is null
            ? []
            : listPostings.Compress(compressKeyOption).Select(x => GrainAndRapeSettlementInvoicePostingDto.Create(x, invoice));
    }
}