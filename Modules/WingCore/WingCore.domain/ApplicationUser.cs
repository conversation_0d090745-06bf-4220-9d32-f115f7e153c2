using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.Identity;

namespace WingCore.domain;

public class ApplicationUser : IdentityUser
{
    [Key]
    public override required string Id { get; set; }
}

public class ApplicationUserRole : IdentityUserRole<string>
{
    [Key]
    public override required string UserId { get; set; }
}

public class ApplicationUserLogin : IdentityUserLogin<string>
{
    [Key]
    public override required string UserId { get; set; }
}

public class ApplicationUserToken : IdentityUserToken<string>
{
    [Key]
    public override required string UserId { get; set; }
    public override required string LoginProvider { get; set; }
    public override required string Name { get; set; }
}
