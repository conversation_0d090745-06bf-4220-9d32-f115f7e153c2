namespace WingCore.domain.Models;

public partial class Nvobilanz
{
    public long BelegNr { get; set; }

    public DateTime? Abschlussdatum { get; set; }

    public long ArtikelNr { get; set; }

    public string? ArtBezText1 { get; set; }

    public string? ArtBezText2 { get; set; }

    public decimal? Anfangsbestand { get; set; }

    public decimal? Zugang { get; set; }

    public decimal? Abgang { get; set; }

    public decimal? Bestand { get; set; }

    public bool? NachhaltigeWare { get; set; }

    public DateTime? ZeitraumVon { get; set; }

    public DateTime? ZeitraumBis { get; set; }

    public bool? Abgeschlossen { get; set; }

    public decimal? Schwund { get; set; }

    public int? FilialNr { get; set; }

    public long Id { get; set; }
}
