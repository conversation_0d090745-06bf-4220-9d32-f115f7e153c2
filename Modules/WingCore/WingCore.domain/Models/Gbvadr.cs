using System.ComponentModel.DataAnnotations.Schema;
using WingCore.domain.Common.Flags;

namespace WingCore.domain.Models;

public partial class Gbvadr
{
    public long Gbnr { get; set; }

    public short? Gbart { get; set; }

    public long? Gblfnr { get; set; }

    public string? Gbname1 { get; set; }

    public string? Gbstrasse { get; set; }

    public string? Gbplz { get; set; }

    public string? Gbort { get; set; }

    public decimal? Gbnetto { get; set; }

    public decimal? Gbmwst { get; set; }

    public decimal? G<PERSON>rutto { get; set; }

    public decimal? Gbabzug { get; set; }

    public decimal? GbzahlBetrag { get; set; }

    public decimal? GbgesMenge { get; set; }

    public decimal? GbangAbfall { get; set; }

    // public decimal? GbabrGew { get; set; }

    public decimal? Gbabfall { get; set; }

    public string? Gbsbg { get; set; }

    public string? Gbanrede { get; set; }

    public string? Gbtelefon { get; set; }

    public string? Gbfax { get; set; }

    public decimal? GbzuschlEur { get; set; }

    public string? GbentfKl { get; set; }

    public short? GbstSchl { get; set; }

    public decimal? GbstProz { get; set; }

    public decimal? GbentfKlzuEur { get; set; }

    public string? Gbland { get; set; }

    public DateTime? Gbdatum { get; set; }

    public string? Gblfstatus { get; set; }

    public short? Gbbldkng { get; set; }

    public string? Gbbld { get; set; }

    public short? Gbasld { get; set; }

    public bool? Gbhaendler { get; set; }

    public bool? Gbezg { get; set; }

    public bool? Send { get; set; }

    public bool? Gbfibu2 { get; set; }

    public string? Gbstorno { get; set; }

    public decimal? GbzuschlSumme { get; set; }

    public string? Gbblz { get; set; }

    public string? Gbkonto { get; set; }

    public string? Gbbank { get; set; }

    public string? Gbvaluta { get; set; }

    public string? Gbnachzhlg { get; set; }

    public long? Gbanlfnr { get; set; }

    public string? Gbfiliale { get; set; }

    public string? GbreNrLf { get; set; }

    public string? GbktrNrLf { get; set; }

    public DateTime? GbvalutaDatum { get; set; }

    public decimal? Gbezgbetr { get; set; }

    public long? Gbaltnr { get; set; }

    public string? GbstNr { get; set; }

    public string? GbdruckKz { get; set; }

    public string? Gbvwhrg { get; set; }

    public decimal? GbvwhrgKurs { get; set; }

    public DateTime? Vdatum { get; set; }

    public string? Gbustidnr { get; set; }

    public double? GbstProz2 { get; set; }

    public string? BioNr { get; set; }

    public long? Gbktnr { get; set; }

    public string? Gbname2 { get; set; }

    public string? Gbname3 { get; set; }

    public string? GbmitText1 { get; set; }

    public string? GbmitText2 { get; set; }

    public string? GbabzgText1 { get; set; }

    public string? GbabzgText2 { get; set; }

    public string? GbabzgText3 { get; set; }

    public string? GbabzgText4 { get; set; }

    public string? GbabzgText5 { get; set; }

    public string? GbabzgVz1 { get; set; }

    public string? GbabzgVz2 { get; set; }

    public string? GbabzgVz3 { get; set; }

    public string? GbabzgVz4 { get; set; }

    public string? GbabzgVz5 { get; set; }

    public decimal? GbabzgBetr1 { get; set; }

    public decimal? GbabzgBetr2 { get; set; }

    public decimal? GbabzgBetr3 { get; set; }

    public decimal? GbabzgBetr4 { get; set; }

    public decimal? GbabzgBetr5 { get; set; }

    public decimal? AnzRestOffen { get; set; }

    public long? Anzahlungsnummer { get; set; }

    public decimal? AnzNetto { get; set; }

    public decimal? AnzBrutto { get; set; }

    public double? AnzKonto { get; set; }

    public decimal? AnzStartNetto { get; set; }

    public decimal? AnzStartBrutto { get; set; }

    public string? SysUser { get; set; }

    public DateTime? SysTime { get; set; }

    public long Id { get; set; }

    public string? Gbiban { get; set; }

    public string? Gbswift { get; set; }
    public long Flags { get; private set; }
    
    public bool? Completed { get; set; } = false;

    [NotMapped]
    public InvoiceFlagsWorker FlagsWorker => new InvoiceFlagsWorker(() => Flags, value => Flags = value);

    public ICollection<Gbvhpt> Positionen { get; set; } = [];
    public Lieferanten InvoiceSupplierCurrentData { get; set; } = new();
    

    public bool IsCancellationInvoice => Gbstorno?.Equals("S") ?? false;
    public bool WasCanceled => Gbstorno?.Equals("Y") ?? false;
    public bool IsInvoiceHolderForeigner => !Gbland?.Equals("Deutschland") ?? false;
}