namespace WingCore.domain.Models;

public partial class Awg
{
    public long Wgsnr { get; set; }

    public DateTime? Wgsdatum { get; set; }

    public long Wgslfnr { get; set; }

    public string? Wgssbg { get; set; }

    public string? Wgsname1 { get; set; }

    public string? Wgsname2 { get; set; }

    public string? Wgsstrasse { get; set; }

    public string? Wgsplz { get; set; }

    public string? Wgsort { get; set; }

    public string? Wgsland { get; set; }

    public string? Wgstelefon { get; set; }

    public string? Wgslfstatus { get; set; }

    public long? WgsmaklerNr { get; set; }

    public long? WgsanlNr { get; set; }

    public string? WgsanlName2 { get; set; }

    public string? WgsanlName3 { get; set; }

    public string? Wgslkwkennz { get; set; }

    public string? WgslsNr { get; set; }

    public decimal? WgsanlGewicht { get; set; }

    public string? Wgsgkennung { get; set; }

    public string? WgsgetrArt { get; set; }

    public decimal? Wgsmenge { get; set; }

    public decimal? Wgsfeuchte { get; set; }

    public long? WgsabrNr { get; set; }

    public long? WgskontraktNr { get; set; }

    public string? WgsktNrHdP { get; set; }

    public long? WgsstreckenNr { get; set; }

    public int? WgsfilialNr { get; set; }

    public float? WgszellenNr { get; set; }

    public decimal? Wgspreis { get; set; }

    public string? WgspartieBlenr { get; set; }

    public string? WgslagerortSkp { get; set; }

    public string? Wgsempfänger { get; set; }

    public string? WgsvonGbvgesp { get; set; }

    public string? Wgsselektion { get; set; }

    public long? WgssammelNr { get; set; }

    public string? Wgsbemerkung { get; set; }

    public long? WgsartNr { get; set; }

    public string? Wgsga { get; set; }

    public decimal? Wgsvz { get; set; }

    public long? Wgsvznr { get; set; }

    public DateTime? Wgsvzdatum { get; set; }

    public string? Wgselnr { get; set; }

    public short? Wgsasld { get; set; }

    public string? WgswgNr { get; set; }

    public string? GegBlgNr { get; set; }

    public string? Wgssorte { get; set; }

    public string? Wgslkw2 { get; set; }

    public string? WgsanlOrt { get; set; }

    public string? WgsanlPlz { get; set; }

    public string? Lkw2 { get; set; }

    public string? WgschargNr { get; set; }

    public long? Wgsrmnr { get; set; }

    public int? FilialNr { get; set; }

    public string? WgsspedFax { get; set; }

    public string? WgsspedTelefon { get; set; }

    public string? WgsspedOrt { get; set; }

    public string? WgsspedPlz { get; set; }

    public string? WgsspedStrasse { get; set; }

    public string? WgsspedName2 { get; set; }

    public string? WgsspedName1 { get; set; }

    public string? WgsspedSbg { get; set; }

    public long? WgsspedNr { get; set; }

    public string? Verwieger { get; set; }

    public DateTime? Wiegezeit { get; set; }

    public bool? Nvo { get; set; }

    public string? Nvonr { get; set; }

    public bool? Nvobilanziert { get; set; }

    public string? NvobilanzNr { get; set; }

    public string? WgsktNrHandPartn { get; set; }

    public bool? LoeschKng { get; set; }

    public bool? Send { get; set; }

    public DateTime? LockLast { get; set; }

    public DateTime? LockStart { get; set; }

    public string? LockUser { get; set; }

    public string? SysUser { get; set; }

    public DateTime? SysTime { get; set; }

    public long Id { get; set; }

    public bool Wgssperr { get; set; }
}
