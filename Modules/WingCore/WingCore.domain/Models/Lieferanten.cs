namespace WingCore.domain.Models;

public partial class Lieferanten
{
    public long Lfnummer { get; set; }

    public string Lfsbg { get; set; } = string.Empty;

    public string? Lfanrede { get; set; }

    public string? Lfname1 { get; set; }

    public string? Lfname2 { get; set; }

    public string? Lfname3 { get; set; }

    public string? Lfstrasse { get; set; }

    public string? Lfplz { get; set; }

    public string? Lfpostfach { get; set; }

    public string? Lfplzpostfach { get; set; }

    public string? Lfort { get; set; }

    public string? Lfland { get; set; }

    public string? Lfbundesland { get; set; }

    public string? LfbldKng { get; set; }

    public string? Lftelefon1 { get; set; }

    public string? Lftelefon2 { get; set; }

    public string? Lftelefax { get; set; }

    public string? Lfmobil { get; set; }

    public string? Lfemail { get; set; }

    public string? Lfinternet { get; set; }

    public string? LfustIdNr { get; set; }

    public short? Lfasld { get; set; }

    public bool? Lfhdlkng { get; set; }

    public decimal? Lfzuschlag { get; set; }

    public short? LfstSchlüssel { get; set; }

    public decimal? LfstProzent { get; set; }

    public short? LfentfKlasse { get; set; }

    public bool? Lferzeugergem { get; set; }

    public long? Lfezg1 { get; set; }

    public long? Lfezg2 { get; set; }

    public long? Lfezg3 { get; set; }

    public long? Lfezg4 { get; set; }

    public long? Lfezg5 { get; set; }

    public string? Lffiliale { get; set; }

    public string? Lfstatus { get; set; }

    public long? LfvertrNr { get; set; }

    public string? LfpräfNr { get; set; }

    public string? Lfblz { get; set; }

    public string? Lfkonto { get; set; }

    public string? Lfbank { get; set; }

    public string? LfbankPlz { get; set; }

    public string? Lfswift { get; set; }

    public string? Lfiban { get; set; }

    public decimal? Lfskto1 { get; set; }

    public short? Lftage1 { get; set; }

    public decimal? Lfskto2 { get; set; }

    public short? Lftage2 { get; set; }

    public short? LfnettoTage { get; set; }

    public string? LfwhgSchl { get; set; }

    public string? Lfwhrg { get; set; }

    public decimal? LfwhrgKurs { get; set; }

    public string? Lfbemerkung { get; set; }

    public short? Lfgaw { get; set; }

    public string? LfstNr { get; set; }

    public string? LbioNr { get; set; }

    public string? LfeigKdnr { get; set; }

    public short? Lfbr1 { get; set; }

    public short? Lfbr2 { get; set; }

    public short? Lfbr3 { get; set; }

    public short? Lfbr4 { get; set; }

    public short? Lfbr5 { get; set; }

    public short? Lfbr6 { get; set; }

    public string? LfintraLand { get; set; }

    public string? LfintraKng { get; set; }

    public DateTime? Zertifizierung { get; set; }

    public bool? Lfsperr { get; set; }

    public bool? EigLager { get; set; }

    public string? Leh { get; set; }

    public short? Gbvgewicht { get; set; }

    public string? DruckForm1 { get; set; }

    public bool? Nvo { get; set; }

    public short? Nvojahr { get; set; }

    public double? Nvokm { get; set; }

    public double? Nvoco2 { get; set; }

    public string? Nvonr { get; set; }

    public bool? Nvoscheinegeschrieben { get; set; }

    public DateTime? Qszertifizierung { get; set; }

    public string? Kreis { get; set; }

    public bool? Send { get; set; }

    public string? SysUser { get; set; }

    public DateTime? SysTime { get; set; }

    public long Id { get; set; }

    public string? QszertNr { get; set; }

    public string? AnspPartn { get; set; }

    public long? KtabrStelle { get; set; }

    public string? DruckGbvform { get; set; }

    public bool? EmailVersand { get; set; }
}
