namespace WingCore.domain.Models;

public partial class LfpreisOffert
{
    public short PosNr { get; set; }

    public long Lfnr { get; set; }

    public long ArtNr { get; set; }

    public string? GetrKng { get; set; }

    public string? GetrArt { get; set; }

    public string? Sorte { get; set; }

    public DateTime? Datum { get; set; }

    public decimal? Preis1 { get; set; }

    public DateTime? Datum1 { get; set; }

    public decimal? Preis2 { get; set; }

    public DateTime? Datum2 { get; set; }

    public decimal? Preis3 { get; set; }

    public DateTime? Datum3 { get; set; }

    public decimal? Feuchte { get; set; }

    public decimal? Menge { get; set; }

    public int? MaskNr { get; set; }

    public string? Bemerkung { get; set; }

    public string? Gwert1 { get; set; }

    public string? Gwert2 { get; set; }

    public string? Gwert3 { get; set; }

    public string? Gwert4 { get; set; }

    public string? Gwert5 { get; set; }

    public string? Gwert6 { get; set; }

    public string? Gwert7 { get; set; }

    public string? Gwert8 { get; set; }

    public string? Gwert9 { get; set; }

    public string? Gwert10 { get; set; }

    public string? Gwert11 { get; set; }

    public string? Gwert12 { get; set; }

    public short? Ernte { get; set; }

    public string? Betreuer { get; set; }

    public bool? Erledigt { get; set; }

    public long Id { get; set; }
}
