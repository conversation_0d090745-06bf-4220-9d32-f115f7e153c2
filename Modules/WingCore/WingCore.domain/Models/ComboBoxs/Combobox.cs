using Commons.ComboBoxs;

namespace WingCore.domain.Models.ComboBoxs;

public record Combobox : ICombobox
{
    public long Id { get; set; }
    public string Description { get; set; } = string.Empty;
    public ComboboxType Type { get; set; } = ComboboxType.Empty;
    public string Key { get; set; } = string.Empty;
    public string Value { get; set; } = string.Empty;
    public void Reset()
    {
        Description = string.Empty;
        Key = string.Empty;
        Value = string.Empty;
        Type = ComboboxType.Empty;
    }

    public static Combobox Create(string description, ComboboxType type, string key, string value)
    {
        return new Combobox
        {
            Description = description,
            Type = type,
            Key = key,
            Value = value
        };
    }
}