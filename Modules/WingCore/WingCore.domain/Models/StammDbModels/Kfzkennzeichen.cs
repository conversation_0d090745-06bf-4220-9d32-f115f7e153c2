using System.ComponentModel.DataAnnotations.Schema;

namespace WingCore.domain.Models.StammDbModels;


[Table("KFZKennzeichen")]
public partial class Kfzkennzeichen
{
    [Column("NUTS2Code")]
    public string? Nuts2code { get; set; }

    [Column("NUTS2Name")]
    public string? Nuts2name { get; set; }

    public string? Kennzeichen { get; set; }

    [Column("ID")]
    public string? Id { get; set; }
}
