using System.ComponentModel.DataAnnotations.Schema;

namespace WingCore.domain.Models.StammDbModels;

[Table("Optionen")]
public partial class Optionen
{
    [Column("idx")]
    public string? Idx { get; set; }

    public string? Fuß { get; set; }

    public string? AutoBuch { get; set; }

    public string? Absender { get; set; }

    public string? EndText { get; set; }

    [Column("ALSOptLiefer")]
    public string? AlsoptLiefer { get; set; }

    [Column("ALSOptAuftr")]
    public string? AlsoptAuftr { get; set; }

    [Column("ALSNr")]
    public string? Alsnr { get; set; }

    [Column("ALSAnzAusdr")]
    public string? AlsanzAusdr { get; set; }

    [Column("ALSLagerakt")]
    public string? Alslagerakt { get; set; }
}
