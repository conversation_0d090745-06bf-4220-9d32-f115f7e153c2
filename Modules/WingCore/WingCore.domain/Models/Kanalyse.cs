namespace WingCore.domain.Models;

public partial class Kanalyse
{
    public long? Aartnr { get; set; }

    public long? Akdnr { get; set; }

    public string? Atext1 { get; set; }

    public string? Atext2 { get; set; }

    public string? Atext3 { get; set; }

    public string? Atext4 { get; set; }

    public string? Atext5 { get; set; }

    public string? Atext6 { get; set; }

    public string? Atext7 { get; set; }

    public string? Atext8 { get; set; }

    public string? Atext9 { get; set; }

    public string? Atext10 { get; set; }

    public string? Atext11 { get; set; }

    public string? Atext12 { get; set; }

    public string? Atext13 { get; set; }

    public string? Atext14 { get; set; }

    public string? Atext15 { get; set; }

    public string? Atext16 { get; set; }

    public string? Atext17 { get; set; }

    public string? Atext18 { get; set; }

    public string? Atext19 { get; set; }

    public string? Atext20 { get; set; }

    public string? Atext21 { get; set; }

    public string? Atext22 { get; set; }

    public string? Atext23 { get; set; }

    public string? Atext24 { get; set; }

    public string? Adesc1 { get; set; }

    public string? Adesc2 { get; set; }

    public string? Adesc3 { get; set; }

    public string? Adesc4 { get; set; }

    public string? Adesc5 { get; set; }

    public string? Adesc6 { get; set; }

    public string? Adesc7 { get; set; }

    public string? Adesc8 { get; set; }

    public string? Adesc9 { get; set; }

    public string? Adesc10 { get; set; }

    public string? Adesc11 { get; set; }

    public string? Adesc12 { get; set; }

    public string? Adesc13 { get; set; }

    public string? Adesc14 { get; set; }

    public string? Adesc15 { get; set; }

    public string? Adesc16 { get; set; }

    public string? Adesc17 { get; set; }

    public string? Adesc18 { get; set; }

    public string? Adesc19 { get; set; }

    public string? Adesc20 { get; set; }

    public string? Adesc21 { get; set; }

    public string? Adesc22 { get; set; }

    public string? Adesc23 { get; set; }

    public string? Adesc24 { get; set; }

    public float? Bvon1 { get; set; }

    public float? Bvon2 { get; set; }

    public float? Bvon3 { get; set; }

    public float? Bvon4 { get; set; }

    public float? Bvon5 { get; set; }

    public float? Bvon6 { get; set; }

    public float? Bvon7 { get; set; }

    public float? Bvon8 { get; set; }

    public float? Bvon9 { get; set; }

    public float? Bvon10 { get; set; }

    public float? Bvon11 { get; set; }

    public float? Bvon12 { get; set; }

    public float? Bvon13 { get; set; }

    public float? Bvon14 { get; set; }

    public float? Bvon15 { get; set; }

    public float? Bvon16 { get; set; }

    public float? Bvon17 { get; set; }

    public float? Bvon18 { get; set; }

    public float? Bvon19 { get; set; }

    public float? Bvon20 { get; set; }

    public float? Bvon21 { get; set; }

    public float? Bvon22 { get; set; }

    public float? Bvon23 { get; set; }

    public float? Bvon24 { get; set; }

    public float? Bbis1 { get; set; }

    public float? Bbis2 { get; set; }

    public float? Bbis3 { get; set; }

    public float? Bbis4 { get; set; }

    public float? Bbis5 { get; set; }

    public float? Bbis6 { get; set; }

    public float? Bbis7 { get; set; }

    public float? Bbis8 { get; set; }

    public float? Bbis9 { get; set; }

    public float? Bbis10 { get; set; }

    public float? Bbis11 { get; set; }

    public float? Bbis12 { get; set; }

    public float? Bbis13 { get; set; }

    public float? Bbis14 { get; set; }

    public float? Bbis15 { get; set; }

    public float? Bbis16 { get; set; }

    public float? Bbis17 { get; set; }

    public float? Bbis18 { get; set; }

    public float? Bbis19 { get; set; }

    public float? Bbis20 { get; set; }

    public float? Bbis21 { get; set; }

    public float? Bbis22 { get; set; }

    public float? Bbis23 { get; set; }

    public float? Bbis24 { get; set; }

    public int? Nk1 { get; set; }

    public int? Nk2 { get; set; }

    public int? Nk3 { get; set; }

    public int? Nk4 { get; set; }

    public int? Nk5 { get; set; }

    public int? Nk6 { get; set; }

    public int? Nk7 { get; set; }

    public int? Nk8 { get; set; }

    public int? Nk9 { get; set; }

    public int? Nk10 { get; set; }

    public int? Nk11 { get; set; }

    public int? Nk12 { get; set; }

    public int? Nk13 { get; set; }

    public int? Nk14 { get; set; }

    public int? Nk15 { get; set; }

    public int? Nk16 { get; set; }

    public int? Nk17 { get; set; }

    public int? Nk18 { get; set; }

    public int? Nk19 { get; set; }

    public int? Nk20 { get; set; }

    public int? Nk21 { get; set; }

    public int? Nk22 { get; set; }

    public int? Nk23 { get; set; }

    public int? Nk24 { get; set; }

    public string? SysUser { get; set; }

    public DateTime? SysTime { get; set; }

    public long Id { get; set; }
}
