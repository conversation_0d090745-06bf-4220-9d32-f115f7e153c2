namespace WingCore.domain.Models;

public partial class Estrecke
{
    public long? Stnr { get; set; }

    public string? <PERSON><PERSON><PERSON>uer { get; set; }

    public DateTime? Stanlagedatum { get; set; }

    public DateTime? Stauftragsdatum { get; set; }

    public bool StdispoKng { get; set; }

    public short? StabrWeg { get; set; }

    public long? StpersNrEk { get; set; }

    public string? Stname1Ek { get; set; }

    public string? Stname2Ek { get; set; }

    public string? StstrasseEk { get; set; }

    public string? Stplzek { get; set; }

    public string? StortEk { get; set; }

    public string? SttelefonEk { get; set; }

    public string? SttelefaxEk { get; set; }

    public string? StasldEk { get; set; }

    public string? StlandEk { get; set; }

    public string? StktnrHdPek { get; set; }

    public long? StktnrEk { get; set; }

    public decimal? StpreisEk { get; set; }

    public long? StartNrEk { get; set; }

    public string? StartBezEk { get; set; }

    public string? StartBez2Ek { get; set; }

    public string? StlskngEk { get; set; }

    public long? StbelStNr { get; set; }

    public string? StbelStName { get; set; }

    public string? StbelStStrasse { get; set; }

    public string? StbelStPlz { get; set; }

    public string? StbelStOrt { get; set; }

    public string? StbelStTelefon { get; set; }

    public string? StbelStTelefax { get; set; }

    public string? StbelStLand { get; set; }

    public long? StauftrNrEk { get; set; }

    public long? StrenrEk { get; set; }

    public string? StfremdLsnrEk { get; set; }

    public string? StfremdRenrEk { get; set; }

    public long? StpersNrVk { get; set; }

    public string? Stname1Vk { get; set; }

    public string? Stname2Vk { get; set; }

    public string? StstrasseVk { get; set; }

    public string? Stplzvk { get; set; }

    public string? StortVk { get; set; }

    public string? SttelefonVk { get; set; }

    public string? SttelefaxVk { get; set; }

    public string? StasldVk { get; set; }

    public string? StlandVk { get; set; }

    public string? StktnrHdPvk { get; set; }

    public long? StktnrVk { get; set; }

    public decimal? StpreisVk { get; set; }

    public long? StartNrVk { get; set; }

    public string? StartBezVk { get; set; }

    public string? StartBez2Vk { get; set; }

    public string? StlskngVk { get; set; }

    public long? StablStNr { get; set; }

    public string? StablStName { get; set; }

    public string? StablStStrasse { get; set; }

    public string? StablStPlz { get; set; }

    public string? StablStOrt { get; set; }

    public string? StablStTelefon { get; set; }

    public string? StablStTelefax { get; set; }

    public string? StablStLand { get; set; }

    public long? StauftrNrVk { get; set; }

    public long? StrenrVk { get; set; }

    public string? StfremdLsnrVk { get; set; }

    public string? StfremdRenrVk { get; set; }

    public DateTime? StverladeTag { get; set; }

    public double? Stmenge { get; set; }

    public double? Stlkwgew { get; set; }

    public DateTime? StabladeTag { get; set; }

    public double? StabladeMenge { get; set; }

    public string? Stlkwkennz { get; set; }

    public long? StspedNr { get; set; }

    public string? StspedName { get; set; }

    public string? StspedStrasse { get; set; }

    public string? StspedPlz { get; set; }

    public string? StspedOrt { get; set; }

    public string? StspedTelefon { get; set; }

    public string? StspedTelefax { get; set; }

    public string? StspedLand { get; set; }

    public long? StartNrFracht { get; set; }

    public decimal? StfrachtPreis { get; set; }

    public long? StfrachtSchNr { get; set; }

    public DateTime? StdatumBis { get; set; }

    public bool Send { get; set; }

    public string? Stlkwkennz2 { get; set; }

    public string? Stlkwkammer1 { get; set; }

    public string? Stlkwkammer2 { get; set; }

    public string? Stlkwkammer3 { get; set; }

    public string? Stlkwkammer4 { get; set; }

    public string? Stlkwkammer5 { get; set; }

    public string? Stlkwkammer6 { get; set; }

    public string? Stlkwkammer7 { get; set; }

    public string? Stlkwkammer8 { get; set; }

    public string? Stlkwkammer9 { get; set; }

    public string? Stlkwkammer10 { get; set; }

    public string? StbemEk { get; set; }

    public string? StbemVk { get; set; }

    public string? StbemBelSt { get; set; }

    public string? StbemAblSt { get; set; }

    public string? StbemSped { get; set; }

    public bool EigLagerEk { get; set; }

    public bool EigLagerVk { get; set; }

    public bool Lkw { get; set; }

    public string? StspedName2 { get; set; }

    public string? StspedName3 { get; set; }

    public string? StfrSpedNr { get; set; }

    public bool EigWare { get; set; }

    public decimal? ZusKost { get; set; }

    public string? SysUser { get; set; }

    public DateTime? SysTime { get; set; }

    public long Id { get; set; }

    public decimal? GesMenge { get; set; }
}
