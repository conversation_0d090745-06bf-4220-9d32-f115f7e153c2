namespace WingCore.domain.Models;
public partial class ArtIdStamm
{
    public long ArtIdNr { get; set; }

    public long ArtIdArtNr { get; set; }

    public string? ArtIdMatch { get; set; }

    public string? ArtIdBez1 { get; set; }

    public string? ArtIdBez2 { get; set; }

    public string? ArtIdBez3 { get; set; }

    public string? ArtIdBez4 { get; set; }

    public string? ArtIdBem { get; set; }

    public DateTime? ArtIdDatum { get; set; }

    public string? SysUser { get; set; }

    public DateTime? SysTime { get; set; }

    public long Id { get; set; }
}
