namespace WingCore.domain.Models;

public partial class ArtIdProz
{
    public long? ArtIdNr { get; set; }

    public int? ArtIdPos { get; set; }

    public long? ArtIdArtNr { get; set; }

    public string? ArtIdBez1 { get; set; }

    public string? ArtIdBez2 { get; set; }

    public string? ArtIdEh { get; set; }

    public decimal? ArtIdSatz { get; set; }

    public string? ArtIdChgNr { get; set; }

    public long Id { get; set; }
}
