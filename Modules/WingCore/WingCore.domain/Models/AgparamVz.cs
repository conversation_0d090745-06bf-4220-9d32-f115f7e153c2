namespace WingCore.domain.Models;

public partial class AgparamVz
{
    public short? PosNr { get; set; }

    public string? Tk { get; set; }

    public string? Ft { get; set; }

    public decimal? Preis { get; set; }

    public string? Laborpos1 { get; set; }

    public string? Laborpos2 { get; set; }

    public string? Laborpos3 { get; set; }

    public string? Laborpos4 { get; set; }

    public string? Laborpos5 { get; set; }

    public string? Laborpos6 { get; set; }

    public string? Laborpos7 { get; set; }

    public string? Laborpos8 { get; set; }

    public string? Laborpos9 { get; set; }

    public string? Laborpos10 { get; set; }

    public string? Laborpos11 { get; set; }

    public string? Laborpos12 { get; set; }

    public decimal? Faktor1 { get; set; }

    public decimal? Faktor2 { get; set; }

    public decimal? Faktor3 { get; set; }

    public decimal? Faktor4 { get; set; }

    public decimal? Faktor5 { get; set; }

    public decimal? Faktor6 { get; set; }

    public decimal? Faktor7 { get; set; }

    public decimal? Faktor8 { get; set; }

    public decimal? Faktor9 { get; set; }

    public decimal? Faktor10 { get; set; }

    public decimal? Faktor11 { get; set; }

    public decimal? Faktor12 { get; set; }

    public decimal? Faktor13 { get; set; }

    public decimal? Faktor14 { get; set; }

    public decimal? Faktor15 { get; set; }

    public decimal? Faktor16 { get; set; }

    public decimal? Faktor17 { get; set; }

    public decimal? Faktor18 { get; set; }

    public decimal? Faktor19 { get; set; }

    public decimal? Faktor20 { get; set; }

    public decimal? Faktor21 { get; set; }

    public decimal? Faktor22 { get; set; }

    public decimal? Faktor23 { get; set; }

    public decimal? Faktor24 { get; set; }

    public decimal? Faktor25 { get; set; }

    public decimal? Faktor26 { get; set; }

    public decimal? Faktor27 { get; set; }

    public decimal? Faktor28 { get; set; }

    public decimal? Faktor29 { get; set; }

    public decimal? Faktor30 { get; set; }

    public decimal? Faktor31 { get; set; }

    public decimal? Faktor32 { get; set; }

    public decimal? Faktor33 { get; set; }

    public decimal? Faktor34 { get; set; }

    public decimal? Faktor35 { get; set; }

    public decimal? Faktor36 { get; set; }

    public long Id { get; set; }
}
