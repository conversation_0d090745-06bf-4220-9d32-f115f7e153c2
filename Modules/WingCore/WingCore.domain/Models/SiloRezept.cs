namespace WingCore.domain.Models;

public partial class SiloRezept
{
    public int? RezeptNr { get; set; }

    public string? Bezeichnung { get; set; }

    public int? SiloNr1 { get; set; }

    public int? SiloNr2 { get; set; }

    public int? SiloNr3 { get; set; }

    public int? SiloNr4 { get; set; }

    public int? SiloNr5 { get; set; }

    public int? SiloNr6 { get; set; }

    public int? SiloNr7 { get; set; }

    public int? SiloNr8 { get; set; }

    public int? SiloNr9 { get; set; }

    public int? SiloNr10 { get; set; }

    public long Id { get; set; }
}
