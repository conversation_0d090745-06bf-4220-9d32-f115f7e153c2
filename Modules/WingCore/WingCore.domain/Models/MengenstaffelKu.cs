namespace WingCore.domain.Models;

public partial class MengenstaffelKu
{
    public long? KundenNr { get; set; }

    public long? ArtikelNr { get; set; }

    public int? Staffel1 { get; set; }

    public int? Staffel2 { get; set; }

    public int? Staffel3 { get; set; }

    public int? Staffel4 { get; set; }

    public int? Staffel5 { get; set; }

    public int? Staffel6 { get; set; }

    public decimal? Bstaffel1 { get; set; }

    public decimal? Bstaffel2 { get; set; }

    public decimal? Bstaffel3 { get; set; }

    public decimal? Bstaffel4 { get; set; }

    public decimal? Bstaffel5 { get; set; }

    public decimal? Bstaffel6 { get; set; }

    public int? Staffeltyp { get; set; }

    public string? SysUser { get; set; }

    public DateTime? SysTime { get; set; }

    public long Id { get; set; }
}
