namespace WingCore.domain.Models;

public partial class KontrZu
{
    public long? Ktnr { get; set; }

    public int? PosNr { get; set; }

    public string? Ktwrname { get; set; }

    public float? Ktwrbasis { get; set; }

    public float? Ktwrmin { get; set; }

    public float? Ktwrmax { get; set; }

    public float? Ktwrstossgr { get; set; }

    public float? Ktwrps { get; set; }

    public float? Ktwrxx { get; set; }

    public long Id { get; set; }

    public long? PktrNr { get; set; }
}
