namespace WingCore.domain.Models;

public partial class FbezKonto
{
    public long Renr { get; set; }

    public long? ArtikelNr { get; set; }

    public long? KundNr { get; set; }

    public string? VgArt { get; set; }

    public long? ScheinNr { get; set; }

    public decimal? Menge { get; set; }

    public decimal? Preis { get; set; }

    public DateTime? Datum { get; set; }

    public long? AbrNr { get; set; }

    public short? FilialNr { get; set; }

    public short? <PERSON><PERSON><PERSON><PERSON> { get; set; }

    public bool? Erledigt { get; set; }

    public string? SysUser { get; set; }

    public DateTime? SysTime { get; set; }

    public long Id { get; set; }
}
