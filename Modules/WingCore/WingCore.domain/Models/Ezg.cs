namespace WingCore.domain.Models;

public partial class Ezg
{
    public long Ezgnr { get; set; }

    public string Ezgsbg { get; set; } = null!;

    public string? Ezganrede { get; set; }

    public string? Ezgname1 { get; set; }

    public string? Ezgname2 { get; set; }

    public string? Ezgname3 { get; set; }

    public string? Ezgstrasse { get; set; }

    public string? Ezgplz { get; set; }

    public string? Ezgort { get; set; }

    public string? Ezgland { get; set; }

    public string? Ezgtelefon1 { get; set; }

    public string? Ezgtelefon2 { get; set; }

    public string? Ezgfax { get; set; }

    public string? Ezgemail { get; set; }

    public string? Ezgkng1 { get; set; }

    public string? Ezgkng2 { get; set; }

    public string? Ezgkng3 { get; set; }

    public string? Ezgkng4 { get; set; }

    public string? Ezgkng5 { get; set; }

    public string? Ezgkng6 { get; set; }

    public string? Ezgkng7 { get; set; }

    public string? Ezgkng8 { get; set; }

    public string? Ezgkng9 { get; set; }

    public string? Ezgkng10 { get; set; }

    public string? Ezgkng11 { get; set; }

    public decimal? Ezgzuschlag { get; set; }

    public decimal? Ezgbeitrag { get; set; }

    public string? Ezgkonto { get; set; }

    public string? Ezgbem { get; set; }

    public bool Ezgcheck1 { get; set; }

    public bool Ezgcheck2 { get; set; }

    public bool Ezgcheck3 { get; set; }

    public bool Ezgcheck4 { get; set; }

    public string? Ezgfiliale { get; set; }

    public string? EzgfilName { get; set; }

    public string? Ezgwährung { get; set; }

    public string? EzgwgBez { get; set; }

    public string? SysUser { get; set; }

    public DateTime? SysTime { get; set; }

    public long Id { get; set; }
}
