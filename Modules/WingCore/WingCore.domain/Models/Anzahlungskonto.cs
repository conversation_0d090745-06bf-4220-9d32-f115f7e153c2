namespace WingCore.domain.Models;

public partial class Anzahlungskonto
{
    public int? AnzPosNr { get; set; }

    public long AnzReNr { get; set; }

    public long? AnzLfnr { get; set; }

    public long? AnzLfsnr { get; set; }

    public long? AnzArtNr { get; set; }

    public DateTime? AnzDatum { get; set; }

    public decimal? AnzStartGuthaben { get; set; }

    public decimal? AnzAktGuthaben { get; set; }

    public decimal? AnzUst { get; set; }

    public decimal? AnzBrutto { get; set; }

    public int? AnzKonto { get; set; }

    public bool? AnzEingang { get; set; }

    public bool? Storno { get; set; }

    public string? SysUser { get; set; }

    public DateTime? SysTime { get; set; }

    public long Id { get; set; }
}
