using System.ComponentModel.DataAnnotations.Schema;
using WingCore.domain.Common.Flags;

namespace WingCore.domain.Models
{
    public partial class Wg
    {
        public long Wgsnr { get; set; }                        // [WGSNr] bigint NOT NULL
        public DateTime? Wgsdatum { get; set; }                // [WGSDatum] datetime NULL
        public long? Wgslfnr { get; set; }                     // [WGSLFNr] bigint NULL
        public string? Wgssbg { get; set; }                    // [WGSSBG] nvarchar(10) NULL
        public string? Wgsname1 { get; set; }                  // [WGSName1] nvarchar(30) NULL
        public string? Wgsname2 { get; set; }                  // [WGSName2] nvarchar(30) NULL
        public string? Wgsstrasse { get; set; }                // [WGSStrasse] nvarchar(30) NULL
        public string? Wgsplz { get; set; }                    // [WGSPLZ] nvarchar(10) NULL
        public string? Wgsort { get; set; }                    // [WGSOrt] nvarchar(30) NULL
        public string? Wgsland { get; set; }                   // [WGSLand] nvarchar(30) NULL
        public string? Wgstelefon { get; set; }                // [WGSTelefon] nvarchar(20) NULL
        public string? Wgslkwkennz { get; set; }               // [WGSLKWKennz] nvarchar(10) NULL
        public string? Wgslkw2 { get; set; }                   // [WGSLKW2] nvarchar(15) NULL
        public string? WgslsNr { get; set; }                   // [WGSLsNr] nvarchar(20) NULL
        public decimal? WgsanlGewicht { get; set; }            // [WGSAnlGewicht] money NULL
        public string? Wgsgkennung { get; set; }               // [WGSGKennung] nvarchar(3) NULL
        public string? WgsgetrArt { get; set; }                // [WGSGetrArt] nvarchar(18) NULL
        public long? WgsartNr { get; set; }                    // [WGSArtNr] bigint NULL
        public string? Wgssorte { get; set; }                  // [WGSSorte] nvarchar(20) NULL
        public decimal? Wgsmenge { get; set; }                 // [WGSMenge] money NULL
        public long? WgsanlNr { get; set; }                    // [WGSAnlNr] bigint NULL
        public string? WgsanlName2 { get; set; }               // [WGSAnlName2] nvarchar(30) NULL
        public string? WgsanlName3 { get; set; }               // [WGSAnlName3] nvarchar(30) NULL
        public string? WgsanlPlz { get; set; }                 // [WGSAnlPLZ] nvarchar(6) NULL
        public string? WgsanlOrt { get; set; }                 // [WGSAnlOrt] nvarchar(30) NULL
        public string? Wgslfstatus { get; set; }               // [WGSLFStatus] nvarchar(10) NULL
        public string? Wgsasld { get; set; }                   // [WGSAsld] nvarchar(2) NULL
        public decimal? Wgsfeuchte { get; set; }               // [WGSFeuchte] money NULL
        public int? WgszellenNr { get; set; }                  // [WGSZellenNr] int NULL
        public string? Verwieger { get; set; }                 // [Verwieger] nvarchar(15) NULL
        public string? WgswgNr { get; set; }                   // [WGSWgNr] nvarchar(15) NULL
        public DateTime? Wiegezeit { get; set; }               // [Wiegezeit] datetime NULL
        public decimal? WiegeGew { get; set; }                 // [WiegeGew] money NULL
        public long? WgsabrNr { get; set; }                    // [WGSAbrNr] bigint NULL
        public decimal? Wgspreis { get; set; }                 // [WGSPreis] money NULL
        public string? Wgsga { get; set; }                     // [WGSGA] nvarchar(1) NULL
        public decimal? Wgsvz { get; set; }                    // [WGSVZ] money NULL
        public long? Wgsvznr { get; set; }                     // [WGSVZNr] bigint NULL
        public DateTime? Wgsvzdatum { get; set; }              // [WGSVZDatum] datetime NULL
        public string? Wgselnr { get; set; }                   // [WGSELNR] nvarchar(4) NULL
        public long? Wgsrmnr { get; set; }                     // [WGSRMNr] bigint NULL
        public string? WgschargNr { get; set; }                // [WGSChargNr] nvarchar(20) NULL
        public string? GegBlgNr { get; set; }                  // [GegBlgNr] nvarchar(15) NULL
        public long? WgskontraktNr { get; set; }               // [WGSKontraktNr] bigint NULL
        public string? WgsmaklerNr { get; set; }               // [WGSMaklerNr] nvarchar(10) NULL
        public string? WgsktNrHdP { get; set; }                // [WGSKtNrHdP] nvarchar(20) NULL
        public long? WgsstreckenNr { get; set; }               // [WGSStreckenNr] bigint NULL
        public string? Wgsbemerkung { get; set; }              // [WGSBemerkung] nvarchar(max) NULL
        public long? WgsspedNr { get; set; }                   // [WGSSpedNr] bigint NULL
        public string? WgsspedSbg { get; set; }                // [WGSSpedSBG] nvarchar(10) NULL
        public string? WgsspedName1 { get; set; }              // [WGSSpedName1] nvarchar(30) NULL
        public string? WgsspedName2 { get; set; }              // [WGSSpedName2] nvarchar(30) NULL
        public string? WgsspedStrasse { get; set; }            // [WGSSpedStrasse] nvarchar(30) NULL
        public string? WgsspedPlz { get; set; }                // [WGSSpedPLZ] nvarchar(10) NULL
        public string? WgsspedOrt { get; set; }                // [WGSSpedOrt] nvarchar(30) NULL
        public string? WgsspedTelefon { get; set; }            // [WGSSpedTelefon] nvarchar(20) NULL
        public string? WgsspedFax { get; set; }                // [WGSSpedFax] nvarchar(20) NULL
        public string? Wgsvorfracht1 { get; set; }             // [WGSVorfracht1] nvarchar(50) NULL
        public string? Wgsvorfracht2 { get; set; }             // [WGSVorfracht2] nvarchar(50) NULL
        public string? Wgsvorfracht3 { get; set; }             // [WGSVorfracht3] nvarchar(50) NULL
        public string? WgsbemLf { get; set; }                  // [WGSBemLF] nvarchar(max) NULL
        public string? WgsbemZus { get; set; }                 // [WGSBemZus] nvarchar(max) NULL
        public string? WgspartieBlenr { get; set; }            // [WGSPartieBLENr] nvarchar(20) NULL
        public string? WgslagerortSkp { get; set; }            // [WGSLagerortSKP] nvarchar(10) NULL
        public string? Wgsempfänger { get; set; }              // [WGSEmpfänger] nvarchar(20) NULL
        public string? WgsvonGbvgesp { get; set; }             // [WGSVonGBVgesp] nvarchar(1) NULL
        public string? Wgsselektion { get; set; }              // [WGSSelektion] nvarchar(1) NULL
        public long? WgssammelNr { get; set; }                 // [WGSSammelNr] bigint NULL
        public bool? Wgsqc { get; set; }                       // [WGSQC] bit NULL
        public bool? Sammelschein { get; set; }                // [Sammelschein] bit NULL
        public bool? SammelStorno { get; set; }                // [SammelStorno] bit NULL
        public bool? LoeschKng { get; set; }                   // [LoeschKng] bit NULL
        public bool? EigenWare { get; set; }                   // [EigenWare] bit NULL
        public bool? Send { get; set; }                        // [Send] bit NULL
        public bool? Wgssperr { get; set; }                    // [WGSSperr] bit NULL
        public bool? Nvo { get; set; }                         // [NVO] bit NULL
        public string? Nvonr { get; set; }                     // [NVONr] nvarchar(30) NULL
        public bool? Nvobilanziert { get; set; }              // [NVOBilanziert] bit NULL
        public string? NvobilanzNr { get; set; }              // [NVOBilanzNr] nvarchar(30) NULL
        public int? FilialNr { get; set; }                     // [FilialNr] int NULL
        public string? Nuts2 { get; set; }                     // [NUTS2] nvarchar(5) NULL
        public decimal? Thgwert { get; set; }                  // [THGWert] money NULL
        public decimal? Thgges { get; set; }                   // [THGGes] money NULL
        public DateTime? LockLast { get; set; }                // [LockLast] datetime NULL
        public DateTime? LockStart { get; set; }               // [LockStart] datetime NULL
        public string? LockUser { get; set; }                  // [LockUser] nvarchar(50) NULL
        public string? SysUser { get; set; }                   // [SysUser] nvarchar(50) NULL
        public DateTime? SysTime { get; set; }                 // [SysTime] datetime NULL
        public long Id { get; set; }                           // [ID] bigint NOT NULL (IDENTITY)
        public string? Nuts2code { get; set; }                 // [NUTS2Code] nvarchar(5) NULL
        public decimal? Thgsumm { get; set; }                  // [THGSumm] money NULL
        public bool? Qmg { get; set; }                         // [QMG] bit NULL
        public bool? Reinigung { get; set; }                   // [Reinigung] bit NULL
        public long? PktrNr { get; set; }                      // [PKtrNr] bigint NULL
        public bool? DepKng { get; set; }                      // [DepKng] bit NULL
        public long Flags { get; set; }                        // [Flags] bigint NOT NULL 

        [NotMapped]
        public WgsFlagsWorker FlagsWorker => new(() => Flags, value => Flags = value);
    }
}