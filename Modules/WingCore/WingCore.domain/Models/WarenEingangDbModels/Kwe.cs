namespace WingCore.domain.Models.WarenEingangDbModels;

public class Kwe
{
    public long ID { get; set; }
    public long KWEWgsNr { get; set; }
    public DateTime KWEDatum { get; set; }
    public long KWELFNr { get; set; }
    public string KWELFSBG { get; set; } = string.Empty;
    public string? KWELFAnrede { get; set; } 
    public string? KWELFName1 { get; set; } 
    public string? KWELFName2 { get; set; } 
    public string? KWELFStrasse { get; set; } 
    public string? KWELFPLZ { get; set; } 
    public string? KWELFOrt { get; set; } 
    public string? KWELFTel { get; set; } 
    public string? KWELFFax { get; set; } 
    public string? KWELFLand { get; set; } 
    public short? KWELFBLD { get; set; } 
    public string? KWELFStatus { get; set; } 
    public string? KWEMaklerNr { get; set; } 
    public string? KWEAnlNr { get; set; } 
    public string? KWEAnlSBG { get; set; } 
    public string? KWEAnlAnrede { get; set; } 
    public string? KWEAnlName1 { get; set; } 
    public string? KWEAnlName2 { get; set; } 
    public string? KWEAnlStrasse { get; set; } 
    public string? KWEAnlPLZ { get; set; } 
    public string? KWEAnlOrt { get; set; } 
    public string? KWEAnlTel { get; set; } 
    public string? KWEAnlFax { get; set; } 
    public string? KWEAnlLand { get; set; } 
    public short? KWEAnlBLD { get; set; } 
    public string? KWEZustellart { get; set; } 
    public string? KWELSNr { get; set; } 
    public decimal? KWEAnlGewicht { get; set; } 
    public string? KWEZelle { get; set; } 
    public long? KWEKontraktNr { get; set; } 
    public string? KWENrHdlsP { get; set; } 
    public decimal? KWEKTPreis { get; set; } 
    public string? KWEBLENr { get; set; } 
    public string? KWESachkPers { get; set; } 
    public long KWEArtNr { get; set; }
    public string KWEGetrKng { get; set; } = string.Empty;
    public string KWEGetrArt { get; set; } = string.Empty;
    public decimal? KWEPreis { get; set; } 
    public decimal? KWEFeuchte { get; set; } 
    public decimal? KWENetto { get; set; } 
    public string? KWEWgNr { get; set; } 
    public DateTime? KWEWaagenZeit { get; set; } 
    public short KWEHofliste { get; set; }
    public string? KWEAbrKng { get; set; } 
    public string? KWEUebergKng { get; set; } 
    public short? KWEArtPar { get; set; } 
    public string? KWEBem { get; set; } 
    public string? KWEManuell { get; set; } 
    public decimal? KWESiloGew { get; set; } 
    public long? KWESpNr { get; set; } 
    public string? KWESpSBG { get; set; } 
    public string? KWESpName1 { get; set; } 
    public string? KWESpName2 { get; set; } 
    public string? KWESpStrasse { get; set; } 
    public string? KWESpPLZ { get; set; } 
    public string? KWESpOrt { get; set; } 
    public string? KWESpTel { get; set; } 
    public string? KWESpFax { get; set; } 
    public long? KWERMNr { get; set; } 
    public decimal? KWEZweiteMenge { get; set; } 
    public decimal? KWEGew2 { get; set; } 
    public string? KWEWgNr2 { get; set; } 
    public DateTime? KWEWaagenZeit2 { get; set; } 
    public long? KWELiefNr { get; set; } 
    public string? KWESorte { get; set; } 
    public bool? QMG { get; set; } 
    public string? ZusTxtInh1 { get; set; } 
    public string? ZusTxtInh2 { get; set; } 
    public string? ZusTxtInh3 { get; set; } 
    public string? ZusTxtInh4 { get; set; } 
    public string? ZusTxtInh5 { get; set; } 
    public string? ZusTxtInh6 { get; set; } 
    public string? ZusTxtBez1 { get; set; } 
    public string? ZusTxtBez2 { get; set; } 
    public string? ZusTxtBez3 { get; set; } 
    public string? ZusTxtBez4 { get; set; } 
    public string? ZusTxtBez5 { get; set; } 
    public string? ZusTxtBez6 { get; set; } 
    public string? Vorfracht1 { get; set; } 
    public string? Vorfracht2 { get; set; } 
    public string? Vorfracht3 { get; set; } 
    public string? LKW2 { get; set; } 
    public string? ChargNr { get; set; } 
    public DateTime? KWEWGSDatum { get; set; } 
    public string? BemWE { get; set; } 
    public string? BemZus { get; set; } 
    public bool? KWEQC { get; set; } 
    public short? FilialNr { get; set; } 
    public short? KWEWaagNr { get; set; } 
    public short? MandNr { get; set; } 
    public long? RMNr2 { get; set; } 
    public long? RMNr3 { get; set; } 
    public decimal? KWEGew3 { get; set; } 
    public string? KWEWgNr3 { get; set; } 
    public DateTime? KWEWaagenZeit3 { get; set; } 
    public string? KWETSilo1 { get; set; } 
    public string? KWETSilo2 { get; set; } 
    public bool? Freigabe { get; set; } 
    public string? ChargNr2 { get; set; } 
    public decimal? KWEAbrGew { get; set; } 
    public decimal? KWEabzGewFeuchte { get; set; } 
    public bool? Reinigung { get; set; } 
    public bool? NVO { get; set; } 
    public string? NVONr { get; set; } 
    public long? FossNr { get; set; } 
    public string? SysUser { get; set; } 
    public DateTime? SysTime { get; set; } 
    public DateTime? LockLast { get; set; } 
    public DateTime? LockStart { get; set; } 
    public string? LockUser { get; set; } 
    public bool LoeschKng { get; set; }
    public string BioNr { get; set; } = string.Empty;
    public string? Zelle2 { get; set; }
    public string SiloText { get; set; } = string.Empty;
    public string Kreis { get; set; } = string.Empty;
    public string KWEMail { get; set; } = string.Empty;
    public bool DispoKng { get; set; }
}
