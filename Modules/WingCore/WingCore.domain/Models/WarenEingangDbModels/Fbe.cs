using System.ComponentModel.DataAnnotations.Schema;

namespace WingCore.domain.Models.WarenEingangDbModels;

public class FBE
{
    public long Id { get; set; }
    public long FBEWgsNr { get; set; }
    public DateTime FBEDatum { get; set; }
    public long FBELFNr { get; set; }
    public string? FBELFName1 { get; set; }
    public string? FBELFName2 { get; set; }
    public long FBEArtNr { get; set; }
    public decimal? FBEBrutto { get; set; }
    public decimal? FBETara { get; set; }
    public decimal? FBENetto { get; set; }
    
    public decimal? FBEWgNr1 { get; set; }
    public DateTime? FBEWgTime1 { get; set; }
    public decimal? FBEWgNr2 { get; set; }
    public DateTime? FBEWgTime2 { get; set; }
    public decimal? FBEZelle { get; set; }
    
}