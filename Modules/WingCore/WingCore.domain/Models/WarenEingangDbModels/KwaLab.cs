namespace WingCore.domain.Models.WarenEingangDbModels;

public class KWALab
{
	public long ID { get; set; }
    public long WgsNr { get; set; }
    public string? LabN1 { get; set; }
    public string? LabN2 { get; set; }
    public string? LabN3 { get; set; }
    public string? LabN4 { get; set; }
    public string? LabN5 { get; set; }
    public string? LabN6 { get; set; }
    public string? LabN7 { get; set; }
    public string? LabN8 { get; set; }
    public string? LabN9 { get; set; }
    public string? LabN10 { get; set; }
    public string? LabN11 { get; set; }
    public string? LabN12 { get; set; }
    public string? LabEh1 { get; set; }
    public string? LabEh2 { get; set; }
    public string? LabEh3 { get; set; }
    public string? LabEh4 { get; set; }
    public string? LabEh5 { get; set; }
    public string? LabEh6 { get; set; }
    public string? LabEh7 { get; set; }
    public string? LabEh8 { get; set; }
    public string? LabEh9 { get; set; }
    public string? LabEh10 { get; set; }
    public string? LabEh11 { get; set; }
    public string? LabEh12 { get; set; }
    public string? LabWert1 { get; set; }
    public string? LabWert2 { get; set; }
    public string? LabWert3 { get; set; }
    public string? LabWert4 { get; set; }
    public string? LabWert5 { get; set; }
    public string? LabWert6 { get; set; }
    public string? LabWert7 { get; set; }
    public string? LabWert8 { get; set; }
    public string? LabWert9 { get; set; }
    public string? LabWert10 { get; set; }
    public string? LabWert11 { get; set; }
    public string? LabWert12 { get; set; }
    public decimal? AbzGew1 { get; set; }
    public decimal? AbzGew2 { get; set; }
    public decimal? AbzGew3 { get; set; }
    public decimal? AbzGew4 { get; set; }
    public decimal? AbzGew5 { get; set; }
    public decimal? AbzGew6 { get; set; }
    public decimal? AbzGew7 { get; set; }
    public decimal? AbzGew8 { get; set; }
    public decimal? AbzGew9 { get; set; }
    public decimal? AbzGew10 { get; set; }
    public decimal? AbzGew11 { get; set; }
    public decimal? AbzGew12 { get; set; }
    public string? KTWert0 { get; set; }
    public string? KTWert1 { get; set; }
    public string? KTWert2 { get; set; }
    public string? KTWert3 { get; set; }
    public string? KTWert4 { get; set; }
    public string? KTWert5 { get; set; }
    public string? KTWert6 { get; set; }
    public string? KTWert7 { get; set; }
    public string? KTWert8 { get; set; }
    public string? KTWert9 { get; set; }
    public string? KTWert10 { get; set; }
    public string? KTWert11 { get; set; }
    public string? KTWert12 { get; set; }
    public bool? KTOK0 { get; set; }
    public bool? KTOK1 { get; set; }
    public bool? KTOK2 { get; set; }
    public bool? KTOK3 { get; set; }
    public bool? KTOK4 { get; set; }
    public bool? KTOK5 { get; set; }
    public bool? KTOK6 { get; set; }
    public bool? KTOK7 { get; set; }
    public bool? KTOK8 { get; set; }
    public bool? KTOK9 { get; set; }
    public bool? KTOK10 { get; set; }
    public bool? KTOK11 { get; set; }
    public bool? KTOK12 { get; set; }
}
