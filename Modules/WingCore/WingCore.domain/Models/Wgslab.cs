namespace WingCore.domain.Models;

public partial class Wgslab
{
    public long Wgsnr { get; set; }

    public string? WgslabBez1 { get; set; }

    public string? WgslabBez2 { get; set; }

    public string? WgslabBez3 { get; set; }

    public string? WgslabBez4 { get; set; }

    public string? WgslabBez5 { get; set; }

    public string? WgslabBez6 { get; set; }

    public string? WgslabBez7 { get; set; }

    public string? WgslabBez8 { get; set; }

    public string? WgslabBez9 { get; set; }

    public string? WgslabBez10 { get; set; }

    public string? WgslabBez11 { get; set; }

    public string? WgslabBez12 { get; set; }

    public string? WgslabWert1 { get; set; }

    public string? WgslabWert2 { get; set; }

    public string? WgslabWert3 { get; set; }

    public string? WgslabWert4 { get; set; }

    public string? WgslabWert5 { get; set; }

    public string? WgslabWert6 { get; set; }

    public string? WgslabWert7 { get; set; }

    public string? WgslabWert8 { get; set; }

    public string? WgslabWert9 { get; set; }

    public string? WgslabWert10 { get; set; }

    public string? WgslabWert11 { get; set; }

    public string? WgslabWert12 { get; set; }

    public string? WgslabEh1 { get; set; }

    public string? WgslabEh2 { get; set; }

    public string? WgslabEh3 { get; set; }

    public string? WgslabEh4 { get; set; }

    public string? WgslabEh5 { get; set; }

    public string? WgslabEh6 { get; set; }

    public string? WgslabEh7 { get; set; }

    public string? WgslabEh8 { get; set; }

    public string? WgslabEh9 { get; set; }

    public string? WgslabEh10 { get; set; }

    public string? WgslabEh11 { get; set; }

    public string? WgslabEh12 { get; set; }

    public string? ZusTxtInh1 { get; set; }

    public string? ZusTxtInh2 { get; set; }

    public string? ZusTxtInh3 { get; set; }

    public string? ZusTxtInh4 { get; set; }

    public string? ZusTxtInh5 { get; set; }

    public string? ZusTxtInh6 { get; set; }

    public string? ZusTxtBez1 { get; set; }

    public string? ZusTxtBez2 { get; set; }

    public string? ZusTxtBez3 { get; set; }

    public string? ZusTxtBez4 { get; set; }

    public string? ZusTxtBez5 { get; set; }

    public string? ZusTxtBez6 { get; set; }

    public long Id { get; set; }
}
