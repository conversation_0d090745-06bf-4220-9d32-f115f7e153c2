namespace WingCore.domain.Models;

public partial class Dbfstat
{
    public DateTime? Dbfdatum { get; set; }

    public DateTime? Dbfredatum { get; set; }

    public long? Dbfartikelnummer { get; set; }

    public long? Dbfkdnrre { get; set; }

    public long? Dbfkdnrwe { get; set; }

    public decimal? Dbfnetto { get; set; }

    public decimal? Dbfmenge { get; set; }

    public decimal? Dbfstck { get; set; }

    public long? Dbfbelegnr { get; set; }

    public string? DbfursprBelegnr { get; set; }

    public string? Dbfvorfall { get; set; }

    public decimal? Dbfekpreis { get; set; }

    public decimal? Dbfvkpreis { get; set; }

    public long? DbfalsNr { get; set; }

    public long? DbfwgsNr { get; set; }

    public long? DbfelsNr { get; set; }

    public short? Dbfbundesland { get; set; }

    public short? Dbfartikelgruppe { get; set; }

    public long? Dbfkdnrrs { get; set; }

    public string? Dbfwgkng { get; set; }

    public short? Dbfvtvstaffel { get; set; }

    public long? Dbfvtvnr { get; set; }

    public string? Dbfstorno { get; set; }

    public short? Dbffaktor { get; set; }

    public string? Dbffiliale { get; set; }

    public string? Dbfzustellart { get; set; }

    public long? Dbfkontraktnr { get; set; }

    public decimal? DbfanlGewWgs { get; set; }

    public string? Dbfstreckennr { get; set; }

    public long? DbfumlArtikel { get; set; }

    public short? Dbfkdgrpre { get; set; }

    public short? Dbfkdgrpwe { get; set; }

    public short? Dbfkdgrprs { get; set; }

    public string? DbfwhgKn { get; set; }

    public decimal? Dbfkurs { get; set; }

    public string? DbfartKtoKn { get; set; }

    public short? DbffaktorWert { get; set; }

    public DateTime? Dbflfdatum { get; set; }

    public long? Dbfkdnrcl { get; set; }

    public short? Dbfkdgrpcl { get; set; }

    public string? DbfprovArt { get; set; }

    public decimal? DbfprovSatz { get; set; }

    public short? DbfkaKu { get; set; }

    public string? DbfgetrKng { get; set; }

    public long Id { get; set; }
}
