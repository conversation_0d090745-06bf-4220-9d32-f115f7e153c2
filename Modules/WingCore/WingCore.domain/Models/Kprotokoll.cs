namespace WingCore.domain.Models;

public partial class Kprotokoll
{
    public long? Ktnr { get; set; }

    public short? Kplkennung { get; set; }

    public DateTime? Kpldatum { get; set; }

    public string? Kplentnahmetext { get; set; }

    public double? Kpldiffmenge { get; set; }

    public double? Kplrestmenge { get; set; }

    public long? Kplrenr { get; set; }

    public int? KpposNr { get; set; }

    public long? Kpvznr { get; set; }

    public decimal? Kppreis { get; set; }

    public int? Kpgpos { get; set; }

    public decimal? KpstMenge { get; set; }

    public long? KpscheinNr { get; set; }

    public long Id { get; set; }

    public long? PktrNr { get; set; }
}
