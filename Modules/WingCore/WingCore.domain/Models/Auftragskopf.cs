using System.ComponentModel.DataAnnotations.Schema;
using WingCore.domain.Common.CustomerAndSuppliers;
using WingCore.domain.Common.Flags;

namespace WingCore.domain.Models;

public class Auftragskopf
{
    public long? Auftragsnummer { get; set; }
    
    public long? KundenNrWE { get; set; }
    
    public string? AStatus { get; set; }

    public bool? Auftrag { get; set; }

    public bool? Lieferschein { get; set; }

    public bool? Retoure { get; set; }

    public DateTime? Auftragsdatum { get; set; }

    public DateTime? LvonDatum { get; set; }

    public DateTime? LbisDatum { get; set; }

    public DateTime? Eldatum { get; set; }

    public bool? Tankzug { get; set; }

    public bool? Planenzug { get; set; }

    public bool? Palettenware { get; set; }

    public string? AbrKennzeichen { get; set; }

    public long? Arvnummer { get; set; }

    public string? Bearbeiter { get; set; }

    public string? Bestellnr { get; set; }

    public string? Uhrzeit { get; set; }

    public string? GegBlgNr { get; set; }

    public double? GesGew { get; set; }

    public string? Zustellart { get; set; }

    public long? KontraktNr { get; set; }

    public long? StreckenNr { get; set; }

    public bool? OhneDisposition { get; set; }

    public string? Transport { get; set; }

    public string? Packungsart { get; set; }

    public long? RegioNummer { get; set; }

    public string? RegioName1 { get; set; }

    public string? RegioOrt { get; set; }

    public string? RegioStatus { get; set; }
    public string? AuslansSchl { get; set; }

    public string? Disponiert { get; set; }

    public short? Preisliste { get; set; }

    public short? Fpreisliste { get; set; }

    public short? BruttoKennung { get; set; }

    public short? Dkennung { get; set; } 

    public long? SiloNummer { get; set; }

    public int? FilialNr { get; set; }

    public string? Zustellart2 { get; set; }

    public bool? LsabrKng { get; set; }

    public string? Lsanliefer { get; set; }

    public string? LslfnrWe { get; set; }

    public int? LsdepotNr { get; set; }

    public short? LsdepotKng { get; set; }

    public double? Skontosatz { get; set; }

    public string? LsfremdNr { get; set; }

    public long? TourNr { get; set; }

    public bool? Send { get; set; }

    public string? UhrzeitTxt { get; set; }

    public string? Bemerkung { get; set; }

    public string? Lieferrest { get; set; }

    public string? Lfrestr1 { get; set; }

    public string? Lfrestr2 { get; set; }

    public string? Lfrestr3 { get; set; }

    public string? Lfrestr4 { get; set; }

    public int? PalNr1 { get; set; }

    public string? PalBez1 { get; set; }

    public double? PalAnz1 { get; set; }

    public int? PalNr2 { get; set; }

    public string? PalBez2 { get; set; }

    public double? PalAnz2 { get; set; }

    public int? PalNr3 { get; set; }

    public string? PalBez3 { get; set; }

    public double? PalAnz3 { get; set; }

    public string? ZusTxtBez1 { get; set; }

    public string? ZusTxtBez2 { get; set; }

    public string? ZusTxtBez3 { get; set; }

    public string? ZusTxtBez4 { get; set; }

    public string? ZusTxtBez5 { get; set; }

    public string? ZusTxtBez6 { get; set; }

    public string? ZusTxtInh1 { get; set; }

    public string? ZusTxtInh2 { get; set; }

    public string? ZusTxtInh3 { get; set; }

    public string? ZusTxtInh4 { get; set; }

    public string? ZusTxtInh5 { get; set; }

    public string? ZusTxtInh6 { get; set; }

    public bool? MitHänger { get; set; }

    public bool? AvisSend { get; set; }

    public long? SpeditionsNr { get; set; }

    public long? Bstrecke { get; set; }

    public bool? LoeschKng { get; set; }

    public string? VvonrWe { get; set; }

    public decimal? FrachtPreis { get; set; }

    public bool? PalKlPck { get; set; }

    public string? FahrerName { get; set; }

    public bool? Abholung { get; set; }

    public bool? SpedSend { get; set; }

    public bool? Mischpalette { get; set; }

    public bool? Xlsexp { get; set; }

    public DateTime? LockLast { get; set; }

    public DateTime? LockStart { get; set; }

    public string? LockUser { get; set; }

    public string? SysUser { get; set; }

    public DateTime? SysTime { get; set; }

    public long Id { get; set; }

    public bool? Angebot { get; set; }

    public DateTime? LadeDatum { get; set; }

    public string? LadeUhrzeit { get; set; }

    public bool? Satlog { get; set; }

    public string? FahrNam { get; set; }

    public string? NrtelefonWe { get; set; }

    public string? NrtelefonRe { get; set; }

    public bool? Wgskng { get; set; }

    public short? NuetzelSend { get; set; }

    public long? FrachtScheinNr { get; set; }

    public long? ElscheinNr { get; set; }

    public bool? MalzKng { get; set; }

    public long? ErstArtikel { get; set; }

    public string? ZahlZiel { get; set; }

    public string? PfadBitMap { get; set; }
    public long Flags { get; private set; }

    [NotMapped]
    public OderFlagsWorker FlagsWorker => new OderFlagsWorker(() => Flags, value => Flags = value);
    
    public AuftragCustomerOrSupplierData WarenEmpfaenger { get; set; } = AuftragCustomerOrSupplierData.Empty;
    
    public AuftragCustomerOrSupplierData InvoiceHolder { get; set; } = AuftragCustomerOrSupplierData.Empty;

    public ICollection<Auftragspo>? Auftragspositions { get; set; } = new List<Auftragspo>();
    
    public static Auftragskopf Create(CreationParamForAuftragskopf creationParam)
    {
        Auftragskopf auftragskopf = new()
        {
            Bestellnr = creationParam.Bestellnr,
            Auftragsdatum = creationParam.Auftragsdatum,
            KundenNrWE = 0,
            LvonDatum = creationParam.LvonDatum,
            LbisDatum = creationParam.LbisDatum,
            Eldatum = creationParam.Eldatum,
            WarenEmpfaenger = creationParam.WarenEmpfaenger,
            InvoiceHolder = creationParam.InvoiceHolder,
            NuetzelSend = 0,
            RegioNummer = 0,
            Preisliste = 0,
            Fpreisliste = 0,
            BruttoKennung = 0,
            Dkennung = 0,
            SiloNummer = 0,
            FilialNr = 0,
            SpeditionsNr = 0,
            GesGew = 0,
            KontraktNr = 0,
            StreckenNr = 0,
            Arvnummer = 0,
            Bearbeiter = string.Empty,
            Zustellart = string.Empty,
            Packungsart = string.Empty,
            RegioName1 = string.Empty,
            RegioOrt = string.Empty,
            RegioStatus = string.Empty,
            Disponiert = string.Empty,
            UhrzeitTxt = string.Empty,
            Bemerkung = string.Empty,
            GegBlgNr =  string.Empty,
            AuslansSchl = "0",
            Transport = string.Empty,
            Zustellart2 = string.Empty,
            Lsanliefer = string.Empty,
            LslfnrWe = string.Empty,
            LsdepotNr = 0,
            LsdepotKng = 0,
            Skontosatz = 0,
            LsfremdNr = string.Empty,
            TourNr = 0,
            Lieferrest = string.Empty,
            Lfrestr1 = string.Empty,
            Lfrestr2 = string.Empty,
            Lfrestr3 = string.Empty,
            Lfrestr4 = string.Empty,
            PalNr1 = 0,
            VvonrWe = string.Empty,
            FrachtPreis = 0,
            FahrerName = string.Empty,
            LockUser = string.Empty,
            AStatus = "NEW",
            Uhrzeit = DateTime.Now.ToString("HH:mm:ss"),
            LsabrKng = true,
            ZusTxtBez1 = string.Empty,
            ZusTxtBez2 = string.Empty,
            ZusTxtBez3 = string.Empty,
            ZusTxtBez4 = string.Empty,
            ZusTxtBez5 = string.Empty,
            ZusTxtBez6 = string.Empty,
            ZusTxtInh1 = string.Empty,
            ZusTxtInh2 = string.Empty,
            ZusTxtInh3 = string.Empty,
            ZusTxtInh4 = string.Empty,
            ZusTxtInh5 = string.Empty,
            ZusTxtInh6 = string.Empty,
            ZahlZiel = string.Empty,
            PfadBitMap = string.Empty,
            AbrKennzeichen = creationParam.AbrechnungKennzeichen switch
            {
                CreationParamForAuftragskopf.AbreKennzeichen.Faktura => "F",
                CreationParamForAuftragskopf.AbreKennzeichen.GutSchrift => "G",
                CreationParamForAuftragskopf.AbreKennzeichen.X => "X",
                _ => throw new ArgumentOutOfRangeException()
            }
        };
        auftragskopf.WarenEmpfaenger.VertrNr ??= 0;
        auftragskopf.InvoiceHolder.VertrNr ??= 0;
        
        // zurzeit hab ich in der DB nur die Möglichkeit, dass wenn ein Lieferschein erstellt wird, dass das AbrKennzeichen auf X gesetzt ist
        // wenn die Logik sich ändert, dann muss hier auch die Logik angepasst werden oder gelöscht
        if(auftragskopf.AbrKennzeichen == "X")
        {
            creationParam.Mode = CreationParamForAuftragskopf.AuftragskopfMode.LieferSchein;
        }
        
        auftragskopf.Auftrag = creationParam.Mode == CreationParamForAuftragskopf.AuftragskopfMode.Auftrag;
        auftragskopf.Lieferschein = creationParam.Mode == CreationParamForAuftragskopf.AuftragskopfMode.LieferSchein;
        auftragskopf.Retoure = creationParam.Mode == CreationParamForAuftragskopf.AuftragskopfMode.Retoure;
        
        return auftragskopf;
    }
}

public record CreationParamForAuftragskopf
{
    public enum AuftragskopfMode
    {
        Auftrag,
        LieferSchein,
        Retoure,
    }

    public enum AbreKennzeichen
    {
        Faktura,
        GutSchrift,
        X // nur für Lieferschein
    }

    public AuftragskopfMode Mode { get; set; } = AuftragskopfMode.Auftrag;
    public AbreKennzeichen AbrechnungKennzeichen { get; set; } = AbreKennzeichen.Faktura;
    public string Bestellnr { get; set; } = string.Empty;
    public DateTime Auftragsdatum { get; set; }
    public DateTime LvonDatum { get; set; }
    public DateTime LbisDatum { get; set; }
    public DateTime Eldatum { get; set; }
    
    public AuftragCustomerOrSupplierData WarenEmpfaenger { get; set; } = AuftragCustomerOrSupplierData.Empty;
    public AuftragCustomerOrSupplierData InvoiceHolder { get; set; } = AuftragCustomerOrSupplierData.Empty;
}