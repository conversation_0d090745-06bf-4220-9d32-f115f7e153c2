namespace WingCore.domain.Models;

public partial class UmbListe
{
    public long? Ubnr { get; set; }

    public string? Ubbez { get; set; }

    public DateTime? Ubdatum { get; set; }

    public int? UbposNr { get; set; }

    public long? UbartNr { get; set; }

    public string? Ubwbez1 { get; set; }

    public string? Ubwbez2 { get; set; }

    public string? UbbzgGr { get; set; }

    public string? Ubpm { get; set; }

    public decimal? Ubmenge { get; set; }

    public decimal? Ubek { get; set; }

    public string? Ubgrund { get; set; }

    public int? Ublgst { get; set; }

    public int? Ubfaktor { get; set; }

    public int? UbartGrp { get; set; }

    public int? Ubfakt { get; set; }

    public string? UbchgNr { get; set; }

    public string? UbprodNr { get; set; }

    public string? Ubmkng { get; set; }

    public string? SysUser { get; set; }

    public DateTime? SysTime { get; set; }

    public long Id { get; set; }
}
