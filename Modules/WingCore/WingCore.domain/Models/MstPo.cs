namespace WingCore.domain.Models;

public partial class MstPo
{
    public long? AuftragsNr { get; set; }

    public int? GridPos { get; set; }

    public string? ErfSchema { get; set; }

    public long? ArtikelNummer { get; set; }

    public string? Bezeichnung { get; set; }

    public string? Txt { get; set; }

    public float? Anzahl { get; set; }

    public string? BezGr { get; set; }

    public decimal? GesMenge { get; set; }

    public decimal? Ekpreis { get; set; }

    public decimal? Vkpreis { get; set; }

    public decimal? MwSt { get; set; }

    public string? Faktor { get; set; }

    public decimal? MwSt2 { get; set; }

    public long? VerpckNr { get; set; }

    public long? ArtikelNummer2 { get; set; }

    public decimal? AlteMenge { get; set; }

    public long? KontraktNr { get; set; }

    public long? KontraktNrVk { get; set; }

    public string? ChargenNr { get; set; }

    public decimal? Pfracht { get; set; }

    public DateTime? Mhd { get; set; }

    public int? FilialNr { get; set; }

    public int? LagerNr { get; set; }

    public decimal? PakGewicht { get; set; }

    public decimal? PalGew { get; set; }

    public long? KolliInh { get; set; }

    public string? SysUser { get; set; }

    public long Id { get; set; }

    public decimal? GesStck { get; set; }
}
