namespace WingCore.domain.Models;

public partial class KasHptTse
{
    public long? Kvnr { get; set; }

    public long? KstNr1 { get; set; }

    public decimal? Knetto1 { get; set; }

    public decimal? KmwSt1 { get; set; }

    public decimal? Kbrutto1 { get; set; }

    public long? KstNr2 { get; set; }

    public decimal? Knetto2 { get; set; }

    public decimal? KmwSt2 { get; set; }

    public decimal? Kbrutto2 { get; set; }

    public long? KstNr3 { get; set; }

    public decimal? Knetto3 { get; set; }

    public decimal? KmwSt3 { get; set; }

    public decimal? Kbrutto3 { get; set; }

    public decimal? G<PERSON>ben { get; set; }

    public decimal? <PERSON><PERSON>ück { get; set; }

    public long? Gsnr { get; set; }

    public decimal? Gsrest { get; set; }

    public string? Kbediener { get; set; }

    public string? BonBegin { get; set; }

    public string? BonEnd { get; set; }

    public string? ProcessTyp { get; set; }

    public string? ProcessData { get; set; }

    public string? SignatureCounter { get; set; }

    public string? Signature { get; set; }

    public string? TimeFormat { get; set; }

    public string? SignatureAlgorithm { get; set; }

    public string? PublicKey { get; set; }

    public string? SerNr { get; set; }

    public long Id { get; set; }
}
