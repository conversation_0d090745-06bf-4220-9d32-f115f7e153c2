namespace WingCore.domain.Mappers;

public class AdfinityCountryLanguageMapper
{
    public static readonly IReadOnlyDictionary<string, string> CountryToLanguageMap = new Dictionary<string, string>
    {
        { "AT", "DE" }, // Austria -> German
        { "CH", "DE" }, // Switzerland -> German
        { "BE", "FR" }, // Belgium -> French
        { "LU", "LB" }, // Luxembourg -> Luxembourgish
        { "GB", "EN" }, // United Kingdom -> English
        { "IE", "EN" }, // Ireland -> English
        { "DK", "DA" }, // Denmark -> Danish
        { "SE", "SV" }, // Sweden -> Swedish
        { "CZ", "CS" }, // Czech Republic -> Czech
        { "SI", "SL" }, // Slovenia -> Slovenian
        { "RS", "SR" }, // Serbia -> Serbian
        { "BA", "BS" }, // Bosnia and Herzegovina -> Bosnian
        { "ME", "SR" }, // Montenegro -> Serbian
        { "AL", "SQ" }, // Albania -> Albanian
        { "GR", "EL" }, // Greece -> Greek
        { "EE", "ET" }  // Estonia -> Estonian
    };
}