namespace WingCore.domain.Mappers;

public static class AdfinityArticleReferenceSizeMapper
{
   public static string MapArticleReferenceSize(string referenceSizeMap, string artBzg)
   {
      var mappings = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase);
        
      var pairs = referenceSizeMap.Split(',');

      foreach (var pair in pairs)
      {
         var keyValue = pair.Split('=');
         if (keyValue.Length != 2) continue;
         
         var key = keyValue[0].Trim();
         var value = keyValue[1].Trim();
         mappings[key] = value;
      }

      return mappings.GetValueOrDefault(artBzg, artBzg);
   } 
}