namespace WingCore.domain.Common.Configurations;

public record ConfigurationAddisonPipeline : ConfigurationInvoiceExportBase
{
    public int AddisonMandantNummer { get; set; } = 0;
    public List<string> BookingExportConfiguration { get; set; } = DefaultBookingConfiguration;
    public char ReplacementCharForSeperator { get; set; } = '_';
    public string? Seperator { get; set; } = DefaultAddisonImportConfiguration.GetValueOrDefault("SEPERATOR");
    
    public List<string> GetBookingExportConfiguration() => BookingExportConfiguration.Count != 0 ? BookingExportConfiguration : DefaultBookingConfiguration;
    public string GetSeperator() => Seperator ?? DefaultAddisonImportConfiguration.GetValueOrDefault("SEPERATOR") ?? ";";
    public bool AddDateTimeNowInFileName{ get; set; } = false;
    
    private static readonly List<string> DefaultBookingConfiguration = new()
    {
        "SATZART", "KONTO", "BUCHUNGSDATUMTAG", "BELEGDATUM",
        "BUCHUNGSTEXT",
        "BELEGNR",
        "BETRAG",
        "MANDANT",
        "GEGENKONTO"
    };

    private static readonly Dictionary<string, string> DefaultAddisonImportConfiguration = new()
    {
        { "ASCII", "0" },
        { "SEPERATOR", ";" },
        { "BETRAG_ENGLISCH", "0" },
    };
}
