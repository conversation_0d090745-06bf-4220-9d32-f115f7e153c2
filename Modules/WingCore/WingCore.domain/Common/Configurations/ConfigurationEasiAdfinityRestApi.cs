namespace WingCore.domain.Common.Configurations;

public record ConfigurationEasiAdfinityRestApi : ConfigurationInvoiceExportBase
{
    public ConfigurationWgsExportBase WgsExportConfiguration { get; set; } = new();
    public string BaseUrl { get; set; } = string.Empty;
    public string EasiAdfinityDatabase { get; set; } = string.Empty;
    public string EasiAdfinityEnvir { get; set; } = string.Empty;
    public string UserName { get; set; } = string.Empty;
    public string UserPassword { get; set; } = string.Empty;
    public string ReferenceSizeMap { get; set; }  = "lose=KG,bulk=KG";
}
