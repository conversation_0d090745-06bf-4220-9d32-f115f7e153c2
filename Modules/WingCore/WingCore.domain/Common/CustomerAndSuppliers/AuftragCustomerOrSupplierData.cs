using WingCore.domain.Models;

namespace WingCore.domain.Common.CustomerAndSuppliers;

public record AuftragCustomerOrSupplierData
    : BaseCustomerOrSupplierData
{
    public AuftragCustomerOrSupplierData(
        long? Number,
        string Sbg,
        string Anrede,
        string? Name,
        string? Name2,
        string? Name3,
        string Street,
        string Plz,
        string Ort,
        string Country,
        string UstIdNr,
        bool IsForeigner,
        string Phone,
        string Email,
        string Kdleh,
        long DueDays,
        string LfNr)
        : base(Number, Sbg, Anrede, Name, Name2, Name3, Street, Plz, Ort, Country, UstIdNr,
        IsForeigner, Phone, Email, Kdleh, DueDays,LfNr)
    {
    }

    public  AuftragCustomerOrSupplierData()
        : this(0,string.Empty,string.Empty,string.Empty,string.Empty,string.Empty,string.Empty,string.Empty,string.Empty,string.Empty,string.Empty,false,string.Empty,string.Empty,string.Empty,0,string.Empty)
    {}
    
    private  AuftragCustomerOrSupplierData(Kunden kunden)
        : base(kunden)
    {}
    
    public static AuftragCustomerOrSupplierData Empty => new();
    
    public static implicit operator AuftragCustomerOrSupplierData(Kunden kunden) => new AuftragCustomerOrSupplierData(kunden);
}