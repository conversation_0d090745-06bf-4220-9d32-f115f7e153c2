namespace WingCore.domain.Common.Flags;

[Flags]
public enum OrderPosFlags
{
    None                                = 0x0000000000000000,
    ProcessOrderCreated                 = 0x0000000000000001,
    // DeleteMeAndUseMe                 = 0x0000000000000002,
    // DeleteMeAndUseMe                 = 0x0000000000000004,
    // DeleteMeAndUseMe                 = 0x0000000000000008,
    // DeleteMeAndUseMe                 = 0x0000000000000010,
    // DeleteMeAndUseMe                 = 0x0000000000000020,
    // DeleteMeAndUseMe                 = s0x0000000000000040,
    // Flag9                               = 0x0000000000000080,
    // Flag10                              = 0x0000000000000100,
    // Flag11                              = 0x0000000000000200,
    // Flag12                              = 0x0000000000000400,
    // Flag13                              = 0x0000000000000800,
    // Flag14                              = 0x0000000000001000,
    // Flag15                              = 0x0000000000002000,
    // Flag16                              = 0x0000000000004000,
    // Flag17                              = 0x0000000000008000,
    // Flag18                              = 0x0000000000010000,
    // Flag19                              = 0x0000000000020000,
    // Flag20                              = 0x0000000000040000,
    // Flag21                              = 0x0000000000080000,
    // Flag22                              = 0x0000000000100000,
    // Flag23                              = 0x0000000000200000,
    // Flag24                              = 0x0000000000400000,
    // Flag25                              = 0x0000000000800000,
    
    
    // notuse                              = 0x8000000000000000
}

public record OderPosFlagsWorker : BaseFlagsWorker
{
    public OderPosFlagsWorker(Func<long> getFlags, Action<long> setFlags) 
        : base(getFlags, setFlags)
    {
    }
    
    public OrderPosFlags Flags
    {
        get => (OrderPosFlags)_getFlags();
        set => _setFlags((long)value);
    }
    
    public long ToLong() => (long)Flags;
    
    public void SetProcessOrderCreated() => Flags = Flags.SetFlag(OrderPosFlags.ProcessOrderCreated);
    public bool IsSetProcessOrderCreated() => Flags.HasFlag(OrderPosFlags.ProcessOrderCreated);
}
