namespace WingCore.domain.Common.Flags;

public static class EnumExtensions
{
    public static T SetFlag<T>(this T flags, T flag) where T : struct, Enum
    {
        var flagsValue = Convert.ToUInt64(flags);
        var flagValue = Convert.ToUInt64(flag);
        flagsValue |= flagValue;
        return (T)Enum.ToObject(typeof(T), flagsValue);
    }
    
    public static T RemoveFlag<T>(this T flags, T flag) where T : struct, Enum
    {
        var flagsValue = Convert.ToUInt64(flags);
        var flagValue = Convert.ToUInt64(flag);
        flagsValue &= ~flagValue;
        return (T)Enum.ToObject(typeof(T), flagsValue);
    }
}