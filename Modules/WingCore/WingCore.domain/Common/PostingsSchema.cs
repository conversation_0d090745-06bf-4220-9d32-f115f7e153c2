namespace WingCore.domain.Common;

public record PostingsSchema:  IComparable<PostingsSchema>
{
    public const string PoString = "PO";
    public const string TeString = "TE";
    
    public static readonly PostingsSchema Empty = new("");
    public static readonly PostingsSchema Po = new(PoString);
    public static readonly PostingsSchema Te = new(TeString);
    

    public PostingsSchema(string value)
    {
        Value = value;
    }

    public string Value { get; }

    public bool IsPo => Value == PoString;
    public bool IsText => Value == TeString;

    
    public override string ToString()
    {
        return Value;
    }
    
    public static implicit operator PostingsSchema(string postingsSchema)
    {
        return postingsSchema.ToUpper() switch
        {
            PoString => Po,
            TeString => Te,
            _ => Empty
        };
    }
        
    public int CompareTo(PostingsSchema? postingsSchema)
    {
        return string.Compare(Value, postingsSchema?.Value, StringComparison.Ordinal);
    }
    public override int GetHashCode() => Value.GetHashCode();
    
    public virtual bool Equals(PostingsSchema? postingsSchema)
    {
        return Value == postingsSchema?.Value;
    }
}