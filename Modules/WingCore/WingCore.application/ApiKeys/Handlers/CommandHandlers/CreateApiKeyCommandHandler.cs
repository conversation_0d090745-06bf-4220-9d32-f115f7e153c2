using MediatR;
using WingCore.application.Contract.IModels.StammDb;
using WingCore.domain.Models.StammDbModels;

namespace WingCore.application.ApiKeys.Handlers.CommandHandlers;

public class CreateApiKeyCommandHandler(IApiKeyRepository apiKeyRepository) : IRequestHandler<CreateApiKeyCommand,ApiKey>
{
    public async Task<ApiKey> Handle(CreateApiKeyCommand command, CancellationToken cancellationToken)
    {
        var newObject = await apiKeyRepository.Create(command.Description);

        await apiKeyRepository.SaveChangesAsync(cancellationToken);
        apiKeyRepository.Detach(newObject);
        
        return newObject;
    }
}