using MediatR;
using WingCore.application.Contract.IModels.StammDb;
using WingCore.domain.Models.StammDbModels;

namespace WingCore.application.ApiKeys.Handlers.QueryHandlers;

public class GetAllApiKeysQueryHandler(IApiKeyRepository apiKeyRepository) : IRequestHandler<GetAllApiKeysQuery,IEnumerable<ApiKey>>
{
    public Task<IEnumerable<ApiKey>> Handle(GetAllApiKeysQuery request, CancellationToken cancellationToken) => apiKeyRepository.GetAllApiKeysMasked();
}