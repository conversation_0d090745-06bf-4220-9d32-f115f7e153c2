using MediatR;
using WingCore.application.Contract.IModels;
using WingCore.domain.Models;

namespace WingCore.application.Aufträge.Auftragspositionen.Handler;

public class GetAuftragsPosByOrderNumberQueryHandler(IAuftragsposRepository repository) : IRequestHandler<GetAuftragsPosByOrderNumberQuery, ICollection<Auftragspo>>
{
    public Task<ICollection<Auftragspo>> <PERSON>le(GetAuftragsPosByOrderNumberQuery request, CancellationToken cancellationToken)
    {
        return repository.GetAuftragsposByOrderNumber(false, request.ID);
    }
}