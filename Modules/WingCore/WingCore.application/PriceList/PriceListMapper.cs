using WingCore.application.ApiDtos.MasterData.V1;
using WingCore.domain.Models;

namespace WingCore.application.PriceList;

public class PriceListMapper
{
    public static PriceListDtoV1 MapPreislistenToPriceListDto(Preislisten preislisten)
    {
        return new PriceListDtoV1()
        {
            Id = preislisten.Id,
            Nr = preislisten.Prlnr,
            Kng = preislisten.Prlkng,
            ArtNr = preislisten.PrlartNr,
            ArtBez = preislisten.PrlartBez,
            ArtBez2 = preislisten.PrlartBez2,
            Bzg = preislisten.Prlbzg,
            ArtPrice = preislisten.PrlartPreis,
            ArtProz = preislisten.PrlartProz,
            Pos = preislisten.Prlpos,
            Freight = preislisten.Prlfracht,
            Date1 = preislisten.Prldatum1,
            Date2 = preislisten.Prldatum2,
            Price1 = preislisten.Prlpreis1,
            Price2 = preislisten.Prlpreis2,
            SysUser = preislisten.SysUser,
            EkPrice = preislisten.Prlekpreis,
            Send = preislisten.Send,
            SysTime = preislisten.SysTime
        };
    }
    public static Preislisten MapPriceListDtoToPreislisten(PriceListDtoV1 priceListDtoV1)
    {
        return new Preislisten()
        {
            Id = priceListDtoV1.Id,
            Prlnr = priceListDtoV1.Nr,
            Prlkng = priceListDtoV1.Kng,
            PrlartNr = priceListDtoV1.ArtNr,
            PrlartBez = priceListDtoV1.ArtBez,
            PrlartBez2 = priceListDtoV1.ArtBez2,
            Prlbzg = priceListDtoV1.Bzg,
            PrlartPreis = priceListDtoV1.ArtPrice,
            PrlartProz = priceListDtoV1.ArtProz,
            Prlpos = priceListDtoV1.Pos,
            Prlfracht = priceListDtoV1.Freight,
            Prldatum1 = priceListDtoV1.Date1,
            Prldatum2 = priceListDtoV1.Date2,
            Prlpreis1 = priceListDtoV1.Price1,
            Prlpreis2 = priceListDtoV1.Price2,
            SysUser = priceListDtoV1.SysUser,
            Prlekpreis = priceListDtoV1.EkPrice,
            Send = priceListDtoV1.Send,
            SysTime = priceListDtoV1.SysTime
        };
    }
}