using WingCore.domain.Models;

namespace WingCore.application.PriceList.IModels;

public interface IPriceListRepository
{
    Task<IEnumerable<Preislisten>> GetAllPriceLists(bool trackChanges);
    Task<Preislisten?> GetPriceList(bool trackChanges, long id);
    Task<Preislisten> AddPriceList(Preislisten preislisten);
    Task UpdatePriceList(Preislisten preislisten);
    Task DeletePriceList(Preislisten preislisten);
}