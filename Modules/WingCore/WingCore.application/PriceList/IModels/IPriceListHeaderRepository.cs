using WingCore.domain.Models;

namespace WingCore.application.PriceList.IModels;

public interface IPriceListHeaderRepository
{
    Task<IEnumerable<Prlkopf>> GetAllPriceListHeaders(bool trackChanges);
    Task<Prlkopf?> GetPriceListHeader(bool trackChanges, long id);
    Task<Prlkopf> AddPriceListHeader(Prlkopf prlkopf);
    Task UpdatePriceListHeader(Prlkopf prlkopf);
    Task DeletePriceListHeader(Prlkopf prlkopf);
}