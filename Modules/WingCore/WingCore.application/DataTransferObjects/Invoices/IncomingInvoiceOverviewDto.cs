using WingCore.domain.Common;
using WingCore.domain.Models;

namespace WingCore.application.DataTransferObjects.Invoices;
public record IncomingInvoiceOverviewDto : BaseInvoiceOverviewEntryDto
{
    public new string ExternalInvoiceNumber { get; set; } = string.Empty;
    public new IEnumerable<IncomingInvoicePostingDto> ListPostings { get; set; } = [];
    public new IEnumerable<IncomingInvoicePostingDto> ListAllPostings { get; set; } = [];
    public virtual bool Equals(IncomingInvoiceOverviewDto? obj)
    {
        return Id == obj?.Id;
    }
    public override int GetHashCode()
    {
        return HashCode.Combine(Id, Type);
    }
    
    private void InitBase(Erv incomingInvoiceHeaderData)
    {
        Type = InvoiceType.IncomingInvoice;
        Id = incomingInvoiceHeaderData.Id;
        InvoiceNumber = incomingInvoiceHeaderData.Ernummer;
        InvoiceDate = incomingInvoiceHeaderData.Erdatum ?? DateTime.MinValue;
        ValutaDate = incomingInvoiceHeaderData.Vdatum ?? DateTime.MinValue;
        DueDays = incomingInvoiceHeaderData.ErnettoTage ?? 0;
        OrderNumber = string.Empty;
        TotalGross = incomingInvoiceHeaderData.ErgesBrutto ?? 0;
        TotalNet = incomingInvoiceHeaderData.ErgesNetto ?? 0;
        TotalVatAmount = incomingInvoiceHeaderData.ErgesMwst ?? 0;
        IsCancellationInvoice = incomingInvoiceHeaderData.IsCancellationInvoice;
        WasCanceled = incomingInvoiceHeaderData.WasCanceled;
        CancelOrCreditNoteInvoiceNumber = incomingInvoiceHeaderData.EralteNr ?? 0;
        InvoiceSupplierCurrent = incomingInvoiceHeaderData.InvoiceSupplierCurrentData;
        SysUser = incomingInvoiceHeaderData.SysUser ?? string.Empty;
        SysTime = incomingInvoiceHeaderData.SysTime;
        Fibu2 = incomingInvoiceHeaderData.Erfibu2 ?? false;
        InvoiceSupplier = new InvoiceSupplierOrHolderDto(
            incomingInvoiceHeaderData.Erlfnr,
            incomingInvoiceHeaderData.Ersbg ?? string.Empty,
            incomingInvoiceHeaderData.Eranrede ?? string.Empty,
            incomingInvoiceHeaderData.Ername1,
            incomingInvoiceHeaderData.Ername2,
            incomingInvoiceHeaderData.Ername3,
             incomingInvoiceHeaderData.Erstrasse ?? string.Empty,
             incomingInvoiceHeaderData.Erplz ?? string.Empty,
             incomingInvoiceHeaderData.Erort ?? string.Empty,
            incomingInvoiceHeaderData.Erland ?? string.Empty,
            incomingInvoiceHeaderData.Erustidnr ?? string.Empty,
            incomingInvoiceHeaderData.IsInvoiceHolderForeigner,
            InvoiceSupplierCurrent?.Phone ?? string.Empty,
            InvoiceSupplierCurrent?.Email ?? string.Empty,
            InvoiceSupplierCurrent?.Kdleh ?? string.Empty,
            InvoiceSupplierCurrent?.DueDays ?? 0,
            InvoiceSupplierCurrent?.LfNr  ?? string.Empty
            );
        GoodsRecipientsCurrent = null;
        GoodsRecipients = null;
        ListTax =
        [
            new TaxDto(0, incomingInvoiceHeaderData.Ermwst1E ?? 0, incomingInvoiceHeaderData.Ermwst1P ?? 0, incomingInvoiceHeaderData.Ernetto1 ?? 0, incomingInvoiceHeaderData.Erbrutto1 ?? 0),
            new TaxDto(1, incomingInvoiceHeaderData.Ermwst2E ?? 0, incomingInvoiceHeaderData.Ermwst2P ?? 0, incomingInvoiceHeaderData.Ernetto2 ?? 0, incomingInvoiceHeaderData.Erbrutto2 ?? 0)
        ];
        BankInfo = new BankInfoDto(incomingInvoiceHeaderData.Erbank ?? string.Empty,
                                   incomingInvoiceHeaderData.Erblz ?? string.Empty,
                                   incomingInvoiceHeaderData.Erkontonr ?? string.Empty,
                                   incomingInvoiceHeaderData.Iban ?? string.Empty,
                                   incomingInvoiceHeaderData.Swift ?? string.Empty);
        ListPostings = incomingInvoiceHeaderData.Positionen.Where(x=> x.ErfSchema?.IsPo ?? true).Select(x => (IncomingInvoicePostingDto)x);
        ListAllPostings = incomingInvoiceHeaderData.Positionen.Select(x => (IncomingInvoicePostingDto)x);
    }
        
    public static implicit operator IncomingInvoiceOverviewDto(Erv incomingInvoiceHeaderData)
    {
        var newObj = new IncomingInvoiceOverviewDto();
        newObj.InitBase(incomingInvoiceHeaderData);
        newObj.ExternalInvoiceNumber = incomingInvoiceHeaderData.Errelf ?? string.Empty;
        return newObj;
    }
}