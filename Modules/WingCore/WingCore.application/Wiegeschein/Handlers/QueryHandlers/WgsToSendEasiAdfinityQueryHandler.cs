using MediatR;
using WingCore.application.Contract.IModels;
using WingCore.application.Contract.Services;
using WingCore.application.DataTransferObjects.Wgs;
using WingCore.application.Wiegeschein.Helper;
using WingCore.domain.Common.Flags;

namespace WingCore.application.Wiegeschein.Handlers.QueryHandlers;

public class WgsToSendEasiAdfinityQueryHandler(IWgsRepository wgsRepository, IConfigurationService configurationService)
    : IRequestHandler<WgsToSendEasiAdfinityQuery,IEnumerable<WgAdfinityJobDto>>
{
    public async Task<IEnumerable<WgAdfinityJobDto>> Handle(WgsToSendEasiAdfinityQuery query, CancellationToken cancellationToken)
    {
        var config = await configurationService.GetConfigurationEasiAdfinityRestApiAsync();
        if (config is null)
            throw new Exception("Adfinity configuration not found.");
        
        var searchParam = new WiegescheinSearchParam()
        {
            WithOutFlags = (long)WgsFlags.EasiAdfinityReadyToSend | (long)WgsFlags.EasiAdfinitySendSuccess,
            FromSystemDate = config.WgsExportConfiguration.FromSystemDate
        };
        return await wgsRepository.GetAllOpenWgsDtoForAdfinity(searchParam);
    }
}