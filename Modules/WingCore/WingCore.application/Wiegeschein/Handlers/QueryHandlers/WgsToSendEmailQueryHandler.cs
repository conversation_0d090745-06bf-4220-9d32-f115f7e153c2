using MediatR;
using WingCore.application.Contract.IModels;
using WingCore.application.Contract.Services;
using WingCore.application.DataTransferObjects.Wgs;
using WingCore.application.Wiegeschein.Helper;
using WingCore.domain.Common.Flags;

namespace WingCore.application.Wiegeschein.Handlers.QueryHandlers;

public class WgsToSendEmailQueryHandler(IWgsRepository wgsRepository, IConfigurationService configurationService)
    : IRequestHandler<WgsToSendEmailQuery, IEnumerable<WgAdfinityJobDto>>
{
    public async Task<IEnumerable<WgAdfinityJobDto>> Handle(WgsToSendEmailQuery query, CancellationToken cancellationToken)
    {
        var config = await configurationService.GetConfigurationElectricWgsAsync();
        if (config is null)
            throw new Exception("E-Wiegeschein configuration not found.");
        
        var searchParam = new WiegescheinSearchParam()
        {
            WithOutFlags = (long)WgsFlags.WgsMailSendSuccess,
            FromSystemDate = config.WgsExportConfiguration?.FromSystemDate ?? DateTime.Now.AddDays(-30)
        };
        return await wgsRepository.GetAllOpenWgsDtoForAdfinity(searchParam);
    }
}
