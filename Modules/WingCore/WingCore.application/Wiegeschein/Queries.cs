using MediatR;
using WingCore.application.DataTransferObjects.Wgs;
using WingCore.domain.Models;

namespace WingCore.application.Wiegeschein;


public record AllOpenWgsQuery(long? SupplierId) : IRequest<IEnumerable<Wg>>;
public record WgsToSendEasiAdfinityQuery() : IRequest<IEnumerable<WgAdfinityJobDto>>;
public record WgsToSendEmailQuery() : IRequest<IEnumerable<WgAdfinityJobDto>>;
public record WgsWithDataQuery(long WgsNumber, bool WithAllData = false) : IRequest<Wg?>;
public record WgsByNumbersQuery(IEnumerable<long> WgsNumbers) : IRequest<IEnumerable<Wg>>;
