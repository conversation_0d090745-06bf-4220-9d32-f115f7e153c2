using MediatR;
using WingCore.application.Contract.IModels;
using WingCore.domain.Models;

namespace WingCore.application.Getreideabrechnung.Positions.Handlers;

public class EditGbvPositionQueryHandler(IGbvhptRepository gbvhptRepository)
    : IRequestHandler<EditGbvPositionQuery, bool>
{
    public async Task<bool> Handle(EditGbvPositionQuery request, CancellationToken cancellationToken)
    {
        return await gbvhptRepository.EditGbvPosition(request.Number, request.Position, request.NewValue);
    }
}