using MediatR;
using WingCore.application.Contract.IModels;
using WingCore.domain.Models;

namespace WingCore.application.Getreideabrechnung.Positions.Handlers;

public class GetGbvPositionsByIdQueryHandler(IGbvhptRepository gbvhptRepository) : IRequestHandler<GetGbvPositionsByNumberQuery, IEnumerable<Gbvhpt>>
{
    public async Task<IEnumerable<Gbvhpt>> Handle(GetGbvPositionsByNumberQuery request, CancellationToken cancellationToken)
    {
        return await gbvhptRepository.GetGbvPositionsByNumber(request.Number);;
    }
}