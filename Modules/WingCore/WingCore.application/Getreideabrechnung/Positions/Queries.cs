using MediatR;
using WingCore.domain.Models;

namespace WingCore.application.Getreideabrechnung.Positions;


public record GetGbvPositionsByNumberQuery(long Number) : IRequest<IEnumerable<Gbvhpt>>;

public record CreateGrainAccountingQuery(long SupplierNumber, List<long> WgsNumbers) : IRequest<long>;

public record DeleteGbvPositionQuery(long Number, long Position) : IRequest<bool>;

public record EditGbvPositionQuery(long Number, long Position, Gbvhpt NewValue) : IRequest<bool>;