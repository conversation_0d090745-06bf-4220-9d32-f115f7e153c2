using WingCore.domain.Models.StammDbModels;
using WingCore.application.Contract.IModels;
using WingCore.application.DataTransferObjects.Invoices;

namespace WingCore.application.Contract;

public interface ISelectedMandant
{
    ValueTask<Mandant?> GetSelectedMandant();
    public ValueTask<InvoiceSupplierOrHolderDto?> GetSelectedMandantAsInvoiceHolder();
    public ValueTask<CompanyMasterData?> GetCompanyMasterData();
    void SetSelectedMandant(string? mnr);
    ValueTask<IDbParameter?> LoadConnectionObjectFromFile();
    Task<string> GetDbConnectionDataFilePath();
    ValueTask<IDbParameter?> LoadConnectionObjectFromFile(string mandantNumber);
    Task SaveDbConnectionData(IDbParameter dbParameter);
    Task<CompanyMasterData> GetNewCompanyMasterData();
}