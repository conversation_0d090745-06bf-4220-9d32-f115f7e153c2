using WingCore.domain.Models;
using WingCore.application.Contract.IModels.Helper;
using WingCore.application.DataTransferObjects.Invoices;

namespace WingCore.application.Contract.Services;


public interface IOutgoingInvoiceHeaderService : IInvoiceHeaderService<Aradressdaten, OutgoingInvoiceOverviewDto>
{
    Task<IEnumerable<OutgoingInvoiceOverviewDto>> GetAllInvoiceCompleteDataForEdifact(InvoiceSearchParam searchParam);
}
public interface IIncomingInvoiceHeaderService : IInvoiceHeaderService<Erv, IncomingInvoiceOverviewDto>;
public interface IGrainSettlementInvoiceHeaderService : IInvoiceHeaderService<Gbvadr, GrainSettlementInvoiceOverviewDto>;
public interface IRapeSettlementInvoiceHeaderService : IInvoiceHeaderService<Rgbvadr, RapeSettlementInvoiceOverviewDto>;

public interface IInvoiceHeaderService<THeader,TDto>
{
    IEnumerable<THeader> GetAllInvoiceHeaderData(bool trackChanges, bool withAllData = false);
    IEnumerable<THeader> GetAllInvoiceWithCompleteData(bool trackChanges);
    Task<TDto?> GetInvoiceWithCompleteDataAsync(bool trackChanges, long invoiceNumber);
    Task<IEnumerable<TDto>> GetAllInvoiceHeaderDataOverview(bool trackChanges, DateTime? fromDate, DateTime? toDate, long fromInvoiceNumber, long toInvoiceNumber, bool withAllData = false);
    
    Task<IEnumerable<TDto>> GetAllInvoiceWithCompleteDataOverview(bool trackChanges, DateTime? fromDate, DateTime? toDate, long fromInvoiceNumber, long toInvoiceNumber);
    Task<IEnumerable<TDto>> GetAllInvoiceHeaderDataOverview(bool trackChanges, InvoiceSearchParam searchParam);
    
    void SetAllInvoiceDatevExportSuccess(List<long> invoiceIds);
    void SetAllInvoiceAddisonTseNitExportSuccess(List<long> invoiceIds);
    void SetAllEdifactExportSuccessAndFibu2(List<long> invoiceIds);
}