using WingCore.application.DataTransferObjects.Invoices;

namespace WingCore.application.Contract.Services;

public interface IZugFeRdxRechnungInvoiceExport
{
    public enum ExportMode
    {
        ZugFerD,
        ZugFerDProfileXRechnung,
        XRechnung
    }
    
    public long NumberOfExportInvoices { get; set; }
    public string FileName { get; set; }

    public Task<MemoryStream> ExportInvoicesAsXmlAsync(DateTime fromDate,
                                                        DateTime toDate,
                                                        long fromInvoiceNumber,
                                                        long toInvoiceNumber,
                                                        bool exportOutgoingInvoices,
                                                        bool exportIncomingInvoices,
                                                        bool exportGrainSettlementInvoices,
                                                        ExportMode mode);
    
    public Task<MemoryStream> ExportInvoicesAsXmlAsync(OutgoingInvoiceOverviewDto outgoingInvoiceOverviewDto, ExportMode mode);
    public Task<MemoryStream> ExportInvoicesAsXmlAsync(IncomingInvoiceOverviewDto outgoingInvoiceOverviewDto, ExportMode mode);
    public Task<MemoryStream> ExportInvoicesAsXmlAsync(GrainSettlementInvoiceOverviewDto outgoingInvoiceOverviewDto, ExportMode mode);
    public Task<MemoryStream> ExportInvoicesAsXmlAsync(RapeSettlementInvoiceOverviewDto outgoingInvoiceOverviewDto, ExportMode mode);
}