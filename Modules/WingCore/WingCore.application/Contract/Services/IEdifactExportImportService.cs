using WingCore.domain.Models;
using WingCore.application.DataTransferObjects.Invoices;
using WingCore.domain.Common.Configurations;

namespace WingCore.application.Contract.Services;

public interface IEdifactExportImportService
{
    public enum ExportMode
    {
        TrueCommerce,
        StratEdi
    }
    
    public long NumberOfExportInvoices { get; set; }
    public string FileName { get; set; }
    public Task<MemoryStream> ExportInvoicesAMemStreamAsync(OutgoingInvoiceOverviewDto outgoingInvoiceOverviewDto, ExportMode mode);
    public Task<MemoryStream> ExportInvoicesAMemStreamAsync(IncomingInvoiceOverviewDto outgoingInvoiceOverviewDto, ExportMode mode);
    public Task<MemoryStream> ExportInvoicesAMemStreamAsync(GrainSettlementInvoiceOverviewDto outgoingInvoiceOverviewDto, ExportMode mode);
    public Task<MemoryStream> ExportInvoicesAMemStreamAsync(RapeSettlementInvoiceOverviewDto outgoingInvoiceOverviewDto, ExportMode mode);
    public Task<MemoryStream> ExportInvoicesAMemStreamAsync(ConfigurationEdifact configurationEdifact, ExportMode mode);
    
    
    public Task<IEnumerable<Auftragskopf>> ImportOrderFromEdifactAsync(string xml);

}