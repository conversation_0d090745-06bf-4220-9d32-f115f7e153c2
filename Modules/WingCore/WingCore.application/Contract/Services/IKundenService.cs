using Commons.Dtos;
using WingCore.domain.Models;
using WingCore.application.Contract.IModels.Helper;

namespace WingCore.application.Contract.Services;

public interface IKundenService
{
    IEnumerable<Kunden> GetAllCustomers(bool trackChanges);
    ValueTask<Kunden?> GetCustomer(bool trackChanges,long id);
    ValueTask<Kunden?> GetCustomerOverNumber(bool trackChanges,long number);
    Task<CustomerDto?> GetCustomerDtoOverNumber(long number);
    ValueTask<IEnumerable<Kunden>> GetCustomer(bool trackChanges, CustomerSearchParam customerSearchParam);
    Task Update(Kunden customer);
}