using WingCore.domain.Models;
using WingCore.domain.Common.Flags;

namespace WingCore.application.Contract.Services;

public interface IOrderPosService
{
    IEnumerable<Auftragspo> GetAllOrderPos(bool trackChanges);
    Task<Auftragspo?> GetOrderPosAsync(bool trackChanges, long posId);
    IEnumerable<Auftragspo> GetOrderPosByArtikels(bool trackChanges, List<long> articleNumbers);
    Task<IEnumerable<Auftragspo>> GetOrderPosWithArticlesHaveSameMainArticleAsync(long mainArticleNumber, OrderPosFlags withOutflag, bool palettenware);
    Task SetFlagAsync(List<long> orderPosIds, OrderPosFlags flag);
    Task SetFlagProcessOrderCreatedAsync(List<long> orderPosIds);
    Task RemoveFlagProcessOrderCreatedAsync(List<long> orderPosIds);
}