using WingCore.application.DataTransferObjects.Wgs;
using WingCore.application.Wiegeschein.Helper;
using WingCore.domain.Models;
using WingCore.domain.Common.Flags;

namespace WingCore.application.Contract.IModels;

public interface IWgsRepository
{
    Task<IEnumerable<Wg>> GetOpenWgs(bool trackBack, long? supplierId);
    Task<IEnumerable<Wg>> GetWgsByNumbers(IEnumerable<long> wgsNumbers);
    Task<IEnumerable<WgAdfinityJobDto>> GetAllOpenWgsDtoForAdfinity(WiegescheinSearchParam searchParam);
    void SetAsUsed(long wgsNumber, long abrNumber);
    void SetWgsFlag(List<long> wgsIds, WgsFlags flag); 
    void RemoveWgsFlag(List<long> wgsIds, WgsFlags flag); 
}