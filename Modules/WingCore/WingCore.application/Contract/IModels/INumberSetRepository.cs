using WingCore.domain.Models;

namespace WingCore.application.Contract.IModels;

public interface INumberSetRepository
{
    public ValueTask<NumberSet?> GetAsync(long id, bool trackChanges = false);
    public Task<NumberSet> GetNextPaletNumberSetAsync();
    public void Create(NumberSet config);
    public void Update(NumberSet config);
    public Task SaveChangesAsync();
    public void ClearChangeTracker();
}