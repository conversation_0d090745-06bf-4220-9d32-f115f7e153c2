using WingCore.domain.Models;

namespace WingCore.application.Contract.IModels;

public interface IAuftragskopfRepository : IRepositoryBas<Auftragskopf>
{
    Task WriteOrderToDb(bool trackChanges, Auftragskopf order);

    Task<IEnumerable<Auftragskopf>> GetAll(bool trackChanges);
    List<long?> GetAllAuftragNummer();
    Task<IEnumerable<Auftragskopf>?> GetAuftragskopfByOrderNumber(long orderId, bool trackChanges);
    Task<ICollection<Auftragskopf>> GetAuftragskopfByOrderNumberList(List<long> orderId);
    IEnumerable<Auftragskopf> GetAuftragskopfsByNumbers(List<long> orderId, bool trackChanges);
    Task SetLockToCreateNumberAsync(string userName);
    void SetUnLockToCreateNumber();
    long? GetMaxAuftragsnummer();
    Auftragskopf? GetOverBestellNumber(string number, bool trackChanges);
    Auftragskopf? GetOverInvoiceNumberForEdifact(long invoiceNumber);
    Task UpdateWithoutTracking(Auftragskopf auftragskopf, string userName, CancellationToken token = default);
    Task SetSend(List<long> sendOrderheaderList);
    Task DisableInsertTrigger();
    Task EnableInsertTrigger();
}