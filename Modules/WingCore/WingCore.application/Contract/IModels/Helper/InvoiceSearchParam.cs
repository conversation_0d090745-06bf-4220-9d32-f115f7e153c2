namespace WingCore.application.Contract.IModels.Helper;

public record InvoiceSearchParam
{
    public bool WithAllData { get; set; } = false;
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public DateTime? FromSystemDate { get; set; }
    public DateTime? ToSystemDate { get; set; }
    public long? FromInvoiceNumber { get; set; }
    public long? ToInvoiceNumber { get; set; }
    public bool? Fibu2 { get; set; }
    public long? CustomerNumber { get; set; }
    public long? LieferNumber { get; set; }
    public long? WithFlags { get; set; }
    public long? WithOutFlags { get; set; }
    public bool? OnlyInvoiceWithEInvoiceEmail { get; set; }
    public bool? OnlyWithInvoiceHolderCanSendEmailAutomatic { get; set; }
    public bool? WithInvoiceHolderData { get; set; }
}