using WingCore.domain.Models;
using WingCore.domain.Models.StammDbModels;

namespace WingCore.application.Contract.IModels.StammDb;

public interface IApiKeyRepository
{
    ValueTask<ApiKey> Create(string description);
    ValueTask<ApiKey?> GetApiKey(string apiKey);
    ValueTask<ApiKey?> GetApiKeyById(long id);
    
    Task<IEnumerable<ApiKey>> GetAllApiKeysMasked();
    
    ValueTask<ApiKey?> GetApiKeyByIp(string ip);
    Task SaveChangesAsync(CancellationToken cancellationToken);
    void Delete(long id);
    void Detach<TEntity>(TEntity entity) where TEntity : class;
}