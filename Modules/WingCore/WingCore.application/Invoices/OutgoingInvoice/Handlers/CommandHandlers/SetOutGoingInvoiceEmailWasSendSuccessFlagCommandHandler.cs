using MediatR;
using WingCore.application.Contract;
using WingCore.application.Contract.IModels;

namespace WingCore.application.Invoices.OutgoingInvoice.Handlers.CommandHandlers;


public class SetOutGoingInvoiceEmailWasSendSuccessFlagCommandHandler(IOutgoingInvoiceRepository outgoingInvoiceRepository, IUnitOfWork iUnitOfWork) : IRequestHandler<SetOutGoingInvoiceEmailWasSendSuccessFlagCommand>
{
    public async Task Handle(SetOutGoingInvoiceEmailWasSendSuccessFlagCommand command, CancellationToken cancellationToken)
    {
        var invoice = await outgoingInvoiceRepository.GetInvoiceDtoCompleteData(false, command.InvoiceNumber);
        if (invoice is null)
            throw new Exception($"Outgoing invoice with number {command.InvoiceNumber} not found.");

        outgoingInvoiceRepository.SetAllInvoiceEmailWasSendSuccess([invoice.Id]);
        await iUnitOfWork.SaveChangesAsync(cancellationToken);
    }
}