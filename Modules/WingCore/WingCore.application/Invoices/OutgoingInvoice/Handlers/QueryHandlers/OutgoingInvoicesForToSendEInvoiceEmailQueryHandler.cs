using MediatR;
using WingCore.application.Contract.IModels;
using WingCore.application.Contract.IModels.Helper;
using WingCore.application.DataTransferObjects.Invoices;
using WingCore.domain.Common.Flags;

namespace WingCore.application.Invoices.OutgoingInvoice.Handlers.QueryHandlers;

public class OutgoingInvoicesForToSendEInvoiceEmailQueryHandler(IOutgoingInvoiceRepository outgoingInvoiceRepository) : IRequestHandler<OutgoingInvoicesForToSendEInvoiceEmailQuery,IEnumerable<OutgoingInvoiceOverviewDto>>
{
    public async Task<IEnumerable<OutgoingInvoiceOverviewDto>> Handle(OutgoingInvoicesForToSendEInvoiceEmailQuery query, CancellationToken cancellationToken)
    {
        var searchParam = new InvoiceSearchParam()
        {
            WithAllData = true,
            OnlyInvoiceWithEInvoiceEmail = true,
            WithOutFlags = (long)(InvoiceFlags.ZugferdExportSuccess | InvoiceFlags.EmailSendWasSuccess)
        };
        
        return await outgoingInvoiceRepository.GetAllInvoiceDtoCompleteData(searchParam);
    }
}