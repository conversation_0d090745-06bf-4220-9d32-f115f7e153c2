using MediatR;
using WingCore.application.Contract.IModels;
using WingCore.application.Contract.IModels.Helper;
using WingCore.application.Contract.Services;
using WingCore.application.DataTransferObjects.Invoices;
using WingCore.domain.Common.Flags;

namespace WingCore.application.Invoices.OutgoingInvoice.Handlers.QueryHandlers;

public class OutgoingInvoicesToSendEasiAdfinityQueryHandler(
    IOutgoingInvoiceRepository outgoingInvoiceRepository,
    IConfigurationService configurationService)
    : IRequestHandler<OutgoingInvoicesToSendEasiAdfinityQuery,IEnumerable<OutgoingInvoiceOverviewDto>>
{
    public async Task<IEnumerable<OutgoingInvoiceOverviewDto>> Handle(OutgoingInvoicesToSendEasiAdfinityQuery query, CancellationToken cancellationToken)
    {
        var config = await configurationService.GetConfigurationEasiAdfinityRestApiAsync();
        if (config is null)
            throw new Exception("Adfinity configuration not found.");
        
        var searchParam = new InvoiceSearchParam()
        {
            WithAllData = true,
            WithOutFlags = (long)InvoiceFlags.EasiAdfinityReadyToSend | (long)InvoiceFlags.EasiAdfinitySendSuccess,
            FromSystemDate = config.BeginingExportDate
        };
        
        return await outgoingInvoiceRepository.GetAllInvoiceDtoCompleteData(searchParam);
    }
}