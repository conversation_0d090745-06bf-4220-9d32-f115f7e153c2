using AdginityRestApi;
using WingCore.application.DataTransferObjects.Invoices;

namespace WingCore.application.Invoices.OutgoingInvoice.Mapper;

public static class AccountingEntriesRequests
{
    public static _v1_accountingEntriesBulk_INPUT_ITEM_entries Mapper(this OutgoingInvoiceOverviewDto invoiceData)
    {
        var isBelgier = invoiceData.InvoiceHolder?.CountryId?.Equals("BE", StringComparison.CurrentCultureIgnoreCase) ?? false;
        var movements = new List<_v1_accountingEntriesBulk_INPUT_ITEM_entries_movements>();
        var invoiceHolderNumber = invoiceData.InvoiceHolder?.Number.ToString() ?? string.Empty;
        
        foreach (var posting in invoiceData.ListPostings
                     .Where(p => p.TotalNettoWithDiscount != decimal.Zero))
        {
            movements.Add(posting.Mapper(isBelgier, invoiceHolderNumber, true));
            movements.Add(posting.Mapper(isBelgier, invoiceHolderNumber,false));
        }

        return new _v1_accountingEntriesBulk_INPUT_ITEM_entries
                {
                    Journal = "220",
                    Exercice = invoiceData.InvoiceDate.ToString("yyyy"),
                    ExternalID = invoiceData.Id.ToString(),
                    ExternalReference = invoiceData.InvoiceNumber.ToString(),
                    Description = $"INV_{invoiceData.InvoiceNumber}",
                    Date = invoiceData.InvoiceDate.ToString("yyyyMMdd"),
                    Type = invoiceData.IsCancellationInvoice ? "CN" : "INV",
                    //credittype ->CN
                    Company = invoiceData.InvoiceHolder?.Number is not null 
                                    ? (int) invoiceData.InvoiceHolder.Number
                                    : 0,
                    AmountsInvoiceCurrency = new _v1_accountingEntriesBulk_INPUT_ITEM_entries_amountsInvoiceCurrency()
                    {
                        Currency = "EUR",
                        Total = Convert.ToDouble(invoiceData.TotalNet),
                        Vat = Convert.ToDouble(invoiceData.TotalVatAmount),
                    },
                    Movements = movements.ToArray()
                };
    }
    
    private static _v1_accountingEntriesBulk_INPUT_ITEM_entries_movements Mapper(
        this OutgoingInvoicePostingDto invoicePosting,
        bool isBelgier,
        string invoiceHolderNumber,
        bool forDebit)
    {
        var konto = "700000"; // SSI 10.06.25 Laut easi sollte das dann automtatisch gesetzt werden

        /*if (forDebit)
        {
            konto = (invoicePosting.TotalNettoWithDiscount > decimal.Zero)
                ? string.IsNullOrWhiteSpace(invoiceHolderNumber) ? "700000" : invoiceHolderNumber
                : string.IsNullOrWhiteSpace(invoicePosting.FibuAccount) ? "700000" : invoicePosting.FibuAccount;
        }
        else
        {
            konto = (invoicePosting.TotalNettoWithDiscount > decimal.Zero)
                ? string.IsNullOrWhiteSpace(invoicePosting.FibuAccount) ? "700000" : invoicePosting.FibuAccount
                : string.IsNullOrWhiteSpace(invoiceHolderNumber) ? "700000" : invoiceHolderNumber;
        }*/
        
        var result = new _v1_accountingEntriesBulk_INPUT_ITEM_entries_movements
        {
            AmountsInvoiceCurrency = new _v1_accountingEntriesBulk_INPUT_ITEM_entries_movements_amountsInvoiceCurrency(),
            // wird automatisch gefüllt vomm adfinity
            GeneralAccount = konto,
            // hier auch 1 für belgier und 2 für europär
            VatType = isBelgier ? "1" : "2",
            VatCode = FormatVatShort(invoicePosting.Tax?.Vat ?? 0),
            VatRate = (double)(invoicePosting.Tax?.Vat ?? 0),
            Description = invoicePosting.Description
        };
        
        
        result.AmountsInvoiceCurrency.Currency = "EUR";
        result.AmountsInvoiceCurrency.Rate = Convert.ToDouble(Math.Round(invoicePosting.TotalNettoWithDiscount, 4, MidpointRounding.AwayFromZero));
        var totalNettoWithDiscountToUse = Math.Abs(result.AmountsInvoiceCurrency.Rate);
        
        
        if (forDebit)
        {
            result.AmountsInvoiceCurrency.Debit = totalNettoWithDiscountToUse;
            result.MovementCode = "CHA";
        }
        else
        {
            result.AmountsInvoiceCurrency.Credit = totalNettoWithDiscountToUse;
            result.MovementCode = "COL";
        }
        

        return result;
    }
    
    private static string FormatVatShort(decimal vat)
    {
        // Entfernt überflüssige Nullen und Punkt am Ende
        return vat.ToString("0.##", System.Globalization.CultureInfo.InvariantCulture);
    }
}