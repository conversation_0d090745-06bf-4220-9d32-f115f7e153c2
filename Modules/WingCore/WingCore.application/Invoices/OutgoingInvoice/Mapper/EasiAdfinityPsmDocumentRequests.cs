
using AdginityRestApi.Output.cs;
using WingCore.application.DataTransferObjects.Invoices;

namespace WingCore.application.Invoices.OutgoingInvoice.Mapper;

public record ArticleAccountsInfo(string DomesticSale, string DomesticPurchase, string AbroadSale, string AbroadPurchase);

public static class EasiAdfinityPsmDocumentRequests
{
    public static PsmDocument MapperToPsmDocument(this OutgoingInvoiceOverviewDto invoiceData, Dictionary<long, ArticleAccountsInfo> articleNumberToAccounts)
    {
        var isInLaender = invoiceData.InvoiceHolder?.CountryId?.Equals("BE") ?? false;
        
        var movements = invoiceData.ListPostings
            .Where(p => p.TotalNettoWithDiscount != decimal.Zero)
            .Select(posting => posting.Mapper(articleNumberToAccounts,isInLaender))
            .ToList();
        
        var movementType = invoiceData.TotalNet < 0 ? "CN2" : "INV2"; 
       
        return new PsmDocument
        {
            MovementType = movementType,
            InvoiceDate = invoiceData.InvoiceDate.ToString("yyyyMMdd"),
            OrderInfo = new OrderInfo
            {
                Company = invoiceData.InvoiceHolder?.Number is not null 
                    ? (int) invoiceData.InvoiceHolder.Number
                    : 0,
            },
            InvoicingInfo = new InvoicingInfo
            {
                Company = invoiceData.InvoiceHolder?.Number is not null 
                    ? (int) invoiceData.InvoiceHolder.Number
                    : 0,
                Amounts = new InvoicingAmounts
                {
                    Rate = invoiceData.TotalNet,
                    Discount = 0,
                    TotalBase = invoiceData.TotalNet
                }
            },
            ExternalReference = invoiceData.InvoiceNumber.ToString(),
            InternalReference = $"INV_{invoiceData.InvoiceNumber}",
            AnalyticalAxis = new AnalyticalAxis(),
            Movements = movements
        };
    }
    
    private static Movement Mapper(this OutgoingInvoicePostingDto invoicePosting,
        Dictionary<long, ArticleAccountsInfo> articleNumberToAccounts,
        bool isInLaender)
    {
        articleNumberToAccounts.TryGetValue(invoicePosting.Number, out var articleAccountsInfo);
        
        var account = isInLaender ? articleAccountsInfo?.DomesticSale: articleAccountsInfo?.AbroadSale;
        
        if (string.IsNullOrWhiteSpace(account))
            account = isInLaender ? "700100" : "700000";
        
        var totalNettoWithDiscount = Math.Abs(Math.Round(invoicePosting.TotalNettoWithDiscount, 4, MidpointRounding.AwayFromZero));
        var vatPercent = isInLaender ? new decimal(6) : decimal.Zero;
        var quantity = Math.Abs(Math.Round(invoicePosting.TotalQuantity / 1000, 3));
        
        var result = new Movement
        {
            ProductCode = invoicePosting.Number.ToString(),
            VatCode = FormatVatShort(vatPercent),
            VatRate = (double)vatPercent,
            NetPrice = (double)totalNettoWithDiscount,
            Quantity = (double)quantity,
            OrderAmounts = new OrderAmounts
            {
                Currency = "EUR",
                UnitPrice = (double)invoicePosting.UnitPrice
            },
            ExternalDescription = invoicePosting.Description,
            InternalDescription = invoicePosting.Description,
            GeneralAccount = account,
            AnalyticalAxis = new AnalyticalAxis()
        };

        return result;
    }
    
    private static string FormatVatShort(decimal vat)
    {
        // Entfernt überflüssige Nullen und Punkt am Ende
        return vat.ToString("0.##", System.Globalization.CultureInfo.InvariantCulture);
    }
}