using PrinterService;
using WingCore.application.DataTransferObjects.Wgs;
using WingCore.domain.Models;

namespace WingCore.application.PrintableDtos;

public class LabelWiegeschein : IPrintObject
{
    public string PrintType() => "WGS";
    public string ObjName() => "LabelWiegeschein";

    public static LabelWiegeschein Create(WgAdfinityJobDto wiegeschein)
    {
        var newObj = new LabelWiegeschein()
        {
            Wgsnr = wiegeschein.WgsNumber,
            WgsDatum = wiegeschein.WgsDatum?.ToString("d") ?? string.Empty,
            WgsLfNr = wiegeschein.WgsLfNr ?? 0,
            WgsArtNr = wiegeschein.WgsArtNr ?? 0,
            WgsPreis = wiegeschein.WgsPreis ?? 0,
            WgsMenge = wiegeschein.WgsMenge ?? 0,
            WgsGegBlgNr = wiegeschein.WgsGegBlgNr
        };
        
        return newObj;
    }

    public static LabelWiegeschein Create(Wg wiegeschein)
    {
        var newObj = new LabelWiegeschein()
        {
            Wgsnr = wiegeschein.Wgsnr,
            WgsDatum = wiegeschein.Wgsdatum?.ToString("d") ?? string.Empty,
            WgsLfNr = wiegeschein.Wgslfnr ?? 0,
            WgsArtNr = wiegeschein.WgsartNr ?? 0,
            WgsPreis = wiegeschein.Wgspreis ?? 0,
            WgsMenge = wiegeschein.Wgsmenge ?? 0,
            WgsGegBlgNr = wiegeschein.GegBlgNr ?? string.Empty,
            WgsName1 = wiegeschein.Wgsname1 ?? string.Empty,
            WgsName2 = wiegeschein.Wgsname2 ?? string.Empty,
            WgsStrasse = wiegeschein.Wgsstrasse ?? string.Empty,
            WgsPlz = wiegeschein.Wgsplz ?? string.Empty,
            WgsOrt = wiegeschein.Wgsort ?? string.Empty,
            WgsLand = wiegeschein.Wgsland ?? string.Empty,
            WgsTelefon = wiegeschein.Wgstelefon ?? string.Empty,
            WgsAnlGewicht = wiegeschein.WgsanlGewicht ?? 0,
            WgsFeuchte = wiegeschein.Wgsfeuchte ?? 0,
            WgsKontraktNr = wiegeschein.WgskontraktNr ?? 0,
            WgsZellenNr = wiegeschein.WgszellenNr ?? 0,
            WgsBemerkung = wiegeschein.Wgsbemerkung ?? string.Empty,
            WgsLkwKennz = wiegeschein.Wgslkwkennz ?? string.Empty,
            WgsLsNr = wiegeschein.WgslsNr ?? string.Empty,
            WgsEmpfaenger = wiegeschein.Wgsempfänger ?? string.Empty,
            WgsChargNr = wiegeschein.WgschargNr ?? string.Empty,
            WiegeGew = wiegeschein.WiegeGew ?? 0,
            Wiegezeit = wiegeschein.Wiegezeit?.ToString("dd.MM.yyyy HH:mm") ?? string.Empty,
            Verwieger = wiegeschein.Verwieger ?? string.Empty
        };
        
        return newObj;
    }

    public static LabelWiegeschein Dummy()
    {
        var newObj = new LabelWiegeschein
        {
            Wgsnr = 99999999,
            WgsDatum = DateTime.Now.ToString("d"),
            WgsLfNr = 12345,
            WgsName1 = "Dummy Lieferant 1",
            WgsName2 = "Dummy Lieferant 2",
            WgsStrasse = "Dummy Straße 123",
            WgsPlz = "12345",
            WgsOrt = "Dummy Stadt",
            WgsLand = "DE",
            WgsTelefon = "0123456789",
            WgsArtNr = 4711,
            WgsPreis = 250.50m,
            WgsMenge = 1000.75m,
            WgsAnlGewicht = 1050.25m,
            WgsFeuchte = 14.5m,
            WgsKontraktNr = 987654,
            WgsZellenNr = 5,
            WgsBemerkung = "Dummy Bemerkung für Wiegeschein",
            WgsLkwKennz = "AB-CD 1234",
            WgsLsNr = "LS-98765",
            WgsGegBlgNr = "GBL-12345",
            WgsEmpfaenger = "Dummy Empfänger",
            WgsChargNr = "CH-2024-001",
            WiegeGew = 1000.00m,
            Wiegezeit = DateTime.Now.ToString("dd.MM.yyyy HH:mm"),
            Verwieger = "Max Mustermann"
        };
        
        return newObj;
    }

    private string FileName { get; set; } = string.Empty;
    public string GetNameForCrationFile() => FileName;
    public string GetBeginNameFromFileList() => "WGS";
    public string GetEndNameFromFileList() => ".lst";

    // Wiegeschein properties
    public long Wgsnr { get; set; }
    public string WgsDatum { get; set; } = string.Empty;
    public long WgsLfNr { get; set; }
    public string WgsName1 { get; set; } = string.Empty;
    public string WgsName2 { get; set; } = string.Empty;
    public string WgsStrasse { get; set; } = string.Empty;
    public string WgsPlz { get; set; } = string.Empty;
    public string WgsOrt { get; set; } = string.Empty;
    public string WgsLand { get; set; } = string.Empty;
    public string WgsTelefon { get; set; } = string.Empty;
    public long WgsArtNr { get; set; }
    public decimal WgsPreis { get; set; }
    public decimal WgsMenge { get; set; }
    public decimal WgsAnlGewicht { get; set; }
    public decimal WgsFeuchte { get; set; }
    public long WgsKontraktNr { get; set; }
    public int WgsZellenNr { get; set; }
    public string WgsBemerkung { get; set; } = string.Empty;
    public string WgsLkwKennz { get; set; } = string.Empty;
    public string WgsLsNr { get; set; } = string.Empty;
    public string WgsGegBlgNr { get; set; } = string.Empty;
    public string WgsEmpfaenger { get; set; } = string.Empty;
    public string WgsChargNr { get; set; } = string.Empty;
    public decimal WiegeGew { get; set; }
    public string Wiegezeit { get; set; } = string.Empty;
    public string Verwieger { get; set; } = string.Empty;

}
