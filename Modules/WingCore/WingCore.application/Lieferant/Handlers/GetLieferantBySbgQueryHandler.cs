using MediatR;
using WingCore.application.Contract.IModels;
using WingCore.domain.Models;

namespace WingCore.application.Lieferant.Handlers;

public class GetLieferantBySbgQueryHandler(ILieferantenRepository lieferantenRepository) : IRequestHandler<GetLieferantBySbgQuery, IEnumerable<Lieferanten>>
{
    public async Task<IEnumerable<Lieferanten>> Handle(GetLieferantBySbgQuery request, CancellationToken cancellationToken)
    {
        var result = await lieferantenRepository.GetSupplierOverSearchParam(false, request.Sbg);
        return result;
    }
}