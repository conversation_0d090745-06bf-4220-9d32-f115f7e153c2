<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>
    
    <ItemGroup>
      <ProjectReference Include="..\..\..\libs\AdginityRestApi\AdginityRestApi.csproj" />
      <ProjectReference Include="..\..\..\libs\Licencing\Licencing.csproj" />
      <ProjectReference Include="..\..\..\libs\ObjectDeAndSerialize\ObjectDeAndSerialize.csproj" />
      <ProjectReference Include="..\WingCore.domain\WingCore.domain.csproj" />
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="MediatR" Version="[12.5.0]" />
        <PackageReference Include="Serilog.Extensions.Hosting" Version="9.0.0" />
        <PackageReference Include="Serilog.AspNetCore" Version="9.0.0" />
        <PackageReference Include="Serilog.Sinks.Console" Version="6.0.0" />
        <PackageReference Include="Serilog.Sinks.File" Version="7.0.0" />
    </ItemGroup>

    <ItemGroup Condition="!$([MSBuild]::IsOSPlatform('Windows'))">
        <Reference Include="PrinterService">
            <HintPath>..\..\..\PrinterService\PublishDlls\PrinterService.dll</HintPath>
            <Private>true</Private>
        </Reference>
    </ItemGroup>

    <ItemGroup Condition="$([MSBuild]::IsOSPlatform('Windows'))">
        <ProjectReference Include="..\..\..\PrinterService\PrinterService.csproj" />
    </ItemGroup>
    
</Project>
