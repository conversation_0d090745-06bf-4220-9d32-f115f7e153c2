using MediatR;
using WingCore.application.ApiDtos.MasterData.V1;
using WingCore.application.Contract.IModels;
using WingCore.application.PriceList;


namespace WingCore.application.Api.Handler;

public record AddPriceListToCustomerQueryHandler(IKontraktRepository KontraktRepository, IPreislistenRepository PreislistenRepository) : IRequestHandler<AddPriceListToCustomerQuery, IEnumerable<CustomerMasterDataDtoV1>>
{
    public Task<IEnumerable<CustomerMasterDataDtoV1>> Handle(AddPriceListToCustomerQuery request, CancellationToken cancellationToken)
    {
        var result = new List<CustomerMasterDataDtoV1>();
        
        foreach (var customer in request.Customer)
        {
            var priceListIds = new List<long?>
            {
                customer.KdpreislNr1, customer.KdpreislNr2, customer.KdpreislNr3,
                customer.KdpreislNr4, customer.KdpreislNr5, customer.KdpreislNr6
            }.Where(id => id.HasValue && id.Value != 0).ToList();

            var customerDto = (CustomerMasterDataDtoV1)customer;
            var contracts =  KontraktRepository.GetKontrakteByKundeId(customerDto.Number, false);
            var priceLists = priceListIds.SelectMany(id => PreislistenRepository.GetPreislistenById(id));
            customerDto.PriceList = priceLists.Select(PriceListMapper.MapPreislistenToPriceListDto).ToList();
            customerDto.PriceList.ForEach(p => p.CustomerNumber = customerDto.Number);
            customerDto.Contracts = contracts.Select(c => (ContractDtoV1)c).ToList();
            result.Add(customerDto);
        }
        return Task.FromResult<IEnumerable<CustomerMasterDataDtoV1>>(result);
    }
}