using MediatR;
using WingCore.application.ApiDtos.MasterData.V1;
using WingCore.application.Contract.IModels.WarenEingangDb;

namespace WingCore.application.Api.Handler;

public record GetIncomingGoodsQueryHandler(IFBERepository FbeRepository) : IRequestHandler<GetIncomingGoodsQuery, IEnumerable<IncomingGoodsDtoV1>>
{
    public Task<IEnumerable<IncomingGoodsDtoV1>> Handle(GetIncomingGoodsQuery request, CancellationToken cancellationToken)
    {
        var incomingGoods = FbeRepository.GetAllFBE();

        return Task.FromResult<IEnumerable<IncomingGoodsDtoV1>>(incomingGoods.Select(c => (FbeDtoV1)c));
    }
}
