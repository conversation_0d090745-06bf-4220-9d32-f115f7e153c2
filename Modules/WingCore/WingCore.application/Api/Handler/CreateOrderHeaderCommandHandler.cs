using MediatR;
using WingCore.application.ApiDtos;
using WingCore.application.Contract.IModels;
using WingCore.application.Contract.Services;

namespace WingCore.application.Api.Handler;

public class CreateOrderHeaderCommandHandler(
    IAuftragskopfRepository auftragskopfRepository,
    IOrderService orderService,
    IAuftragsposRepository auftragsposRepository,
    IKundenRepository kundenRepository,
    IArtikelRepository artikelRepository) : IRequestHandler<CreateOrderHeaderCommand, long>
{
    public async Task<long> Handle(CreateOrderHeaderCommand request, CancellationToken cancellationToken)
    {
        var highestOrdernumber = auftragskopfRepository.GetMaxAuftragsnummer();
        var customer = await kundenRepository.GetCustomerOverNumber(false, request.OrderHeaderCreate.CustomerNumber);
        if (customer is null)
            throw new Exception("No customer in database found with provided number");

        if (highestOrdernumber is null)
            throw new Exception("Could not Create OrderHeader");
        highestOrdernumber++;
        var auftragskopf = request.OrderHeaderCreate.ToAuftragskopf(highestOrdernumber.Value);
        await auftragskopfRepository.DisableInsertTrigger();
        await orderService.Create("system",auftragskopf);
        await auftragskopfRepository.EnableInsertTrigger();

        if (request.OrderHeaderCreate.OrderLineList.Any())
        {
            var orderLineCreateList =
                request.OrderHeaderCreate.OrderLineList.Select(o => o.ToAuftragspos(highestOrdernumber.Value));
            foreach (var orderLine in orderLineCreateList)
            {
                var articel = await artikelRepository.GetArtikelById(orderLine.ArtikelNummer.Value!, false);
                if (articel is null)
                    throw new Exception("No Artikel in database found with provided number");
                auftragsposRepository.Create(orderLine);
            }
        } 
        await auftragsposRepository.SaveChangesAsync();

        return highestOrdernumber.Value;
    }
}