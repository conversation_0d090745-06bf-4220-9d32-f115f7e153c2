using MediatR;
using WingCore.application.ApiDtos;
using WingCore.application.ApiDtos.MasterData.V1;
using WingCore.application.Contract.IModels;

namespace WingCore.application.Api.Handler;

public class GetSupplierDtoV1ByNumberQueryHandler(ILieferantenRepository lieferantenRepository, IKontraktRepository kontraktRepository) : IRequestHandler<GetSupplierDtoV1ByNumberQuery, SupplierDtoV1>
{
    public async Task<SupplierDtoV1> Handle(GetSupplierDtoV1ByNumberQuery request, CancellationToken cancellationToken)
    {
        var supplierList = (await lieferantenRepository.GetSupplierOverNumber(false, request.Number)).ToList();
        if(!supplierList.Any())
            throw new Exception("Supplier not found");
        var supplier = supplierList.First().ToSupplierDtoV1();
        var contracts =  kontraktRepository.GetKontrakteByKundeId(supplier.Number, false);
        supplier.Contracts = contracts.Select(c => (ContractDtoV1)c).ToList();

        return supplier;
    }
}