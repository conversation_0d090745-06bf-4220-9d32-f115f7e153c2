using MediatR;
using WingCore.application.ApiDtos;
using WingCore.application.ApiDtos.OrderData;
using WingCore.application.Contract.IModels;

namespace WingCore.application.Api.Handler;

public class GetAllOrderHeaderQueryHandler(
    IAuftragskopfRepository auftragskopfRepository,
    IAuftragsposRepository auftragsposRepository,
    IKundenRepository kundenRepository)
    : IRequestHandler<GetAllOrderHeaderQuery, ICollection<OrderHeaderDtoV1>>
{
    public async Task<ICollection<OrderHeaderDtoV1>> Handle(GetAllOrderHeaderQuery request,
        CancellationToken cancellationToken)
    {
        var autragKopfList = await auftragskopfRepository.GetAll(false);
        var orderHeaderDtoList = autragKopfList.Select(c => c.ToOrderHeaderDtoV1Async()).Where(o => o.Send == false).ToList();
        var sendOrderheaderList = orderHeaderDtoList.Select(o => o.OrderNumber).ToList();
        await auftragskopfRepository.SetSend(sendOrderheaderList);
        foreach (var order in orderHeaderDtoList)
        {
            var customer = await kundenRepository.GetCustomerOverNumber(false, order.CustomerNumber);
            if (customer is null)
                continue;
            order.Customer = customer;
        }
        if (!request.WithOrderLine)
            return orderHeaderDtoList.Where(o => o.Category == request.Category).ToList();
        
        foreach (var orderHeader in orderHeaderDtoList)
        {
            var orderlineList = await auftragsposRepository.GetAuftragsposByOrderNumber(false, orderHeader.OrderNumber);
            orderHeader.OrderLineList = orderlineList.Select(c => c.ToOrderLineDtoV1()).ToList();
        }
        var result = orderHeaderDtoList.Where(o => o.Category == request.Category).ToList();
        return result;
    }
}