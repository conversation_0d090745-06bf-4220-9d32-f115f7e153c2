using MediatR;
using WingCore.application.Contract.IModels;
using WingCore.domain.Models;

namespace WingCore.application.Api.Handler;

public record GetCustomerBySearchParamQueryHandler(IKundenRepository repository): IRequestHandler<GetCustomerBySearchParamQuery, IEnumerable<Kunden>>
{
    public async Task<IEnumerable<Kunden>> Handle(GetCustomerBySearchParamQuery request, CancellationToken cancellationToken)
    {
        var customerList = await repository.GetCustomerOverSearchParam(true, request.customerSearchParam);
        return customerList;
    }
}