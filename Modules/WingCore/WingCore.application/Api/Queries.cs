using MediatR;
using WingCore.application.ApiDtos.MasterData.V1;
using WingCore.application.ApiDtos.OrderData;
using WingCore.application.Contract.IModels.Helper;
using WingCore.application.DataTransferObjects.Invoices;
using WingCore.domain.Models;

namespace WingCore.application.Api;


public record GetSupplierDtoV1ByNumberQuery(long Number) : IRequest<SupplierDtoV1>;
public record GetAllSupplierDtoV1Query(DateTime LastChange) : IRequest<ICollection<SupplierDtoV1>>;
public record GetCustomerBySearchParamQuery(CustomerSearchParam customerSearchParam) : IRequest<IEnumerable<Kunden>>;
public record GetInvoiceBySearchParamQuery(InvoiceSearchParam searchParam) : IRequest<IEnumerable<OutgoingInvoiceOverviewDto>>;
public record GetArticleBySearchParamQuery(ArticleSearchParam ArticleSearchParam) : IRequest<List<Artikel>>;
public record AddPriceListToCustomerQuery(IEnumerable<Kunden> Customer) : IRequest<IEnumerable<CustomerMasterDataDtoV1>>;

public record GetIncomingGoodsQuery() : IRequest<IEnumerable<IncomingGoodsDtoV1>>;

public record GetAllOrderHeaderQuery(bool WithOrderLine, string? Category = null) : IRequest<ICollection<OrderHeaderDtoV1>>;

public record GetAllOrderLineQuery : IRequest<ICollection<OrderLineDtoV1>>;

public record GetOrderLineByOrderNumberQuery(long Id) : IRequest<ICollection<OrderLineDtoV1>>;
public record GetOrderHeaderByOrderNumberQuery(long OrderNumber, bool WithOrderLine) : IRequest<OrderHeaderDtoV1>;