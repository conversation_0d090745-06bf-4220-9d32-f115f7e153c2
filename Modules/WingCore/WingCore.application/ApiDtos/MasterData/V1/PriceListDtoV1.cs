namespace WingCore.application.ApiDtos.MasterData.V1;

public record PriceListDtoV1 : PricelistItemDtoV1
{
    public long Nr { get; set; }

    public short? Kng { get; set; }

    public long ArtNr { get; set; }

    public string? ArtBez { get; set; }

    public string? ArtBez2 { get; set; }

    public string? Bzg { get; set; }

    public decimal? ArtPrice { get; set; }

    public decimal? EkPrice { get; set; }

    public decimal? ArtProz { get; set; }

    public int Pos { get; set; }

    public decimal? Freight { get; set; }

    public DateTime? Date1 { get; set; }

    public DateTime? Date2 { get; set; }

    public decimal? Price1 { get; set; }

    public decimal? Price2 { get; set; }

    public bool? Send { get; set; }

    public string SysUser { get; set; } = string.Empty;

    public DateTime? SysTime { get; set; }

    public long Id { get; set; }
    
    public static PriceListDtoV1 Create()
    {
        return new PriceListDtoV1
        {
            ArtBez = string.Empty,
            ArtBez2 = string.Empty,
            ArtNr = 0,
            ArtPrice = 0,
            ArtProz = 0,
            Bzg = string.Empty,
            Date1 = DateTime.Now,
            Date2 = DateTime.Now,
            EkPrice = 0,
            SysUser = string.Empty
        };
    } 
}