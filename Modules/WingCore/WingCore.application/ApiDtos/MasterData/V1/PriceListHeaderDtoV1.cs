namespace WingCore.application.ApiDtos.MasterData.V1;

public record PriceListHeaderDtoV1 
{
    public long Number { get; set; }

    public string? Sbg { get; set; }

    public string? Text1 { get; set; }

    public string? Text2 { get; set; }

    public DateTime Date { get; set; }

    public short Art { get; set; }

    public bool? Excl { get; set; }

    public string SysUser { get; set; } = string.Empty;

    public DateTime? SysTime { get; set; }

    public long Id { get; set; }

    public bool LfDate { get; set; }

    public int? Category { get; set; }
    
    public static PriceListHeaderDtoV1 Create()
    {
        return new PriceListHeaderDtoV1
        {
            Number = 0,
            Sbg = string.Empty,
            Text1 = string.Empty,
            Text2 = string.Empty,
            Date = DateTime.Now,
            Art = 0,
            Excl = false,
            SysUser = string.Empty,
            SysTime = DateTime.Now,
            Id = 0,
            LfDate = false,
            Category = 0
        };
    } 
}