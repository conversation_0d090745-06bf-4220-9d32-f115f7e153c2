using WingCore.domain.Models;

namespace WingCore.application.ApiDtos.MasterData.V1;

public record ArticelDtoV1
{
    public long Number { get; init; }
    public string? Name { get; init; }
    public string? Name2 { get; init; }
    public string? Name3 { get; init; }
    public string? Name4 { get; init; }
    public decimal? Price { get; init; }
    //public string BasicUnit { get; init; } = string.Empty;
    public long? Weight { get; init; }
    public long? ColliContent { get; init; }
    public string? ReferenceSize { get; init; }
    public DateTime? LastChange { get; init; }

    public static ArticelDtoV1 Create(Artikel a)
    {
        return new ArticelDtoV1
        {
            Number = a.Artikelnr,
            Name = a.ArtBezText1,
            Name2 = a.ArtBezText2,
            Name3 = a.ArtBezText3,
            Name4 = a.ArtBezText4,
            Price = a.<PERSON>,
            Weight = a.ArtKolliInhalt,
            ColliContent = a.Art<PERSON>olliInhalt,
            ReferenceSize = a.ArtBezugsgr,
            //BasicUnit = ,
            LastChange = a.ArtLetzteÄnderung
        };
    }
}