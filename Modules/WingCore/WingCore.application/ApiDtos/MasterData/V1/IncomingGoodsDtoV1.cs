namespace WingCore.application.ApiDtos.MasterData.V1;

public abstract class IncomingGoodsDtoV1
{
    public string Tag { get; init; } = "WarenEingangBase"; 
    public long WeightTicketNumber { get; init; }
    public string ArticleName { get; init; } = string.Empty;
    public long ArticleNumber { get; init; }
    public string SupplierName { get; init; } = string.Empty;
    public long SupplierNumber { get; init; }
    public decimal? Tara { get; init; }
    public decimal? Silo { get; init; }
    public List<LabEntryDtoV1> LabEntries { get; init; } = [];
}