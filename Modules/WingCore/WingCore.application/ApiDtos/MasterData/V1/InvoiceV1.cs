using WingCore.application.DataTransferObjects.Invoices;
using WingCore.domain.Models;

namespace WingCore.application.ApiDtos.MasterData.V1;

public record InvoiceV1
{
    public long InvoiceNumber { get; init; }
    public DateTime? InvoiceDate { get; init; }
    public long? GoodsRecipientNumber { get; init; }
    public long? InvoiceHolderNumber { get; init; }
    
    public decimal TotalNetto { get; init; }
    public decimal TotalGross { get; init; }
    public decimal TotalVatAmount { get; init; }
    public string? OrderNumber { get; init; }
    public bool Canceled { get; init; }
    public bool IsCancellationInvoice { get; init; }
    public long? OriginalInvoiceNumber { get; init; }
    public long? CancelationInvoiceNumber { get; init; }

    public List<InvoicePositionV1> Positions { get; set; } = [];
    
    public static InvoiceV1 Create(OutgoingInvoiceOverviewDto o)
    {
        var obj = new InvoiceV1
        {
            InvoiceNumber = o.InvoiceNumber,
            InvoiceDate = o.InvoiceDate,
            GoodsRecipientNumber = o.GoodsRecipients?.Number,
            InvoiceHolderNumber = o.InvoiceHolder?.Number,
            TotalNetto = o.TotalNet,
            TotalGross = o.TotalGross,
            TotalVatAmount = o.TotalVatAmount,
            OrderNumber = o.OrderNumber,
            Canceled = o.WasCanceled,
            IsCancellationInvoice = o.IsCancellationInvoice,
            
            OriginalInvoiceNumber = o.WasCanceled ? o.CancelOrCreditNoteInvoiceNumber : null,
            CancelationInvoiceNumber = o.IsCancellationInvoice ? o.CancelOrCreditNoteInvoiceNumber : null,
            Positions = o.ListPostings.Select(p => (InvoicePositionV1)p).ToList()
        };

        return obj;
    }
}

public record InvoicePositionV1(long Id,
                                string Name,
                                long ArticleNumber,
                                decimal Quantity,
                                decimal TotalNetto,
                                decimal TotalGross,
                                decimal TotalDiscount,
                                decimal TotalVatAmount,
                                decimal Vat,
                                decimal VatCode)
{
    public static implicit operator InvoicePositionV1(OutgoingInvoicePostingDto p)
    {
        return new InvoicePositionV1(
                p.Id,
                p.Description,
                p.Number,
                p.Quantity, 
                p.TotalNetto,
                p.TotalGross,
                p.TotalDiscount,
                p.TotalGross - p.TotalNettoWithDiscount,
                p.Tax.Vat,
            p.Tax.VatId
        );
    }
}