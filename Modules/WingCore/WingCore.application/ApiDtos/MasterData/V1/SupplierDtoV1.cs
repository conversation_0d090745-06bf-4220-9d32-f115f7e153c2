using System.Text.Json.Serialization;

namespace WingCore.application.ApiDtos.MasterData.V1;

public record SupplierDtoV1
{
    public long Number { get; init; }
    public string? Matchcode { get; init; }
    public string? Name { get; init; }
    public string? Name2 { get; init; }
    public string? Name3 { get; init; }
    public AdressV1? Address { get; init; }
    public string? Email { get; init; }
    public string? MobileNumber { get; init; }
    public string? PhoneNumber { get; init; }
    public string? PhoneNumber2 { get; init; }
    public DateTime? LastChange { get; init; }
    public long? RepresentativenNumber { get; init; }

    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingDefault)]
    public List<ContractDtoV1> Contracts { get; set; } = [];
}