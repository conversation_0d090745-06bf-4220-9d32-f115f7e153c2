using System.Text.Json.Serialization;

namespace WingCore.application.ApiDtos.MasterData.V1;

[JsonDerivedType(typeof(PriceListDtoV1))]
[JsonDerivedType(typeof(ContractDtoV1))]
public abstract record PricelistItemDtoV1
{
    public long Number { get; init; }
    public decimal Price { get; init; }
    public long ArticleNumber { get; init; }
    public long CustomerNumber { get; set; }
    public DateTime? StartDate { get; init; }
    public DateTime? EndDate { get; init; }
}