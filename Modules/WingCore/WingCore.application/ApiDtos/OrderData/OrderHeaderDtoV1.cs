using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;
using WingCore.application.ApiDtos.MasterData.V1;

namespace WingCore.application.ApiDtos.OrderData;

public record OrderHeaderDtoV1()
{
    [Required]
    public long OrderNumber { get; set;}
    [Required]
    public string Category { get; set; } = string.Empty;
    public DateTime OrderDate { get; set; } = DateTime.MinValue;
    public CustomerMasterDataDtoV1 Customer { get; set; } = new();
    public string Trucknumber { get; set; } = string.Empty;
    
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingDefault)]
    public List<OrderLineDtoV1> OrderLineList { get; set; } = [];
    public string Status { get; set; } = string.Empty;
    public long CustomerNumber { get; set; }
    
    [JsonIgnore]
    public bool Send { get; set; }      
    
}

public static class  OrderHeaderCategoryV1
{
    public const string TankerTruckString = "Tanker Truck";
    public const string PlanzugString = "Tarpaulin Truck";
    public const string PalletString = "Palletized Goods";
    
    public static List<string> GetAllowedValues() =>  [TankerTruckString, PlanzugString, PalletString];
}

public static class OrderHeaderStatusV1
{
    public const string OrderString = "Order";
    public const string DeliveryNoteString = "Delivery Note";
    public const string DeletedString = "Deleted";

    public static IList<string> All = [OrderString, DeliveryNoteString, DeletedString];
}