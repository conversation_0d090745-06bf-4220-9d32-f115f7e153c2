using CommandLine;
using QuartzScheduler;
using ServiceDefaults;
using WingCore.application.Contract.IModels;
using WinGng.SchedulerBackgroundService;
using ApplicationPath = WinGng.SchedulerBackgroundService.ApplicationPath;

var builder = Host.CreateApplicationBuilder(args);

var mnParameter = "98";
var stammDbConfigFilePath = "/Users/<USER>/Library/DBConfig";
Parser.Default.ParseArguments<CommandParameterOptions>(args)
    .WithParsed(opts =>
    {
        mnParameter = opts.MandantNumber;
        stammDbConfigFilePath = opts.StammDbConfigFilePath;
    });

builder.Configuration["mnParameter"] = mnParameter;
builder.Configuration["stammDbConfigFilePath"] = stammDbConfigFilePath;




builder.Services.AddSingleton<IApplicationPath, ApplicationPath>();
        
builder.Services.ConfigureLoggerService();
builder.Services.ConfigureSqlContext();
builder.Services.ConfigureServiceManager();

builder.Services.AddHostedService<Worker>();
builder.Services.ConfigureScheduler(true);


var host = builder.Build();
host.Run();