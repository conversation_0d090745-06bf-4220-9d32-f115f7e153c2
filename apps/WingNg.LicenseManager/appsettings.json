{"ConnectionStrings": {"license-db": "Host=localhost;Port=54320;Database=LicenseDB;Username=postgres;Password=Test123"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "DetailedErrors": "true", "Kestrel": {"EndPoints": {"Http": {"Url": "http://*:7001"}, "Https": {"Url": "https://*:7000", "Certificate": {"Path": "cert/vas.software.pfx", "Password": "Vas.Software.2024"}}}}}