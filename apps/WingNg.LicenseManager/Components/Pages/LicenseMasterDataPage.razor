@page "/licenseMasterData"

@inherits Common.BasePage


@using licenses.application.LicenseMasterDatas
@using licenses.domain.LicenseMasterDatas
@using licenses.domain.LicenseMasterDatas.Events
@using WingNg.LicenseManager.Components.Common
@using Wolverine

@inject IMessageBus MessageBus
@inject IDialogService DialogService


<MainBody Title="Lizenz-Stammdaten" IsLoading="@IsLoading" IsBodyVisible="@IsBodyVisible" FunctionBarCollapsible="false">
    <FunctionBar>
        <FluentNavLink OnClick="Create" Icon="@(new Icons.Regular.Size20.Add())" hidden=@(SelectedLicenseMasterData.Id != Guid.Empty) IconColor="Color.Accent" Tooltip="Neu">Neu</FluentNavLink>
        <FluentNavLink OnClick="Reset" Icon="@(new Icons.Regular.Size20.Document())" hidden=@(SelectedLicenseMasterData.Id == Guid.Empty) IconColor="Color.Accent" Tooltip="Leeren">Leeren</FluentNavLink>
        <FluentNavLink OnClick="Modify" Icon="@(new Icons.Regular.Size20.Save())" hidden=@(SelectedLicenseMasterData.Id == Guid.Empty) IconColor="Color.Accent" Tooltip="Speichern">Speichern</FluentNavLink>
        <FluentNavLink OnClick="Delete" Icon="@(new Icons.Regular.Size20.Delete())" hidden=@(SelectedLicenseMasterData.Id == Guid.Empty) IconColor="Color.Accent" Tooltip="Löschen">Löschen</FluentNavLink>
        <FluentNavLink OnClick="OpenSearch" Icon="@(new Icons.Regular.Size20.Search())" IconColor="Color.Accent" Tooltip="Lizenzsuche">Suchen</FluentNavLink>
    </FunctionBar>
    <BaseBody>
        <FluentGrid Spacing="1" Justify="JustifyContent.Center" Style="min-width: 100%; min-height: 100%;padding: 5px">

            @if (Env.IsDevelopment())
            {
                <FluentGridItem xs="3" sm="2">
                    ID
                </FluentGridItem>
                <FluentGridItem xs="9" sm="10">
                    @SelectedLicenseMasterData.Id
                </FluentGridItem>
            }
            <FluentGridItem xs="6" sm="2">Kürzel</FluentGridItem>
            <FluentGridItem xs="6" sm="4">
                <FluentTextField @ref=FocusDesc Style="min-width: 100%; max-width: 100%" @bind-Value="@SelectedLicenseMasterData.Kuerzel" Disabled="@(SelectedLicenseMasterData.Id != Guid.Empty)" Required="@(SelectedLicenseMasterData.Id == Guid.Empty)"/>
            </FluentGridItem>
            <FluentGridItem xs="6" sm="2">Beschreibung</FluentGridItem>
            <FluentGridItem xs="6" sm="4">
                <FluentTextField Style="min-width: 100%; max-width: 100%" @bind-Value="@SelectedLicenseMasterData.Name" Required="true"/>
            </FluentGridItem>

            <FluentGridItem xs="6" sm="2">Claims</FluentGridItem>
            <FluentGridItem xs="6" sm="4">
                <FluentTextField Style="min-width: 100%; max-width: 100%" @bind-Value="@SelectedLicenseMasterData.ClaimsStr" Required="true"/>
            </FluentGridItem>
            <FluentGridItem xs="6" sm="2">Nur bei Benutzung</FluentGridItem>
            <FluentGridItem xs="6" sm="4">
                <FluentCheckbox Style="min-width: 100%; max-width: 100%" @bind-Value="@SelectedLicenseMasterData.UseOnlyWhenInUse"/>
            </FluentGridItem>
            <FluentGridItem xs="12" sm="6"></FluentGridItem>
        </FluentGrid>
    </BaseBody>
</MainBody>

@code {
    bool IsBodyVisible => SelectedLicenseMasterData.Id == Guid.Empty || SelectedLicenseMasterData.Id != Guid.Empty;
    private LicenseMasterDataDto SelectedLicenseMasterData { get; set; } = new();

    FluentTextField? FocusDesc { get; set; }

    protected override async Task LoadDataOnParametersSetAsync()
    {
        if (!string.IsNullOrEmpty(LoadingIdFromUiParameter))
            await LoadLicense(LoadingIdFromUiParameter);
    }

    protected override async void OnAfterRender(bool firstRender)
    {
        if (!firstRender) return;

        await StartLoading();
        try
        {
            await LoadLicense(LoadingIdFromUiParameter);
        }
        catch (Exception e)
        {
            StopLoading();
            await DialogService.ShowErrorAsync(e.Message);
        }

        StopLoading();
        StateHasChanged();
    }

    bool _startCreate = false;

    private async void Reset() => await LoadLicense(null);

    private async void Create()
    {
        if (_startCreate)
            return;

        _startCreate = true;

        StateHasChanged();
        await StartLoading();
        try
        {
            var createLicenseMasterDataCommand = new CreateLicenseMasterDataCommand(SelectedLicenseMasterData.Kuerzel,
                                                                                    SelectedLicenseMasterData.Name,
                                                                                    SelectedLicenseMasterData.Claims,
                                                                                    SelectedLicenseMasterData.UseOnlyWhenInUse);
            await MessageBus.InvokeAsync<LicenseMasterData?>(createLicenseMasterDataCommand, CancellationToken.None)
                .ContinueWith(t =>
                {
                    if (t.Result is not null)
                    {
                        SelectedLicenseMasterData = new LicenseMasterDataDto
                        {
                            Id = t.Result.Id,
                            Kuerzel = t.Result.Kuerzel,
                            Name = t.Result.Name,
                            Claims = t.Result.Claims,
                            UseOnlyWhenInUse = t.Result.UseOnlyWhenInUse,
                            CreationDateTime = t.Result.CreationDateTime,
                            CreationUserName = t.Result.CreationUserName
                        };
                    }
                });

            FocusDesc?.FocusAsync();
            _startCreate = false;
            StopLoading();
        }
        catch (Exception e)
        {
            StopLoading();
            await DialogService.ShowErrorAsync(e.Message);
            _startCreate = false;
        }

        StateHasChanged();
    }

    private async void Modify()
    {
        await StartLoading();
        try
        {
            var modifyClaimsToLicenseMasterDataCommand = new ModifyLicenseMasterDataCommand(SelectedLicenseMasterData.Id,
                new ModifyLicenseMasterDataEvent(SelectedLicenseMasterData.Name,
                                                        SelectedLicenseMasterData.Claims,
                                                        SelectedLicenseMasterData.UseOnlyWhenInUse,
                                                        ""));
            await MessageBus.InvokeAsync(modifyClaimsToLicenseMasterDataCommand, CancellationToken.None);

            FocusDesc?.FocusAsync();
            StopLoading();
        }
        catch (Exception e)
        {
            StopLoading();
            await DialogService.ShowErrorAsync(e.Message);
            await LoadLicense(SelectedLicenseMasterData.Id.ToString(), true);
        }

        StateHasChanged();
    }

    private async void Delete()
    {
        if (SelectedLicenseMasterData.Id == Guid.Empty)
            return;

        await StartLoading();
        try
        {
            var deleteLicenseMasterDataCommand = new DeleteLicenseMasterDataCommand(SelectedLicenseMasterData.Id);
            await MessageBus.InvokeAsync(deleteLicenseMasterDataCommand, CancellationToken.None);

            await LoadLicense(null);

            FocusDesc?.FocusAsync();
            StopLoading();
        }
        catch (Exception e)
        {
            StopLoading();
            await DialogService.ShowErrorAsync(e.Message);
        }

        StateHasChanged();
    }

    private async Task LoadLicense(string? licenceMasterDatatIdStr, bool forceLoad = false)
    {
        FocusDesc?.FocusAsync();
        Guid.TryParse(licenceMasterDatatIdStr, out var licenceMasterDatatId);

        if (licenceMasterDatatId == Guid.Empty)
        {
            SelectedLicenseMasterData = new LicenseMasterDataDto();
            return;
        }

        if (!forceLoad && SelectedLicenseMasterData.Id == licenceMasterDatatId)
            return;

        try
        {
            await MessageBus.InvokeAsync<LicenseMasterData?>(new LicenseMasterDataQuery(licenceMasterDatatId), CancellationToken.None)
                .ContinueWith(t =>
                {
                    if (t.Result is not null)
                    {
                        SelectedLicenseMasterData = new LicenseMasterDataDto
                        {
                            Id = t.Result.Id,
                            Kuerzel = t.Result.Kuerzel,
                            Name = t.Result.Name,
                            Claims = t.Result.Claims,
                            UseOnlyWhenInUse = t.Result.UseOnlyWhenInUse,
                            CreationDateTime = t.Result.CreationDateTime,
                            CreationUserName = t.Result.CreationUserName
                        };
                    }
                });
        }
        catch (Exception e)
        {
            await DialogService.ShowErrorAsync(e.Message);
            SelectedLicenseMasterData = new LicenseMasterDataDto();
        }
    }

    protected async void OpenSearch()
    {
        await DoOpenSearch().ContinueWith(t => t.Wait());
        FocusDesc?.FocusAsync();
    }

    private async Task DoOpenSearch()
    {
        var searchParam = new SearchLicenseMasterDataDialog.SearchParamForLicenseMasterData();

        var dialog = await DialogService.ShowDialogAsync<SearchLicenseMasterDataDialog>(searchParam, new DialogParameters()
        {
            Height = "auto",
            Width = "80%",
            PreventScroll = false,
        });

        var result = await dialog.Result;
        LicenseMasterData? selectedLicenseMasterData = null;
        if (!result.Cancelled && result.Data != null)
        {
            selectedLicenseMasterData = (LicenseMasterData)result.Data;
        }

        if (selectedLicenseMasterData is not null)
        {
            SelectedLicenseMasterData = new LicenseMasterDataDto()
            {
                Id = selectedLicenseMasterData.Id,
                Kuerzel = selectedLicenseMasterData.Kuerzel,
                Name = selectedLicenseMasterData.Name,
                Claims = selectedLicenseMasterData.Claims,
                UseOnlyWhenInUse = selectedLicenseMasterData.UseOnlyWhenInUse,
                CreationDateTime = selectedLicenseMasterData.CreationDateTime,
                CreationUserName = selectedLicenseMasterData.CreationUserName
            };
        }

        NavigationManager.NavigateTo($"licenseMasterData?Id={SelectedLicenseMasterData.Id}", forceLoad: false);

        StateHasChanged();
    }

}