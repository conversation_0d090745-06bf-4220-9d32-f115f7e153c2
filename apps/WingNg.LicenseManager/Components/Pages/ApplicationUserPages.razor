@page "/applicationUser"

@inherits Common.BasePage

@using System.Threading
@using Licencing
@using licenses.application.ApplicationUsers
@using licenses.application.LicensesCertificate
@using licenses.domain.ApplicationUsers
@using licenses.domain.Models
@using WingNg.LicenseManager.Components.Common
@using Wolverine

@inject IMessageBus MessageBus
@inject IDialogService DialogService
@inject IJSRuntime JsRuntime

<MainBody Title="Benutzer" IsLoading="@IsLoading" IsBodyVisible="@_isBodyVisible" NeedFullPlace="true" FunctionBarCollapsible="false">
    <FunctionBar>
        <FluentNavLink OnClick="CreateLicenseCertificateButton" Icon="@(new Icons.Regular.Size20.DocumentOnePageAdd())" IconColor="Color.Accent" Tooltip="Zertifikat erstellen">Zertifikat erstellen</FluentNavLink>
        <FluentNavLink OnClick="OpenSearch" Icon="@(new Icons.Regular.Size20.Search())" IconColor="Color.Accent" Tooltip="User Suche">Suchen</FluentNavLink>
    </FunctionBar>
    <BaseBody>
        <FluentGrid Justify="JustifyContent.Center" Style="min-width: 100%; min-height: 100%;">
            <FluentGridItem xs="12">
                <FluentAccordion Style="min-height: 40rem">
                    @if (SelectedApplicationUser.Id != Guid.Empty)
                    {
                        <FluentAccordionItem Expanded="true" Heading=@($"Date") Style="background-color: lightgrey;">
                            <FluentGrid Spacing="3" Justify="JustifyContent.FlexStart" Style="min-width: 100%">

                                <FluentGridItem xs="6" sm="2">Name</FluentGridItem>
                                <FluentGridItem xs="6" sm="4">
                                    <FluentTextField Style="min-width:100%; max-width:100%" @bind-Value="@SelectedApplicationUser.Name"/>
                                </FluentGridItem>

                                <FluentGridItem xs="6" sm="2">E-Mail</FluentGridItem>
                                <FluentGridItem xs="6" sm="4">
                                    <FluentTextField Style="min-width:100%; max-width:100%" @bind-Value="@SelectedApplicationUser.Email"/>
                                </FluentGridItem>

                                <FluentGridItem xs="6" sm="2">Ist Online</FluentGridItem>
                                <FluentGridItem xs="6" sm="4">
                                    <FluentCheckbox ReadOnly="true" Style="min-width:100%; max-width:100%" @bind-Value="@SelectedApplicationUser.IsOnline"/>
                                </FluentGridItem>

                            </FluentGrid>
                        </FluentAccordionItem>
                        <FluentAccordionItem Expanded="true" Heading=@($"Tenant: {SelectedApplicationUser.TenantData?.Name}") Style="background-color: lightgrey;">
                            <FluentGrid Spacing="3" Justify="JustifyContent.FlexStart" Style="min-width: 100%">
                                <FluentGridItem xs="12" sm="12">
                                    <FluentButton
                                        OnClick="@OpenTenant">
                                        <FluentIcon Value="@(new Icons.Regular.Size20.PeopleSwap())" Color="Color.Accent" Slot="start"/>
                                    </FluentButton>
                                </FluentGridItem>
                                <FluentGridItem xs="6" sm="2">Name</FluentGridItem>
                                <FluentGridItem xs="6" sm="4">
                                    <FluentTextField ReadOnly="true" Style="min-width:100%; max-width:100%" Value="@SelectedApplicationUser.TenantData?.Name"/>
                                </FluentGridItem>

                                <FluentGridItem xs="6" sm="2">E-Mail</FluentGridItem>
                                <FluentGridItem xs="6" sm="4">
                                    <FluentTextField ReadOnly="true" Style="min-width:100%; max-width:100%" Value="@SelectedApplicationUser.TenantData?.Email"/>
                                </FluentGridItem>
                            </FluentGrid>
                        </FluentAccordionItem>
                        <FluentAccordionItem Expanded="true" Heading="Erstellte Zertifikate" Style="background-color: lightgrey;">
                            @if (SelectedApplicationUser.LicenseCertificatesList is not null)
                            {
                                <FluentGrid Justify="JustifyContent.Center">
                                    <FluentGridItem Justify="JustifyContent.Center" Style="min-width: 96%; min-height: 100%;height: 100%;">
                                        <FluentDataGrid Items="@SelectedApplicationUser.LicenseCertificatesList.AsQueryable()"
                                                        ShowHover="@SelectFromEntireRow"
                                                        Style="overflow:auto;min-width: 100%">

                                            <SelectColumn TGridItem=LicenseCertificate
                                                          SelectMode=DataGridSelectMode.Single
                                                          SelectFromEntireRow="@SelectFromEntireRow"
                                                          @bind-SelectedItems="@SelectedItems"/>
                                            <PropertyColumn Property="@(p => p.Id)" Sortable="true" Title="Id"/>
                                            <PropertyColumn Property="@(p => p.ExpireAt)" Sortable="true" Title="Gültig bis"/>
                                            <TemplateColumn Title="Key">
                                                @context.ToLicenseData().ToBase64String()
                                            </TemplateColumn>
                                            <TemplateColumn Title="Claims">
                                                @string.Join(", ", @context.ToLicenseData().GetClaimsFromLicence())
                                            </TemplateColumn>
                                            <TemplateColumn Title="" Align="@Align.End">
                                                <FluentButton aria-label="Kopieren" IconEnd="@(new Icons.Regular.Size16.Copy())" OnClick="@(() => WriteLicenseBase64ToClipboardAsync(@context))"/>
                                                <FluentButton aria-label="Löschen" IconEnd="@(new Icons.Regular.Size16.Delete())" OnClick="@(() => DeleteLicenseCertificate(context))"/>
                                                <FluentButton aria-label="Lizenz Zertifikat" IconEnd="@(new Icons.Regular.Size16.Key())" OnClick="@(() => OpenLicenseCertificateButton(context.Id))"/>
                                            </TemplateColumn>
                                        </FluentDataGrid>
                                    </FluentGridItem>
                                    <FluentGridItem Justify="JustifyContent.Center" Style="min-width: 4%; min-height: 100%;height: 100%;">
                                        <FluentNavMenu Collapsible="false" Expanded="false">
                                            @* <FluentNavLink OnClick="CreateLicenseCertificateButton" Icon="@(new Icons.Regular.Size20.DocumentOnePageAdd())" IconColor="Color.FillInverse" Tooltip="Lizenz hinzufügen">Lizenz hinzufügen</FluentNavLink> *@
                                        </FluentNavMenu>
                                    </FluentGridItem>
                                </FluentGrid>
                            }
                            else
                            {
                                <FluentGrid Spacing="1" Justify="JustifyContent.FlexStart" Style="min-width: 100%">
                                    <FluentGridItem xs="11">
                                        <FluentSkeleton Style="min-width: 100%; margin-top:10px;" Shimmer="true"></FluentSkeleton>
                                        <FluentSkeleton Style="min-width: 100%; margin-top:10px;" Shimmer="true"></FluentSkeleton>
                                        <FluentSkeleton Style="min-width: 100%; margin-top:10px;" Shimmer="true"></FluentSkeleton>
                                        <FluentSkeleton Style="min-width: 100%; margin-top:10px;" Shimmer="true"></FluentSkeleton>
                                    </FluentGridItem>
                                    <FluentGridItem xs="1">
                                        <FluentSkeleton Style="min-width: 100%; margin-top:10px;" Shimmer="true"></FluentSkeleton>
                                    </FluentGridItem>
                                </FluentGrid>
                            }
                        </FluentAccordionItem>
                    }
                </FluentAccordion>
            </FluentGridItem>
        </FluentGrid>
    </BaseBody>
</MainBody>

@code {
    bool _isBodyVisible;
    private ApplicationUser SelectedApplicationUser { get; set; } = new();

    bool SelectFromEntireRow { get; set; } = true;
    IEnumerable<LicenseCertificate> SelectedItems { get; set; } = [];

    FluentTextField? FocusDesc { get; set; }


    protected override async void OnAfterRender(bool firstRender)
    {
        if (!firstRender) return;
        try
        {
            await StartLoading();
            await LoadApllicationUser(LoadingIdFromUiParameter);

            _isBodyVisible = true;
            IsLoading = false;

            FocusDesc?.FocusAsync();


            StateHasChanged();
        }
        catch (Exception e)
        {
            Console.WriteLine(e);
            throw;
        }
    }

    private async Task LoadApllicationUser(string? idStr)
    {
        Guid.TryParse(idStr, out var id);

        if (id == Guid.Empty)
        {
            SelectedApplicationUser = new ApplicationUser();
            return;
        }

        try
        {
            ApplicationUser? selectedApplicationUser = null;

            await MessageBus.InvokeAsync<ApplicationUser>(new ApplicationUserQuery(id), CancellationToken.None)
                .ContinueWith(t => { return selectedApplicationUser = t.Result; });

            SelectedApplicationUser = selectedApplicationUser ?? new ApplicationUser();
        }
        catch (Exception e)
        {
            await DialogService.ShowErrorAsync(e.InnerException?.Message ?? e.InnerException?.Message ?? e.Message);
            SelectedApplicationUser = new();
        }
    }

    private Task OpenTenant(MouseEventArgs args)
    {
        if (SelectedApplicationUser.TenantId == Guid.Empty) return Task.CompletedTask;
        NavigationManager.NavigateTo($"tenant?Id={SelectedApplicationUser.TenantId}", forceLoad: false);
        return Task.CompletedTask;
    }

    private Task<ValueTask> WriteLicenseBase64ToClipboardAsync(LicenseCertificate licenseCertificate)
    {
        return Task.FromResult(JsRuntime.InvokeVoidAsync("navigator.clipboard.writeText", licenseCertificate.ToLicenseData().ToBase64String()));
    }

    private Task OpenLicenseCertificateButton(Guid licenseCertificateId)
    {
        if (licenseCertificateId == Guid.Empty) return Task.CompletedTask;

        NavigationManager.NavigateTo($"licensecertificate?Id={licenseCertificateId}", forceLoad: false);
        return Task.CompletedTask;
    }

    protected async void OpenSearch()
    {
        await DoOpenSearch().ContinueWith(t => t.Wait());
        FocusDesc?.FocusAsync();
    }

    private async Task DoOpenSearch()
    {
        var searchParam = new SearchApplicationUserDialog.SearchParamForApplicationUser();

        var dialog = await DialogService.ShowDialogAsync<SearchApplicationUserDialog>(searchParam, new DialogParameters()
        {
            Height = "auto",
            Width = "80%",
            Alignment = HorizontalAlignment.Center,
            PreventScroll = false,
            PrimaryActionEnabled = false,
            SecondaryActionEnabled = false,
        });

        var result = await dialog.Result;
        ApplicationUser? selectedApplicationUser = null;
        if (result is { Cancelled: false, Data: not null })
        {
            selectedApplicationUser = (ApplicationUser)result.Data;
        }

        SelectedApplicationUser = selectedApplicationUser ?? new ApplicationUser();

        NavigationManager.NavigateTo($"applicationUser?Id={SelectedApplicationUser.Id}", forceLoad: false);

        await LoadApllicationUser(SelectedApplicationUser.Id.ToString());
        StateHasChanged();
    }

    private async Task DeleteLicenseCertificate(LicenseCertificate? licenseCertificate)
    {
        try
        {
            if (licenseCertificate is null)
            {
                return;
            }

            await MessageBus.InvokeAsync<LicenseCertificate>(new DeleteLicenseCertificateCommand(licenseCertificate.Id), CancellationToken.None);
            await LoadApllicationUser(SelectedApplicationUser.Id.ToString());

            StateHasChanged();
        }
        catch (Exception e)
        {
            await DialogService.ShowErrorAsync(e.InnerException?.Message ?? e.Message);
        }
    }

    private async Task CreateLicenseCertificateButton()
    {
        try
        {
            if (SelectedApplicationUser.Id == Guid.Empty)
            {
                return;
            }

            await MessageBus.InvokeAsync<LicenseCertificate>(new CreateLicenseCertificateToApplicationUserCommand(SelectedApplicationUser.Id), CancellationToken.None);
            await LoadApllicationUser(SelectedApplicationUser.Id.ToString());

            StateHasChanged();
        }
        catch (Exception e)
        {
            await DialogService.ShowErrorAsync(e.InnerException?.Message ?? e.Message);
        }
    }

}