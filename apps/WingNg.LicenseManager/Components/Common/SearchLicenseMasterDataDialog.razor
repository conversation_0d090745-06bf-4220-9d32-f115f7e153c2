@inject IDialogService DialogService


@using licenses.application.LicenseMasterDatas
@using licenses.domain.LicenseMasterDatas
@using Wolverine
@implements IDialogContentComponent<SearchLicenseMasterDataDialog.SearchParamForLicenseMasterData>

@inject IMessageBus MessageBus

@* Header *@
<FluentDialogHeader ShowDismiss="true">
    <FluentStack VerticalAlignment="VerticalAlignment.Center" Orientation="Orientation.Horizontal">
        <FluentLabel Typo="Typography.PaneHeader">
            Liz<PERSON>z suche
        </FluentLabel>
        <FluentLabel Typo="Typography.Header">
            @Dialog!.Instance.Parameters.Title
        </FluentLabel>
    </FluentStack>
</FluentDialogHeader>

@* Footer *@
<FluentDialogFooter>
    <FluentButton Appearance="Appearance.Accent" OnClick="@SelectAsync">Auswählen</FluentButton>
    <FluentButton Appearance="Appearance.Neutral" OnClick="@CancelAsync">Abbrechen</FluentButton>
</FluentDialogFooter>

@* Body *@
<FluentDialogBody >
    @if (visibleLoading)
    {
        <FluentOverlay @bind-Visible=visibleLoading
                       Opacity="0.4"
                       Alignment="Align.Center"
                       Justification="JustifyContent.Center">
            <FluentProgressRing/>
        </FluentOverlay>
    }
    else
    {
        <FluentDataGrid Items="@AllLicenceMasterDatas.AsQueryable()"
                        TGridItem=LicenseMasterData
                        ResizableColumns=true
                        OnRowFocus="@(arg => SelectValue(arg))"
                        @ondblclick="SelectAsync"
                        Style="height: 405px; overflow:auto;"
                        GridTemplateColumns=@GridTemplateColumns
                        RowClass="@(arg => ((SelectedLicenceMasterData is not null && SelectedLicenceMasterData.Id == arg.Id) ? "highlighted-row" : ""))"
                        RowStyle="@(arg => ((SelectedLicenceMasterData is not null && SelectedLicenceMasterData.Id == arg.Id) ? $"grid-template-columns: {GridTemplateColumns}; background-color:#EBEBEB" : $"grid-template-columns: {GridTemplateColumns};"))">
                        <PropertyColumn Property="@(p => p.Kuerzel)" Sortable="true" Title="Kürzel"/>
                        <PropertyColumn Property="@(p => p.Name)" Sortable="true" Title="Beschreibung"/>
                        <PropertyColumn Property="@(p => string.Join(", ", p.Claims))" Sortable="true" Title="Claims"/>
        </FluentDataGrid>
    }
</FluentDialogBody>

@code {
    [Parameter]
    public SearchParamForLicenseMasterData Content { get; set; } = default!;

    [CascadingParameter]
    public FluentDialog? Dialog { get; set; }

    string GridTemplateColumns = "2fr 2fr 2fr";

    public IDialogService? DialogServiceCallBack { get; set; }

    private IEnumerable<LicenseMasterData> AllLicenceMasterDatas { get; set; } = [];
    private LicenseMasterData? SelectedLicenceMasterData { get; set; }
    bool visibleLoading = true;

    protected override async void OnAfterRender(bool firstRender)
    {
        if (!firstRender) return;

        await LoadAllLicenseMasterData();

        StateHasChanged();
    }

    public async Task LoadAllLicenseMasterData()
    {
        try
        {
            AllLicenceMasterDatas = await MessageBus.InvokeAsync<IEnumerable<LicenseMasterData>>(new LicenseMasterDatasQuery(), CancellationToken.None) ?? [];
        }
        catch (Exception e)
        {
            if (DialogServiceCallBack is not null)
                await DialogServiceCallBack.ShowErrorAsync(e.Message);
            if (DialogService is not null)
                await DialogService.ShowErrorAsync(e.Message);
        }

        visibleLoading = false;
    }

   
    private async Task SelectAsync()
    {
        if (Dialog is null)
            return;
        if (SelectedLicenceMasterData is null)
            return;

        await Dialog.CloseAsync(SelectedLicenceMasterData);
    }

    private async Task CancelAsync()
    {
        if (Dialog is null)
            return;

        await Dialog.CancelAsync();
    }

    private void SelectValue(FluentDataGridRow<LicenseMasterData> arg)
    {
        SelectedLicenceMasterData = arg.Item;
    }

    public record SearchParamForLicenseMasterData
    {
        public Guid? Guid { get; set; }
    }

}