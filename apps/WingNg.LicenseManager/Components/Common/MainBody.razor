@inject IDialogService DialogService
@inject NavigationManager NavigationManager
@if (Hidden)
{
    return;
}

<FluentDialogProvider/>

<FluentStack Orientation="Orientation.Vertical" Style="background-color: var(--neutral-layer-3);min-width: 100%; max-width: 100%;min-height: 100%; max-height: 100%;">
    <FluentCard Style="padding: 0px; background-color: var(--neutral-layer-3)" >
        @if (Title is not null)
        {
            <FluentHeader style="min-width: 100%"> <h3 style="margin-left: 10px;margin-top: 10px; min-width: 100%">@Title</h3></FluentHeader>
        }
        <FluentStack Orientation="Orientation.Horizontal" Style="min-width: 100%; min-height: 100%;">
            @if (FunctionBarCollapsible)
            {
                <FluentNavMenu Collapsible="false"  @bind-Expanded="@_mFromMenuSelected[0]" Title="Custom navigation menu"  onmouseover=@(()=>OnMenu(0)) onmouseleave=@(()=>OnMenuOut(0))>
                    @FunctionBar
                </FluentNavMenu>    
            }
            else
            {
                <FluentNavMenu Collapsible="false"  Expanded="false" Title="Custom navigation menu">
                    @FunctionBar
                </FluentNavMenu>
            }
            
            @if (NeedFullPlace)
            {
                <FluentCard Style="min-width: 90%; min-height: 100%;  height: 100%;padding: 0;background-color: whitesmoke">
                    @if (IsBodyVisible)
                    {
                        @BaseBody
                    }
                    else
                    {
                        <FluentCard Class="card-padding">
                            <FluentGrid Spacing="3" Justify="JustifyContent.FlexStart">
                                <FluentGridItem xs="12" sm="12"><FluentSkeleton Width="100%" Shimmer="true"></FluentSkeleton></FluentGridItem>
                                <FluentGridItem xs="12" sm="12"><FluentSkeleton Width="100%" Shimmer="true"></FluentSkeleton></FluentGridItem>
                                <FluentGridItem xs="12" sm="12"><FluentSkeleton Width="100%" Shimmer="true"></FluentSkeleton></FluentGridItem>
                            </FluentGrid>
                        </FluentCard>
                    }
                </FluentCard>
            }
            else
            {
                <FluentCard Style="min-width: 90%; min-height: 100%;  height: 100%;padding-left: 0;padding-right: 0;background-color: whitesmoke">
                    @if (IsBodyVisible)
                    {
                        @BaseBody
                    }
                    else
                    {
                        <FluentCard Class="card-padding">
                            <FluentGrid Spacing="3" Justify="JustifyContent.FlexStart">
                                <FluentGridItem xs="12" sm="12"><FluentSkeleton Width="100%" Shimmer="true"></FluentSkeleton></FluentGridItem>
                                <FluentGridItem xs="12" sm="12"><FluentSkeleton Width="100%" Shimmer="true"></FluentSkeleton></FluentGridItem>
                                <FluentGridItem xs="12" sm="12"><FluentSkeleton Width="100%" Shimmer="true"></FluentSkeleton></FluentGridItem>
                            </FluentGrid>
                        </FluentCard>
                    }
                </FluentCard>
            }
        </FluentStack>
    </FluentCard>
</FluentStack>



@if (IsLoading)
{
    <FluentOverlay @bind-Visible=IsLoading
                   Opacity="0.4"
                   Alignment="Align.Center"
                   Justification="JustifyContent.Center">
        <FluentProgressRing />
    </FluentOverlay>
}



@code {
    [Parameter] public bool Hidden { get; set; } = false;
    [Parameter] public RenderFragment? BaseBody { get; set; }
    [Parameter] public RenderFragment? FunctionBar { get; set; }
    [Parameter] public RenderFragment? SubFunctionBar { get; set; }
    [Parameter] public string? Title { get; set; }
    [Parameter] public bool IsBodyVisible { get; set; } = true;
    [Parameter] public bool IsLoading { get; set; } = false;
    [Parameter] public bool NeedFullPlace { get; set; } = false;
    [Parameter] public bool FunctionBarCollapsible { get; set; } = true;

    

    bool _selectedMenu = false;
    Dictionary<int, bool> _mFromMenuSelected = new Dictionary<int, bool>(){{0,false},{1,false}};


    
    private void OnMenu(int indexOfNavMenu)
    {
        _mFromMenuSelected[indexOfNavMenu] = !_selectedMenu;
    }
  
    private void OnMenuOut(int indexOfNavMenu)
    {
        _selectedMenu = false;
        _mFromMenuSelected[indexOfNavMenu] = false;
    }
    protected override void OnInitialized()
    {
        // Subscribe to the event
        NavigationManager.LocationChanged += LocationChanged!;
        base.OnInitialized();
    }

    void LocationChanged(object sender, LocationChangedEventArgs e)
    {
        _selectedMenu = true;
        _mFromMenuSelected = _mFromMenuSelected.ToDictionary(p => p.Key, p => false);
        StateHasChanged();
    }
  
    public void Dispose()
    {
        //empty
    }
}