@inject IDialogService DialogService

@using licenses.application.Tenants
@using licenses.domain.Tenants

@using Wolverine

@implements IDialogContentComponent<SearchTenantDialog.SearchParamForTenant>

@inject IMessageBus MessageBus

@* Header *@
<FluentDialogHeader ShowDismiss="true">
    <FluentStack VerticalAlignment="VerticalAlignment.Center" Orientation="Orientation.Horizontal">
        <FluentLabel Typo="Typography.PaneHeader">
            Tenant suche
        </FluentLabel>
        <FluentLabel Typo="Typography.Header">
            @Dialog?.Instance.Parameters.Title
        </FluentLabel>
    </FluentStack>
</FluentDialogHeader>

@* Footer *@
<FluentDialogFooter>
    <FluentButton Appearance="Appearance.Accent" OnClick="@SelectAsync">Auswählen</FluentButton>
    <FluentButton Appearance="Appearance.Neutral" OnClick="@CancelAsync">Abbrechen</FluentButton>
</FluentDialogFooter>

@* Body *@
<FluentDialogBody >
    @if (_visibleLoading)
    {
        <FluentOverlay @bind-Visible=_visibleLoading
                       Opacity="0.4"
                       Alignment="Align.Center"
                       Justification="JustifyContent.Center">
            <FluentProgressRing/>
        </FluentOverlay>
    }
    else
    {
        <FluentDataGrid Items="@AllLDatas.AsQueryable()"
                        TGridItem=Tenant
                        ResizableColumns=true
                        OnRowFocus="@(arg => SelectValue(arg))"
                        @ondblclick="SelectAsync"
                        Style="height: 405px; overflow:auto;"
                        GridTemplateColumns="@GridTemplateColumns"
                        RowClass="@(arg => ((SelectedData is not null && SelectedData.Id == arg.Id) ? "highlighted-row" : ""))"
                        RowStyle="@(arg => ((SelectedData is not null && SelectedData.Id == arg.Id) ? $"grid-template-columns: {GridTemplateColumns} ; background-color:#EBEBEB" : $"grid-template-columns: {GridTemplateColumns};"))">
                        <PropertyColumn Property="@(p => p.Name)" Sortable="true" Title="Beschreibung"/>
                        <PropertyColumn Property="@(p => p.Email)" Sortable="true" Title="Email"/>
                        <PropertyColumn Property="@(p => p.WartungsDatumBeginn)" Sortable="true" Title="Wartungsdatum beginn"/>
                        <PropertyColumn Property="@(p => p.WartungsDatumEnde)" Sortable="true" Title="Wartungsdatum end"/>
        </FluentDataGrid>
    }
</FluentDialogBody>

@code {
    [Parameter] public SearchParamForTenant Content { get; set; } = new();

    [CascadingParameter]
    public FluentDialog? Dialog { get; set; }

    private const string GridTemplateColumns = "2fr 2fr 2fr 2fr";
    public IDialogService? DialogServiceCallBack { get; set; }

    private IEnumerable<Tenant> AllLDatas { get; set; } = [];
    private Tenant? SelectedData { get; set; }
    bool _visibleLoading = true;

    protected override async void OnAfterRender(bool firstRender)
    {
        if (!firstRender) return;

        await LoadAllData();

        StateHasChanged();
    }

    public async Task LoadAllData()
    {
        try
        {
            AllLDatas = await MessageBus.InvokeAsync<IEnumerable<Tenant>>(new TenantsQuery(), CancellationToken.None);
        }
        catch (Exception e)
        {
            if (DialogServiceCallBack is not null)
                await DialogServiceCallBack.ShowErrorAsync(e.Message);
            if (DialogService is not null)
                await DialogService.ShowErrorAsync(e.Message);
        }

        _visibleLoading = false;
    }

   
    private async Task SelectAsync()
    {
        if (Dialog is null)
            return;
        if (SelectedData is null)
            return;

        await Dialog.CloseAsync(SelectedData);
    }

    private async Task CancelAsync()
    {
        if (Dialog is null)
            return;

        await Dialog.CancelAsync();
    }

    private void SelectValue(FluentDataGridRow<Tenant> arg)
    {
        SelectedData = arg.Item;
    }

    public record SearchParamForTenant
    {
        public Guid? Guid { get; set; }
    }

}