@inject IDialogService DialogService

@using licenses.application.Licenses
@using licenses.application.LicensesCertificate
@using licenses.domain.Models

@using Wolverine

@implements IDialogContentComponent<SearchLicenseDialog.SearchParamForLicense>

@inject IMessageBus MessageBus

@* Header *@
<FluentDialogHeader ShowDismiss="true">
    <FluentStack VerticalAlignment="VerticalAlignment.Center" Orientation="Orientation.Horizontal">
        <FluentLabel Typo="Typography.PaneHeader">
            Lizenz suche
        </FluentLabel>
        <FluentLabel Typo="Typography.Header">
            @Dialog?.Instance.Parameters.Title
        </FluentLabel>
    </FluentStack>
</FluentDialogHeader>

@* Footer *@
<FluentDialogFooter>
    <FluentButton Appearance="Appearance.Accent" OnClick="@SelectAsync">Auswählen</FluentButton>
    <FluentButton Appearance="Appearance.Neutral" OnClick="@CancelAsync">Abbrechen</FluentButton>
</FluentDialogFooter>

@* Body *@
<FluentDialogBody >
    @if (_visibleLoading)
    {
        <FluentOverlay @bind-Visible=_visibleLoading
                       Opacity="0.4"
                       Alignment="Align.Center"
                       Justification="JustifyContent.Center">
            <FluentProgressRing/>
        </FluentOverlay>
    }
    else
    {
        <FluentDataGrid Items="@AllLDatas.AsQueryable()"
                        TGridItem=License
                        ResizableColumns=true
                        OnRowFocus="@(arg => SelectValue(arg))"
                        @ondblclick="SelectAsync"
                        Style="height: 405px; overflow:auto;"
                        GridTemplateColumns="@GridTemplateColumns"
                        RowClass="@(arg => ((SelectedData is not null && SelectedData.Id == arg.Id) ? "highlighted-row" : ""))"
                        RowStyle="@(arg => ((SelectedData is not null && SelectedData.Id == arg.Id) ? $"grid-template-columns: {GridTemplateColumns} ; background-color:#EBEBEB" : $"grid-template-columns: {GridTemplateColumns};"))">
                        <PropertyColumn Property="@(p => p.Name)" Sortable="true" Title="Beschreibung"/>
                        <PropertyColumn Property="@(p => p.TenantData!.Email)" Sortable="true" Title="Email"/>
                        <PropertyColumn Property="@(p => p.TenantData!.Name)" Sortable="true" Title="Kunden"/>
                        <PropertyColumn Property="@(p => p.BeginnDateTime)" Sortable="true" Title="Gültig ab"/>
                        <PropertyColumn Property="@(p => p.EndDateTime)" Sortable="true" Title="Gültig bis"/>
        </FluentDataGrid>
    }
</FluentDialogBody>

@code {
    [Parameter] public SearchParamForLicense Content { get; set; } = new();

    [CascadingParameter]
    public FluentDialog? Dialog { get; set; }

    private const string GridTemplateColumns = "2fr 2fr 2fr 2fr";
    public IDialogService? DialogServiceCallBack { get; set; }

    private IEnumerable<License> AllLDatas { get; set; } = [];
    private License? SelectedData { get; set; }
    bool _visibleLoading = true;

    protected override async void OnAfterRender(bool firstRender)
    {
        if (!firstRender) return;

        await LoadAllLicenceMasterData();

        StateHasChanged();
    }

    private async Task LoadAllLicenceMasterData()
    {
        try
        {
            AllLDatas = await MessageBus.InvokeAsync<IEnumerable<License>>(new LicensesQuery(true), CancellationToken.None);
        }
        catch (Exception e)
        {
            if (DialogServiceCallBack is not null)
                await DialogServiceCallBack.ShowErrorAsync(e.Message);
            if (DialogService is not null)
                await DialogService.ShowErrorAsync(e.Message);
        }

        _visibleLoading = false;
    }

   
    private async Task SelectAsync()
    {
        if (Dialog is null)
            return;
        if (SelectedData is null)
            return;

        await Dialog.CloseAsync(SelectedData);
    }

    private async Task CancelAsync()
    {
        if (Dialog is null)
            return;

        await Dialog.CancelAsync();
    }

    private void SelectValue(FluentDataGridRow<License> arg)
    {
        SelectedData = arg.Item;
    }

    public record SearchParamForLicense
    {
        public Guid? Guid { get; set; }
    }

}