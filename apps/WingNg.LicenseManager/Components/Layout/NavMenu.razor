@rendermode InteractiveServer

@implements IDisposable

@inject NavigationManager NavigationManager

<div class="navmenu">
    <input type="checkbox" title="Menu expand/collapse toggle" id="navmenu-toggle" class="navmenu-icon"/>
    <label for="navmenu-toggle" class="navmenu-icon">
        <FluentIcon Value="@(new Icons.Regular.Size20.Navigation())" Color="Color.Fill"/>
    </label>
    <nav class="sitenav" aria-labelledby="main-menu">
        <FluentNavMenu Id="main-menu" Collapsible="true" Width="250" Title="Navigation menu" @bind-Expanded="expanded" CustomToggle="true">
            <FluentNavLink Href="licenseMasterData" Icon="@(new Icons.Regular.Size20.DocumentSettings())" IconColor="Color.Accent">Lizenzstammdaten</FluentNavLink>
            <FluentNavLink Href="/" Icon="@(new Icons.Regular.Size20.PersonBoard())" IconColor="Color.Accent">Tenant</FluentNavLink>
            <FluentNavLink Href="applicationUser" Icon="@(new Icons.Regular.Size20.People())" IconColor="Color.Accent">Benutzer</FluentNavLink>
            <FluentNavLink Href="license" Icon="@(new Icons.Regular.Size20.Certificate())" IconColor="Color.Accent">Lizenz</FluentNavLink>
            <FluentNavLink Href="licensecertificate" Icon="@(new Icons.Regular.Size20.Key())" IconColor="Color.Accent">Lizenz Zertifikat</FluentNavLink>
        </FluentNavMenu>
    </nav>
</div>

@code {
    private bool expanded = true;
    private string? currentUrl;

    protected override void OnInitialized()
    {
        currentUrl = NavigationManager.ToBaseRelativePath(NavigationManager.Uri);
        NavigationManager.LocationChanged += OnLocationChanged;
    }

    private void OnLocationChanged(object? sender, LocationChangedEventArgs e)
    {
        currentUrl = NavigationManager.ToBaseRelativePath(e.Location);
        StateHasChanged();
    }

    public void Dispose()
    {
        NavigationManager.LocationChanged -= OnLocationChanged;
    }

}