<Project Sdk="Microsoft.NET.Sdk.Razor">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <VersionPrefix>1.0.0$(Date:yyyyMMdd)</VersionPrefix>
        <LangVersion>default</LangVersion>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="DocumentFormat.OpenXml" Version="3.3.0" />
        <PackageReference Include="CommunityToolkit.Maui.Core" Version="12.1.0" />
        <PackageReference Include="MediatR" Version="[12.5.0]" />
        <PackageReference Include="Microsoft.AspNetCore.Components.Authorization" Version="9.0.8" />
        <PackageReference Include="Microsoft.AspNetCore.Components.Web" Version="9.0.8" />
        <PackageReference Include="Microsoft.AspNetCore.Components.WebView.Maui" Version="9.0.90" />
        <PackageReference Include="Microsoft.Maui.Controls.Core" Version="9.0.90" />
        <PackageReference Include="Radzen.Blazor" Version="7.1.7" />
    </ItemGroup>


    <ItemGroup>
      <ProjectReference Include="..\..\..\libs\FTPLib\FTPLib.csproj" />
      <ProjectReference Include="..\..\..\libs\Languages\Languages.csproj" />
      <ProjectReference Include="..\..\..\Modules\QuartzScheduler\QuartzScheduler.csproj" />
      <ProjectReference Include="..\..\..\Modules\WingLager\wingLager.ioc\wingLager.ioc.csproj" />
      <ProjectReference Include="..\..\..\Modules\WingPrinterListLabel\wingPrinterListLabel.ioc\wingPrinterListLabel.ioc.csproj" />
    </ItemGroup>

    <ItemGroup Condition="!$([MSBuild]::IsOSPlatform('Windows'))">
        <Reference Include="PrinterService">
            <HintPath>..\..\..\PrinterService\PublishDlls\PrinterService.dll</HintPath>
            <Private>true</Private>
        </Reference>
        <Reference Include="combit.ListLabel30">
            <HintPath>..\..\..\PrinterService\PublishDlls\combit.ListLabel30.dll</HintPath>
            <Private>true</Private>
        </Reference>
    </ItemGroup>
    
    <ItemGroup Condition="$([MSBuild]::IsOSPlatform('Windows'))">
        <ProjectReference Include="..\..\..\PrinterService\PrinterService.csproj" />
    </ItemGroup>
    
</Project>
