@using System.Net.Http
@using System.Net.Http.Json
@using Microsoft.AspNetCore.Components.Forms
@using Microsoft.AspNetCore.Components.Routing
@using Microsoft.AspNetCore.Components.Web
@using static Microsoft.AspNetCore.Components.Web.RenderMode
@using Microsoft.AspNetCore.Components.Web.Virtualization

@using Microsoft.JSInterop
@using WinGng.RazorLib
@using WinGng.RazorLib.Components
@using Ra<PERSON>zen
@using Radzen.Blazor
@using Microsoft.AspNetCore.Components.Authorization
@using WinGng.RazorLib.Extensions
@using WinGng.RazorLib.Components.Model
@using WingCore.application.DataTransferObjects.Invoices

@using Microsoft.Extensions.Localization
@inject IStringLocalizer<Languages.Resources.Resource> Localizer