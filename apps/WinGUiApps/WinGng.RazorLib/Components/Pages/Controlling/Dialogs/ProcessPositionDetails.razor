@using wingLager.domain.ProcessArticles
@using wingLager.domain.ProcessOrders

@inject NavigationManager NavigationManager

<RadzenStack Gap="1rem" Orientation="Orientation.Vertical" Style="height: 100%; width:100%">

    <RadzenRow Gap="1rem">
        <RadzenColumn Size="12" SizeMD="6">
            <RadzenStack Orientation="Orientation.Vertical">
                <RadzenText TextStyle="TextStyle.Overline" Class="rz-mt-4 rz-my-0" Style="color: var(--rz-text-tertiary-color);" Text="Beschreibung"/>
                <RadzenText>@ProcessOrderPosition?.Description</RadzenText>
            </RadzenStack>
        </RadzenColumn>
        <RadzenColumn Size="12" SizeMD="6">
            <RadzenStack Orientation="Orientation.Vertical">
                <RadzenText TextStyle="TextStyle.Overline" Class="rz-mt-4 rz-my-0" Style="color: var(--rz-text-tertiary-color);" Text="Menge"/>
                <RadzenText>@ProcessOrderPosition?.WeightInKg Kg</RadzenText>
            </RadzenStack>
        </RadzenColumn>
    </RadzenRow>
    <RadzenRow Gap="1rem">
        <RadzenColumn Size="12" SizeMD="6">
            <RadzenStack Orientation="Orientation.Vertical">
                <RadzenText TextStyle="TextStyle.Overline" Class="rz-mt-4 rz-my-0" Style="color: var(--rz-text-tertiary-color);" Text="Artikelnummer"/>
                <RadzenText>@ProcessOrderPosition.ArticleNumber</RadzenText>
            </RadzenStack>
        </RadzenColumn>
    </RadzenRow>
    <RadzenRow Gap="1rem">
        <RadzenColumn Size="12" SizeMD="6">
            <RadzenStack Orientation="Orientation.Vertical">
                <RadzenText TextStyle="TextStyle.Overline" Class="rz-mt-4 rz-my-0" Style="color: var(--rz-text-tertiary-color);" Text="Anzahl Paletten"/>
                <RadzenText>@ProcessOrderPosition.PaletLager.Count</RadzenText>
            </RadzenStack>
        </RadzenColumn>
        <RadzenColumn Size="12" SizeMD="6">
            <RadzenStack Orientation="Orientation.Vertical">
                <RadzenText TextStyle="TextStyle.Overline" Class="rz-mt-4 rz-my-0" Style="color: var(--rz-text-tertiary-color);" Text="Anzahl Säcke"/>
                <RadzenText>@ProcessOrderPosition.PaletLager.Sum(p => p.AnzSack)</RadzenText>
            </RadzenStack>
        </RadzenColumn>
    </RadzenRow>

    @if (@ProcessArticle?.Number is not null)
    {       
        <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
            <RadzenFieldset AllowCollapse="false" Style="background-color:white">
                <HeaderTemplate>
                    <RadzenStack Orientation="Orientation.Horizontal" Gap="0.25rem">
                        <strong>Prozessartikel Informationen</strong>
                    </RadzenStack>
                </HeaderTemplate>
                <ChildContent>
                    
                    <ProcessArticleCreationPage ProcessArticleNumber="@ProcessArticle.Number"
                                                IsOnlyShowForShowMode="true"
                                                ShowOpenRelationToProcessArticle="true"/>
               
                </ChildContent>
            </RadzenFieldset>
        </RadzenStack>
    }
</RadzenStack>

@code {
    [Parameter]
    public ProcessOrderPosition ProcessOrderPosition { get; set; } = null!;
    [Parameter]
    public ProcessArticle? ProcessArticle { get; set; }
}
