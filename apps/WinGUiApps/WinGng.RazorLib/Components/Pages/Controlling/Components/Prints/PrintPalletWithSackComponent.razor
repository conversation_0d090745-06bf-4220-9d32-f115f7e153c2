   <RadzenCard Style="width: 100%; height: 60vh; overflow:hidden">
       <RadzenColumn>
           <RadzenStack Gap="2rem" Orientation="Orientation.Vertical" JustifyContent="JustifyContent.SpaceBetween" Style="height: 100%;">
               <RadzenRow Style="width: 100%;" >
                   <RadzenStack Style="width: 100%;" Gap="1rem" Orientation="Orientation.Horizontal" JustifyContent="JustifyContent.SpaceBetween">
                       <RadzenText>
                           Kundeinformationen: <b>@PrintParameter.CustomerInformation</b>
                       </RadzenText>
                   </RadzenStack>
               </RadzenRow>
               <RadzenRow Style="width: 100%;" Visible="@PrintParameter.ShowSackCount">
                   <RadzenStack Style="width: 100%;" Gap="1rem" Orientation="Orientation.Horizontal" JustifyContent="JustifyContent.SpaceBetween">
                       <RadzenText>
                           Etikettentyp: <b>@PrintParameter.Etikettentyp</b>
                       </RadzenText>
                   </RadzenStack>
               </RadzenRow>
               <RadzenRow Style="width: 100%;" Visible="@PrintParameter.ShowPalletCount">
                   <RadzenStack Style="width: 100%;" Gap="1rem" Orientation="Orientation.Vertical" JustifyContent="JustifyContent.SpaceBetween">
                       <RadzenText>
                           Palettentyp: <b>@PrintParameter.Palettentyp</b>
                       </RadzenText>
                   </RadzenStack>
               </RadzenRow>
               <RadzenRow Style="width: 100%;" Visible="@PrintParameter.ShowPalletCount">
                   <RadzenStack Style="width: 100%;" Gap="1rem" Orientation="Orientation.Vertical" JustifyContent="JustifyContent.SpaceBetween">
                       <RadzenText>
                           Palettiererrezept: <b>@PrintParameter.Palettiererrezept</b>
                       </RadzenText>
                   </RadzenStack>
               </RadzenRow>
               <RadzenRow Style="width: 100%;" Visible="@PrintParameter.ShowSackCount">
                   <RadzenStack Style="width: 100%;" Gap="1rem" Orientation="Orientation.Vertical" JustifyContent="JustifyContent.SpaceBetween">
                       <RadzenLabel Text="Gesamtanzahl Sack Etikett"/>
                       <RadzenNumeric TValue="int" @bind-Value="@PrintParameter.SackCount"/>
                   </RadzenStack>
               </RadzenRow>
               <RadzenRow Style="width: 100%;" Visible="@PrintParameter.ShowPalletCount">
                   <RadzenStack Style="width: 100%;" Gap="1rem" Orientation="Orientation.Vertical" JustifyContent="JustifyContent.SpaceBetween">
                       <RadzenLabel Text="Gesamtanzahl Paletten Etikett pro Palette"/>
                       <RadzenNumeric @bind-Value="@PrintParameter.PalletCount"/>
                   </RadzenStack>
               </RadzenRow>
               <RadzenRow Style="width: 100%;" >
                   <RadzenStack Style="width: 100%;" Gap="1rem" Orientation="Orientation.Horizontal" JustifyContent="JustifyContent.SpaceBetween">
                       <RadzenText>
                           Notiz: <b>@PrintParameter.Note</b>
                       </RadzenText>
                   </RadzenStack>
               </RadzenRow>
               <RadzenRow Style="width: 100%;">
                   <RadzenStack Style="width: 100%;" Orientation="Orientation.Horizontal" JustifyContent="JustifyContent.Center" Gap="1rem" Class="rz-mt-8 rz-mb-4">
                       <RadzenButton ButtonType="ButtonType.Submit" Size="ButtonSize.Medium" Text="Drucken" Click="@PrintButton"/>
                       <RadzenButton ButtonType="ButtonType.Submit" Size="ButtonSize.Medium" Text="Schließen" Click="@CloseButton"/>
                   </RadzenStack>
               </RadzenRow>
           </RadzenStack>
       </RadzenColumn>
    </RadzenCard>

@code
{
    [Parameter] public PrintPalletWithSackModel PrintParameter { get; set; } = new PrintPalletWithSackModel();
    [Parameter] public DialogService? DialogService { get; set; }
    

    private void PrintButton()
    {
        PrintParameter.DoPrint = true;
        DialogService?.Close();
    }

    private void CloseButton()
    {
        PrintParameter.DoPrint = false;
        DialogService?.Close();
    }
    
    public record PrintPalletWithSackModel
    {
        public int PalletCount { get; set; }
        public int SackCount { get; set; }
        public bool DoPrint { get; set; }
        public bool ShowSackCount { get; set; } = true;
        public bool ShowPalletCount { get; set; } = true;
        public string Etikettentyp { get; set; } = string.Empty;
        public string Palettentyp { get; set; } = string.Empty;
        public string Palettiererrezept { get; set; } = string.Empty;
        public string CustomerInformation { get; set; } = string.Empty;
        public string Note { get; set; } = string.Empty;
    }

}
