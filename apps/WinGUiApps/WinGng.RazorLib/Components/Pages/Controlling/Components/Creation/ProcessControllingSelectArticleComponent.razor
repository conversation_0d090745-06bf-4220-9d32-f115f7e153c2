@using WingCore.domain.Models
@using WingCore.application.Contract.Services

@inject Lazy<IArticleService> ArticleService

<RadzenHeading Size="H2" Text="Bitte Daten eingeben"/>
<RadzenRow Gap="1rem" Style="max-height: 100%">
    <RadzenColumn Size="12" SizeSM="6">
        <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
            <RadzenLabel Text="Artikelnummer"/>
            <RadzenDropDownDataGrid AllowClear="true"
                                    @bind-Value="ArticleAndWeightModel!.SelectedMainArticle"
                                    AllowVirtualization="true"
                                    AllowFiltering="true"
                                    Data="@AllMainArticles"
                                    TextProperty="@nameof(Artikel.ArtBezText1)"
                                    AllowColumnResize="true"
                                    AllowFilteringByAllStringColumns="true"
                                    SearchTextPlaceholder="Wählen Sie einen Hauptartikel"
                                    EmptyText="Keine Ergebnisse">
                <ValueTemplate>
                    @{
                        var artikel = context as Artikel;
                        if (artikel is not null)
                        {
                            if (string.IsNullOrWhiteSpace(artikel.ArtBezText1))
                                return;
                            @($"{artikel.ArtBezText1}, {(artikel.ArtKolliInhalt == 0 ? "lose" : artikel.ArtKolliInhalt.ToString())}")
                        }
                    }
                </ValueTemplate>
                <Columns>
                    <RadzenDropDownDataGridColumn Property="@nameof(Artikel.ArtikelnrAsString)" Title="Artikelnr."/>
                    <RadzenDropDownDataGridColumn Property="@nameof(Artikel.ArtBezText1)" Title="Bezeichnung"/>
                    <RadzenDropDownDataGridColumn Property="@nameof(Artikel.ArtBezText2)" Title="Bezeichnung2"/>
                    <RadzenDropDownDataGridColumn Property="@nameof(Artikel.ArtKolliInhalt)" Title="Linientyp">
                        <Template Context="artikel">
                            @{
                                @(artikel.ArtKolliInhalt == 0 ? "lose" : artikel.ArtKolliInhalt.ToString())
                            }
                        </Template>
                    </RadzenDropDownDataGridColumn>
                </Columns>
            </RadzenDropDownDataGrid>
        </RadzenStack>
    </RadzenColumn>

    <RadzenColumn Size="12" SizeSM="6">
        <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
            <RadzenLabel Text="Verfügbare Menge (Kg)"/>
            <RadzenNumeric Format="N0"
                           TValue="long"
                           @bind-value="ArticleAndWeightModel!.MaxWeightIngKg"
                           @oninput=@OnInputMaxWeight
                           Min="1"
                           Placeholder="Menge"
                           ShowUpDown="false"/>
        </RadzenStack>
    </RadzenColumn>
</RadzenRow>
<RadzenStack Orientation="Orientation.Horizontal" JustifyContent="JustifyContent.Start" Gap="1rem"
             Class="rz-mt-8 rz-mb-4" Style="margin-top: 10px">
    <RadzenButton Text="Weiter" Size="ButtonSize.Large" Click="@DoGoNext"
                  Disabled="@DisableButtonForNext"/>
</RadzenStack>


@code {
    [Parameter] public SelectionArticelAndWeightModel? ArticleAndWeightModel { get; set; } = default!;
    [Parameter, EditorRequired] public EventCallback DoGoNext { get; set; }

    private List<Artikel> AllMainArticles { get; set; } = [];
    private bool DisableButtonForNext { get; set; } = true;

    protected override Task OnInitializedAsync()
    {
        AllMainArticles = ArticleService.Value.GetMainArtikels(false).ToList();

        DisableButtonForNext = ArticleAndWeightModel!.SelectedMainArticle.Artikelnr == 0 ||
                               ArticleAndWeightModel!.MaxWeightIngKg <= 0;
        return Task.CompletedTask;
    }

    private void OnInputMaxWeight(ChangeEventArgs args)
    {
        long value = 0;
        if (args.Value is string)
            long.TryParse(args.Value.ToString(), out value);

        DisableButtonForNext = ArticleAndWeightModel!.SelectedMainArticle.Artikelnr == 0 ||
                               value <= 0;
    }

}