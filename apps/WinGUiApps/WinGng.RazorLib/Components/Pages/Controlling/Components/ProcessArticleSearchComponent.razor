@using MediatR
@using Radzen.Blazor.Rendering
@using WingCore.application.Contract.IModels.Helper
@using WingCore.application.Contract.Services
@using WingCore.domain.Models
@using wingLager.application.ProcessArticles
@using wingLager.application.SearchParams
@using wingLager.domain.ProcessArticles
@using WinGng.RazorLib.Components.Common.LoadingPage
@using WinGng.RazorLib.Components.Common.SearchParam
@using WinGng.RazorLib.Components.Pages.MasterData.Costumer

@inherits BasePage

@inject NavigationManager NavigationManager
@inject ISender Sender
@inject SearchParametersService SearchParametersService
@inject IKundenService KundenService

<LoadingIndicator @ref="_loadingIndicator"
                  DoLoadDataCallback="SearchAllProcessArticles"
                  Option="_options" DialogService="DialogService"/>

<RadzenContent Container="main">
    <ChildContent>
        @if (ShowSearchParam)
        {
            <RadzenRow >
                @* <RadzenIcon Icon="assessment"> *@
                @* </RadzenIcon> *@
                <RadzenHeading Size="H1" style="display: inline-block" Text="@Localizer["ProcessArticleSearch"]">
                </RadzenHeading>

            </RadzenRow>

            <RadzenHeading Size="H2" style="display: inline-block" Text="@Localizer["SearchParameters"]"/>
            <RadzenRow Gap="1rem" style="font-size: large"> 
                <RadzenColumn Size="12" SizeSM="6"> 
                    <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                        <RadzenLabel Text="@Localizer["CustomerSearch"]"/>
                        <RadzenDropDown @bind-Value=@SearchParametersService.ProcessArticleSearchParam.SelectCustomerSearchMode
                                        Data=@SearchParametersService.ProcessArticleSearchParam.CustomerSearchModes
                                        Style="width: 100%;">
                            <Template>
                                @GetCustomerSearchModeText(((CustomerSearchMode)context).Mode)
                            </Template>
                        </RadzenDropDown>
                        
                        @if (SearchParametersService.ProcessArticleSearchParam.SelectCustomerSearchMode.IsWithCustomer)
                        {
                            <RadzenLabel Text="@Localizer["Customer"]"/>
                            <RadzenTextBox @ref=_customerSearchTextBox
                                           Style="width:100%"
                                           Value="@SearchParametersService.ProcessArticleSearchParam.SelectedCustomerName"
                                           @oninput=@OnInput
                                           @onclick="@(args => _popup.ToggleAsync(_customerSearchTextBox.Element))"/>
                        }
                                                
                    </RadzenStack> 
                </RadzenColumn>
                @*<RadzenColumn Size="12" SizeSM="6">
                     <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                         von Datum
                         <RadzenDatePicker @bind-Value="@SearchParametersService.ProcessOrderSearchParam.FromDate" DateFormat="dd.MM.yyyy" Name="DatePickerDateOnlyType"/>

                         von Prozessnummer
                         <RadzenNumeric @bind-Value="@SearchParametersService.ProcessOrderSearchParam.FromNumber"/>

                     </RadzenStack>
                 </RadzenColumn>
                 <RadzenColumn Size="12" SizeSM="6"> 
                     <RadzenStack Orientation="Orientation.Vertical" Gap="6px"> 
                         bis Datum 
                         <RadzenDatePicker @bind-Value="@SearchParametersService.ProcessOrderSearchParam.ToDate" DateFormat="dd.MM.yyyy" Name="DatePickerDateOnlyType"/> 
             
                         bis Prozessnummer 
                         <RadzenNumeric @bind-Value=@SearchParametersService.ProcessOrderSearchParam.ToNumber/> 
                     </RadzenStack>
                 </RadzenColumn>*@
            </RadzenRow>
         
            <RadzenRow Gap="1rem" style="font-size: large">
                <RadzenStack Orientation="Orientation.Horizontal" JustifyContent="JustifyContent.Start" Gap="1rem" Class="rz-mt-8 rz-mb-4">
                    <RadzenButton ButtonType="ButtonType.Submit" Size="ButtonSize.Large" Icon="search" Text="@Localizer["Search"]" Click="@SearchAllProcessHeaderButton"/>
                </RadzenStack>
                <RadzenStack Orientation="Orientation.Horizontal" JustifyContent="JustifyContent.Start" Gap="1rem" Class="rz-mt-8 rz-mb-4">
                    <RadzenButton ButtonType="ButtonType.Submit" Size="ButtonSize.Large" Icon="add" Text="@Localizer["Create"]" Click="@OpenCreation"/>
                </RadzenStack>
            </RadzenRow>
        }

        <RadzenRow Gap="1rem" style="font-size: large">
            <RadzenToggleButton @bind-Value=@AllowPaging Text="@(AllowPaging ? Localizer["DeactivatePaging"] : Localizer["ActivatePaging"])" ButtonStyle="ButtonStyle.Light"
                                ToggleButtonStyle="ButtonStyle.Light" />
        </RadzenRow>
        <RadzenRow Gap="1rem" style="font-size: large;">
            <div style="height: 100%; display: flex; flex-direction: column; width: 100%">
                <RadzenPanel>
                    <ChildContent>
                        <RadzenDataGrid
                            Style="@GridHeight"
                            @ref="@_grid"
                            Count="@_count"
                            Data="@_processArticleSearch"
                            AllowSorting="true"
                            AllowFiltering="true"
                            AllowPaging="@AllowPaging"
                            FilterMode="FilterMode.Simple"
                            ShowPagingSummary="true"
                            PagingSummaryFormat="@Localizer["PagingSummaryFormat"]"
                            GroupPanelText="@Localizer["GroupPanelText"]"
                            PageSize="30"
                            Render="@OnRender"
                            AllowColumnResize="true"
                            PagerHorizontalAlign="HorizontalAlign.Center"
                            LogicalFilterOperator="LogicalFilterOperator.Or" ColumnWidth="100%"
                            SelectionMode="DataGridSelectionMode.Single"
                            @bind-Value="@SelectedObjects"
                            AllowGrouping="true"
                            TItem="ProcessArticle"
                            RowDoubleClick="@(e => OpenEdit(e.Data))">
        
                            <EmptyTemplate>
                                <p style="color: lightgrey; font-size: 24px; text-align: center; margin: 2rem;">@Localizer["NoOrderFound"]</p>
                            </EmptyTemplate>
                            <Columns>
                                <RadzenDataGridColumn Property="Number" Sortable="true" Title="@Localizer["Number"]">
                                    <FooterTemplate>
                                        <b>@Localizer["Total"]</b>
                                    </FooterTemplate>
                                </RadzenDataGridColumn>
                                <RadzenDataGridColumn Property="DetailDescription" Sortable="false" Title="@Localizer["Description"]"/>
                                <RadzenDataGridColumn Property="Description" Sortable="false" Title="@Localizer["Name"]"/>
                                <RadzenDataGridColumn Property="Description2" Sortable="false" Title="@(Localizer["Name"] + " " + 2)"/>
                                <RadzenDataGridColumn Property="Description3" Sortable="false" Title="@(Localizer["Name"] + " " + 3)"/>
                                <RadzenDataGridColumn Property="Description4" Sortable="false" Title="@(Localizer["Name"] + " " + 4)"/>
                                <RadzenDataGridColumn Property="CustomerId" Sortable="false" Title="@Localizer["Customer"]">
                                    <Template Context="orderPos">
                                        @GetCustomerName(orderPos.CustomerId)
                                    </Template>
                                </RadzenDataGridColumn>
                            </Columns>
                        </RadzenDataGrid>
        
                    </ChildContent>
                    <SummaryTemplate>
                        <RadzenCard class="rz-mt-4">
                            <b>@_count @Localizer["Orders"]</b>
                        </RadzenCard>
                    </SummaryTemplate>
                </RadzenPanel>
            </div>
        </RadzenRow>
    </ChildContent>
</RadzenContent>

<Popup @ref=_popup id="popup" AutoFocusFirstElement="false" class="customerSearch-popup">
    <CustomerMasterDataSearchPage @ref="_customerMasterDataSearchPagref"
                                  DoOpenCustomerPager="false"
                                  OwnRowDoubleClick="@OnRowSelect"
                                  AsPopUp="true"
    ></CustomerMasterDataSearchPage>
</Popup>

@code
{
    [Parameter] public bool ShowSearchParam { get; set; } = true;
    [Parameter] public DialogService DialogService { get; set; } = default!;
    private bool AllowPaging { get; set; } = true;
    
    RadzenDataGrid<ProcessArticle>? _grid;
    int _count;


    IEnumerable<ProcessArticle>? _processArticleSearch;
    LoadingIndicator? _loadingIndicator;
    readonly LoadingIndicatorOptions _options = new(false, false, false, false);
    private string GridHeight => $"min-height: 100%";

    IList<ProcessArticle> SelectedObjects { get; set; } = [];
    Dictionary<long,string> CustomerIdToNameFromProcessArticles { get; set; } = [];

    private async Task SearchAllProcessHeaderButton()
    {
        if (_loadingIndicator is null)
            return;

        await _loadingIndicator.Run();
    }

    void OnRender(DataGridRenderEventArgs<ProcessArticle> args)
    {
        if (args.FirstRender)
        {
            args.Grid.Sorts.Add(new SortDescriptor() { Property = "Number", SortOrder = SortOrder.Ascending });
            SearchParametersService.ProcessArticleSearchParam = new();
        }
    }

    private async Task SearchAllProcessArticles()
    {
        try
        {
            await Task.Delay(100);

            _processArticleSearch = [];
            var searchParam = new ProcessArticleSearchParam();
            
            if(SearchParametersService.ProcessArticleSearchParam.SelectCustomerSearchMode.IsWithCustomer && 
               SearchCustomerId != 0)
                searchParam.WithCustomerIds = [SearchCustomerId];
            if (SearchParametersService.ProcessArticleSearchParam.SelectCustomerSearchMode.IsWithoutCustomer)
                searchParam.WithCustomerIds = [];
            if (SearchParametersService.ProcessArticleSearchParam.SelectCustomerSearchMode.IsOnlyCustomer)
                searchParam.AllWithCustomerIds = true;
            
            _processArticleSearch = await Sender.Send(new ProcessArticlesQuery(searchParam), CancellationToken.None);
            
            _options.CurrentPercent = 1;

            var processArticleSearch = _processArticleSearch?.ToList();

            if (processArticleSearch is not null)
            {
                _count = await Task.FromResult(processArticleSearch.Count());
                CustomerIdToNameFromProcessArticles = [];
                var searchParamForCustomer = new CustomerSearchParam()
                {
                    CustomerIds = processArticleSearch.Select(p => p.CustomerId).ToList()
                };
                var result = await KundenService.GetCustomer(false, searchParamForCustomer);
                CustomerIdToNameFromProcessArticles = result.ToDictionary(x => x.Id, x => x.FullName());
            }
        }
        catch (Exception e)
        {
            DialogService.Close();
            await DialogService.ShowError(e.Message);
        }

        StateHasChanged();
    }


    private async Task OpenCreation()
    {
        try
        {
            NavigationManager.NavigateTo($"/ProcessArticleCreationPage");
        }
        catch (Exception e)
        {
            DialogService.Close();
            await DialogService.ShowError(e.Message);
        }
    }

    
    private async Task OpenEdit(ProcessArticle processArticle)
    {
        try
        {
            NavigationManager.NavigateTo($"/ProcessArticleEditPage/{processArticle.Number}");
        }
        catch (Exception e)
        {
            DialogService.Close();
            await DialogService.ShowError(e.Message);
        }
    }
    
    private string GetCustomerSearchModeText(int mode)
    {
        return mode switch
        {
            1 => Localizer["WithCustomer"],
            2 => Localizer["WithoutCustomer"],
            3 => Localizer["OnlyCustomer"],
            _ => Localizer["Unknown"]
        };
    }
    
    RadzenTextBox? _customerSearchTextBox;
    Popup? _popup;
    CustomerMasterDataSearchPage? _customerMasterDataSearchPagref;
 
    long SearchCustomerId { get; set; } = 0;
    
    async Task OnRowSelect(Kunden customer)
    {
        SearchCustomerId = customer.Id;
        
        SearchParametersService.ProcessArticleSearchParam.SelectedCustomerName = customer.FullName();
        await _popup.CloseAsync();
        StateHasChanged();
    }
    
    private Timer? _debounceTimer;
    async Task OnInput(ChangeEventArgs args)
    {
        await Task.Yield();
        var newSelectedCustomerName = args.Value?.ToString() ?? string.Empty;

        if (string.IsNullOrWhiteSpace(newSelectedCustomerName))
        {
            SearchCustomerId = 0;
        }
        
        if(string.Equals(SearchParametersService.ProcessArticleSearchParam.SelectedCustomerName, newSelectedCustomerName, StringComparison.InvariantCultureIgnoreCase))
            return;

        SearchParametersService.ProcessArticleSearchParam.SelectedCustomerName = newSelectedCustomerName;
        
        // Setze den Timer zurück
        if(_debounceTimer is not null)
            await _debounceTimer.DisposeAsync();
        _debounceTimer = new Timer(async void (_) =>
        {
            try
            {
                await InvokeAsync(async () =>
                {
                    await _customerMasterDataSearchPagref?.SearchAllCustomerMasterData(SearchParametersService.ProcessArticleSearchParam.SelectedCustomerName)!;
                });
            }
            catch (Exception e)
            {
                await DialogService.ShowError(e.InnerException?.Message ?? e.Message);
            }
        }, null, 1500, Timeout.Infinite);
    }

    private string GetCustomerName(long orderPosCustomerId)
    {
        return CustomerIdToNameFromProcessArticles.TryGetValue(orderPosCustomerId, out var article)
            ? article
            : string.Empty;
    }
}