@using MediatR
@using WingCore.application.ApiKeys
@using WingCore.domain.Models.StammDbModels
@using WinGng.RazorLib.Components.Common.LoadingPage


@inject NavigationManager NavigationManager
@inject ISender Sender

<LoadingIndicator @ref="_loadingIndicator"
                  DoLoadDataCallback="CreateApiKey"
                  Option="_options" DialogService="DialogService"/>


<RadzenTemplateForm TItem="ApiKey" Data="@ApiKeyObj" >
    <RadzenContent Container="main">
        <ChildContent>
            <RadzenStack Orientation="Orientation.Vertical" Gap="1rem">
                <RadzenRow Gap="1rem" style="font-size: large">
                    <RadzenColumn Size="12" SizeSM="12">
                        <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                            @Localizer["Description"] 
                            <RadzenTextBox @bind-Value="@ApiKeyObj.Description"/>
                        </RadzenStack>
                    </RadzenColumn>
                </RadzenRow>
            </RadzenStack>
            <RadzenRow Gap="1rem" style="font-size: large">
                <RadzenStack Orientation="Orientation.Horizontal" JustifyContent="JustifyContent.Start" Gap="1rem" Class="rz-mt-8 rz-mb-4">
                    <RadzenButton ButtonType="ButtonType.Submit" Size="ButtonSize.Large" Icon="save" Text=@Localizer["Save"] Click="@SaveObjectButton" />
                </RadzenStack>
            </RadzenRow>
        </ChildContent>
    </RadzenContent>
</RadzenTemplateForm>

@code {
    
    [Parameter] public string Titel { get; set; } = string.Empty;
    [Parameter] public DialogService? DialogService { get; set; }
    
    ApiKey ApiKeyObj { get; set; } = new ();
    LoadingIndicator? _loadingIndicator;
    readonly LoadingIndicatorOptions _options = new(false, false, false, false);

    private async Task SaveObjectButton(object? arg)
    {
        if (_loadingIndicator is null)
            return;

        await _loadingIndicator.Run(arg);

        DialogService?.Close();
    }
    
    bool _beginToCreate = false;
    private async Task CreateApiKey()
    {
        if(_beginToCreate)
            return;
        
        _beginToCreate = true;
        StateHasChanged();
        
        try
        {
            var _ = await Sender.Send(new CreateApiKeyCommand(ApiKeyObj.Description));
        }
        catch (Exception e)
        {
            DialogService?.Close();

            if(DialogService is not null)
                await DialogService.ShowError(e.Message);
        }
    }
}