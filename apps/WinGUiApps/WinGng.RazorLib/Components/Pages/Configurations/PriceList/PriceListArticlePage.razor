@page "/PriceListArticlePage"
@using MediatR
@using WingCore.application.ApiDtos.MasterData.V1
@using WingCore.application.PriceList.Queries

@inject IMediator Mediator
@inject NotificationService NotificationService
@inject DialogService DialogService

@inherits BasePage

<RadzenStack>
    <RadzenDataGrid @ref="_priceListGrid"
                    AllowAlternatingRows="true"
                    AllowFiltering="true"
                    FilterMode="FilterMode.CheckBoxList"
                    ClearFilterText="Entfernen"
                    ApplyFilterText="Anwenden"
                    EmptyText="Keine Einträge gefunden"
                    NextPageTitle="Nächste Seite"
                    PrevPageTitle="Vorherige Seite"
                    LastPageTitle="Letzte Seite"
                    FirstPageTitle="Erste Seite"
                    PageTitleFormat="Seite {0}"
                    AllowPaging="true"
                    PageSize="7"
                    AllowSorting="true"
                    EditMode="@EditMode"
                    Data="@PriceLists"
                    TItem="PriceListDtoV1"
                    RowUpdate="@OnUpdatePriceListRow"
                    RowCreate="@OnCreatePriceListRow"
                    SelectionMode="DataGridSelectionMode.Single"
                    @bind-Value="@_selectedPriceLists"
                    click="OnCellClick"
                    Sort="@Reset"
                    Page="@Reset"
                    Filter="@Reset"
                    ColumnWidth="200px">
        <HeaderTemplate>
            <RadzenButton ButtonStyle="ButtonStyle.Success" 
                          Icon="add_circle" 
                          Text="Artikel hinzufügen" 
                          Click="@InsertPriceListRow" 
                          Disabled="@(EditMode == DataGridEditMode.Single && _priceListsToInsert.Any())"/>
            @if (SelectedPriceListHeader is not null)
            {
                <RadzenText class="rz-pt-2">
                    <RadzenIcon Icon="filter_alt"/> Gefiltert nach: @SelectedPriceListHeader.Text1
                </RadzenText>
            }
        </HeaderTemplate>
        <Columns>
            <RadzenDataGridColumn Property="Nr" Title="Pos." Width="100px" Frozen="true"/>
            <RadzenDataGridColumn Property="ArtBez" Title="Bezeichnung 1" Width="280px">
                <EditTemplate Context="priceList">
                    <RadzenTextBox @bind-Value="priceList.ArtBez" 
                                   Style="width:200px; display: block" 
                                   Name="Bezeichnung 1" 
                                   aria-label="Bezeichnung 1 eingeben"/>
                </EditTemplate>
            </RadzenDataGridColumn>
            <RadzenDataGridColumn Property="ArtBez2" Title="Bezeichnung 2" Width="280px">
                <EditTemplate Context="priceList">
                    <RadzenTextBox @bind-Value="priceList.ArtBez2" 
                                   Style="width:200px; display: block" 
                                   Name="Bezeichnung 2" 
                                   aria-label="Bezeichnung 2 eingeben"/>
                </EditTemplate>
            </RadzenDataGridColumn>
            <RadzenDataGridColumn Property="Bzg" Title="Bzg" Width="150px">
                <EditTemplate Context="priceList">
                    <RadzenTextBox @bind-Value="priceList.Bzg" 
                                   Style="width:125px; display: block" 
                                   Name="Bzg" 
                                   aria-label="Bzg eingeben"/>
                </EditTemplate>
            </RadzenDataGridColumn>
            <RadzenDataGridColumn Property="Price1" Title="Preis 1" Width="150px" FormatString="{0:F2} €">
                <EditTemplate Context="priceList">
                    <RadzenNumeric @bind-Value="priceList.Price1" 
                                   Style="width:125px; display: block" 
                                   Name="Preis 1" 
                                   aria-label="Preis 1 eingeben"/>
                </EditTemplate>
            </RadzenDataGridColumn>
            <RadzenDataGridColumn Property="Price2" Title="Preis 2" Width="150px" FormatString="{0:F2} €">
                <EditTemplate Context="priceList">
                    <RadzenNumeric @bind-Value="priceList.Price2" 
                                   Style="width:125px; display: block" 
                                   Name="Preis 2" 
                                   aria-label="Preis 2 eingeben"/>
                </EditTemplate>
            </RadzenDataGridColumn>
            <RadzenDataGridColumn Property="Date1" Title="Datum 1" Width="150px" FormatString="{0:d}">
                <EditTemplate Context="priceList">
                    <RadzenDatePicker @bind-Value="priceList.Date1" 
                                      Style="width:125px; display: block" 
                                      DateFormat="dd/MM/yyyy"
                                      Name="Datum 1" 
                                      aria-label="Datum 1 eingeben"/>
                    <RadzenRequiredValidator Text="Datum ist erforderlich" Component="Datum 1" Popup="true"/>
                </EditTemplate>
            </RadzenDataGridColumn>
            <RadzenDataGridColumn Property="Date2" Title="Datum 2" Width="150px" FormatString="{0:d}">
                <EditTemplate Context="priceList">
                    <RadzenDatePicker @bind-Value="priceList.Date2" 
                                      Style="width:125px; display: block" 
                                      DateFormat="dd/MM/yyyy"
                                      Name="Datum 2" 
                                      aria-label="Datum 2 eingeben"/>
                    <RadzenRequiredValidator Text="Datum ist erforderlich" Component="Datum 2" Popup="true"/>
                </EditTemplate>
            </RadzenDataGridColumn>
            <RadzenDataGridColumn Context="priceList" 
                                  Filterable="false" 
                                  Sortable="false" 
                                  TextAlign="TextAlign.Right" 
                                  Frozen="true" 
                                  FrozenPosition="FrozenColumnPosition.Right"
                                  Width="150px">
                <Template Context="priceList">
                    <RadzenButton Icon="edit" 
                                  ButtonStyle="ButtonStyle.Light" 
                                  Variant="Variant.Flat" 
                                  Size="ButtonSize.Medium" 
                                  class="rz-my-1 rz-ms-1" 
                                  Click="@(args => EditPriceListRow(priceList))"
                                  @onclick:stopPropagation="true"/>
                    <RadzenButton Icon="delete" 
                                  ButtonStyle="ButtonStyle.Danger" 
                                  Variant="Variant.Flat" 
                                  Size="ButtonSize.Medium" 
                                  Shade="Shade.Lighter" 
                                  class="rz-my-1 rz-ms-1" 
                                  Click="@(args => DeletePriceListRow(priceList))" 
                                  @onclick:stopPropagation="true"/>
                </Template>
                <EditTemplate Context="priceList">
                    <RadzenButton Icon="check" 
                                  ButtonStyle="ButtonStyle.Success" 
                                  Variant="Variant.Flat" 
                                  Size="ButtonSize.Medium" 
                                  Click="@((args) => SavePriceListRow(priceList))" 
                                  aria-label="Save"/>
                    <RadzenButton Icon="close" 
                                  ButtonStyle="ButtonStyle.Light" 
                                  Variant="Variant.Flat" 
                                  Size="ButtonSize.Medium" 
                                  class="rz-my-1 rz-ms-1" 
                                  Click="@((args) => CancelPriceListEdit(priceList))" 
                                  aria-label="Cancel"/>
                    <RadzenButton Icon="delete" 
                                  ButtonStyle="ButtonStyle.Danger" 
                                  Variant="Variant.Flat" 
                                  Size="ButtonSize.Medium" 
                                  Shade="Shade.Lighter" 
                                  class="rz-my-1 rz-ms-1" 
                                  Click="@(args => DeletePriceListRow(priceList))" 
                                  aria-label="Delete"/>
                </EditTemplate>
            </RadzenDataGridColumn>
        </Columns>
    </RadzenDataGrid>
</RadzenStack>

@code{
    private RadzenDataGrid<PriceListDtoV1> _priceListGrid = null!;
    private const DataGridEditMode EditMode = DataGridEditMode.Single;

    private readonly List<PriceListDtoV1> _priceListsToInsert = [];
    
    [Parameter] public required IList<PriceListDtoV1> PriceLists { get; set; }
    [Parameter] public PriceListHeaderDtoV1? SelectedPriceListHeader { get; set; }
    [Parameter] public EventCallback<PriceListDtoV1> OnCellClick { get; set; }
    [Parameter] public EventCallback ItemAdded { get; set; }
    
    private IList<PriceListDtoV1?> _selectedPriceLists = [_selectedPriceList];
    private static PriceListDtoV1? _selectedPriceList = PriceListDtoV1.Create();
    
    protected override Task OnParametersSetAsync()
    {
        if (SelectedPriceListHeader is not null) PriceLists = PriceLists.Where(priceList => priceList.Nr == SelectedPriceListHeader.Number).ToList();
        
        return Task.CompletedTask;
    }
    
    void Reset()
    {
        _priceListsToInsert.Clear();
    }

    void Reset(PriceListDtoV1 priceListDtoV1)
    {
        _priceListsToInsert.Remove(priceListDtoV1);
    }
    
    private void ClearSelectedPriceListRow()
    {
        _selectedPriceList = null;
        _selectedPriceLists = [];
    }
    
    async Task InsertPriceListRow()
    {
        if (!_priceListGrid.IsValid) return;

        foreach (var column in _priceListGrid.ColumnsCollection)
        {
            await _priceListGrid.ClearFilter(column, true, false);
        }
        
        _priceListGrid.Sorts.Clear();
        
        await _priceListGrid.GoToPage(0);
        
        StateHasChanged();
        
        if (EditMode == DataGridEditMode.Single) Reset();
        
        var priceList = new PriceListDtoV1();
        _priceListsToInsert.Add(priceList);
        await _priceListGrid.InsertRow(priceList);
    }
    
    async Task EditPriceListRow(PriceListDtoV1 priceListDtoV1)
    {
        if (!_priceListGrid.IsValid) return;

        if (EditMode == DataGridEditMode.Single) Reset();
        
        await _priceListGrid.EditRow(priceListDtoV1);
    }
    
    async Task OnUpdatePriceListRow(PriceListDtoV1 priceListDtoV1)
    {
        Reset(priceListDtoV1);
        
        await Mediator.Send(new UpdatePriceListCommand(priceListDtoV1));
        
        NotificationService.Notify(new NotificationMessage { Severity = NotificationSeverity.Success, Summary = "Erfolg", Detail = "Artikel bearbeitet", Duration = 6000 });
    }
    
    async Task SavePriceListRow(PriceListDtoV1 priceListDtoV1)
    {
        await _priceListGrid.UpdateRow(priceListDtoV1);
    }

    void CancelPriceListEdit(PriceListDtoV1 priceListDtoV1)
    {
        Reset(priceListDtoV1);

        _priceListGrid.CancelEditRow(priceListDtoV1);
    }
    
    async Task DeletePriceListRow(PriceListDtoV1 priceListDtoV1)
    {
        var confirmed = await DialogService.Confirm($"Soll der Artikel '{priceListDtoV1.ArtBez}' wirklich gelöscht werden?", 
                                                            "Preisliste löschen",
                                                            new ConfirmOptions()
                                                            {
                                                                OkButtonText = "Ja", 
                                                                CancelButtonText = "Nein"
                                                            }) ?? false;

        if (!confirmed) return;
        
        Reset(priceListDtoV1);

        if (PriceLists.Contains(priceListDtoV1))
        {
            await Mediator.Send(new DeletePriceListCommand(priceListDtoV1));
            
            NotificationService.Notify(new NotificationMessage { Severity = NotificationSeverity.Success, Summary = "Erfolg", Detail = "Artikel gelöscht", Duration = 6000 });
        }
        else
        {
            _priceListGrid.CancelEditRow(priceListDtoV1);
        }

        PriceLists = PriceLists.Where(priceList => priceList != priceListDtoV1).ToList();
        
        ClearSelectedPriceListRow();
        
        await _priceListGrid.Reload();
    }
    
    async Task OnCreatePriceListRow(PriceListDtoV1 priceListDtoV1)
    {
        if (SelectedPriceListHeader is not null)
            priceListDtoV1.Nr = SelectedPriceListHeader.Number;
        
        var createdArticle = await Mediator.Send(new AddPriceListCommand(priceListDtoV1));

        ClearSelectedPriceListRow();
        _selectedPriceList = createdArticle;
        
        NotificationService.Notify(new NotificationMessage { Severity = NotificationSeverity.Success, Summary = "Erfolg", Detail = "Artikel hinzugefügt", Duration = 6000 });

        await ItemAdded.InvokeAsync();
    }
}