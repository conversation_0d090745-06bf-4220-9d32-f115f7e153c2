using Commons.ComboBoxs;
using MediatR;
using wingLager.application.ComboBoxs;
using wingLager.domain.ComboBoxs;

namespace WinGng.RazorLib.Components.Pages.Configurations.ComboBoxPagesAndComponent;

public class LagerComboBoxHelper(ISender sender)
{
    public ComboBoxConfigurationPage.ComboBoxConfigModule GetModule()
    {
        var module =  new ComboBoxConfigurationPage.ComboBoxConfigModule()
        {
            ModuleName = "Lager",
        };

        foreach (var cType in ComboboxType.PossibleComboboxTypesForWingLager)
        {
            var entry = new ComboBoxConfigurationPage.ComboBoxConfigModuleEntry()
            {
                Type = cType,
                LoadCommand = async (object obj) => await sender.Send(new GetAllComboBoxByTypeQuery((ComboboxType)obj)),
                SaveCommand = async (object obj) => await sender.Send(new UpdateComboBoxCommand((Combobox)obj)),
                CreateCommand = async (object obj) => await sender.Send(new CreateComboBoxCommand((Combobox)obj)),
                DeleteCommand = async (object obj) => await sender.Send(new DeleteComboBoxCommand((long)obj)),
                ComboBoxObj = new Combobox()
                {
                    Type = cType,
                    Key = "",
                    Value = "",
                    Description = ""
                }
            };
            
            module.PossibleComboBoxItems.Add(entry);
        }
        return module;
    }
}