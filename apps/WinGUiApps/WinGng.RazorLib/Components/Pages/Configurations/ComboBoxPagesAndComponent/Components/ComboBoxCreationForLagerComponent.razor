@using Commons.ComboBoxs
@using MediatR
@using wingLager.application.ComboBoxs
@using wingLager.domain.ComboBoxs

@inject ISender Sender
@inject DialogService DialogService

<ComboBoxCreationComponent ComboBoxObj="_comboBoxObj"
                           Titel="Bl<PERSON><PERSON>feld"
                           SaveObjectCallback="CreateOrUpdateComboBox"
                           DeleteObjectCallback="DeleteComboBox"
                           PossibleType="@ComboboxType.PossibleComboboxTypesForWingLager"/>

@code {
    
    [Parameter]
    public long ComboBoxId { get; set; }
    private Combobox _comboBoxObj = new Combobox();
    
    protected override async Task OnInitializedAsync()
    {
        try
        {
            if (ComboBoxId != 0)
                _comboBoxObj = await Sender.Send(new GetComboBoxQuery(ComboBoxId)) ?? new Combobox();
        }
        catch (Exception e)
        {
            DialogService.Close();
            await DialogService.ShowError(e.InnerException?.Message ?? e.Message);
        }

        await base.OnInitializedAsync();
    }
    
    private async Task CreateOrUpdateComboBox()
    {
        try
        {
            if(_comboBoxObj.Id == 0)
                _comboBoxObj = await Sender.Send(new CreateComboBoxCommand(_comboBoxObj));
            else
                _comboBoxObj = await Sender.Send(new UpdateComboBoxCommand(_comboBoxObj));
        }
        catch (Exception ex)
        {
            DialogService.Close();
            await DialogService.ShowError(ex.Message);
        }
    }
    
    private async Task DeleteComboBox()
    {
        try
        {
            if(_comboBoxObj.Id != 0)
                await Sender.Send(new DeleteComboBoxCommand(_comboBoxObj.Id));
        }
        catch (Exception ex)
        {
            DialogService.Close();
            await DialogService.ShowError(ex.Message);
        }
    }
}