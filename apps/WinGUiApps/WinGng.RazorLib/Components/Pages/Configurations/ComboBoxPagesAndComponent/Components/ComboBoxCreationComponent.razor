@using Commons.ComboBoxs
@using WinGng.RazorLib.Components.Common.LoadingPage


@inject NavigationManager NavigationManager

@if (ComboBoxObj is null)
{
    return;
}

<LoadingIndicator @ref="_loadingIndicator"
                  DoLoadDataCallback="SaveObjectCallback"
                  Option="_options" DialogService="DialogService"/>


<RadzenTemplateForm TItem="ICombobox" Data="@ComboBoxObj"  >
    <RadzenContent Container="main" >
        <ChildContent>
                
            <RadzenStack Orientation="Orientation.Vertical" Gap="1rem">
                <RadzenRow Gap="1rem" style="font-size: large">
                    <RadzenColumn Size="12" SizeSM="6">
                        <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                            <RadzenLabel Text=@Localizer["Type"] Style="margin-right: 8px; vertical-align: middle;"/>
                            <RadzenTextBox Value="@ComboBoxObj.Type.ToString()" ReadOnly="true" />
                        </RadzenStack>
                    </RadzenColumn>
                    <RadzenColumn Size="12" SizeSM="6">
                        <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                   
                        </RadzenStack>
                    </RadzenColumn>
                </RadzenRow>
                <RadzenRow Gap="1rem" style="font-size: large">
                    <RadzenColumn Size="12" SizeSM="6">
                        <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                            @Localizer["Index"] 
                            <RadzenTextBox @bind-Value="@ComboBoxObj.Key"/>
                        </RadzenStack>
                    </RadzenColumn>
                    <RadzenColumn Size="12" SizeSM="6">
                        <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                            @Localizer["Description"] 
                            <RadzenTextBox @bind-Value="@ComboBoxObj.Value"/>
                        </RadzenStack>
                    </RadzenColumn>
                </RadzenRow>
            </RadzenStack>
            <RadzenRow Gap="1rem" style="font-size: large">
                <RadzenStack Orientation="Orientation.Horizontal" JustifyContent="JustifyContent.Start" Gap="1rem" Class="rz-mt-8 rz-mb-4">
                    <RadzenButton ButtonType="ButtonType.Submit" Size="ButtonSize.Large" Icon="save" Text=@Localizer["Save"] Click="@SaveObjectButton" />
                </RadzenStack>
            </RadzenRow>
        </ChildContent>
    </RadzenContent>
</RadzenTemplateForm>

@code {
    [Parameter] public ICombobox? ComboBoxObj { get; set; }
    [Parameter] public string Titel { get; set; } = string.Empty;
    [Parameter] public EventCallback SaveObjectCallback { get; set; }
    [Parameter] public DialogService? DialogService { get; set; }
    
    LoadingIndicator? _loadingIndicator;
    readonly LoadingIndicatorOptions _options = new(false, false, false, false);

    private async Task SaveObjectButton(object? arg)
    {
        if (_loadingIndicator is null)
            return;

        await _loadingIndicator.Run(arg);

        DialogService?.Close();
    }
    
    
    private void SetType(object? value)
    {
        if(value is null)
            return;
        if(ComboBoxObj is null)
            return;
        if(value is not ComboboxType valueCast)
            return;
        
        ComboBoxObj.Type = valueCast;
    }
    
}