@using MediatR
@using PrinterService
@using WinGng.RazorLib.Components.Common.LoadingPage
@using WingCore.domain.Models.StammDbModels
@using WingCore.application.Contract.IModels
@using WingCore.application.Contract.Services
@using WingCore.application.PrintableDtos
@using WingCore.domain.Common.Configurations
@using WinGng.RazorLib.Components.Pages.PrintView
@using wingPrinterListLabel.application.Contracts
@using wingPrinterListLabel.application.Wgs
@using wingPrinterListLabel.application.RepositoryItems
@using wingPrinterListLabel.domain

@inherits BasePage


@inject IConfigurationService ConfigurationService
@inject DialogService DialogService
@inject ICountryService CountryService
@inject IApplicationPath ApplicationPath
@inject ISender Sender

@inject IListLabelFactory ListLabelFactory

<LoadingIndicator @ref="_loadingIndicatorLoadConfigData"
                  DoLoadDataCallback="LoadConfigData"
                  Option="_optionsLoadConfigData" DialogService="DialogService"/>
<LoadingIndicator @ref="_loadingIndicatorToEmailView"
                  DoLoadDataCallbackWithArg="async (arg) => await OpenEmailView((string)arg)"
                  Option="_optionsIndicatorToEmailView" DialogService="DialogService"/>

<RadzenContent Container="main"  Style="min-width: 100%">
    <ChildContent>
        <RadzenRow >
            <RadzenHeading Size="H1" style="display: inline-block" Text=@Localizer["ElectronicWgsConfiguration"]/>
        </RadzenRow>
        <RadzenRow Gap="1rem" style="font-size: large">
            <RadzenStack Orientation="Orientation.Horizontal" JustifyContent="JustifyContent.Start" Gap="1rem" Class="rz-mt-8 rz-mb-4">
                <RadzenButton ButtonType="ButtonType.Submit" Size="ButtonSize.Large" Icon="save" Text=@Localizer["SaveConfiguration"] Click="@SaveConfigData"/>
            </RadzenStack>
        </RadzenRow>
        <RadzenTabs RenderMode="TabRenderMode.Client" TabPosition="@TabPosition.Top" >
            <Tabs>
                <RadzenTabsItem Text=@Localizer["Configuration"]>
                    <ElectricWgsConfigurationEmailComponent
                        DialogService = "@DialogService"
                        ConfigurationElectricWgs="@ConfigurationElectricWgs">
                    </ElectricWgsConfigurationEmailComponent>
                </RadzenTabsItem>

                <RadzenTabsItem Text=@Localizer["Email"]>

                    <RadzenTabs @ref="TabsCountry" RenderMode="TabRenderMode.Server" TabPosition="@TabPosition.Left"  @bind-SelectedIndex="SelectedLanguageTab">
                        <Tabs>
                            @foreach (var languageToEmailFields in ConfigurationElectricWgs.LanguageToEmailFields)
                            {
                                <RadzenTabsItem Text="@languageToEmailFields.Key">
                                    <RadzenStack Orientation="Orientation.Vertical" Gap="1rem" Style="text-align: left;">
                                        <RadzenRow Gap="1rem" style="font-size: large">
                                            <RadzenColumn Size="12" SizeSM="6">
                                                <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                                    <RadzenLabel Text=@Localizer["PrintLayout"]/>
                                                    <RadzenStack Orientation="Orientation.Horizontal" Gap="6px">
                                                        <RadzenStack Orientation="Orientation.Vertical" Gap="1rem"  Style="width:90%">
                                                            <RadzenDropDown @bind-Value="@languageToEmailFields.Value.LayoutForPrint"
                                                                            Data="@_allLayoutCanUseKeyToDescription"
                                                                            TextProperty="@nameof(CustomizedRepositoryItem.UIName)"
                                                                            TValue="@string"
                                                                            ValueProperty="InternalID"
                                                                            AllowClear="true"
                                                                            AllowFiltering="true"
                                                                            InputAttributes="@(new Dictionary<string,object>(){ { "aria-label", Localizer["SelectPrintLayout"] }})"
                                                                            Placeholder=@Localizer["SelectPrintLayout"]/>
                                                        </RadzenStack>
                                                        <RadzenButton Click="@(_ => OpenDesignerForLayout(languageToEmailFields.Value.LayoutForPrint))"
                                                                      Icon="edit" ButtonStyle="ButtonStyle.Primary"
                                                                      Size="ButtonSize.ExtraSmall"
                                                                      Style="width:9%"
                                                                      MouseEnter="@(args => ShowTooltip(args, Localizer["OpenDesignerForEWgs"], new TooltipOptions() { Position = TooltipPosition.Left }))"/>
                                                    </RadzenStack>
                                                </RadzenStack>
                                            </RadzenColumn>
                                            <RadzenColumn Size="12" SizeSM="6">
                                                <RadzenStack Orientation="Orientation.Vertical" Gap="6px" JustifyContent="JustifyContent.Right">
                                                    <RadzenRow Gap="1rem" style="font-size: large" JustifyContent="JustifyContent.Right">
                                                        <RadzenButton Click="@(_ => OpenEmailViewButton(languageToEmailFields.Key))"
                                                                      Icon="mail"
                                                                      ButtonStyle="ButtonStyle.Primary"
                                                                      Size="ButtonSize.Medium"
                                                                      MouseEnter="@(args => ShowTooltip(args, Localizer["SendTestEmail"], new TooltipOptions() { Position = TooltipPosition.Left }))"/>
                                                        <RadzenButton Click="@(_ => RemoveLanguageEmailFieldToConfig(languageToEmailFields.Key))"
                                                                      Icon="delete"
                                                                      ButtonStyle="ButtonStyle.Primary"
                                                                      Size="ButtonSize.Medium"
                                                                      MouseEnter="@(args => ShowTooltip(args, Localizer["DeleteEmailConfiguration"], new TooltipOptions() { Position = TooltipPosition.Left }))"/>
                                                    </RadzenRow>
                                                </RadzenStack>
                                            </RadzenColumn>
                                        </RadzenRow>
                                        <RadzenRow Gap="1rem" style="font-size: large">
                                            <RadzenColumn Size="12" SizeSM="6">
                                                <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                                    <RadzenLabel Text=@Localizer["Subject"]/>
                                                    <RadzenTextBox @bind-Value="@languageToEmailFields.Value.Subject" Style="width:100%"/>
                                                </RadzenStack>
                                            </RadzenColumn>
                                            <RadzenColumn Size="12" SizeSM="6">
                                                <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                                    <RadzenLabel Text=@Localizer["FileName"]/>
                                                    <RadzenTextBox @bind-Value="@languageToEmailFields.Value.FileName" Style="width:100%"/>
                                                </RadzenStack>
                                            </RadzenColumn>
                                        </RadzenRow>

                                        <RadzenRow Gap="0.1rem" Style="@HtmlHeight">
                                            <RadzenColumn Size="9">
                                                <RadzenHtmlEditor @bind-Value="@languageToEmailFields.Value.EmailBodyHtml" UploadUrl="upload/image" Style="@HtmlHeight"/>
                                            </RadzenColumn>
                                            <RadzenColumn Size="3">
                                                <RadzenListBox FilterCaseSensitivity="FilterCaseSensitivity.CaseInsensitive"
                                                               FilterOperator="StringFilterOperator.StartsWith"
                                                               AllowFiltering="true"
                                                               TValue="string"
                                                               Value="@_value"
                                                               Data=@_fieldsName
                                                               AllowClear="true"
                                                               Style="@HtmlHeight"
                                                               InputAttributes="@(new Dictionary<string, object>() { { "aria-label", "select" } })"/>
                                            </RadzenColumn>
                                        </RadzenRow>
                                    </RadzenStack>
                                </RadzenTabsItem>
                            }

                            <RadzenTabsItem Icon="add" @onclick="OpenSelectLangauge"/>

                        </Tabs >
                    </RadzenTabs >
                </RadzenTabsItem>
            </Tabs>
        </RadzenTabs>
    </ChildContent>
</RadzenContent>


@code {
    [Parameter] public ConfigurationElectricWgs ConfigurationElectricWgs { get; set; } = new();
    readonly LoadingIndicatorOptions _optionsLoadConfigData = new(false, false, false, false, 0, 6);

    LoadingIndicator? _loadingIndicatorToEmailView;
    readonly LoadingIndicatorOptions _optionsIndicatorToEmailView = new(false, false, false, false);

    LoadingIndicator? _loadingIndicatorLoadConfigData;
    IEnumerable<string> _fieldsName = [];
    string _value = string.Empty;
    private string HtmlHeight => $"height: {(Dimensions.Height < 600 ? 500 : Dimensions.Height - 400)}px; width:100%";

    IEnumerable<LandKennung> _allCountries = [];
    string SelectedLand { get; set; } = string.Empty;

    public int SelectedLanguageTab { get; set; } = 1;
    RadzenTabs? TabsCountry { get; set; }

    protected override async void OnAfterRender(bool firstRender)
    {
        if (!firstRender)
            return;
        await LoadConfigDataButton();
        _allCountries = await CountryService.GetAllCountries();
        _allCountries = _allCountries
                            .Where(l=> !string.IsNullOrWhiteSpace(l.IntraKennung))
                            .OrderBy(l => l.Landesbezeichnung);

        await ReloadLayoutItems();

        TabsCountry?.Reload();
        StateHasChanged();
    }

    IEnumerable<CustomizedRepositoryItem> _allLayoutCanUseKeyToDescription = [];

    private async Task ReloadLayoutItems()
    {
        try
        {
            _allLayoutCanUseKeyToDescription = await Sender.Send(new AllRepositoryItemsFromPrintObjectQuery(LabelWiegeschein.Dummy()));
        }
        catch (Exception e)
        {
            await DialogService.ShowError(e.InnerException?.Message ?? e.Message);
        }
    }


    private async Task LoadConfigDataButton()
    {
        if (_loadingIndicatorLoadConfigData is null)
            return;

        await _loadingIndicatorLoadConfigData.Run();
    }

    public async Task LoadConfigData()
    {
        try
        {
            ConfigurationElectricWgs = await ConfigurationService.GetConfigurationElectricWgsAsync();
            _fieldsName = PrinterCreator.GetVariablesFromObject(LabelWiegeschein.Dummy()).Keys;
        }
        catch (Exception e)
        {
            await DialogService.ShowError(e.InnerException?.Message ?? e.Message);
        }
    }

    bool _saveConfigDataStart;

    private async Task SaveConfigData()
    {
        if (_saveConfigDataStart)
            return;

        _saveConfigDataStart = true;
        try
        {
            var id = await ConfigurationService.SetConfigurationElectricWgsAsync(ConfigurationElectricWgs);
            await LoadConfigData();
        }
        catch (Exception e)
        {
            await DialogService.ShowError(e.InnerException?.Message ?? e.Message);
        }
        finally
        {
            _saveConfigDataStart = false;
        }
    }

    async Task OpenSelectLangauge()
    {
        await DialogService.OpenAsync(Localizer["SelectCountry"], ds =>
            @<RadzenStack Orientation="Orientation.Vertical" Gap="1rem" class="rz-h-100 rz-p-4">
                <RadzenDropDown @bind-Value="@SelectedLand"
                                Data="@_allCountries"
                                TextProperty="@nameof(LandKennung.Landesbezeichnung)"
                                TValue="@string"
                                ValueProperty="IntraKennung"
                                AllowClear="true"
                                AllowFiltering="true"
                                InputAttributes="@(new Dictionary<string,object>(){ { "aria-label", Localizer["SelectCountry"] }})"
                                Placeholder=@Localizer["SelectCountry"] />
                <RadzenButton Click="AddLanguageEmailFieldToConfig" Icon="add"></RadzenButton>
            </RadzenStack>);
    }

    void AddLanguageEmailFieldToConfig()
    {
        if (string.IsNullOrWhiteSpace(SelectedLand))
        {
            DialogService.Close();
            return;
        }

        if(!ConfigurationElectricWgs.LanguageToEmailFields.ContainsKey(SelectedLand))
            ConfigurationElectricWgs.LanguageToEmailFields.Add(SelectedLand, new LanguageAndEmailFields());

        SelectedLanguageTab = ConfigurationElectricWgs.LanguageToEmailFields.Count;

        TabsCountry?.Reload();
        StateHasChanged();
        DialogService.Close();
    }

    void RemoveLanguageEmailFieldToConfig(string selectedLandToDelete)
    {
        if (string.IsNullOrWhiteSpace(selectedLandToDelete))
            return;

        if(ConfigurationElectricWgs.LanguageToEmailFields.ContainsKey(selectedLandToDelete))
            ConfigurationElectricWgs.LanguageToEmailFields.Remove(selectedLandToDelete);

        SelectedLanguageTab = ConfigurationElectricWgs.LanguageToEmailFields.Count - 1;

        TabsCountry?.Reload();
        StateHasChanged();
    }

    private async Task OpenDesignerForLayout(string layoutForPrint)
    {
        try
        {
            if (ApplicationPath.IAmBlazorServer())
            {
                _ = Task.Run(() =>
                {
                    ListLabelFactory.OpenWebDesigner(LabelWiegeschein.Dummy(), "");
                }).ConfigureAwait(false);
            }
            else
            {
                await ListLabelFactory.OpenDesktopDesigner(LabelWiegeschein.Dummy());
                //DesigneHelper.OpenDesigner(LabelWiegeschein.Dummy(),layoutForPrint,ApplicationPath.GetPath());
            }
        }
        catch (Exception e)
        {
            await DialogService.ShowError(e.InnerException?.Message ?? e.Message);
        }
        finally
        {
            DialogService.Close();
        }
    }
    private async Task OpenEmailViewButton(object key)
    {
        if (_loadingIndicatorToEmailView is null)
            return;

        await _loadingIndicatorToEmailView.Run(key);
    }

    private async Task OpenEmailView(string key)
    {
        try
        {

            var command = new DummyWgsSendEmailCommand("",key,ConfigurationElectricWgs);
            var emailDataToCreateEmail = await Sender.Send(command);

            if (emailDataToCreateEmail is null)
            {
                await DialogService.ShowError(Localizer["EmailCouldNotBeCreated"]);
                return;
            }

            await DialogService.OpenAsync<EmailViewPage>(Localizer["EmailTestPreview"],
                new Dictionary<string, object>()
                {
                    { "EmailDataToCreateEmailParam", emailDataToCreateEmail },
                    { "ShowHeaderText", false },
                    { "DialogService", DialogService }
                },
                new DialogOptions()
                {
                    Resizable = false,
                    Draggable = false,

                    Width = "90%",
                    Height = "100%"
                });
        }
        catch (Exception e)
        {
            await DialogService.ShowError(e.InnerException?.Message ?? e.Message);
        }
        finally
        {
            DialogService.Close();
        }
    }
}
