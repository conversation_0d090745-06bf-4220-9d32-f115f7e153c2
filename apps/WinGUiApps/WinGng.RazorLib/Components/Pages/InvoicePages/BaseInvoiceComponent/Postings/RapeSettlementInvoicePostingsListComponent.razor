
@if (RapeSettlementInvoiceOverviewDto is not null)
{
    <RadzenDataGrid AllowFiltering="true"
                    AllowPaging="true"
                    AllowSorting="true"
                    FilterPopupRenderMode="PopupRenderMode.OnDemand"
                    FilterCaseSensitivity="FilterCaseSensitivity.CaseInsensitive"
                    PageSize="15"
                    PagerHorizontalAlign="HorizontalAlign.Center"
                    AllowColumnResize="true"
                    ShowPagingSummary="true"
                    PagingSummaryFormat="@Localizer["PagingSummaryFormat"]"
                    Data="@RapeSettlementInvoiceOverviewDto?.ListAllPostings" Density="Density.Compact" AllowAlternatingRows="false">
        <Columns>
            <RadzenDataGridColumn Property="@nameof(GrainAndRapeSettlementInvoicePostingDto.GridPosition)" Title="@Localizer["GridPosition"]">
                <FooterTemplate>
                    <b>@Localizer["Total"]</b>
                </FooterTemplate>
            </RadzenDataGridColumn>
            <RadzenDataGridColumn Property="@nameof(GrainAndRapeSettlementInvoicePostingDto.Schema)" Title="@Localizer["Type"]"/>
            <RadzenDataGridColumn Property="@nameof(GrainAndRapeSettlementInvoicePostingDto.Number)" Title="@Localizer["ItemNumber"]"/>
            <RadzenDataGridColumn Property="@nameof(GrainAndRapeSettlementInvoicePostingDto.Description)" Title="@Localizer["Description"]"/>
            <RadzenDataGridColumn Property="@nameof(GrainAndRapeSettlementInvoicePostingDto.TotalNetto)" Title="@Localizer["TotalNet"]" FormatString="{0:F4}" TextAlign="TextAlign.Right">
                <FooterTemplate>
                    <b>@string.Format(new System.Globalization.CultureInfo("de-De"), "{0:F4}", RapeSettlementInvoiceOverviewDto?.ListAllPostings?.Sum(o => o.TotalNetto))</b>
                </FooterTemplate>
            </RadzenDataGridColumn>
            @if (RapeSettlementInvoiceOverviewDto?.ListAllPostings.Sum(o => o.TotalDiscount) != decimal.Zero)
            {
                <RadzenDataGridColumn Title="@Localizer["TotalDiscount"]" FormatString="{0:F4}" TextAlign="TextAlign.Right">
                    <Template >
                        @if(context.Schema.IsPo)
                        {
                            @context.TotalDiscount
                        }
                        else
                        {
                        @string.Format(new System.Globalization.CultureInfo("de-De"), "{0:F4}", decimal.Zero)
                        }
                    </Template>
                    <FooterTemplate>
                        <b>@string.Format(new System.Globalization.CultureInfo("de-De"), "{0:F4}", RapeSettlementInvoiceOverviewDto?.ListAllPostings.Where(o => o.Schema.IsPo).Sum(o => o.TotalDiscount))</b>
                    </FooterTemplate>
                </RadzenDataGridColumn>
                <RadzenDataGridColumn Title="@Localizer["TotalNetWithDiscount"]" FormatString="{0:F4}" TextAlign="TextAlign.Right"> <Template >
                        @if(context.Schema.IsPo)
                        {
                            @context.TotalNettoWithDiscount
                        }
                        else
                        {
                            @string.Format(new System.Globalization.CultureInfo("de-De"), "{0:F4}", decimal.Zero)
                        }
                    </Template>
                    <FooterTemplate>
                        <b>@string.Format(new System.Globalization.CultureInfo("de-De"), "{0:F4}", RapeSettlementInvoiceOverviewDto?.ListAllPostings.Where(o=> o.Schema.IsPo).Sum(o => o.TotalNettoWithDiscount))</b>
                    </FooterTemplate>
                </RadzenDataGridColumn>
            }
            
            <RadzenDataGridColumn Property="@nameof(GrainAndRapeSettlementInvoicePostingDto.TotalGross)" Title="@Localizer["TotalGross"]" FormatString="{0:F4}" TextAlign="TextAlign.Right">
                <FooterTemplate>
                    <b>@string.Format(new System.Globalization.CultureInfo("de-De"), "{0:F4}", RapeSettlementInvoiceOverviewDto?.ListAllPostings?.Sum(o => o.TotalGross))</b>
                </FooterTemplate>
            </RadzenDataGridColumn>
            <RadzenDataGridColumn  Title="Prozent" FormatString="{0:F4}" TextAlign="TextAlign.Right" >
                <Template>
                    @context.Tax.Vat
                </Template>
            </RadzenDataGridColumn>
            <RadzenDataGridColumn Title="Steuerbetrag" FormatString="{0:F4}" TextAlign="TextAlign.Right">
                <Template>
                    @context.Tax.VatAmount
                </Template>
                <FooterTemplate>
                    <b>@string.Format(new System.Globalization.CultureInfo("de-De"), "{0:F4}", RapeSettlementInvoiceOverviewDto?.ListAllPostings.Sum(o => o.Tax.VatAmount))</b>
                </FooterTemplate>
            </RadzenDataGridColumn>
            <RadzenDataGridColumn Property="@nameof(GrainAndRapeSettlementInvoicePostingDto.FibuAccount)" Title="Konto"/>
            <RadzenDataGridColumn Property="@nameof(GrainAndRapeSettlementInvoicePostingDto.LsNumber)" Title="LS-Nr"/>
        </Columns>
    </RadzenDataGrid>
}

@code {
    [Parameter] public RapeSettlementInvoiceOverviewDto? RapeSettlementInvoiceOverviewDto { get; set; }
}