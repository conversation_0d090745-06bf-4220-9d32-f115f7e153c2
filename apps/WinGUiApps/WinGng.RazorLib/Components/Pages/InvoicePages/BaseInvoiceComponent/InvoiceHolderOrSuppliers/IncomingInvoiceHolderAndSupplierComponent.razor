
@if (IncomingInvoiceOverviewDto is null)
{
    return;
}

@if (ShowInvoiceHolder)
{
    <BaseInvoiceHolderOrSupplierComponent InvoiceSupplierOrHolderDto="@IncomingInvoiceOverviewDto?.InvoiceHolder"></BaseInvoiceHolderOrSupplierComponent>
}

@if (ShowInvoiceSupplier)
{
    <BaseInvoiceHolderOrSupplierComponent InvoiceSupplierOrHolderDto="@IncomingInvoiceOverviewDto?.InvoiceSupplier"></BaseInvoiceHolderOrSupplierComponent>
}

@code {
    [Parameter] public  IncomingInvoiceOverviewDto? IncomingInvoiceOverviewDto { get; set; }
    [Parameter] public  bool ShowInvoiceHolder { get; set; }
    [Parameter] public  bool ShowInvoiceSupplier { get; set; }
}