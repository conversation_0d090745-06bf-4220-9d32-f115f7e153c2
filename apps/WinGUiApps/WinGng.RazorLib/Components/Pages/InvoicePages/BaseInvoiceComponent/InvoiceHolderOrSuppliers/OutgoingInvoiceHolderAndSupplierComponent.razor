
@if (OutgoingInvoiceOverviewDto is null)
{ 
    return;
}

@if (ShowInvoiceHolder)
{
    <BaseInvoiceHolderOrSupplierComponent InvoiceSupplierOrHolderDto="@OutgoingInvoiceOverviewDto?.InvoiceHolder"></BaseInvoiceHolderOrSupplierComponent>
}


@if (ShowInvoiceSupplier)
{
    <BaseInvoiceHolderOrSupplierComponent InvoiceSupplierOrHolderDto="@OutgoingInvoiceOverviewDto?.InvoiceSupplier"></BaseInvoiceHolderOrSupplierComponent>
}

@code {
    [Parameter] public  OutgoingInvoiceOverviewDto? OutgoingInvoiceOverviewDto { get; set; }
    [Parameter] public  bool ShowInvoiceHolder { get; set; }
    [Parameter] public  bool ShowInvoiceSupplier { get; set; }
}