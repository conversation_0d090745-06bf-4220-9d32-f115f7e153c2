
@if (GrainSettlementInvoiceOverviewDto is null)
{
    return;
}

@if (ShowInvoiceHolder)
{
    <BaseInvoiceHolderOrSupplierComponent InvoiceSupplierOrHolderDto="@GrainSettlementInvoiceOverviewDto?.InvoiceHolder"></BaseInvoiceHolderOrSupplierComponent>
}

@if (ShowInvoiceSupplier)
{
    <BaseInvoiceHolderOrSupplierComponent InvoiceSupplierOrHolderDto="@GrainSettlementInvoiceOverviewDto?.InvoiceSupplier"></BaseInvoiceHolderOrSupplierComponent>
}

@code {
    [Parameter] public  GrainSettlementInvoiceOverviewDto? GrainSettlementInvoiceOverviewDto { get; set; }
    [Parameter] public  bool ShowInvoiceHolder { get; set; }
    [Parameter] public  bool ShowInvoiceSupplier { get; set; }
}