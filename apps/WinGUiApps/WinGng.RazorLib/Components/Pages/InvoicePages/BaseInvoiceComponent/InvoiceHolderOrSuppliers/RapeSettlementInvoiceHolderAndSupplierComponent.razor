
@if (RapeSettlementInvoiceOverviewDto is null)
{
    return;
}

@if (ShowInvoiceHolder)
{
    <BaseInvoiceHolderOrSupplierComponent InvoiceSupplierOrHolderDto="@RapeSettlementInvoiceOverviewDto?.InvoiceHolder"></BaseInvoiceHolderOrSupplierComponent>
}

@if (ShowInvoiceSupplier)
{
    <BaseInvoiceHolderOrSupplierComponent InvoiceSupplierOrHolderDto="@RapeSettlementInvoiceOverviewDto?.InvoiceSupplier"></BaseInvoiceHolderOrSupplierComponent>
}

@code {
    [Parameter] public  RapeSettlementInvoiceOverviewDto? RapeSettlementInvoiceOverviewDto { get; set; }
    [Parameter] public  bool ShowInvoiceHolder { get; set; }
    [Parameter] public  bool ShowInvoiceSupplier { get; set; }
}