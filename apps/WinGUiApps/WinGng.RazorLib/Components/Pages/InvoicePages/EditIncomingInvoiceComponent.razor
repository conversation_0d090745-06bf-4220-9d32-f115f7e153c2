@page "/invoiceOverView/editIncomingInvoice/{InvoiceNumber:long}"

@using WingCore.application.Contract.Services
@using WinGng.RazorLib.Components.Common.LoadingPage
@using WinGng.RazorLib.Components.Pages.InvoicePages.BaseInvoiceComponent

@inject IIncomingInvoiceHeaderService IncomingInvoiceHeaderService
@inject DialogService DialogService

<LoadingIndicator @ref="_loadingIndicator"
                  DoLoadDataCallback="LoadInvoice"
                  Option="_options" DialogService="DialogService"/>

<BaseInvoiceInformationComponent BaseInvoiceOverviewDto="@IncomingInvoiceOverviewDto"
                                 Titel="@Localizer["IncomingInvoiceInformation"]"
                                 ReloadInvoiceCallback="@ReloadInvoice"/>

@code {
    [Parameter] public long InvoiceNumber { get; set; }

    private IncomingInvoiceOverviewDto? IncomingInvoiceOverviewDto { get; set; }
    LoadingIndicator? _loadingIndicator;
    readonly LoadingIndicatorOptions _options = new(true, false, false, false);

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();
    }

    protected async Task LoadInvoice()
    {
        try
        {
            IncomingInvoiceOverviewDto = await IncomingInvoiceHeaderService.GetInvoiceWithCompleteDataAsync(false, InvoiceNumber);
        }
        catch (Exception e)
        {
            DialogService.Close();
            await DialogService.ShowError(e.InnerException?.Message ?? e.Message);
        }
    }

    private async Task ReloadInvoice(long invoiceNumber)
    {
        InvoiceNumber = invoiceNumber;
        IncomingInvoiceOverviewDto = await IncomingInvoiceHeaderService.GetInvoiceWithCompleteDataAsync(false, InvoiceNumber);
        StateHasChanged();
    }

}