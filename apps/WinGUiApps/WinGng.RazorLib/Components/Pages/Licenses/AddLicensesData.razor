@inherits BasePage

@page "/license/addLicensesData"


@using System.Security.Claims
@using Licencing
@using Microsoft.AspNetCore.Identity
@using ObjectDeAndSerialize
@using Microsoft.Maui.ApplicationModel.DataTransfer;
@using WingCore.application.Contract.IModels
@using WingCore.application.Contract.Services
@using WingCore.domain
@using WinGng.RazorLib.Components.Common.LoadingPage

@inject IConfigurationForAllMandantService ConfigurationForAllMandantService
@inject DialogService DialogService
@inject IApplicationPath ApplicationPath
@inject IJSRuntime JsRuntime
@inject UserManager<ApplicationUser> UserManager

<LoadingIndicator @ref="_loadingIndicator"
                  DoLoadDataCallback="ValidateAndSaveLicense"
                  Option="_options" DialogService="DialogService"/>

<LoadingIndicator @ref="_loadingIndicatorLoadLicense"
                  DoLoadDataCallback="LoadLicense"
                  Option="_optionsToLoadLicense" DialogService="DialogService"/>

<RadzenContent Container="main">
    <ChildContent>
        <RadzenRow>
            @* <RadzenIcon Icon="assessment"> *@
            @* </RadzenIcon> *@
            <RadzenHeading Size="H1" style="display: inline-block" Text=@Localizer["License"]>
            </RadzenHeading>

        </RadzenRow>
        <RadzenRow Gap="1rem" style="font-size: large">
            <RadzenStack Orientation="Orientation.Horizontal" JustifyContent="JustifyContent.Start" Gap="1rem" Class="rz-mt-8 rz-mb-4">
                <RadzenButton ButtonType="ButtonType.Submit" Size="ButtonSize.Large" Icon="save" Text=@Localizer["Save"] Click="@ValidateAndSaveLicenseButton"/>
            </RadzenStack>
        </RadzenRow>
        <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
            <RadzenFieldset AllowCollapse="true" Style="background-color:white">
                <HeaderTemplate>
                    <RadzenStack Orientation="Orientation.Horizontal" Gap="0.25rem">
                        <b>@Localizer["License"]</b>
                    </RadzenStack>
                </HeaderTemplate>
                <ChildContent>
                    <RadzenStack Orientation="Orientation.Vertical" Gap="1rem">
                        <RadzenRow Gap="1rem" style="font-size: large">
                            <RadzenColumn Size="12" SizeSM="12">
                                <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                    <RadzenButton Click="@ReadFromClipboard">@Localizer["AddFromClipboard"]</RadzenButton>
                                </RadzenStack>
                            </RadzenColumn>
                            <RadzenColumn Size="12" SizeSM="12">
                                <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                    @Localizer["LicenseData"] 
                                    <RadzenTextBox @bind-Value="@LicenceDataAsBase64ToCheck"/>
                                </RadzenStack>
                            </RadzenColumn>
                        </RadzenRow>
                    </RadzenStack>
                </ChildContent>
            </RadzenFieldset>
        </RadzenStack>
        <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                <RadzenFieldset AllowCollapse="true" Style="background-color:white" Visible="HasLicenseData">
                    <HeaderTemplate>
                        <RadzenStack Orientation="Orientation.Horizontal" Gap="0.25rem">
                            <b>@Localizer["LicenseInformation"]</b>
                        </RadzenStack>
                    </HeaderTemplate>
                    <ChildContent>
                        <RadzenStack Orientation="Orientation.Vertical" Gap="1rem">
                            <RadzenRow Gap="1rem" style="font-size: large">
                                <RadzenColumn Size="12" SizeSM="2">
                                    <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                        @Localizer["ValidUntil"] 
                                        <RadzenDatePicker ReadOnly="true" @bind-Value="@ExpireAt"/>
                                    </RadzenStack>
                                </RadzenColumn>
                                <RadzenColumn Size="12" SizeSM="12">

                                    <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                       @Localizer["Modules"] 
                                        <RadzenStack Orientation="Orientation.Vertical" Gap="6px">

                                            <RadzenDataGrid Data="@LicenseClaimTypes">
                                                <Columns>
                                                    <RadzenDataGridColumn Title=@Localizer["Licenses"] >
                                                        <Template>
                                                            <strong>@(context)</strong>
                                                        </Template>
                                                    </RadzenDataGridColumn>
                                                </Columns>
                                            </RadzenDataGrid>
                                        </RadzenStack>
                                    </RadzenStack>
                                </RadzenColumn>
                            </RadzenRow>
                        </RadzenStack>
                    </ChildContent>
                </RadzenFieldset>
        </RadzenStack>
    </ChildContent>
</RadzenContent>

@code {
    LoadingIndicator? _loadingIndicator;
    LoadingIndicator? _loadingIndicatorLoadLicense;
    readonly LoadingIndicatorOptions _options = new(false, false, false, false);
    readonly LoadingIndicatorOptions _optionsToLoadLicense = new(true, false, false, false);


    bool HasLicenseData { get; set; }
    DateTime? ExpireAt { get; set; }
    List<string> LicenseClaimTypes { get; set; } = new List<string>();

    string LicenceDataAsBase64ToCheck { get; set; } = string.Empty;

    private async void ValidateAndSaveLicenseButton()
    {
        if (_loadingIndicator is null)
            return;

        await _loadingIndicator.Run();
    }

    public async Task LoadLicense()
    {
        LicenseData licenseData = new LicenseData();
        try
        {
            licenseData = await ConfigurationForAllMandantService.GetConfigurationProgramLicAsync();
        }
        catch (Exception ex)
        {
            await DialogService.ShowError(ex.InnerException?.Message ?? ex.Message);
        }

        await SetLicenseData(licenseData);
    }

    private async Task SetLicenseData(LicenseData licenseData)
    {
        try
        {
            LicenceDataAsBase64ToCheck = licenseData.ToBase64String();
            
            LicenseClaimTypes = licenseData.GetClaimsFromLicence();

            LicenseClaimTypes = LicenseClaimTypes.Select(claimType =>
            {
                LicenseClaims.PossibleClaimsWithDescriptionAndTypes.TryGetValue(claimType,out var claimTypesDesc);
                return claimTypesDesc ?? string.Empty;
            }).ToList();

            ExpireAt = licenseData.GetLicence()?.Expiration;
            HasLicenseData = true;
        }
        catch (Exception ex)
        {
            HasLicenseData = false;
            await DialogService.ShowError(ex.InnerException?.Message ?? ex.Message);
        }
    }

    public async Task ValidateAndSaveLicense()
    {
        try
        {
            if (string.IsNullOrEmpty(LicenceDataAsBase64ToCheck))
                return;

            var licenseData = LicenceDataAsBase64ToCheck.Base64ToString().DeserializeObject<LicenseData>();

            if (licenseData is null) return;

            licenseData.Validate();

            await ConfigurationForAllMandantService.SetConfigurationProgramLicAsync(licenseData);
            /*foreach(var user in UserManager.Users.ToList())
            {
                await UserManager.RemoveClaimsAsync(user,LicenseClaimTypes.Select(claimType => new Claim(claimType, "true"))).ContinueWith(async _ =>
                {
                    List<Claim> claims = [];
                    HashSet<string> usedGroupClaim = [];
                    foreach (var claimType in licenseData.GetClaimsFromLicence())
                    {
                        claims.Add(new Claim(claimType, "true"));
                
                        LicenseClaims.PossibleGroupClaimsWithDescriptionAndTypes.TryGetValue(claimType, out var groupValues);
                        if (groupValues is null) continue;
                        foreach (var groupValue in groupValues.Where(groupValue => usedGroupClaim.Contains(groupValue) is false))
                        {
                            claims.Add(new Claim(groupValue, "true"));
                            usedGroupClaim.Add(groupValue);
                        }
                    }
                    await UserManager.AddClaimsAsync(user, claims);
                });
            }*/
            
            await SetLicenseData(licenseData);
            
            StateHasChanged();
        }
        catch (Exception ex)
        {
            await DialogService.ShowError(
                ex switch
                {
                    FormatException => Localizer["InvalidLicenseDataError"],
                    _ => ex.InnerException?.Message ?? ex.Message
                }
            );
        }
    }

    async Task ReadFromClipboard()
    {
        try
        {
            if (ApplicationPath.IAmBlazorServer())
            {
                LicenceDataAsBase64ToCheck =  await JsRuntime.InvokeAsync<string>("navigator.clipboard.readText");
            }
            else
            {
                if (Clipboard.HasText)
                {
                    LicenceDataAsBase64ToCheck = await Clipboard.GetTextAsync() ?? string.Empty;
                    await ValidateAndSaveLicense();
                }    
            }
            
        }
        catch (Exception ex)
        {
            await DialogService.ShowError(ex.InnerException?.Message ?? ex.Message);
        }
    }
}