@using MediatR
@using wingLager.application.WarehouseManagement.Commands
@using wingLager.domain.WarehouseManagementModels
@using WinGng.RazorLib.Components.Pages.WareHouseManager.WmDialog
@inject DialogService DialogService
@inject ISender Sender

<RadzenDataGrid @ref="_gridVbr" AllowFiltering="true" AllowPaging="true" PageSize="10" AllowSorting="true"
                Data="@VbrList" TItem="WmVbr" EmptyText="Keine Einträge gefunden">
    <Columns>
        <RadzenDataGridColumn TItem="WmVbr" Property="@nameof(WmVbr.Number)" Title="Kommissionsspur Nummer"/>
        <RadzenDataGridColumn TItem="WmVbr" Property="@nameof(WmVbr.Description)" Title="Kommissionsspur Beschreibung"/>
        <RadzenDataGridColumn TItem="WmVbr" Property="@nameof(WmVbr.StellplatzCount)" Title="Stellplatz Anzahl"></RadzenDataGridColumn>
        <RadzenDataGridColumn TItem="WmVbr" Filterable="false" TextAlign="TextAlign.Right">
            <Template Context="vbr">
                <RadzenButton Icon="edit" ButtonStyle="ButtonStyle.Warning" Click="() => EditVbr(vbr)"/>
                <RadzenButton Icon="delete" ButtonStyle="ButtonStyle.Danger" Click="() => DeleteVbr(vbr, true)"/>
            </Template>
        </RadzenDataGridColumn>
    </Columns>
</RadzenDataGrid>
<RadzenRow class="rz-text-align-center rz-p-5">
    <RadzenColumn Offset="3" Size="6">
        <RadzenButton Text="Neue Kommissionsspur" Icon="add_circle" ButtonStyle="ButtonStyle.Primary"
                      Click="() => AddNewVbr()"/>
    </RadzenColumn>
</RadzenRow>

@code {
    [Parameter] public required List<WmVbr> VbrList { get; set; }
    private RadzenDataGrid<WmVbr> _gridVbr = null!;


    protected async Task AddNewVbr()
    {
        var vbrNumberList = VbrList.Select(v => v.Number).ToList();
        WmVbr? vbr = await DialogService.OpenAsync<VbrCreationDialog>("Kommissionsspur hinzufügen", new Dictionary<string, object> { { "VbrNummerList", vbrNumberList } });
        if (vbr is not null)
        {
            var newVbr = await Sender.Send(new CreateWmVbrCommand(vbr.Description, vbr.Number));
            VbrList.Add(newVbr);
            await _gridVbr.Reload();
        }
    }

    protected async Task EditVbr(WmVbr vbr)
    {
        var vbrNumberList = VbrList.Select(v => v.Number).Where(n => n != vbr.Number).ToList();
        WmVbr newVbr = await DialogService.OpenAsync<VbrCreationDialog>("Kommissionsspur bearbeiten", new Dictionary<string, object> { { "Vbr", vbr }, { "VbrNummerList", vbrNumberList }, { "IsEdit", true } });
        if (newVbr is not null)
        {
            await Sender.Send(new UpdateWmVbrCommand(newVbr));
            var index = VbrList.IndexOf(vbr);
            if (index < 0)
                return;
            VbrList[index] = newVbr;
            await _gridVbr.Reload();
        }
    }

    protected async Task DeleteVbr(WmVbr vbr, bool withDialog)
    {
        bool? confirm = true;
        if (withDialog)
        {
            confirm = await DialogService.Confirm("Sind Sie sicher, dass Sie diese Kommissionsspur und alles darin löschen möchten?", "Kommissionsspur löschen?", new ConfirmOptions() { OkButtonText = "Ja", CancelButtonText = "Nein" });
        }

        if (confirm is false)
            return;
        await Sender.Send(new DeleteWmVbrCommand(vbr));
        VbrList.Remove(vbr);
        await _gridVbr.Reload();
    }

}