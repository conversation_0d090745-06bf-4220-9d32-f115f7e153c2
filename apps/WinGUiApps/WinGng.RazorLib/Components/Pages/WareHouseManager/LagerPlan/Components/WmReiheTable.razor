@using MediatR
@using wingLager.application.WarehouseManagement.Commands
@using wingLager.application.WarehouseManagement.Queries
@using wingLager.domain.WarehouseManagementModels
@using WinGng.RazorLib.Components.Pages.WareHouseManager.WmDialog
@inject ISender Sender
@inject DialogService DialogService

<RadzenDataGrid @ref="_grid" AllowFiltering="true" AllowPaging="true" PageSize="10" AllowSorting="true" TItem="WmReihe"
                Data="@Halle.Reihen" RowExpand="ReiheRowExpand" EmptyText="Keine Einträge gefunden">
    <Columns>
        <RadzenDataGridColumn TItem="WmReihe" Property="@nameof(WmReihe.Number)" Title="Reihen Nummer"/>
        <RadzenDataGridColumn TItem="WmReihe" Property="@nameof(WmReihe.Description)" Title="Reihen Beschreibung"/>
        <RadzenDataGridColumn TItem="WmReihe" Filterable="false" TextAlign="TextAlign.Right">
            <Template Context="regal">
                <RadzenButton Icon="edit" ButtonStyle="ButtonStyle.Warning" Click="() => EditReihe(regal, Halle)"/>
                <RadzenButton Icon="delete" ButtonStyle="ButtonStyle.Danger"
                              Click="() => DeleteReihe(regal, Halle, true)"/>
            </Template>
        </RadzenDataGridColumn>
    </Columns>
    <Template Context="wmReihe">
        <WmFeldTable Reihe="wmReihe"/>
    </Template>
</RadzenDataGrid>
<RadzenRow class="rz-text-align-center rz-p-5">
    <RadzenColumn Offset="3" Size="6">
        <RadzenButton Text="Neue Reihe" Icon="add_circle" ButtonStyle="ButtonStyle.Primary"
                      Click="() => AddNewReiheDialog(Halle)"/>
    </RadzenColumn>
</RadzenRow>

@code {
    [Parameter] public required WmHall Halle { get; set; }
    private RadzenDataGrid<WmReihe> _grid = null!;


    protected async Task AddNewReiheDialog(WmHall hall)
    {
        var reihenNummerList = hall.Reihen.Select(r => r.Number).ToList();
        WmReihe? reiheTemp = await DialogService.OpenAsync<ReiheCreationDialog>("Reihe hinzufügen", new Dictionary<string, object> { { "Hall", Halle }, { "ReiheNummerList", reihenNummerList } });
        if (reiheTemp is not null)
        {
            var newReihe = await Sender.Send(new CreateWmReiheCommand(reiheTemp.Description, reiheTemp.Number, hall.Id));
            hall.Reihen.Add(newReihe);
            await _grid.Reload();
        }
    }

    protected async Task EditReihe(WmReihe reihe, WmHall halle)
    {
        var regalNumberList = halle.Reihen.Select(r => r.Number).Where(n => n != reihe.Number).ToList();
        WmReihe newReihe = await DialogService.OpenAsync<ReiheCreationDialog>("Regal bearbeiten", new Dictionary<string, object> { { "Hall", halle }, { "Reihe", reihe }, { "ReiheNummerList", regalNumberList }, { "IsEdit", true } });
        if (newReihe is not null)
        {
            await Sender.Send(new UpdateWmReiheCommand(newReihe));
            var index = halle.Reihen.IndexOf(reihe);
            if (index < 0)
                return;
            halle.Reihen[index] = newReihe;
            await _grid.Reload();
        }
    }

    protected async Task DeleteReihe(WmReihe reihe, WmHall halle, bool withDialog)
    {
        bool? confirm = true;
        if (withDialog)
        {
            confirm = await DialogService.Confirm("Sind Sie sicher, dass Sie dieses Reihe und alles darin löschen möchten?", "Regal löschen?", new ConfirmOptions() { OkButtonText = "Ja", CancelButtonText = "Nein" });
        }

        if (confirm is false)
            return;
        await Sender.Send(new DeleteWmReiheCommand(reihe.Id));
        halle.Reihen.Remove(reihe);
        await _grid.Reload();
    }

    protected async Task ReiheRowExpand(WmReihe reihe)
    {
        try
        {
            if (reihe.FeldList.Count == 0) return;
            foreach (var fach in reihe.FeldList)
            {
                var zellen = await Sender.Send(new GetWmEbeneByFeldIdQuery(fach.Id));
                fach.EbenenList = [..zellen];
            }
        }
        catch (Exception e)
        {
            Console.WriteLine(e);
            throw;
        }
    }

}