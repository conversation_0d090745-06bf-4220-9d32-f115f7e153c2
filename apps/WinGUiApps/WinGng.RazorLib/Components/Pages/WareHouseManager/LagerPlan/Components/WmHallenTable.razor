@using MediatR
@using wingLager.application.WarehouseManagement.Commands
@using wingLager.domain.WarehouseManagementModels
@using WinGng.RazorLib.Components.Pages.WareHouseManager.WmDialog
@inject ISender Sender
@inject DialogService DialogService

<RadzenDataGrid @ref="_gridHall" AllowFiltering="true" AllowPaging="true" PageSize="10" AllowSorting="true"
                Data="@HallList" TItem="WmHall" RowExpand="HallRowExpand" EmptyText="Keine Einträge gefunden">
    <Columns>
        <RadzenDataGridColumn TItem="WmHall" Property="@nameof(WmHall.Number)" Title="Hallen Nummer"/>
        <RadzenDataGridColumn TItem="WmHall" Property="@nameof(WmHall.Description)" Title="Hallen Beschreibung"/>
        <RadzenDataGridColumn TItem="WmHall" Property="@nameof(WmHall.Location)" Title="Hallen Standort"/>
        <RadzenDataGridColumn TItem="WmHall" Filterable="false" TextAlign="TextAlign.Right" Width="auto">
            <Template Context="halle">
                <RadzenButton Icon="edit" ButtonStyle="ButtonStyle.Warning" Click="() => EditHalle(halle)"/>
                <RadzenButton Icon="delete" ButtonStyle="ButtonStyle.Danger" Click="() => DeleteHalle(halle, true)"/>
            </Template>
        </RadzenDataGridColumn>
    </Columns>
    <Template Context="wmHall">
        <WmReiheTable Halle="wmHall"/>
    </Template>
</RadzenDataGrid>
<RadzenRow class="rz-text-align-center rz-p-5">
    <RadzenColumn Offset="3" Size="6">
        <RadzenButton Text="Neue Halle" Icon="add_circle" ButtonStyle="ButtonStyle.Primary"
                      Click="() => AddNewHallDialog()"/>
    </RadzenColumn>
</RadzenRow>

@code {
    [Parameter] public required List<WmHall> HallList { get; set; }
    private RadzenDataGrid<WmHall> _gridHall = null!;

    protected async Task AddNewHallDialog()
    {
        var hallNumberList = HallList.Select(h => h.Number).ToList();
        WmHall? hall = await DialogService.OpenAsync<HallCreationDialog>("Halle hinzufügen", new Dictionary<string, object> { { "HallenNummerList", hallNumberList } });
        if (hall is not null)
        {
            var newHall = await Sender.Send(new CreateWmHallCommand(hall.Description, hall.Number, hall.Location));
            HallList.Add(newHall);
            await _gridHall.Reload();
        }
    }

    protected async Task EditHalle(WmHall halle)
    {
        var hallNumberList = HallList.Select(h => h.Number).Where(h => h != halle.Number).ToList();
        WmHall newHall = await DialogService.OpenAsync<HallCreationDialog>("Halle bearbeiten", new Dictionary<string, object> { { "Halle", halle }, { "HallenNummerList", hallNumberList }, { "IsEdit", true } });
        if (newHall is not null)
        {
            await Sender.Send(new UpdateWmHallCommand(newHall));
            var index = HallList.IndexOf(halle);
            if (index < 0)
                return;
            HallList[index] = newHall;
            await _gridHall.Reload();
        }
    }

    protected async Task DeleteHalle(WmHall halle, bool withDialog)
    {
        bool? confirm = true;
        if (withDialog)
        {
            confirm = await DialogService.Confirm("Sind Sie sicher, dass Sie diese Halle und alles darin löschen möchten?", "Halle löschen?", new ConfirmOptions() { OkButtonText = "Ja", CancelButtonText = "Nein" });
        }

        if (confirm is false)
            return;
        await Sender.Send(new DeleteWmHallCommand(halle.Id));
        HallList.Remove(halle);
        await _gridHall.Reload();
    }

    protected async Task HallRowExpand(WmHall hallen)
    {
        try
        {
            if (hallen.Reihen.Count == 0) return;
            foreach (var regal in hallen.Reihen)
            {
                var faecher = await Sender.Send(new GetWmFeldByReiheIdQuery(regal.Id));
                regal.FeldList = [..faecher];
            }
        }
        catch (Exception e)
        {
            Console.WriteLine(e);
            throw;
        }
    }

}