@using wingLager.domain.WarehouseManagementModels
@inject DialogService DialogService

<RadzenTemplateForm TItem="WmReihe" Data="@_reiheTemp" Submit="@OnSubmit">
    <RadzenStack Gap="2rem" class="rz-p-4 rz-p-md-12">
        <RadzenRow RowGap="0.25rem">
            <RadzenColumn Size="12">
                <RadzenLabel Text="Regal Nummer" Style="width: 150px;" />
                <RadzenTextBox Name="number" @bind-Value="@_reiheTemp.Number" Style="width: 100%;" />
                <RadzenRequiredValidator Component="number" Text="Reihe Nummer darf nicht leer sein" />
                <RadzenCustomValidator Component="number" Text="Reihe Nummer existiert bereits" Validator="() => ValidateNumber(_reiheTemp.Number)" />
                <RadzenRegexValidator Component="number" Text="Reihe Nummer darf keine Leerzeichen enthalten" Pattern="^\S*$"/>
            </RadzenColumn>
            <RadzenColumn Size="12">
                <RadzenLabel Text="Beschreibung" Style="width: 150px;" />
                <RadzenTextBox Name="description" @bind-Value="@_reiheTemp.Description" Style="width: 100%;" />
                <RadzenRequiredValidator Component="description" Text="Beschreibung darf nicht leer sein" />
            </RadzenColumn>
        </RadzenRow>
        <RadzenColumn Size="12">
            <RadzenButton ButtonType="ButtonType.Submit" Text="@(IsEdit ? "Speichern" : "Erstellen")" />
            <RadzenButton ButtonStyle="ButtonStyle.Secondary" Text="Abbrechen" Click="@(() => DialogService.Close())" />
        </RadzenColumn>
    </RadzenStack>
</RadzenTemplateForm>

@code {
    [Parameter] public required WmHall Hall { get; set; }
    [Parameter] public WmReihe? Reihe { get; set; } = null;
    [Parameter] public required List<string> ReiheNummerList { get; set; }
    [Parameter] public bool IsEdit { get; set; } = false;
    
    private WmReihe _reiheTemp = new();
    
    protected override void OnInitialized()
    {
        if (Reihe is not null)
        {
            _reiheTemp = Reihe with { };
        }
    }
    
    bool ValidateNumber(string number)
    {
        return !ReiheNummerList.Contains(number);
    }
    
    private void OnSubmit(WmReihe reihe)
    {
        reihe.WmHallId = Hall.Id;
        DialogService.Close(reihe);
    }
}