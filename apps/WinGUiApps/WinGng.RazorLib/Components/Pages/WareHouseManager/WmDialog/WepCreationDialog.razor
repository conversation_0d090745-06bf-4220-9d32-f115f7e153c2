@using wingLager.domain.WarehouseManagementModels
@inject DialogService DialogService

<RadzenTemplateForm TItem="WmWep" Data=@_wepTemp Submit=@OnSubmit>
    <RadzenStack Gap="2rem" class="rz-p-4 rz-p-md-12">
        <RadzenRow  RowGap="0.25rem">
            <RadzenColumn  Size="12">
                <RadzenLabel Text="Produktionsspur Nummer" Style="width: 100%;" />
                <RadzenTextBox Name="number" @bind-Value="@_wepTemp.Number" Style="width: 100%;" />
                <RadzenRequiredValidator Component="number" Text="Produktionsspur-Nummer darf nicht leer sein"/>
                <RadzenCustomValidator Component="number" Text="Produktionsspur-Nummer existiert bereits" Validator="() => ValidateNumber(_wepTemp.Number)" />
                <RadzenRegexValidator Component="number" Text="Produktionsspur-Nummer darf keine Leerzeichen enthalten" Pattern="^\S*$"/>
            </RadzenColumn>
            <RadzenColumn Size="12">
                <RadzenLabel Text="Beschreibung" Style="width: 150px;" />
                <RadzenTextBox Name="description" @bind-Value="@_wepTemp.Description" Style="width: 100%;" />
                <RadzenRequiredValidator Component="description" Text="Beschreibung darf nicht leer sein" />
            </RadzenColumn>
        </RadzenRow>
        <RadzenColumn Size="12">
            <RadzenButton ButtonType="ButtonType.Submit" Text="@(IsEdit ? "Speichern" : "Erstellen")"></RadzenButton>
            <RadzenButton ButtonStyle="ButtonStyle.Secondary" Text="Abbrechen" Click="@(() => DialogService.Close())" />
        </RadzenColumn>
    </RadzenStack>
</RadzenTemplateForm>

@code {
    [Parameter] public WmWep? Wep { get; set; } = null;
    [Parameter] public required List<string> WepNummerList { get; set; }
    [Parameter] public bool IsEdit { get; set; } = false;

    private WmWep _wepTemp = new();

    protected override void OnInitialized()
    {
        if (Wep is not null)
        {
            _wepTemp = Wep with { };
        }
    }

    bool ValidateNumber(string number)
    {
        return !WepNummerList.Contains(number);
    }

    void OnSubmit(WmWep wep)
    {
        DialogService.Close(wep);
    }
}