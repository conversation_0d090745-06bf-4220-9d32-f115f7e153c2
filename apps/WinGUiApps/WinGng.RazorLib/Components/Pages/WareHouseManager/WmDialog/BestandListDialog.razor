
@using WingCore.application.Contract.IModels
@using wingLager.domain.WarehouseManagementModels
@inject IApplicationPath ApplicationPath
@inject IJSRuntime Js
<RadzenStack>
    <RadzenRow>
        <RadzenButton Text="Export to Excel" Click="ExportToExcel" />
        <RadzenButton Text="Export CSV" Click="ExportToCsv" />
    </RadzenRow>
    <RadzenDataGrid @ref="_dataGrid" AllowColumnResize="true" AllowSorting="true" AllowFiltering="true" FilterMode="FilterMode.CheckBoxList" Data="@WmBestandList" TItem="WmBestand" ColumnWidth="200px">
        <Columns>
            <RadzenDataGridColumn TItem="WmBestand" Property="@nameof(WmBestand.NveNumber)" Title="NVE Nummer"/>
            <RadzenDataGridColumn TItem="WmBestand" Property="@nameof(WmBestand.ChargeNr)" Title="Charge Nr"/>
            <RadzenDataGridColumn TItem="WmBestand" Property="@nameof(WmBestand.OrderNumber)" Title="Auftragsnummer"/>
            <RadzenDataGridColumn TItem="WmBestand" Property="@nameof(WmBestand.ProductionDate)" Title="Produktionsdatum"  FormatString="{0:dd.MM.yyyy}"/>
            <RadzenDataGridColumn TItem="WmBestand" Property="@nameof(WmBestand.ProductionTime)" Title="Produktionsuhrzeit"  >
                <Template Context="data">
                    @data.ProductionTime.ToString(@"hh\:mm\:ss")
                </Template>
            </RadzenDataGridColumn>
            <RadzenDataGridColumn TItem="WmBestand" Property="@nameof(WmBestand.ExpiryDate)" Title="MHD">
                <Template Context="data">
                    @(data.ExpiryDate == DateTime.MinValue ? "" : data.ExpiryDate.ToString("dd.MM.yyyy"))
                </Template>
            </RadzenDataGridColumn>
            <RadzenDataGridColumn TItem="WmBestand" Property="@nameof(WmBestand.Weight)" Title="Gewicht"/>
            <RadzenDataGridColumn TItem="WmBestand" Property="@nameof(WmBestand.LoadedAtDate)"Title="Beladedatum">
                <Template Context="data">
                    @(data.LoadedAtDate == DateTime.MinValue.Date ? "" : data.LoadedAtDate.ToString("dd.MM.yyyy"))
                </Template>
            </RadzenDataGridColumn>
            <RadzenDataGridColumn TItem="WmBestand" Property="@nameof(WmBestand.LoadedAtDateTime)" Title="Beladeuhrzeit">
                <Template Context="data">
                    @(data.LoadedAtTime == TimeSpan.Zero ? "" : data.LoadedAtTime.ToString(@"hh\:mm\:ss"))
                </Template>
            </RadzenDataGridColumn>
            <RadzenDataGridColumn TItem="WmBestand" Property="@nameof(WmBestand.Category)" Title="Kategorie"/>
            <RadzenDataGridColumn TItem="WmBestand" Property="@nameof(WmBestand.StellplatzBeschreibung)" Title="Beschreibung"/>
            <RadzenDataGridColumn TItem="WmBestand" Property="@nameof(WmBestand.Status)" Title="Status" Sortable="false" Filterable="false"/>
            <RadzenDataGridColumn TItem="WmBestand" Property="@nameof(WmBestand.CustomerNumber)" Title="Kundennummer"/>
            <RadzenDataGridColumn TItem="WmBestand" Property="@(nameof(WmBestand.Article) + "." + nameof(WmBestand.Article.ArticleNumber))" Title="Artikelnummer"/>
            <RadzenDataGridColumn TItem="WmBestand" Property="@(nameof(WmBestand.Article) + "." + nameof(WmBestand.Article.ArticleName1))" Title="Artikelname 1"/>
            <RadzenDataGridColumn TItem="WmBestand" Property="@(nameof(WmBestand.Article) + "." + nameof(WmBestand.Article.ArticleName2))" Title="Artikelname 2"/>
            <RadzenDataGridColumn TItem="WmBestand" Property="@(nameof(WmBestand.Article) + "." + nameof(WmBestand.Article.ArticleName3))" Title="Artikelname 3"/>
            <RadzenDataGridColumn TItem="WmBestand" Property="@(nameof(WmBestand.Article) + "." + nameof(WmBestand.Article.ArticleName4))" Title="Artikelname 4"/>
            <RadzenDataGridColumn TItem="WmBestand" Property="@(nameof(WmBestand.Article) + "." + nameof(WmBestand.Article.ArticleEAN))" Title="EAN"/>
            <RadzenDataGridColumn TItem="WmBestand" Property="@(nameof(WmBestand.Article) + "." + nameof(WmBestand.Article.ArticleUnit))" Title="Einheit"/>
            <RadzenDataGridColumn TItem="WmBestand" Property="@(nameof(WmBestand.Article) + "." + nameof(WmBestand.Article.ArticleNumberForSupplier))" Title="Lieferanten-Artikelnummer"/>
        </Columns>
    </RadzenDataGrid>
</RadzenStack>
@code {
    [Parameter] public required ICollection<WmBestand> WmBestandList { get; set; }
    private RadzenDataGrid<WmBestand> _dataGrid = null!;

    private async Task ExportToExcel()
    {
        var export = ExcelExportService.ExportToExcel(_dataGrid, WmBestandList, $"Bestand_{DateTime.Now:yyyyMMdd_HHmmss}");
       
        if (ApplicationPath.IAmBlazorServer())
        {
            export.FileStream.Position = 0;
            using var streamRef = new DotNetStreamReference(stream: export.FileStream);

            await Js.InvokeVoidAsync("downloadFileFromStream", export.FileDownloadName, streamRef);                    
        }
    }

    private async Task ExportToCsv()
    {
        var export = ExcelExportService.ExportToCsv(_dataGrid, WmBestandList, $"Bestand_{DateTime.Now:yyyyMMdd_HHmmss}");
        if (ApplicationPath.IAmBlazorServer())
        {
            export.FileStream.Position = 0;
            using var streamRef = new DotNetStreamReference(stream: export.FileStream);

            await Js.InvokeVoidAsync("downloadFileFromStream", export.FileDownloadName, streamRef);                    
        }
    }
}