@using wingLager.domain.WarehouseManagementModels
@inject DialogService DialogService

<RadzenTemplateForm TItem="WmVbr" Data=@_vbrTemp Submit=@OnSubmit>
    <RadzenStack Gap="2rem" class="rz-p-4 rz-p-md-12">
        <RadzenStack Orientation="Orientation.Vertical" Gap="20px" >
            <RadzenRow Size="12">
                <RadzenStack Orientation="Orientation.Vertical" Gap="5px" Style="width: 100%;">
                <RadzenLabel Text="Kommissionsspur Nummer"/>
                <RadzenTextBox Name="number" @bind-Value="@_vbrTemp.Number" Style="width: 100%;"/>
                <RadzenRequiredValidator Component="number" Text="Kommissionsspur-Nummer darf nicht leer sein"/>
                <RadzenCustomValidator Component="number" Text="Kommissionsspur-Nummer existiert bereits"
                                       Validator="() => ValidateNumber(_vbrTemp.Number)"/>
                <RadzenRegexValidator Component="number" Text="Kommissionsspur-Nummer muss mit 'VBR' beginnen und darf nur aus Zahlen bestehen"
                                      Pattern="^VBR\d+$"/>
                </RadzenStack>
            </RadzenRow>
            <RadzenRow Size="12">
                <RadzenStack Orientation="Orientation.Vertical" Gap="5px" Style="width: 100%;">

                <RadzenLabel Text="Beschreibung" Style="width: 150px;"/>
                <RadzenTextBox Name="description" @bind-Value="@_vbrTemp.Description" Style="width: 100%;"/>
                <RadzenRequiredValidator Component="description" Text="Beschreibung darf nicht leer sein"/>
                </RadzenStack>
            </RadzenRow>
            <RadzenRow>
                <RadzenStack Orientation="Orientation.Vertical" Gap="5px" Style="width: 100%;">
                <RadzenLabel Text="Anzahl der Stellplätze"/>
                <RadzenNumeric Min="1" Name="count" @bind-Value="@_vbrTemp.StellplatzCount" Style="width: 100%;"/>
                <RadzenCustomValidator Component="count" Text="Bitte nur ganze Zahlen größer als 0 eingeben"
                                       Validator="() => _vbrTemp.StellplatzCount > 0"/>
                </RadzenStack>
            </RadzenRow>
        </RadzenStack>
        
        <RadzenStack Orientation="Orientation.Horizontal" Style="width: 100%" JustifyContent="JustifyContent.End">
            <RadzenButton ButtonType="ButtonType.Submit" Text="@(IsEdit ? "Speichern" : "Erstellen")"></RadzenButton>
            <RadzenButton ButtonStyle="ButtonStyle.Secondary" Text="Abbrechen" Click="@(() => DialogService.Close())"/>
        </RadzenStack>
    </RadzenStack>
</RadzenTemplateForm>

@code {
    [Parameter] public WmVbr? Vbr { get; set; } = null;
    [Parameter] public required List<string> VbrNummerList { get; set; }
    [Parameter] public bool IsEdit { get; set; } = false;

    private WmVbr _vbrTemp = new();

    protected override void OnInitialized()
    {
        if (Vbr is not null)
        {
            _vbrTemp = Vbr with { };
        }
    }

    bool ValidateNumber(string number)
    {
        return !VbrNummerList.Contains(number);
    }

    void OnSubmit(WmVbr vbr)
    {
        DialogService.Close(vbr);
    }

}