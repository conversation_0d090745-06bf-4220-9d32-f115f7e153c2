@using wingLager.domain.WarehouseManagementModels
@inject DialogService DialogService

<RadzenTemplateForm TItem="WmHall" Data=@_halleTemp Submit=@OnSubmit>
    <RadzenStack Gap="2rem" class="rz-p-4 rz-p-md-12">
        <RadzenRow  RowGap="0.25rem">
            <RadzenColumn  Size="12">
                <RadzenLabel Text="Hallen Nummer" Style="width: 150px;" />
                <RadzenTextBox Name="number" @bind-Value="@_halleTemp.Number" Style="width: 100%;" />
                <RadzenRequiredValidator Component="number" Text="Hallen-Nummer darf nicht leer sein"/>
                <RadzenCustomValidator Component="number" Text="Hallen-Nummer existiert bereits" Validator="() => ValidateNumber(_halleTemp.Number)" />
                <RadzenRegexValidator Component="number" Text="Hallen-Nummer darf keine Leerzeichen enthalten" Pattern="^\S*$"/>
            </RadzenColumn>
            <RadzenColumn Size="12">
                <RadzenLabel Text="Beschreibung" Style="width: 150px;" />
                <RadzenTextBox Name="description" @bind-Value="@_halleTemp.Description" Style="width: 100%;" />
                <RadzenRequiredValidator Component="description" Text="Beschreibung darf nicht leer sein" />
            </RadzenColumn>
            <RadzenColumn Size="12">
                <RadzenLabel Text="Standort" Style="width: 150px;" />
                <RadzenTextBox Name="location" @bind-Value="@_halleTemp.Location" Style="width: 100%;" />
                <RadzenRequiredValidator Component="location" Text="Standort darf nicht leer sein" />
            </RadzenColumn>
        </RadzenRow>
        <RadzenColumn Size="12">
            <RadzenButton ButtonType="ButtonType.Submit" Text="@(IsEdit ? "Speichern" : "Erstellen")"></RadzenButton>
            <RadzenButton ButtonStyle="ButtonStyle.Secondary" Text="Abbrechen" Click="@(() => DialogService.Close())" />
        </RadzenColumn>
    </RadzenStack>
</RadzenTemplateForm>

@code {
    [Parameter] public WmHall? Halle { get; set; } = null;
    [Parameter] public required List<string> HallenNummerList { get; set; }
    [Parameter] public bool IsEdit { get; set; } = false;
    
    private WmHall _halleTemp = new();

    protected override void OnInitialized()
    {
        if (Halle is not null)
        {
            _halleTemp = Halle with { };
        }
    }

    bool ValidateNumber(string number)
    {
        return !HallenNummerList.Contains(number);
    }
    
    void OnSubmit(WmHall halle)
    {
        DialogService.Close(halle);
    }
}