
@using MediatR
@using wingLager.domain.WarehouseManagementModels
@inject ISender Sender

<RadzenDropDownDataGrid
    Name="@Name"
    Data="@Data"
    TValue="WmOrder"
    AllowFiltering="true"
    AllowClear="true"
    Placeholder="Auftrag auswählen..."
    Style="width: 100%; max-width: 300px;"
    FilterCaseSensitivity="FilterCaseSensitivity.CaseInsensitive"
    PageSize="10"
    ColumnWidth="150px"
    TextProperty="@nameof(WmOrder.OrderNumber)"
    Change="@OnChange"
    @bind-Value="@Value"
    >

    <Columns>
        <RadzenDropDownDataGridColumn
            Property="@nameof(WmOrder.OrderNumber)"
            Title=@Localizer["PurchaseOrderNumber"]
            Filterable="true"/>
        <RadzenDropDownDataGridColumn
            Property="@nameof(WmOrder.WarenEmpfaenger)"
            Title=@Localizer["GoodsRecipiento"]
            Filterable="true"/>
        <RadzenDropDownDataGridColumn
            Title=@Localizer["CreationDate"]
            FormatString="{0:dd.MM.yyyy HH:mm}">
            <Template Context="commissioning">
                @(commissioning.ProcessOrderHeader?.CreatedAt != null ? commissioning.ProcessOrderHeader.CreatedAt.ToString("dd.MM.yyyy HH:mm") : string.Empty)
            </Template>
        </RadzenDropDownDataGridColumn>
        <RadzenDropDownDataGridColumn
            Title=@Localizer["Positions"]>
            <Template Context="commissioning">
                <RadzenBadge BadgeStyle="BadgeStyle.Info"
                             Text="@commissioning.ProcessOrderPositions.Count.ToString()"/>
            </Template>
        </RadzenDropDownDataGridColumn>
        <RadzenDropDownDataGridColumn
            Title=@Localizer["PalletCount"]>
            <Template Context="commissioning">
                <RadzenBadge BadgeStyle="BadgeStyle.Secondary"
                             Text="@commissioning.PaletLager.Count.ToString()"/>
            </Template>
        </RadzenDropDownDataGridColumn>
        <RadzenDropDownDataGridColumn Title=@Localizer["OrderNumber"]>
            <Template Context="commissioning">
                @((commissioning.Auftragskopf?.Bestellnr ?? string.Empty))
            </Template>
        </RadzenDropDownDataGridColumn>
        <RadzenDropDownDataGridColumn Title=@Localizer["Status"] Width="100px">
            <Template Context="commissioning">
                @(commissioning.Auftragskopf?.AStatus?.ToString() ?? string.Empty)
            </Template>
        </RadzenDropDownDataGridColumn>
        <RadzenDropDownDataGridColumn Title=@Localizer["Weight"] Width="100px">
            <Template Context="data">
                @((data.Auftragskopf?.GesGew?.ToString() ?? string.Empty))
            </Template>
        </RadzenDropDownDataGridColumn>
    </Columns>
</RadzenDropDownDataGrid>
<RadzenCustomValidator Component="@Name" Text="Bitte Auftrag auswählen"
                       Validator="() => Value is not null"/>

@code {
    [Parameter] public string Name { get; set; } = "orderSelectionDropDown";
    [Parameter] public IEnumerable<WmOrder> Data { get; set; } = new List<WmOrder>();
    [Parameter] public WmOrder? Value { get; set; }
    [Parameter] public EventCallback<WmOrder?> ValueChanged { get; set; }


    private async Task OnChange(object? value)
    {
        await ValueChanged.InvokeAsync(value as WmOrder);
    }
}