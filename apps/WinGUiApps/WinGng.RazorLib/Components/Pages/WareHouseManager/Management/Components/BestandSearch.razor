@page "/BestandSearch"
@using MediatR
@using WingCore.application.Contract.IModels
@using WingCore.data.Repositories
@using wingLager.application.WarehouseManagement.Queries
@using wingLager.domain.WarehouseManagementModels
@using wingLager.domain.WarehouseManagementModels.SearchParams
@using wingLager.domain.WarehouseManagementModels.Status
@using WinGng.RazorLib.Components.Pages.WareHouseManager.WmDialog

@inject ISender Sender
@inject DialogService DialogService
@inject IKundenRepository KundenRepository
@inject IAuftragskopfRepository AuftragskopfRepository

<RadzenFieldset AllowCollapse="true" Style="width: 100%">
    <HeaderTemplate>
        <RadzenStack Orientation="Orientation.Horizontal" Gap="0.25rem">
            <b>Bestand</b>
        </RadzenStack>
    </HeaderTemplate>
    <ChildContent>
        <RadzenTemplateForm TItem="WmBestandSearchParams" Data="@_searchParams"
                            Submit="@OpenSearchResultDialog">
            <RadzenStack Gap="2rem" class="rz-p-4 rz-p-md-12">
                <RadzenRow>
                    <RadzenColumn Size="12" Style="text-align:right;">
                        <RadzenButton ButtonType="ButtonType.Submit" Text="Suchen" Icon="search"
                                      Class="rz-button-primary"/>
                    </RadzenColumn>
                </RadzenRow>
                <RadzenRow RowGap="1rem">
                    <RadzenColumn Size="6">
                        <RadzenLabel Text="Charge Nummer"/>
                        <RadzenTextBox Name="chargeN" @bind-Value="@_searchParams.ChargeNr" Style="width: 100%;"
                                       Placeholder="Charge Nummer eingeben"/>
                    </RadzenColumn>
                    <RadzenColumn Size="6">
                        <RadzenFormField Text="Auftragsnummer" Variant="Variant.Outlined" Style="width: 100%;">
                            <RadzenDropDownDataGrid Data="@AuftragNumberList"
                                                    @bind-Value="@_searchParams.OrderNumber" AllowClear="true"
                                                    Style="width: 100%;"/>
                        </RadzenFormField>
                    </RadzenColumn>
                </RadzenRow>
                <RadzenRow RowGap="1rem">
                    <RadzenColumn Size="6">
                        <RadzenFormField Text="Kundennummer" Variant="Variant.Outlined" Style="width: 100%;">
                            <RadzenDropDownDataGrid Data="@KundenNummerList"
                                                    @bind-Value="@_searchParams.KundenNrWe"
                                                    TextProperty="@nameof(WmCustomerMini.Name)"
                                                    ValueProperty="@nameof(WmCustomerMini.Number)"
                                                    AllowClear="true" AllowFilteringByAllStringColumns="true"
                                                    Style="width: 100%;">
                                <Columns>
                                    <RadzenDropDownDataGridColumn Property="@nameof(WmCustomerMini.Number)"
                                                                  Title="Kunden Nummer"/>
                                    <RadzenDropDownDataGridColumn Property="@nameof(WmCustomerMini.Name)"
                                                                  Title="Kunden Name"/>
                                </Columns>
                            </RadzenDropDownDataGrid>
                        </RadzenFormField>
                    </RadzenColumn>
                    <RadzenColumn Size="6">
                        <RadzenLabel Text="Prozessauftragsnummer"/>
                        <RadzenNumeric ShowUpDown="false" Name="processOrderPosition"
                                       @bind-Value="@_searchParams.ProcessOrderPosition" Style="width: 100%;"
                                       Placeholder="Prozessauftragsnummer eingeben"/>
                    </RadzenColumn>
                </RadzenRow>
                <RadzenRow RowGap="1rem">
                    <RadzenColumn Size="6">
                        <RadzenLabel Text="Produktionsdatum (von)"/>
                        <RadzenDatePicker Name="productionDateFrom"
                                          @bind-Value="@_searchParams.ProductionDateFrom" Style="width: 100%;"/>
                    </RadzenColumn>
                    <RadzenColumn Size="6">
                        <RadzenLabel Text="Produktionsdatum (bis)"/>
                        <RadzenDatePicker Name="productionDateTo" @bind-Value="@_searchParams.ProductionDateTo"
                                          Style="width: 100%;"/>
                    </RadzenColumn>
                </RadzenRow>
                <RadzenRow RowGap="1rem">
                    <RadzenColumn Size="6">
                        <RadzenLabel Text="Kategorie"/>
                        <RadzenDropDown Name="Category" @bind-Value="@_searchParams.Category"
                                        Data="@_warenTypen" TextProperty="@nameof(WmStellplatzLoadType.Value)"
                                        ValueProperty="@nameof(WmStellplatzLoadType.Value)"
                                        Placeholder="Warentyp auswählen"
                                        AllowClear="true"
                                        Style="width: 100%;"/>
                    </RadzenColumn>
                    <RadzenColumn Size="6">
                        <RadzenLabel Text=""/>
                        <RadzenCheckBox Name="isInWarehouse" @bind-Value="@_searchParams.IsInWarehouse"/>
                        <RadzenLabel Text="Im Lager" Style="margin-left: 0.5rem;"/>
                    </RadzenColumn>
                </RadzenRow>
            </RadzenStack>
        </RadzenTemplateForm>
    </ChildContent>
</RadzenFieldset>

@code {

    private List<WmCustomerMini> KundenNummerList { get; set; } = [];
    private List<long?> AuftragNumberList { get; set; } = [];
    private WmBestandSearchParams _searchParams = new();
    private readonly List<WmStellplatzLoadType> _warenTypen = WmStellplatzLoadType.GetAll();
    private ICollection<WmBestand> _wmBestandList = [];

    protected override void OnInitialized()
    {
        AuftragNumberList = AuftragskopfRepository.GetAllAuftragNummer();
        var result = KundenRepository.GetAllCostumers(false);
        KundenNummerList = result.ToWmCustomerMini().ToList();
    }


    private async Task OpenSearchResultDialog()
    {
        _wmBestandList = await Sender.Send(new GetAllWmBestandWithSearchParamsQuery(_searchParams));
        await DialogService.OpenAsync<BestandListDialog>("Bestand", new Dictionary<string, object>
        {
            { "WmBestandList", _wmBestandList }
        }, new DialogOptions()
        {
            Resizable = true,
            Width = "90%",
            Height = "70%",
        });
    }

}