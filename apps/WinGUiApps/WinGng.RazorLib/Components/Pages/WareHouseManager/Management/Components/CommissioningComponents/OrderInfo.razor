@using wingLager.domain.WarehouseManagementModels
@using wingLager.domain.ProcessOrders
@using WingCore.domain.Models
@using wingLager.domain.PaletLagers

<div class="container" style="@(Visible ? "display:block" : "display:none")">
    <RadzenCard Style="margin-bottom: 20px;">
        <RadzenLabel Text="Infos zum Auftrag" Style="margin-bottom: 10px;" />

        @if (Commission.Order != null)
        {
            <div class="row-mt-12">
                <div class="col-md-6">
                    <RadzenFieldset Text="Allgemeine Informationen">
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <RadzenLabel Text="Auftragsnummer" />
                            </div>
                            <div class="col-md-8">
                                <RadzenLabel Text="@Commission.Order.OrderNumber.ToString()" />
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <RadzenLabel Text="Warenempfänger" />
                            </div>
                            <div class="col-md-8">
                                <RadzenLabel Text="@Commission.Order.WarenEmpfaenger" />
                            </div>
                        </div>
                        @if (Commission.Customer is not null)
                        {
                            <div class="row mb-3">
                                <div class="col-md-4">
                                    <RadzenLabel Text="Adresse"/>
                                </div>
                                <div class="col-md-8">
                                    <div>
                                        <RadzenLabel Text="@Commission.Customer.Kdstrasse"/>
                                    </div>
                                    <div>
                                        <RadzenLabel Text="@($"{Commission.Customer.Kdort} {Commission.Customer.Kdplz}")"/>
                                    </div>
                                </div>
                            </div>
                        }
                        @if (Commission.Order.Auftragskopf != null)
                        {
                            <div class="row mb-3">
                                <div class="col-md-4">
                                    <RadzenLabel Text="Kundennummer" />
                                </div>
                                <div class="col-md-8">
                                    <RadzenLabel Text="@Commission.Order.Auftragskopf.KundenNrWE.ToString()" />
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-4">
                                    <RadzenLabel Text="Liefertermin" />
                                </div>
                                <div class="col-md-8">
                                    <RadzenLabel Text="@Commission.Order.Auftragskopf.LbisDatum.ToString()" />
                                </div>
                            </div>
                        }
                    </RadzenFieldset>
                </div>
                @*@if (Commission.Customer is not null)
                {
                    <div class="col-md-6">
                        <RadzenFieldset Text="Kunden Informationen">
                            <div class="row mb-3">
                                <div class="col-md-4">
                                    <RadzenLabel Text="Name"/>
                                </div>
                                <div class="col-md-8">
                                    <RadzenLabel Text="@($"{Commission.Customer.Kdname1} {Commission.Customer.Kdname2}")"/>
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-md-4">
                                    <RadzenLabel Text="Adresse"/>
                                </div>
                                <div class="col-md-8">
                                    <RadzenLabel Text="@Commission.Customer.Kdstrasse"/>
                                    
                                    <RadzenLabel Text="@($"{Commission.Customer.Kdort} {Commission.Customer.Kdplz}")"/>
                                </div>
                            </div>
                        </RadzenFieldset>
                    </div>
                }*@

                @*<div class="col-md-6">
                    @if (Commission.Order.ProcessOrderHeader != null)
                    {
                        <RadzenFieldset Text="Prozess-Informationen">
                            <div class="row mb-3">
                                <div class="col-md-4">
                                    <RadzenLabel Text="Beschreibung" />
                                </div>
                                <div class="col-md-8">
                                    <RadzenLabel Text="@Commission.Order.ProcessOrderHeader.Description" />
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-md-4">
                                    <RadzenLabel Text="Erstellt am"/>
                                </div>
                                <div class="col-md-8">
                                    <RadzenLabel Text="@Commission.Order.ProcessOrderHeader.CreatedAt.ToString("g")"/>
                                </div>
                            </div>

                        </RadzenFieldset>
                    }
                </div>*@
            </div>

            <div class="row mt-4">
                <div class="col-md-12">
                    <RadzenFieldset Text="Paletten Übersicht">
                        <RadzenDataGrid Data="@Commission.Order.PaletLager" 
                                      AllowPaging="true" 
                                      PageSize="5"
                                      AllowSorting="true">
                            <Columns>
                                <RadzenDataGridColumn TItem="PaletLager" 
                                                    Property="@nameof(PaletLager.NVENr)" 
                                                    Title="Paletten-Nvenr." />
                                <RadzenDataGridColumn TItem="PaletLager" 
                                                    Property="@nameof(PaletLager.Gewicht)" 
                                                    Title="Gewicht" />
                                <RadzenDataGridColumn TItem="PaletLager" 
                                                    Property="@nameof(PaletLager.ArtikelNr)" 
                                                    Title="Artikel-Nr." />
                                <RadzenDataGridColumn TItem="PaletLager" 
                                                    Property="@nameof(PaletLager.ArtBez1)" 
                                                    Title="Artikelbezeichnung" />
                                <RadzenDataGridColumn TItem="PaletLager" 
                                                      Property="@nameof(PaletLager.MHD)" 
                                                      Title="MHD" />
                                <RadzenDataGridColumn TItem="PaletLager" 
                                                      Property="@nameof(PaletLager.AnzSack)" 
                                                      Title="Anzahl Sack" />
                                
                            </Columns>
                        </RadzenDataGrid>
                    </RadzenFieldset>
                </div>
            </div>

            @*@if (Commission.Order.ProcessOrderPositions.Any() == true)
            {
                <div class="row mt-4">
                    <div class="col-md-12">
                        <RadzenFieldset Text="Auftragspositionen">
                            <RadzenDataGrid Data="@Commission.Order.ProcessOrderPositions" 
                                          AllowPaging="true" 
                                          PageSize="5"
                                          AllowSorting="true">
                                <Columns>
                                    <RadzenDataGridColumn TItem="ProcessOrderPosition" 
                                                        Property="@nameof(ProcessOrderPosition.Position)" 
                                                        Title="Position" />
                                    <RadzenDataGridColumn TItem="ProcessOrderPosition" 
                                                        Property="@nameof(ProcessOrderPosition.ArticleNumber)" 
                                                        Title="Artikel-Nr." />
                                    <RadzenDataGridColumn TItem="ProcessOrderPosition" 
                                                        Property="@nameof(ProcessOrderPosition.)" 
                                                        Title="Menge" />
                                    <RadzenDataGridColumn TItem="ProcessOrderPosition" 
                                                        Property="Status" 
                                                        Title="Status" />
                                    <RadzenDataGridColumn TItem="ProcessOrderPosition" 
                                                        Property="BearbeiteteMenge" 
                                                        Title="Bearbeitete Menge" />
                                </Columns>
                            </RadzenDataGrid>
                        </RadzenFieldset>
                    </div>
                </div>
            }*@

            @if (Commission.Order.AuftragsposList?.Any() == true)
            {
                <div class="row mt-4">
                    <div class="col-md-12">
                        <RadzenFieldset Text="Auftragspositionen Details">
                            <RadzenDataGrid Data="@Commission.Order.AuftragsposList" 
                                          AllowPaging="true" 
                                          PageSize="5"
                                          AllowSorting="true">
                                <Columns>
                                    <RadzenDataGridColumn TItem="Auftragspo" 
                                                          Property="@nameof(Auftragspo.GridPos)" 
                                                          Title="Position" />
                                    <RadzenDataGridColumn TItem="Auftragspo" 
                                                          Property="@nameof(Auftragspo.BestNr)" 
                                                          Title="Bestellnummer" />
                                    <RadzenDataGridColumn TItem="Auftragspo" 
                                                        Property="@nameof(Auftragspo.ArtikelNummer)" 
                                                        Title="Artikel-Nr." />
                                    <RadzenDataGridColumn TItem="Auftragspo" 
                                                          Property="@nameof(Auftragspo.GesMenge)" 
                                                          Title="Menge" />
                                </Columns>
                            </RadzenDataGrid>
                        </RadzenFieldset>
                    </div>
                </div>
            }
        }
        else
        {
            <RadzenText TextStyle="TextStyle.Subtitle1">Keine Auftragsdaten verfügbar.</RadzenText>
        }
    </RadzenCard>
</div>

@code {
    [Parameter]
    public required WmCommission Commission { get; set; }
    [Parameter]
    public bool Visible { get; set; } = true;
}