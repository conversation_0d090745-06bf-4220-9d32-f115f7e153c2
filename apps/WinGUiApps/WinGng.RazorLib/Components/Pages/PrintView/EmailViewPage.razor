@using CommunityToolkit.Maui.Storage
@using PrinterService
@using PrinterService.Models
@using WingCore.application.Contract.IModels
@inherits BasePage

@inject IApplicationPath ApplicationPath
@inject IJSRuntime Js


<RadzenContent Container="main"  Style="min-width: 100%">
    <ChildContent>
        @if (ShowHeaderText)
        {
            <RadzenRow Visible="ShowHeaderText">
                <RadzenHeading Size="H1" style="display: inline-block" Text=@Localizer["EmailPreview"]/>
            </RadzenRow>    
        }
        
        <RadzenRow Gap="1rem" style="font-size: large">
            <RadzenStack Orientation="Orientation.Horizontal" JustifyContent="JustifyContent.Start" Gap="1rem" Class="rz-mt-8 rz-mb-4">
                <RadzenButton ButtonType="ButtonType.Submit" Size="ButtonSize.Large" Icon="send" Text=@Localizer["Send"] Click="@SendEmail"/>
            </RadzenStack>
        </RadzenRow>

        <RadzenStack Orientation="Orientation.Vertical" Gap="1rem" Style="text-align: left;">
            <RadzenRow Gap="1rem" style="font-size: large">
                <RadzenColumn Size="12" SizeSM="12">
                    <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                        <RadzenLabel Text=@Localizer["To"]/>
                        <RadzenTextBox @bind-Value="@EmailDataToCreateEmailParam.EmailParameter.To" Style="width:100%"/>
                    </RadzenStack>
                </RadzenColumn>
                <RadzenColumn Size="12" SizeSM="6">
                    <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                        <RadzenLabel Text=@Localizer["Attachment"]/>
                        <RadzenStack Gap="0.5rem" Orientation="Orientation.Horizontal">
                            @foreach (var file in EmailDataToCreateEmailParam.AttachmentFilePathList)
                            {
                                <RadzenButton Text="@(GetFileName(file))" Click="(_ => DownloadFile(file))"></RadzenButton>
                            }
                        </RadzenStack>
                    </RadzenStack>
                </RadzenColumn>
            </RadzenRow>
            <RadzenRow Gap="1rem" style="font-size: large">
                <RadzenColumn Size="12">
                    <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                        <RadzenLabel Text=@Localizer["Subject"]/>
                        <RadzenTextBox @bind-Value="@EmailDataToCreateEmailParam.ParsedSubject" Style="width:100%"/>
                    </RadzenStack>
                </RadzenColumn>
            </RadzenRow>

            <RadzenRow Gap="0.1rem" Style="@HtmlHeight">
                <RadzenHtmlEditor @bind-Value="@EmailDataToCreateEmailParam.ParsedHtmlBody" UploadUrl="upload/image" Style="@HtmlHeight"/>
            </RadzenRow>
        </RadzenStack>
    </ChildContent>
</RadzenContent>


@code {
    [Parameter,EditorRequired]
    public EmailDataToCreateEmail EmailDataToCreateEmailParam { get; set; } = new();
    [Parameter]
    public bool ShowHeaderText { get; set; }
    [Parameter,EditorRequired]
    public DialogService DialogService { get; set; } = default!;
    [Parameter]
    public EventCallback? CallBackIfSendWasSuccess { get; set; } = null;

    
    private string HtmlHeight => $"height: {(Dimensions.Height < 600 ? 500 : Dimensions.Height - 500)}px; width:100%";
    

    protected override void OnAfterRender(bool firstRender)
    {
        if (!firstRender)
            return;

        StateHasChanged();
    }

    protected override bool ShouldRender()
    {
        return true;
    }
    

    bool _sendEmail;

    private async Task SendEmail()
    {
        if (_sendEmail)
            return;

        _sendEmail = true;

        try
        {
            if(EmailDataToCreateEmailParam.PrintableObject is null)
                return;
            
            EmailDataToCreateEmailParam.AppFolder = ApplicationPath.GetPath();
            
            PrinterCreator.SendEmailWithSetData(EmailDataToCreateEmailParam);
            CallBackIfSendWasSuccess?.InvokeAsync();
        }
        catch (Exception e)
        {
            await DialogService.ShowError(e.InnerException?.Message ?? e.Message);
        }
        finally
        {
            _sendEmail = false;
            DialogService.Close();
        }
    }

    private async Task DownloadFile(string fileNameWithPath)
    {
        if(string.IsNullOrWhiteSpace(fileNameWithPath))
            return;
        
        if(!File.Exists(fileNameWithPath))
            return;

        using var memoryStream = new MemoryStream();
        await using var fileStream = File.OpenRead(fileNameWithPath);
        await fileStream.CopyToAsync(memoryStream);
        
        var fileName = Path.GetFileName(fileNameWithPath);

        
        if (ApplicationPath.IAmBlazorServer())
        {
            fileStream.Position = 0;
            using var streamRef = new DotNetStreamReference(stream: fileStream);

            await Js.InvokeVoidAsync("downloadFileFromStream", fileName, streamRef);                    
        }
        else
        {
            var _ = await FileSaver.Default.SaveAsync(fileName, fileStream, CancellationToken.None);    
        }
    }

    private string GetFileName(string fileNameWithPath)
    {
        if(string.IsNullOrWhiteSpace(fileNameWithPath))
            return "";
        
        if(!File.Exists(fileNameWithPath))
            return "";
        
        return  Path.GetFileName(fileNameWithPath);
    }

}