@page "/grainAccountingMainList/{status:int?}"
@using MediatR
@using WingCore.application.Getreideabrechnung.Positions
@using WingCore.domain.Models
@using WingCore.application.Wiegeschein
@using Radzen.Blazor
@using WingCore.application.Getreideabrechnung.Header

@inherits BasePage

@inject DialogService DialogService
@inject IMediator Mediator
@inject NavigationManager NavigationManager
@inject NotificationService NotificationService

<RadzenContent Container="main">
    <RadzenRow JustifyContent="JustifyContent.SpaceBetween" AlignItems="AlignItems.Center" Class="rz-mb-4">
        <RadzenColumn>
            <RadzenButton Icon="refresh" class="secondary-button" Text="@Localizer["Update"]" IsBusy="@isLoading" Click="@LoadData" />
        </RadzenColumn>
    </RadzenRow>
    
    <RadzenRow Class="rz-mt-4">
        <RadzenColumn Size="12">
            @if (isLoading && gbvadrList == null) 
            {
                <RadzenProgressBar Value="100" ShowValue="false" Mode="ProgressBarMode.Indeterminate" Style="margin-bottom: 20px;" />
            }
            else
            {
                <RadzenDataGrid @ref="dataGrid" Data="@gbvadrList" TItem="Gbvadr" AllowFiltering="true" 
                                AllowColumnResize="true" AllowAlternatingRows="true" FilterMode="FilterMode.Advanced"
                                AllowSorting="true" PageSize="15" AllowPaging="true" PagerHorizontalAlign="HorizontalAlign.Left"
                                ShowPagingSummary="true" RowSelect="@RowSelect" EmptyText="@Localizer["NoAccountingFound"]"
                                IsLoading="@isLoading" Style="height: calc(100vh - 220px);"> 
                                <Columns>
                        <RadzenDataGridColumn TItem="Gbvadr" Property="Gbnr" Title="@Localizer["Number"]" Width="100px" Frozen="true" />
                        <RadzenDataGridColumn TItem="Gbvadr" Property="Gbdatum" Title="@Localizer["Date"]" Width="130px" FormatString="{0:d}" />
                        <RadzenDataGridColumn TItem="Gbvadr" Property="Completed" Title="@Localizer["Status"]" Width="150px" Sortable="false" Filterable="false">
                            <Template Context="data">
                                @if ((bool)data.Completed!)
                                {
                                    <RadzenBadge BadgeStyle="BadgeStyle.Success" IsPill="true" Text="@Localizer["Completed"]" />
                                }
                                else
                                {
                                    <RadzenBadge BadgeStyle="BadgeStyle.Info" IsPill="true" Text="@Localizer["Open"]" />
                                }
                            </Template>
                        </RadzenDataGridColumn>
                        <RadzenDataGridColumn TItem="Gbvadr" Property="Gbname1" Title="@Localizer["Name"]" />
                        <RadzenDataGridColumn TItem="Gbvadr" Property="Gbgesmenge" Title="@Localizer["Weight"]" TextAlign="TextAlign.Right" Width="150px">
                            <Template Context="data">
                                @string.Format("{0:N2} kg", data.GbgesMenge)
                            </Template>
                        </RadzenDataGridColumn>
                        <RadzenDataGridColumn TItem="Gbvadr" Property="Gbnetto" Title="@Localizer["Price"]" TextAlign="TextAlign.Right" Width="150px">
                             <Template Context="data">
                                @string.Format("{0:C2}", data.Gbnetto)
                            </Template>
                        </RadzenDataGridColumn>
                        <RadzenDataGridColumn TItem="Gbvadr" Width="80px" Filterable="false" Sortable="false" TextAlign="TextAlign.Center">
                            <Template Context="data">
                                <RadzenButton Icon="edit" ButtonStyle="ButtonStyle.Dark" Variant="Variant.Text" Click="@(() => EditGbvadr(data))" />
                            </Template>
                        </RadzenDataGridColumn>
                    </Columns>
                </RadzenDataGrid>
            }
        </RadzenColumn>
    </RadzenRow>
</RadzenContent>

@code
{
    [Parameter]
    public int? status { get; set; } // URL parameter: null or 0 for Open, 1 for Completed
    
    private RadzenDataGrid<Gbvadr> dataGrid;
    private IEnumerable<Gbvadr> gbvadrList;
    private bool isLoading = false;
    
    protected override async Task OnInitializedAsync()
    {
        // Default to open entries (status = 0) if no status is provided in the URL
        // This ensures 'status' has a non-null value for logic within LoadData or for the heading text.
        if (!status.HasValue)
        {
            status = 0; 
        }
        await LoadData();
    }


    protected override async Task OnParametersSetAsync()
    {
        // Ensure 'status' is defaulted if navigating to the base URL without a parameter after initial load
        if (!status.HasValue)
        {
            status = 0;
        }
        await LoadData();
    }
    
    private async Task LoadData()
    {
        if (isLoading) return;

        isLoading = true;
        StateHasChanged(); 

        try
        {
            // Determine if we are fetching completed or open based on the 'status' parameter
            // status == 1 means completed, otherwise (null or 0) means open.
            bool fetchCompleted = (status == 1); 
            
            
            gbvadrList = await Mediator.Send(new GetGbvHeaderByCompletionQuery(fetchCompleted));
            
            
        }
        catch (Exception ex)
        {
            NotificationService.Notify(NotificationSeverity.Error, Localizer["Error"], Localizer["ErrorDuringLoadingTheData", ex.Message]);
            gbvadrList = new List<Gbvadr>(); // Ensure gbvadrList is not null on error to avoid render issues
        }
        finally
        {
            isLoading = false;
            StateHasChanged(); 
        }
    }
    
    private void RowSelect(Gbvadr gbvadr)
    {
        //pass
    }
    
    private void EditGbvadr(Gbvadr gbvadr)
    {

        if (gbvadr.Completed == true)
        {
            NavigationManager.NavigateTo($"/grainAccountingPositions/{gbvadr.Gbnr}");
        }
        else
        {
            NavigationManager.NavigateTo($"/grainAccountingPositions/{gbvadr.Gbnr}/editable");
        }
    }
}