@page "/ModifierEditDialog"
@using WingCore.domain.Models

@inject DialogService DialogService

@* Use RadzenTemplateForm for model binding and submission handling *@
<RadzenTemplateForm Data="@CurrentModifier" TItem="Gbvhpt" Submit="@OnSubmit">
    <div class="rz-p-4"> @* Add padding around the main content area *@

        @* Use RadzenRow and RadzenColumn for a horizontal layout (e.g., 2 columns) *@
        <RadzenRow GutterSize="GutterSize.Medium"> @* Add spacing between columns *@
            <RadzenColumn Size="12" SizeMd="6"> @* Column 1 (takes 12/12 on small, 6/12 on medium+)*@
                <div class="rz-mb-4">
                    <RadzenLabel Text="@Localizer["Description"]" Component="Gbtext" class="rz-display-block rz-mb-1" />
                    <RadzenTextBox Name="Gbtext" TValue="string?" @bind-Value="CurrentModifier.Gbtext" Placeholder="@(Localizer["Eg"] + " " + "Material A")" Style="width: 100%;" />
                    @* Optional: <RadzenRequiredValidator Component="Gbtext" Text="Bezeichnung ist erforderlich" Popup="true" Style="position: absolute" /> *@
                </div>

                <div class="rz-mb-4">
                    <RadzenLabel Text="@Localizer["LaboratoryValue"]" Component="Gblbwert" class="rz-display-block rz-mb-1" />
                    <RadzenNumeric Name="Gblbwert" TValue="decimal?" @bind-Value="CurrentModifier.Gblbwert" Placeholder="@(Localizer["Eg"] + " " + "1,23")" Style="width: 100%;" Format="0.##" />
                </div>
                
                <div class="rz-mb-4">
                    <RadzenLabel Text="@Localizer["Unit"]" Component="Gblbeh" class="rz-display-block rz-mb-1" />
                    <RadzenTextBox Name="Gblbeh" TValue="string?" @bind-Value="CurrentModifier.Gblbeh" Placeholder="@(Localizer["Eg"] + " " + "kg, Stk, mm")" Style="width: 100%;" ReadOnly="@( !string.IsNullOrWhiteSpace(CurrentModifier.Gblbeh) )" />
                </div>
    

            </RadzenColumn>

            <RadzenColumn Size="12" SizeMd="6"> @* Column 2 (takes 12/12 on small, 6/12 on medium+) *@
                <div class="rz-mb-4">
                    <RadzenLabel Text="@Localizer["Weight"]" Component="GbabzGew" class="rz-display-block rz-mb-1" />
                    <RadzenNumeric Name="GbabzGew" TValue="decimal?" @bind-Value="CurrentModifier.GbabzGew" Placeholder="@(Localizer["Eg"] + " " + "0,5")" Style="width: 100%;" Format="0.###" />
                </div>

                <div class="rz-mb-4">
                    <RadzenLabel Text="@(Localizer["UnitPrice"] + " " + "(€)")" Component="Gbep" class="rz-display-block rz-mb-1" />
                    <RadzenNumeric Name="Gbep" TValue="decimal?" @bind-Value="CurrentModifier.Gbep" Placeholder="@(Localizer["Eg"] + " " + "10,99")" Style="width: 100%;" Format="c" />
                </div>

                <div class="rz-mb-4">
                    <RadzenLabel Text="@(Localizer["TotalPriceNet"] + " " + "(€)")" Component="GbgesNetto" class="rz-display-block rz-mb-1" />
                    <RadzenNumeric Name="GbgesNetto" TValue="decimal?" @bind-Value="CurrentModifier.GbgesNetto" Placeholder="@(Localizer["Eg"] + " " + "109,90")" Style="width: 100%;" Format="c" />
                </div>
            </RadzenColumn>
        </RadzenRow>
    </div>

    @* Footer remains the same - RadzenStack works well here *@
    <RadzenStack Orientation="Orientation.Horizontal" JustifyContent="JustifyContent.End" Gap="0.5rem" Class="rz-p-4 rz-border-top-1 rz-border-color-gray-lighter">
        <RadzenButton Text="@Localizer["Cancel"]" ButtonStyle="ButtonStyle.Light" Click="@CloseDialog" />
        <RadzenButton Text="@Localizer["Save"]" ButtonStyle="ButtonStyle.Primary" ButtonType="ButtonType.Submit" />
    </RadzenStack>
</RadzenTemplateForm>

@code {
    [Parameter] public Gbvhpt CurrentModifier { get; set; } = new Gbvhpt();

    // ParentWgs parameter seems unused in this specific dialog's logic,
    // but kept as it was present in the original code.
    [Parameter] public Gbvhpt ParentWgs { get; set; } = new Gbvhpt();

    // It's good practice to ensure the model object isn't null when the component initializes,
    // especially if it's being passed as a parameter that could potentially be null.
    protected override void OnInitialized()
    {
        CurrentModifier ??= new Gbvhpt(); // If CurrentModifier is null, create a new instance
    }

    /// <summary>
    /// Handles the form submission. Closes the dialog and returns the potentially modified data.
    /// </summary>
    private void OnSubmit()
    {
        // Optional: Add custom validation logic here if Radzen's built-in validation isn't sufficient
        DialogService.Close(CurrentModifier); // Pass the bound model back
    }

    /// <summary>
    /// Handles the cancel button click. Closes the dialog without returning data (returns null).
    /// </summary>
    private void CloseDialog()
    {
        DialogService.Close(null); // Indicate cancellation or no changes to save
    }
}
