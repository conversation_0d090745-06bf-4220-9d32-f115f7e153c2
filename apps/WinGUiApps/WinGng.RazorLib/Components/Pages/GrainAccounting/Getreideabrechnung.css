
    /* Updated colors from wireframe pdf */
body {
    background-color: #F2F2F2; /* light background from wireframe */
}
.rz-card {
    background-color: #FFFFFF;
    border: 1px solid #CCCCCC; /* lighter gray border */
    margin-bottom: 20px;
}
.rz-button-light {
    background-color: #c7c2fc !important; /* primary blue from wireframe */
    color: white;
    border-radius: 5px;
}
.rz-button-light:hover {
    background-color: rgba(199, 194, 252, 0.8) !important; /* darker blue on hover */
}

.rz-button-dark {
    background-color: #9793d2 !important; /* primary blue from wireframe */
    color: white;
    border-radius: 5px;
}
.rz-button-dark:hover {
    background-color: rgba(151, 147, 210, 0.9) !important; /* darker blue on hover */
}

.rz-button-hollow {
    background-color: rgba(199, 194, 252, 0) !important; /* primary blue from wireframe */
    color: black !important;
    border-radius: 5px;
    border: 3px solid #c7c2fc; /* primary blue from wireframe */
}
.rz-button-hollow:hover {
    background-color: #c7c2fc !important; /* darker blue on hover */
    color: white !important;
}
.rz-button-symbol {
    background-color: rgba(199, 194, 252, 0) !important; /* primary blue from wireframe */
    color: #7570c4 !important;
}
.rz-button-symbol:hover {
    color: #6963ad !important;
}
.header-card {
    border-top: 5px solid rgba(0, 0, 0, 0.24); /* accent red from wireframe */
}
