@page "/grainAccountingLsList/{searchNumber:int?}"
@using MediatR
@using WingCore.application.Getreideabrechnung.Positions
@using WingCore.domain.Models
@using WingCore.application.Wiegeschein
@using Radzen.Blazor
@using <PERSON><PERSON><PERSON>
@using System.Linq

@inherits BasePage

@inject DialogService DialogService
@inject IMediator Mediator
@inject NavigationManager NavigationManager
@inject NotificationService NotificationService

<PageTitle>@Localizer["GrainAccounting"]</PageTitle>

<RadzenContent Container="main">
    <RadzenDataGrid @ref="grid" Data="@GetFilteredData()" TItem="Wg"
                    SelectionMode="DataGridSelectionMode.Multiple"
                    SelectedItems="@selectedItems"
                    SelectionChanged="@OnSelectionChanged"
                    AllowPaging="true" PageSize="13" AllowSorting="true" AllowFiltering="true"
                    FilterMode="FilterMode.Advanced" ShowFilterRow="true"
                    Style="height: 600px; margin: 20px;">
        <Columns>
            <!-- Selection Checkbox Column -->
            <RadzenDataGridColumn TItem="Wg" Context="data" Width="50">
                <HeaderTemplate>
                    <RadzenCheckBox @bind-Value="AllSelected" Style="" Disabled="@IsSelectAllDisabled"/>
                </HeaderTemplate>
                <Template Context="data">
                    <RadzenCheckBox
                        Value="selectedItems.Contains(data)"
                        Change="@((bool value) => ToggleSelection(data, value))" />
                </Template>
            </RadzenDataGridColumn>

            <!-- Important Fields with Filters -->
            <RadzenDataGridColumn TItem="Wg" Property="Wgsnr" Title="@Localizer["WeighingSlipNumber"]" Sortable="true" Filterable="true"/>
            <RadzenDataGridColumn TItem="Wg" Property="Wgsdatum" Title="@Localizer["Date"]" FormatString="{0:dd.MM.yyyy}" Sortable="true" Filterable="true"/>
            <RadzenDataGridColumn TItem="Wg" Property="Wgslfnr" Title="@Localizer["SupplierNumber"]" Sortable="true" Filterable="true"/>
            <RadzenDataGridColumn TItem="Wg" Property="Wgssbg" Title="@Localizer["Matchcode"]" Sortable="true" Filterable="true"/>
            <RadzenDataGridColumn TItem="Wg" Property="Wgsname1" Title="@Localizer["Name"]" Sortable="true" Filterable="true"/>
            <RadzenDataGridColumn TItem="Wg" Property="WgsartNr" Title="@Localizer["ItemNumber"]" Sortable="true" Filterable="true"/>
            <RadzenDataGridColumn TItem="Wg" Property="WgsgetrArt" Title="@Localizer["Item"]" Sortable="true" Filterable="true"/>
            <RadzenDataGridColumn TItem="Wg" Property="Wgsmenge" Title="@Localizer["Weight"]" Sortable="true" Filterable="true" FormatString="{0:F2}" />
        </Columns>
    </RadzenDataGrid>

    <!-- Übernehmen Button -->
    <div style="display: flex; justify-content: flex-end; margin: 20px;">
        <RadzenButton Text="@Localizer["Apply"]" Style="margin-top: 10px;" Click="@OnApply" Disabled="@(!selectedItems.Any())"/>
    </div>
</RadzenContent>

@code {
    [Parameter]
    public int? searchNumber { get; set; }
    
    private RadzenDataGrid<Wg> grid;
    private IEnumerable<Wg> wgsData;
    private List<Wg> selectedItems = new List<Wg>();
    private long? selectedWgslfnr; // Tracks the Wgslfnr of selected items
    private long? lastFilteredWgslfnr; // Track the last filtered value to avoid unnecessary data changes
    private IEnumerable<Wg> cachedFilteredData; // Cache the filtered data

    // State preservation variables
    private List<SortDescriptor> savedSorts = new List<SortDescriptor>();
    private string savedFilter = string.Empty;
    private int savedPage = 0;

    // Computed property to determine if all items with the same Wgslfnr are selected
    private bool AllSelected
    {
        get
        {
            if (selectedWgslfnr == null)
                return false;
            return selectedItems.Count == wgsData.Count(x => x.Wgslfnr == selectedWgslfnr);
        }
        set
        {
            SaveGridState(); // Save state before making changes

            if (value)
            {
                // Select all items with the same Wgslfnr
                selectedItems = wgsData.Where(x => x.Wgslfnr == selectedWgslfnr).ToList();
            }
            else
            {
                // Deselect all items
                selectedItems.Clear();
                selectedWgslfnr = null;
            }

            StateHasChanged();

            // Restore state after the grid re-renders
            Task.Run(async () =>
            {
                await Task.Delay(100); // Wait for grid to re-render
                await InvokeAsync(RestoreGridState);
            });
        }
    }

    // Determines if the "Select All" checkbox should be disabled
    private bool IsSelectAllDisabled => selectedWgslfnr != null && !wgsData.Any(x => x.Wgslfnr == selectedWgslfnr);

    protected override async Task OnInitializedAsync()
    {
        await LoadWgsData();
    }

    private async Task LoadWgsData()
    {
        if (searchNumber.HasValue)
        {
            wgsData = await Mediator.Send(new AllOpenWgsQuery(searchNumber.Value));
        }
        else
        {
            wgsData = await Mediator.Send(new AllOpenWgsQuery(null));
            await base.OnInitializedAsync();
        }
    }

    // Simple method to filter data based on selected supplier with caching
    private IEnumerable<Wg> GetFilteredData()
    {
        if (wgsData == null)
            return Enumerable.Empty<Wg>();

        // Only recalculate filtered data if the filter criteria has changed
        if (lastFilteredWgslfnr != selectedWgslfnr)
        {
            if (selectedWgslfnr.HasValue)
            {
                cachedFilteredData = wgsData.Where(x => x.Wgslfnr == selectedWgslfnr).ToList();
            }
            else
            {
                cachedFilteredData = wgsData;
            }
            lastFilteredWgslfnr = selectedWgslfnr;
        }

        return cachedFilteredData ?? wgsData;
    }

    // Save current grid state before data changes
    private void SaveGridState()
    {
        if (grid == null) return;

        try
        {
            savedSorts = grid.Sorts?.ToList() ?? new List<SortDescriptor>();
            savedFilter = grid.Query?.Filter ?? string.Empty;
            savedPage = grid.CurrentPage;
        }
        catch
        {
            // Ignore errors when saving state
        }
    }

    // Restore grid state after data changes
    private async Task RestoreGridState()
    {
        if (grid == null) return;

        try
        {
            // Restore sorts
            if (savedSorts.Any())
            {
                grid.Sorts.Clear();
                foreach (var sort in savedSorts)
                {
                    grid.Sorts.Add(sort);
                }
            }

            // Note: Filter restoration is more complex and might not work perfectly
            // The grid's filter state is internal and harder to restore programmatically

            // Restore page (do this after a small delay to ensure grid is ready)
            if (savedPage > 0)
            {
                await Task.Delay(50); // Small delay to ensure grid is rendered
                await grid.GoToPage(savedPage);
            }
        }
        catch
        {
            // Ignore errors when restoring state
        }
    }

    // Handle selection changes from the grid
    private void OnSelectionChanged(IEnumerable<Wg> items)
    {
        selectedItems = items.ToList();

        if (selectedItems.Any())
        {
            // Set the selectedWgslfnr based on the first selected item
            selectedWgslfnr = selectedItems.First().Wgslfnr;

            // Ensure all selected items have the same Wgslfnr
            var inconsistentSelections = selectedItems.Where(x => x.Wgslfnr != selectedWgslfnr).ToList();
            if (inconsistentSelections.Any())
            {
                foreach (var item in inconsistentSelections)
                {
                    selectedItems.Remove(item);
                }
                // Optionally, notify the user about inconsistent selections
                DialogService.OpenAsync<ConfirmationDialog>(Localizer["InconsistentSelection"], new Dictionary<string, object>
                {
                    { "Message", Localizer["AllSelectedItemsSameWeighingSlipSupplierNumber"] }
                });
            }
        }
        else
        {
            selectedWgslfnr = null;
        }

        StateHasChanged();
    }

    // Helper method to check if an item has the same Wgslfnr as the selected ones
    private bool IsSameWgslfnr(Wg item)
    {
        if (selectedWgslfnr == null)
            return true; // No selection yet, all items are enabled
        return item.Wgslfnr == selectedWgslfnr;
    }

    // Toggle selection of a single item
    private void ToggleSelection(Wg item, bool isChecked)
    {
        SaveGridState(); // Save state before making changes

        if (isChecked)
        {
            if (selectedWgslfnr == null)
            {
                // First selection sets the Wgslfnr
                selectedWgslfnr = item.Wgslfnr;
            }

            if (item.Wgslfnr == selectedWgslfnr)
            {
                if (!selectedItems.Contains(item))
                    selectedItems.Add(item);
            }
            else
            {
                // Optionally, notify the user that only items with the same Wgslfnr can be selected
                DialogService.OpenAsync<ConfirmationDialog>(Localizer["SelectionError"], new Dictionary<string, object>
                {
                    { "Message", Localizer["OnlySelectItemsWithSameWeighingSlipSupplierNumber"] }
                });
            }
        }
        else
        {
            selectedItems.Remove(item);
            if (!selectedItems.Any())
            {
                selectedWgslfnr = null;
            }
        }

        StateHasChanged();

        // Restore state after the grid re-renders
        Task.Run(async () =>
        {
            await Task.Delay(100); // Wait for grid to re-render
            await InvokeAsync(RestoreGridState);
        });
    }

    private async Task OnApply(MouseEventArgs args)
    {
        if (!selectedItems.Any())
        {
            NotificationService.Notify(new NotificationMessage
            {
                Severity = NotificationSeverity.Warning,
                Summary = Localizer["NoSelection"],
                Detail = Localizer["SelectAtLeastOneWeighingSlip"],
                Duration = 4000
            });
            return;
        }

        try
        {
            // Get supplier information from the first selected item (they all have the same supplier)
            var supplierNumber = selectedItems.First().Wgslfnr;
        
            // Create a confirmation dialog asking if the user wants to proceed
            var confirmResult = await DialogService.Confirm(
                Localizer["WeighingSlipsUsedForBillingConfirmation", selectedItems.Count],
                Localizer["Confirmation"],
                new ConfirmOptions() { OkButtonText = Localizer["Yes"], CancelButtonText = Localizer["No"] });
            
            if (confirmResult != true)
            {
                return; // User cancelled
            }
        
            // Create a new grain accounting record with the selected WGS
            // This is done through a MediatR query that creates data and returns the new ID
            if (supplierNumber != null)
            {
                var newAccountingId = await Mediator.Send(new CreateGrainAccountingQuery(
                    (long)supplierNumber,
                    selectedItems.Select(w => w.Wgsnr).ToList()
                ));
        
                // Navigate to the positions page in edit mode with the new ID
                NavigationManager.NavigateTo($"/grainAccountingPositions/{newAccountingId}/editable");
            }
        }
        catch (Exception ex)
        {
            NotificationService.Notify(new NotificationMessage
            {
                Severity = NotificationSeverity.Error,
                Summary = Localizer["Error"],
                Detail = Localizer["AnErrorOccuredWhenCreatingTheInvoice", ex.Message],
                Duration = 6000
            });
        }
    }
}
