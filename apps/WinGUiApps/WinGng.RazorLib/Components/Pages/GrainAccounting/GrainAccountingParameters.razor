@page "/grainAccountingParameters"
@using MediatR
@using WingCore.application.Contract.Services
@using WingCore.domain.Models
@using wingPrinterListLabel.application.Contracts
@using Microsoft.EntityFrameworkCore

@inherits BasePage

@inject IGrainParametersService GrainParametersService
@inject NotificationService NotificationService

<PageTitle>@Localizer["GrainParameters"]</PageTitle>

<RadzenContent Container="main">
    <div class="p-4">
        <RadzenCard Style="margin:20px;">
            <RadzenHeading Size="H3" Text="@Localizer["GrainParameters"]" Style="margin-bottom:20px;" />

            
            <!-- Existing rows grid -->
            <RadzenDataGrid @ref="grid" TItem="GrainParameter" Data="@grainParameters"
                            AllowPaging="true" PageSize="10"
                            AllowSorting="true" AllowFiltering="true"
                            EditMode="DataGridEditMode.Single" Groupable="true"
                            GroupDescriptors="@groupDescriptors" EmptyText="@Localizer["NoEntriesFound"]">
                <Columns>
                    <RadzenDataGridColumn TItem="GrainParameter" Property="MaskNr" Title="@Localizer["MaskNo"]">
                        <EditTemplate Context="data">
                            <RadzenNumeric @bind-Value="data.MaskNr" Style="width:100%;" />
                        </EditTemplate>
                    </RadzenDataGridColumn>

                    <RadzenDataGridColumn TItem="GrainParameter" Property="FixedType" Title="@Localizer["ParameterType"]">
                        <EditTemplate Context="data">
                            <RadzenDropDown @bind-Value="data.FixedType" Data="@fixedTypes" Style="width:100%;" />
                        </EditTemplate>
                    </RadzenDataGridColumn>

                    <RadzenDataGridColumn TItem="GrainParameter" Property="Description" Title="@Localizer["Name"]">
                        <EditTemplate Context="data">
                            <RadzenTextBox @bind-Value="data.Description" Style="width:100%;" />
                        </EditTemplate>
                    </RadzenDataGridColumn>

                    <RadzenDataGridColumn TItem="GrainParameter" Property="Unit" Title="@Localizer["Unit"]">
                        <EditTemplate Context="data">
                            <RadzenDropDown @bind-Value="data.Unit" Data="@units" Style="width:100%;" />
                        </EditTemplate>
                    </RadzenDataGridColumn>

                    <RadzenDataGridColumn TItem="GrainParameter" Property="BaseType" Title="@Localizer["CalculationType"]">
                        <EditTemplate Context="data">
                            <RadzenDropDown @bind-Value="data.BaseType" Data="@calcTypes" Style="width:100%;" />
                        </EditTemplate>
                    </RadzenDataGridColumn>

                    <RadzenDataGridColumn TItem="GrainParameter" Property="BaseValueFrom" Title="@Localizer["CalculateFrom"]" Sortable="true">
                        <EditTemplate Context="data">
                            <RadzenNumeric @bind-Value="data.BaseValueFrom" Style="width:100%;" />
                        </EditTemplate>
                    </RadzenDataGridColumn>

                    <RadzenDataGridColumn TItem="GrainParameter" Property="BaseValueTo" Title="@Localizer["CalculateUntil"]">
                        <EditTemplate Context="data">
                            <RadzenNumeric @bind-Value="data.BaseValueTo" Style="width:100%;" />
                        </EditTemplate>
                    </RadzenDataGridColumn>

                    <RadzenDataGridColumn TItem="GrainParameter" Property="ModifierValueStart" Title="@Localizer["BaseValue"]">
                        <EditTemplate Context="data">
                            <RadzenNumeric @bind-Value="data.ModifierValueStart" Style="width:100%;" />
                        </EditTemplate>
                    </RadzenDataGridColumn>

                    <RadzenDataGridColumn TItem="GrainParameter" Property="ModifierValueStep" Title="@Localizer["StepValue"]">
                        <EditTemplate Context="data">
                            <RadzenNumeric @bind-Value="data.ModifierValueStep" Style="width:100%;" />
                        </EditTemplate>
                    </RadzenDataGridColumn>

                    <RadzenDataGridColumn TItem="GrainParameter" Property="Factor" Title="@Localizer["Factor"]">
                        <EditTemplate Context="data">
                            <RadzenNumeric @bind-Value="data.Factor" Style="width:100%;" />
                        </EditTemplate>
                    </RadzenDataGridColumn>
                        
                    <!-- Command column -->
                    <RadzenDataGridColumn TItem="GrainParameter">
                        <!-- Inside the command column's Template -->
                        <Template Context="data">
                            @if (grid.IsRowInEditMode(data))
                            {
                                <RadzenButton Icon="save" Size="ButtonSize.Small" Click="@(() => SaveRow(data))" Style="margin-right:5px" />
                                <RadzenButton Icon="cancel" Size="ButtonSize.Small" ButtonStyle="ButtonStyle.Danger" Click="@(() => CancelEdit(data))" />
                            }
                            else
                            {
                                <RadzenButton Icon="edit" Size="ButtonSize.Small" Click="@(() => EditRow(data))" Style="margin-right:5px" />
                                <RadzenButton Icon="delete" Size="ButtonSize.Small" ButtonStyle="ButtonStyle.Danger" Click="@(() => DeleteRow(data))" />
                            }
                        </Template>
                    </RadzenDataGridColumn>
                </Columns>
            </RadzenDataGrid>

            <!-- Always-visible New Row Form -->
            <div style="margin-top:20px; border-top:1px solid #ccc; padding-top:10px; display: flex; flex-direction: column; gap: 10px;">

                <!-- New Entry Form -->
                <RadzenTemplateForm Data="@newGrainParameter">
                    <div style="display: flex; gap: 10px;">
                        <div style="flex:1;">
                            <RadzenLabel Text="@Localizer["MaskNo"]" />
                            <RadzenNumeric @bind-Value="newGrainParameter.MaskNr" Placeholder="" Style="width: 100%;" />
                        </div>
                        <div style="flex:1;">
                            <RadzenLabel Text="@Localizer["MaskType"]" />
                            <RadzenDropDown @bind-Value="newGrainParameter.FixedType" Data="@fixedTypes" Style="width: 100%;">
                                <Template Context="item">@GetTypeTranslation(item)</Template>
                            </RadzenDropDown>
                        </div>
                        <div style="flex:1;">
                            <RadzenLabel Text="@Localizer["Name"]" />
                            <RadzenTextBox @bind-Value="newGrainParameter.Description" Placeholder="" Style="width: 100%;" />
                        </div>
                        <div style="flex:1;">
                            <RadzenLabel Text="@Localizer["Unit"]" />
                            <RadzenDropDown @bind-Value="newGrainParameter.Unit" Data="@units" Placeholder="" Style="width: 100%;" />
                        </div>
                        <div style="flex:1;">
                            <RadzenLabel Text="@Localizer["CalculationType"]" />
                            <RadzenDropDown @bind-Value="newGrainParameter.BaseType" Data="@calcTypes" Placeholder="" Style="width: 100%;">
                                <Template Context="item">@GetTypeTranslation(item)</Template>
                            </RadzenDropDown>
                        </div>
                        <div style="flex:1;">
                            <RadzenLabel Text="@Localizer["CalculateFrom"]" />
                            <RadzenNumeric @bind-Value="newGrainParameter.BaseValueFrom" Placeholder="@Localizer["BaseValueFrom"]" Style="width: 100%;" />
                        </div>
                        <div style="flex:1;">
                            <RadzenLabel Text="@Localizer["CalculateUntil"]" />
                            <RadzenNumeric @bind-Value="newGrainParameter.BaseValueTo" Placeholder="@Localizer["BaseValueTo"]" Style="width: 100%;" />
                        </div>
                        <div style="flex:1;">
                            <RadzenLabel Text="@Localizer["BaseValue"]" />
                            <RadzenNumeric @bind-Value="newGrainParameter.ModifierValueStart" Placeholder="@Localizer["ModifierValueStart"]" Style="width: 100%;" />
                        </div>
                        <div style="flex:1;">
                            <RadzenLabel Text="@Localizer["StepValue"]" />
                            <RadzenNumeric @bind-Value="newGrainParameter.ModifierValueStep" Placeholder="@Localizer["ModifierValueStep"]" Style="width: 100%;" />
                        </div>
                        <div style="flex:1;">
                            <RadzenLabel Text="@Localizer["Factor"]" />
                            <RadzenNumeric @bind-Value="newGrainParameter.Factor" Placeholder="@Localizer["Factor"]" Style="width: 100%;" />
                        </div>
                        <div style="flex: 0 0 auto; padding-top: 1rem;">
                            <RadzenButton Icon="add"
                                          Click="@OnNewRowSubmit"
                                          Style="display: inline-flex;
                         align-items: center;
                         justify-content: center;
                         background-color: limegreen;
                         border-radius: 300px;
                         width: 20px;
                         height: 20px;
                         color: white;
                         padding: 0;"/>
                        </div>
                    </div>
                </RadzenTemplateForm>
            </div>

        </RadzenCard>
    </div>
</RadzenContent>

@code {

    // The list of existing GrainParameter records
    List<GrainParameter> grainParameters;

    // Reference to the grid
    RadzenDataGrid<GrainParameter> grid;

    // Sample list of allowed FixedType values.
    List<string> fixedTypes = new List<string> { "Feuchtigkeit", "Besatz", "Protein" };
    List<string> calcTypes = new List<string> { "Gewicht", "Geld"};
    List<string> units = new List<string> { "%", "Kg"};

    // Grouping settings
    private List<GroupDescriptor> groupDescriptors = new List<GroupDescriptor>
    {
        new GroupDescriptor() { Property = "MaskNr" },
        new GroupDescriptor() { Property = "FixedType" }
    };
    
    GrainParameter newGrainParameter = new GrainParameter();

    protected override async Task OnInitializedAsync()
    {
        grainParameters = await LoadGrainParametersAsync();

        // Optional: sort the list if needed.
        grainParameters = grainParameters.OrderBy(x => x.BaseValueFrom).ToList();
        
        newGrainParameter.Factor = 0.1;
    }

    // Simulated data loading – replace with your EF Core service call.
    private async Task<List<GrainParameter>> LoadGrainParametersAsync()
    {
        return await GrainParametersService.GetAllGrainParameters();
    }
    
    private string GetTypeTranslation(string mode)
    {
        return mode switch
        {
            "Feuchtigkeit" => Localizer["Humidity"],
            "Besatz" => Localizer["Trimming"],
            "Protein" => Localizer["OnlyCustomer"],
            "Gewicht" => Localizer["Weight"],
            "Geld" => Localizer["Money"],
            _ => Localizer["Unknown"]
        };
    }

    void EditRow(GrainParameter parameter)
    {
        grid.EditRow(parameter);
    }

    // Save changes from an inline edit in the grid
    async Task SaveRow(GrainParameter parameter)
    {
        grid.UpdateRow(parameter);
        
        if (parameter.Id == 0)
        {
            GrainParametersService.AddGrainParameter(parameter);
        }
        else
        {
            GrainParametersService.UpdateGrainParameter(parameter);
        }
    }

    // Cancel editing a row
    void CancelEdit(GrainParameter parameter)
    {
        grid.CancelEditRow(parameter);
        // Optionally, revert changes if necessary.
    }

    // Delete a row
    async Task DeleteRow(GrainParameter parameter)
    {
        GrainParametersService.DeleteGrainParameter(parameter);
        grainParameters.Remove(parameter);

        await grid.Reload();
    }

    async Task OnNewRowSubmit()
    {
        var isValid = newGrainParameter.IsValid();
        if (!isValid)
        {
            NotificationService.Notify(new NotificationMessage
            {
                Severity = NotificationSeverity.Warning,
                Summary = Localizer["SavingFailed"],
                Detail = Localizer["AllFieldsMustBeFilled"],
                Duration = 4000
            });
            return;
        }
        // You can either use 'model' directly, or continue to use the 
        // 'newGrainParameter' instance if that's what you prefer.
        GrainParametersService.AddGrainParameter(newGrainParameter);
    
        grainParameters.Add(newGrainParameter);

        // Reset the new row model for the next entry.
        newGrainParameter = new GrainParameter();

        // Optionally, refresh the grid.
        await grid.Reload();
    }
}