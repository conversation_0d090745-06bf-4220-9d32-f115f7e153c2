@page "/"
@page "/home"
@using System.Diagnostics
@using WingCore.domain.Models.StammDbModels
@using Licencing
@using Microsoft.Extensions.Configuration
@using Microsoft.Maui.Devices
@using WingCore.application
@using WingCore.application.Contract
@using WingCore.application.Contract.IModels
@using WingCore.application.Contract.IModels.StammDb
@using WinGng.RazorLib.Components.Common.LoadingPage
@using WinGng.RazorLib.Components.Pages.Configurations

@inject IStammDbContextAccessor StammDbContextAccessor
@inject IWingNgContextAccessor WingNgContextAccessor
@inject ISelectedMandant SelectedMandant
@inject DialogService DialogService
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject IMandantRepository MandantRepository
@inject NavigationManager NavigationManager
@inject IApplicationPath ApplicationPath
@inject IConfiguration AppConfiguration

<LoadingIndicator @ref="_loadingIndicator"
                  DoLoadDataCallback="MandantDbConnect"
                  Option="_options" DialogService="DialogService"/>

<RadzenContent Container="main">
    <ChildContent>
        <RadzenRow >
            @* <RadzenIcon Icon="assessment"> *@
            @* </RadzenIcon> *@
            <RadzenHeading Size="H1" style="display: inline-block; font-family: 'Rubik', sans-serif;" Text="@Localizer["ManageConnection"]">
            </RadzenHeading>
        </RadzenRow>
        <RadzenRow >
            <RadzenStack Orientation="Orientation.Horizontal" JustifyContent="JustifyContent.Start" Gap="1rem" Class="rz-mt-8 rz-mb-4">
                <RadzenButton ButtonType="ButtonType.Submit" Size="ButtonSize.Large" Icon="login" Text="@Localizer["Connect"]" Click="@MandantDbConnectButton"/>
            </RadzenStack>
        </RadzenRow >
        <RadzenTabs RenderMode="TabRenderMode.Client" TabPosition="@TabPosition.Top">
            <Tabs>
                <RadzenTabsItem Text="@Localizer["MasterDatabase"]">
                    <RadzenRow Gap="1rem" style="font-size: large">
                        <RadzenColumn Size="12" SizeSM="6">
                            <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                <RadzenLabel>@Localizer["DatabasePath"]</RadzenLabel>
                                <RadzenTextBox @ref=FocusDesc @bind-Value="@StammDbParameter.DbPath"/>
                                <RadzenLabel>@Localizer["DatabaseUsername"]</RadzenLabel>
                                <RadzenTextBox @bind-Value="@StammDbParameter.DbUsername"/>
                            </RadzenStack>
                        </RadzenColumn>
                        <RadzenColumn Size="12" SizeSM="6">
                            <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                <RadzenLabel>@Localizer["DatabaseName"]</RadzenLabel>
                                <RadzenTextBox @bind-Value="@StammDbParameter.DbName"/>
                                <RadzenLabel>@Localizer["DatabasePassword"]</RadzenLabel>
                                <RadzenTextBox @bind-Value="@StammDbParameter.DbPassword"/>
                            </RadzenStack>
                        </RadzenColumn>
                        <RadzenColumn Size="12" SizeSM="6">

                            <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                <RadzenLabel Text="@Localizer["ClientNo"]" Component="DropDownBindValue" Style="margin-right: 8px; vertical-align: middle;"/>
                                <RadzenDropDown @bind-Value=@StammDbParameter.MandantNumber
                                                Data=@AllMandant
                                                Style="width: 100%; max-width: 100%;"
                                                Name="DropDownBindValue"
                                                TextProperty="@nameof(Mandant.Name)"
                                                ValueProperty="@nameof(Mandant.Mnr)"
                                                Change=@(arg => SetWorkingMandant(arg.ToString()))/>
                            </RadzenStack>
                        </RadzenColumn>
                        <RadzenColumn Size="12" SizeSM="6">
                            <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                <RadzenLabel>@Localizer["AutoLogin"]</RadzenLabel>
                                <RadzenCheckBox @bind-Value="@StammDbParameter.AutoLogin"/>
                            </RadzenStack>
                        </RadzenColumn>
                        <RadzenColumn Size="12" SizeSM="6">
                            <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                <RadzenLabel>@Localizer["Language"]</RadzenLabel>
                                <CultureSelector/>
                            </RadzenStack>
                        </RadzenColumn>
                    </RadzenRow>
                    <RadzenStack Orientation="Orientation.Horizontal" JustifyContent="JustifyContent.Start" Gap="1rem" Class="rz-mt-8 rz-mb-4">
                        <RadzenButton ButtonType="ButtonType.Submit" Size="ButtonSize.Large" Icon="save" Text="@Localizer["Save"]" Click="@SaveDbConnectData"/>
                    </RadzenStack>
                </RadzenTabsItem>

                <RadzenTabsItem Text="@Localizer["ClientDatabase"]">
                    <RadzenRow Gap="1rem" style="font-size: large">
                        <RadzenColumn Size="12" SizeSM="6">
                            <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                <RadzenLabel>@Localizer["DatabasePath"]</RadzenLabel>
                                <RadzenTextBox @bind-Value="@MandantDbParameter.DbPath"/>
                                <RadzenLabel>@Localizer["DatabaseUsername"]</RadzenLabel>
                                <RadzenTextBox @bind-Value="@MandantDbParameter.DbUsername"/>
                            </RadzenStack>
                        </RadzenColumn>
                        <RadzenColumn Size="12" SizeSM="6">
                            <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                <RadzenLabel>@Localizer["DatabaseName"]</RadzenLabel>
                                <RadzenTextBox @bind-Value="@MandantDbParameter.DbName"/>
                                <RadzenLabel>@Localizer["DatabasePassword"]</RadzenLabel>
                                <RadzenTextBox @bind-Value="@MandantDbParameter.DbPassword"/>
                            </RadzenStack>
                        </RadzenColumn>
                    </RadzenRow>
                    <RadzenStack Orientation="Orientation.Horizontal" JustifyContent="JustifyContent.Start" Gap="1rem" Class="rz-mt-8 rz-mb-4">
                        <RadzenButton ButtonType="ButtonType.Submit" Size="ButtonSize.Large" Icon="save" Text="@Localizer["Save"]" Click="@SaveDbConnectData"/>
                        <RadzenButton ButtonType="ButtonType.Submit" Size="ButtonSize.Large" Icon="save" Text="@Localizer["OpenConfigurationFolder"]" Click="@OpenFileConfig"/>
                    </RadzenStack>
                </RadzenTabsItem>
                <RadzenTabsItem Text="@Localizer["WarehouseDatabase"]" Visible="IsProcessOrderHandlingAllowed">
                    <RadzenRow Gap="1rem" style="font-size: large">
                        <RadzenColumn Size="12" SizeSM="6">
                            <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                <RadzenLabel>@Localizer["DatabasePath"]</RadzenLabel>
                                <RadzenTextBox @bind-Value="@MandantDbParameter.LagerDbPath"/>
                                <RadzenLabel>@Localizer["DatabaseUsername"]</RadzenLabel>
                                <RadzenTextBox @bind-Value="@MandantDbParameter.LagerDbUsername"/>
                            </RadzenStack>
                        </RadzenColumn>
                        <RadzenColumn Size="12" SizeSM="6">
                            <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                <RadzenLabel>@Localizer["DatabaseName"]</RadzenLabel>
                                <RadzenTextBox @bind-Value="@MandantDbParameter.LagerDbName"/>
                                <RadzenLabel>@Localizer["DatabasePassword"]</RadzenLabel>
                                <RadzenTextBox @bind-Value="@MandantDbParameter.LagerDbPassword"/>
                            </RadzenStack>
                        </RadzenColumn>
                    </RadzenRow>
                    <RadzenStack Orientation="Orientation.Horizontal" JustifyContent="JustifyContent.Start" Gap="1rem" Class="rz-mt-8 rz-mb-4">
                        <RadzenButton ButtonType="ButtonType.Submit" Size="ButtonSize.Large" Icon="save" Text="@Localizer["Save"]" Click="@SaveDbConnectData"/>
                        <RadzenButton ButtonType="ButtonType.Submit" Size="ButtonSize.Large" Icon="save" Text="@Localizer["OpenConfigurationFolder"]" Click="@OpenFileConfig"/>
                    </RadzenStack>
                </RadzenTabsItem>
            </Tabs>
        </RadzenTabs>
    </ChildContent>
</RadzenContent>

@code {
    [Parameter] public DbParameter StammDbParameter { get; set; } = new();
    [Parameter] public DbParameter MandantDbParameter { get; set; } = new();
    private IEnumerable<Mandant> AllMandant { get; set; } = [];
    RadzenTextBox? FocusDesc { get; set; }
    private bool IsProcessOrderHandlingAllowed { get; set; }

    LoadingIndicator? _loadingIndicator;
    private LoadingIndicatorOptions? _options; 
    
    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();
        _options = new LoadingIndicatorOptions(false, false, false, true, 0, 1, Localizer["DatabaseIsBeingLoaded"]);
        
        IsProcessOrderHandlingAllowed = await ((ExternalAuthStateProvider)AuthenticationStateProvider).CheckPolicyAsync(LicenseClaimType.ProcessOderHandlingValue);
    }

    protected override async void OnAfterRender(bool firstRender)
    {
        if (!firstRender)
            return;
        FocusDesc?.FocusAsync();
        await LoadDbConnectionData();
        
        if(!string.IsNullOrWhiteSpace(AppConfiguration["showOnlyPage"]))
        {
            await MandantDbConnectButton();
            NavigationManager.NavigateTo(AppConfiguration["showOnlyPage"] ?? string.Empty);
        }
        else if (StammDbParameter.AutoLogin)
        {
            await MandantDbConnect();
        }
    }

    private async Task LoadDbConnectionData()
    {
        try
        {
            StammDbParameter = await StammDbContextAccessor.LoadConnectionObjectFromFile() as DbParameter ?? StammDbParameter with{DbName = "ACCESS"};
            StateHasChanged();
            var (isSuccess, errMsg) = StammDbContextAccessor.TestConnection();
            if (isSuccess)
            {
                if(StammDbParameter.IsValid(true))
                    AllMandant = await MandantRepository.GetAll();    
            }
            
            if(!string.IsNullOrEmpty(errMsg))
                await DialogService.ShowError($"{errMsg}");
        }
        catch (Exception e)
        {
            await DialogService.ShowError(Localizer["MasterDatabaseNotReachable", e.Message]);
            return;
        }


        try
        {
            if(StammDbParameter.IsValid())
                MandantDbParameter = await WingNgContextAccessor.LoadConnectionObjectFromFile() as DbParameter ?? MandantDbParameter;
            StateHasChanged();
        }
        catch (Exception e)
        {
            await DialogService.ShowError(Localizer["MasterDatabaseNotReachable", e.Message]);
            return;
        }

        var selected = await SelectedMandant.GetSelectedMandant();
        if (StammDbParameter.IsValid() && selected is null && !MandantDbParameter.IsValid())
        {
            await DialogService.ShowError(Localizer["NoDatabaseExistsForClient", StammDbParameter.MandantNumber]);
        }
    }


    private async Task MandantDbConnectButton()
    {
        if (_loadingIndicator is null)
            return;

        await  _loadingIndicator.Run();
    }

    private async Task MandantDbConnect()
    {
        StateHasChanged();
        try
        {
            var task = await ((ExternalAuthStateProvider)AuthenticationStateProvider).LogInAsync();

            if (!string.IsNullOrWhiteSpace(task.Item2))
            {
                await DialogService.ShowError(task.Item2);
                return;
            }
            
            /*if (MandantDbParameter.IsValid())
            {
                var result = Task.Run(() => WingNgDbContextFactory.CreateDbContext());
                while(!result.IsCompleted)
                    await Task.Delay(50);
            }*/
        }
        catch (Exception e)
        {
            DialogService.Close();

            await DialogService.ShowError(e.InnerException?.Message ?? e.Message);
        }
    }

    private async Task<bool> CheckWorkingMandant(string? value)
    {
        if (string.IsNullOrEmpty(value))
            return false;

        var mandantDbParameter = await SelectedMandant.LoadConnectionObjectFromFile(value) as DbParameter;
        if (mandantDbParameter is null)
        {
            await DialogService.ShowError(Localizer["NoDatabaseExistsForClient", value]);
            return false;
        }

        return true;
    }

    private async void SetWorkingMandant(string? value)
    {
        if (string.IsNullOrEmpty(value))
            return;

        if (await SelectedMandant.LoadConnectionObjectFromFile(value) is DbParameter mandantDbParameter)
        {
            MandantDbParameter = mandantDbParameter;
        }
    }

    private async Task SaveDbConnectData()
    {
        await StammDbContextAccessor.SaveDbConnectionData(StammDbParameter);
        StammDbContextAccessor.SetConnectionString(StammDbParameter.DbPath, StammDbParameter.DbName, StammDbParameter.DbUsername, StammDbParameter.DbPassword);
    
        if (!string.IsNullOrEmpty(StammDbParameter.MandantNumber) && await CheckWorkingMandant(StammDbParameter.MandantNumber))
        {
            WingNgContextAccessor.SetConnectionString(MandantDbParameter.DbPath, MandantDbParameter.DbName, MandantDbParameter.DbUsername, MandantDbParameter.DbPassword);
            await WingNgContextAccessor.SaveDbConnectionData(MandantDbParameter);

            await LoadDbConnectionData();
            await ((ExternalAuthStateProvider)AuthenticationStateProvider).Logout();
        }

        NavigationManager.Refresh();
    }
    
    private Task OpenFileConfig()
    {
        try
        {
            var folderProgramm = "explorer.exe";

            if (DeviceInfo.Platform == DevicePlatform.WinUI)
            {
                folderProgramm = "explorer.exe";
            }
            
            if (DeviceInfo.Platform == DevicePlatform.MacCatalyst)
            {
                folderProgramm = "open";
            }
            
            Process.Start(folderProgramm, ApplicationPath.GetPath());
        }
        catch (Exception ex)
        {
            // Handle any exceptions that occur
            Console.WriteLine($"An error occurred: {ex.Message}");
        }

        return Task.CompletedTask;
    }
}