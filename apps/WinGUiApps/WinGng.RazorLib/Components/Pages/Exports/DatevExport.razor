@page "/datevexportcsv"

@using System.Diagnostics
@using CommunityToolkit.Maui.Storage
@using Licencing
@using ObjectDeAndSerialize
@using QuartzScheduler
@using WingCore.application.Contract.IModels
@using WingCore.application.Contract.Services
@using WingCore.domain.Common.Configurations
@using WinGng.RazorLib.Components.Common.LoadingPage
@using WinGng.RazorLib.Components.Pages.Scheduler


@inject IConfigurationService ConfigurationService
@inject IDatevExportService DatevExportService
@inject SeedData SeedData
@inject DialogService DialogService
@inject IJSRuntime Js
@inject IApplicationPath ApplicationPath


<LoadingIndicator @ref="_loadingIndicatorLoadConfigData"
                  DoLoadDataCallback="LoadConfigData"
                  Option="_optionsLoadConfigData" DialogService="DialogService"/>

<LoadingIndicator @ref="_loadingIndicator"
                  DoLoadDataCallback="Exportieren"
                  Option="_options" DialogService="DialogService"/>
<RadzenContent Container="main">
    <ChildContent>
        <RadzenRow >
            @* <RadzenIcon Icon="assessment"> *@
            @* </RadzenIcon> *@
            <RadzenHeading Size="H1" style="display: inline-block" Text="@Localizer["DATEVExport"]">
            </RadzenHeading>

        </RadzenRow>

        <RadzenHeading Size="H2" style="display: inline-block" Text="@Localizer["ExportParameters"]">
        </RadzenHeading>
        <RadzenTabs RenderMode="TabRenderMode.Client" TabPosition="@TabPosition.Top">
            <Tabs>
                <RadzenTabsItem Text="@Localizer["SearchParameters"]">
                    <RadzenRow Gap="1rem" style="font-size: large">
                        <RadzenColumn Size="12" SizeSM="6">
                            <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                <RadzenLabel>@Localizer["FromDate"]</RadzenLabel>
                                <RadzenDatePicker @bind-Value="@DatevExportParameter.FromDate" DateFormat="dd.MM.yyyy" Name="DatePickerDateOnlyType"/>

                                <RadzenLabel>@Localizer["FromInvoiceNumber"]</RadzenLabel>
                                <RadzenNumeric @bind-Value=@DatevExportParameter.FromInvoiceNumber InputAttributes="@(new Dictionary<string, object>() { { "aria-label", "enter value" } })"/>
                            </RadzenStack>
                        </RadzenColumn>
                        <RadzenColumn Size="12" SizeSM="6">
                            <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                <RadzenLabel>@Localizer["ToDate"]</RadzenLabel>
                                <RadzenDatePicker @bind-Value="@DatevExportParameter.ToDate" DateFormat="dd.MM.yyyy" Name="DatePickerDateOnlyType"/>

                                <RadzenLabel>@Localizer["ToInvoiceNumber"]</RadzenLabel>
                                <RadzenNumeric @bind-Value=@DatevExportParameter.ToInvoiceNumber InputAttributes="@(new Dictionary<string, object>() { { "aria-label", "enter value" } })"/>
                            </RadzenStack>
                        </RadzenColumn>
                    </RadzenRow>
                    <RadzenRow Gap="1rem" style="font-size: large">
                            <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                <RadzenRow Gap="6px" Style="margin-top: 8px">
                                    <div></div>
                                    <div>
                                        <RadzenCheckBox @bind-Value=@DatevExportParameter.WithOutgoingInvoice Name="CheckBoxOutgoingInvoice"/>
                                        <RadzenLabel Text="@Localizer["OutgoingInvoice"]" Component="CheckBoxOutgoingInvoice" Style="margin-left: 8px; vertical-align: middle;"/>
                                    </div>
                                    <div>
                                        <RadzenCheckBox @bind-Value=@DatevExportParameter.WithIncomingInvoice Name="CheckBoxWithIncomingInvoice" Style="margin-left: 8px"/>
                                        <RadzenLabel Text="@Localizer["IncomingInvoice"]" Component="CheckBoxWithIncomingInvoice" Style="margin-left: 8px; vertical-align: middle;"/>
                                    </div>
                                    <div>
                                        <RadzenCheckBox @bind-Value=@DatevExportParameter.WithGrainSettlementInvoice Name="CheckBoxWithGrainSettlementInvoice" Style="margin-left: 8px"/>
                                        <RadzenLabel Text="@Localizer["GrainAccounting"]" Component="CheckBoxWithGrainSettlementInvoice" Style="margin-left: 8px; vertical-align: middle;"/>
                                    </div>
                                    <div>
                                        <RadzenCheckBox @bind-Value=@DatevExportParameter.WithExportedInvoices Name="CheckMitExportierteRechnungen" Style="margin-left: 8px"/>
                                        <RadzenLabel Text="@Localizer["ReExportExportedInvoices"]" Component="CheckMitExportierteRechnungen" Style="margin-left: 8px; vertical-align: middle;"/>
                                    </div>
                                </RadzenRow>
                            </RadzenStack>
                    </RadzenRow>
                    <RadzenStack Orientation="Orientation.Horizontal" JustifyContent="JustifyContent.Start" Gap="1rem" Class="rz-mt-8 rz-mb-4">
                        <RadzenButton ButtonType="ButtonType.Submit" Size="ButtonSize.Large" Icon="outbox" Text="@Localizer["Export"]" Click="@ExportierenButton"/>
                        <RadzenButton ButtonType="ButtonType.Submit" Size="ButtonSize.Large" Icon="save" Text="@Localizer["SaveConfiguration"]" Click="@SaveConfigData"/>

                        <CascadingAuthenticationState>
                            <AuthorizeView Policy="@LicenseClaimType.SchedulerValue">
                                <RadzenButton Icon="calendar_clock" Click="CreateScheduler" ButtonStyle="ButtonStyle.Primary" Size="ButtonSize.Large"  Text="@Localizer["CreateJob"]"/>
                            </AuthorizeView>
                        </CascadingAuthenticationState>

                    </RadzenStack>
                </RadzenTabsItem>
                <RadzenTabsItem Text="@Localizer["Configuration"]">
                    <RadzenRow Gap="1rem" style="font-size: large">
                        <RadzenColumn Size="12" SizeSM="6">
                            <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                <RadzenLabel>@Localizer["ClientNo"]</RadzenLabel>
                                <RadzenNumeric @bind-Value=@DatevExportParameter.MandantenNummer InputAttributes="@(new Dictionary<string, object>() { { "aria-label", "enter value" } })"/>
                                
                                <RadzenLabel>@Localizer["StartOfFinancialYear"]</RadzenLabel>
                                <RadzenDatePicker @bind-Value="@DatevExportParameter.Wirtschaftsjahresbeginn" DateFormat="dd.MM.yyyy" Name="DatePickerDateOnlyType"/>
                                
                                <RadzenLabel>@Localizer["BeginningExportDate"]</RadzenLabel>
                                <RadzenDatePicker @bind-Value="@DatevExportParameter.BeginingExportDate" DateFormat="dd.MM.yyyy" Name="DatePickerDateOnlyType"/>

                            </RadzenStack>
                        </RadzenColumn>
                        <RadzenColumn Size="12" SizeSM="6">
                            <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                <RadzenLabel>@Localizer["ConsultantNumber"]</RadzenLabel>
                                <RadzenNumeric @bind-Value=@DatevExportParameter.BeraterNummer InputAttributes="@(new Dictionary<string, object>() { { "aria-label", "enter value" } })"/>
                                
                                <RadzenLabel>@Localizer["GeneralLedgerAccountLength"]</RadzenLabel>
                                <RadzenNumeric @bind-Value=@DatevExportParameter.SachKontenLaenge InputAttributes="@(new Dictionary<string, object>() { { "aria-label", "enter value" } })"/>
                                <RadzenLabel>@Localizer["SuffixForAccounts"]</RadzenLabel>
                                <RadzenNumeric @bind-Value=@DatevExportParameter.SuffixForKundenkonto InputAttributes="@(new Dictionary<string, object>() { { "aria-label", "enter value" } })"/>
                            </RadzenStack>
                        </RadzenColumn>
                        <RadzenColumn Size="12" SizeSM="6">
                            <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                <RadzenLabel Text="@Localizer["FilePath"]"/>
                                <RadzenTextBox @bind-Value=@DatevExportParameter.SelectedFilePath/>
                            </RadzenStack>
                        </RadzenColumn>
                    </RadzenRow>
                    <RadzenRow Gap="1rem" style="font-size: large">
                            <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                <RadzenRow Gap="6px" Style="margin-top: 8px">
                                    <div></div>
                                    <div>
                                        <RadzenCheckBox @bind-Value=@DatevExportParameter.UseExternalInvoiceNumberForIncomingInvoice Name="CheckBoxUseExternalInvoiceNumberForIncomingInvoice"/>
                                        <RadzenLabel Text="@Localizer["UseExternalInvoiceNumberForIncomingInvoice"]" Component="CheckBoxUseExternalInvoiceNumberForIncomingInvoice" Style="margin-left: 8px; vertical-align: middle;"/>
                                    </div>
                                </RadzenRow>
                            </RadzenStack>
                    </RadzenRow>
                    <RadzenStack Orientation="Orientation.Horizontal" JustifyContent="JustifyContent.Start" Gap="1rem" Class="rz-mt-8 rz-mb-4">
                        <RadzenButton ButtonType="ButtonType.Submit" Size="ButtonSize.Large" Icon="save" Text="@Localizer["SaveConfiguration"]" Click="@SaveConfigData"/>
                    </RadzenStack>
                </RadzenTabsItem>
            </Tabs>
        </RadzenTabs>
    </ChildContent>
</RadzenContent>


@code {
    [Parameter] public ConfigurationDatev DatevExportParameter { get; set; } = new();
    readonly LoadingIndicatorOptions _options = new(false, false, false, false, 0, 6);
    readonly LoadingIndicatorOptions _optionsLoadConfigData = new(false, false, false, false, 0, 6);
    LoadingIndicator? _loadingIndicator;
    LoadingIndicator? _loadingIndicatorLoadConfigData;
    bool _exportStart;

    protected override async void OnAfterRender(bool firstRender)
    {
        if (!firstRender)
            return;
        await LoadConfigDataButton();
        StateHasChanged();
    }

    protected override bool ShouldRender()
    {
        return true;
    }

    private async Task ExportierenButton()
    {
        if (_loadingIndicator is null)
            return;

        await _loadingIndicator.Run();
    }

    private async Task Exportieren()
    {
        if (_exportStart)
            return;

        _exportStart = true;

        try
        {
            _options.CurrentPercent = 1;
            
            var stopwatch = Stopwatch.StartNew();

            var result = Task.Run(async () => await ExportAndDownload());
            while (!result.IsCompleted)
                await Task.Delay(500);

            if (result.Result is null or { Length: 0 })
            {
                _exportStart = false;
                return;
            }

            stopwatch.Stop();

            if(DatevExportService.NumberOfExportInvoices == 0)
            {
                await DialogService.ShowInfo(Localizer["NoInvoicesFound"]); 
                _exportStart = false;
                return;
            }
            

            if(!string.IsNullOrWhiteSpace(DatevExportParameter.SelectedFilePath))
            {
                var path = Path.Combine(DatevExportParameter.SelectedFilePath, DatevExportService.FileName);
                await File.WriteAllBytesAsync(path, result.Result.ToArray());
            }
            else
            {
                if (ApplicationPath.IAmBlazorServer())
                {
                    result.Result.Position = 0;
                    using var streamRef = new DotNetStreamReference(stream: result.Result);

                    await Js.InvokeVoidAsync("downloadFileFromStream", DatevExportService.FileName, streamRef);                    
                }
                else
                {
                    var _ = await FileSaver.Default.SaveAsync(DatevExportService.FileName, result.Result, CancellationToken.None);    
                }
            }

            
            await DialogService.ShowInfo(Localizer["ExportOfInvoicesSuccessfullyCompleted", DatevExportService.NumberOfExportInvoices, stopwatch.Elapsed]);
        }
        catch (Exception e)
        {
            DialogService.Close();
            await DialogService.ShowError(e.InnerException?.Message ?? e.Message);
            _exportStart = false;
        }

        _exportStart = false;
        StateHasChanged();
    }

    private async Task<MemoryStream?> ExportAndDownload()
    {
        var base64Zip = await DatevExportService
            .ExportInvoicesAsZip(DatevExportParameter);

        return base64Zip;
    }

    private async Task LoadConfigDataButton()
    {
        if (_loadingIndicatorLoadConfigData is null)
            return;

        await _loadingIndicatorLoadConfigData.Run();
    }
    
    public async Task LoadConfigData()
    {
        try
        {
            DatevExportParameter = await ConfigurationService.GetConfigurationDatevAsync();
        }
        catch (Exception e)
        {
            await DialogService.ShowError(e.InnerException?.Message ?? e.Message);
        }
    }

    bool _saveConfigDataStart;
    
    private async Task SaveConfigData()
    {
        if (_saveConfigDataStart)
            return;

        _saveConfigDataStart = true;
        try
        {
            await ConfigurationService.SetConfigurationDatevAsync(DatevExportParameter);
        }
        catch (Exception e)
        {
            await DialogService.ShowError(e.InnerException?.Message ?? e.Message);
        }
        finally
        {
            _saveConfigDataStart = false;
        }
    }

    private async Task CreateScheduler()
    {
        try
        {
            var jobViewModel = new JobViewModel() with
            {
                Gruppe = "Export",
                SelectedJobClass = SeedData.JobsNameExportDatevJob
            };

            JobViewModel? data = await DialogService.OpenAsync<CreateJob>(Localizer["CreateJob"], new Dictionary<string, object>
            {
                { "JobViewModel", jobViewModel }
            });

            if (data?.TimeIntervallInSec is null)
                return;

            DatevExportParameter.SelectedFilePath = data.SelectedFilePath;
            var result = await SeedData.CheckAndCreateJobsData(
                data.Name,
                data.Gruppe,
                data.ServerName,
                data.TimeIntervallInSec.Value,
                data.SelectedJobClass,
                DatevExportParameter.SerializeToXml());
        }
        catch (Exception e)
        {
            await DialogService.ShowError(e.InnerException?.Message ?? e.Message);
        }
    }
}