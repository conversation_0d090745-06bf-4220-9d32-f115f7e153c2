@page "/edifactimport"

@using System.Diagnostics
@using WingCore.domain.Models
@using QuartzScheduler
@using WingCore.application.Contract.IModels
@using WingCore.application.Contract.Services
@using WingCore.domain.Common.Configurations
@using WinGng.RazorLib.Components.Common.LoadingPage

@inherits BasePage

@inject IConfigurationService ConfigurationService
@inject IEdifactExportImportService EdifactExportService
@inject DialogService DialogService

<LoadingIndicator @ref="_loadingIndicatorLoadConfigData"
                  DoLoadDataCallback="LoadConfigData"
                  Option="_optionsLoadConfigData" DialogService="DialogService"/>

<LoadingIndicator @ref="_loadingIndicator"
                  DoLoadDataCallback="Importieren"
                  Option="_options" DialogService="DialogService"/>


<InputFile OnChange="HandleFileSelected" />
<RadzenButton Text="@Localizer["Import"]" Click=@(args => ImportierenButton()) class="rz-mt-4" />


<RadzenRow Gap="1rem" style="font-size: large;">
    <div style="height: 100%; display: flex; flex-direction: column;">
        <RadzenPanel>
            <ChildContent>
                <RadzenDataGrid
                    Style="@GridHeight"
                    @ref="@_grid"
                    Count="@_count"
                    Data="@CreatedAuftragskopfe"
                    AllowSorting="true"
                    AllowFiltering="true"
                    AllowPaging="true"
                    ShowPagingSummary="true"
                    PagingSummaryFormat="@Localizer["PagingSummaryFormat"]"
                    GroupPanelText="@Localizer["GroupPanelText"]"
                    PageSize="30"
                    AllowColumnResize="true"
                    PagerHorizontalAlign="HorizontalAlign.Center"
                    LogicalFilterOperator="LogicalFilterOperator.Or" ColumnWidth="100%"
                    SelectionMode="DataGridSelectionMode.Single"
                    @bind-Value="@SelectedAuftragskopfe"
                    AllowGrouping="true"
                    TItem="Auftragskopf">

                    <EmptyTemplate>
                        <p style="color: lightgrey; font-size: 24px; text-align: center; margin: 2rem;">@Localizer["NoOrderImported"]</p>
                    </EmptyTemplate>
                    <Columns>
                        <RadzenDataGridColumn Property="Auftragsnummer" Sortable="true" Title="@Localizer["OrderNumber"]"/>
                        <RadzenDataGridColumn Property="Bestellnr" Sortable="false" Title="@Localizer["PurchaseOrderNumber"]"/>
                        <RadzenDataGridColumn Property="Auftragsdatum" Sortable="true" FormatString="{0:d}" Title="@Localizer["OrderDate"]"/>
                        <RadzenDataGridColumn Property="LvonDatum" Sortable="true" FormatString="{0:d}" Title="@Localizer["DeliveryFromDate"]"/>
                        <RadzenDataGridColumn Property="LbisDatum" Sortable="true" FormatString="{0:d}" Title="@Localizer["DeliveryToDate"]"/>
                    </Columns>
                </RadzenDataGrid>
            </ChildContent>
        </RadzenPanel>
    </div>
</RadzenRow>

@code {
    [Parameter] public ConfigurationEdifact ExportParameter { get; set; } = new();
    readonly LoadingIndicatorOptions _options = new(false, false, false, false, 0, 6);
    readonly LoadingIndicatorOptions _optionsLoadConfigData = new(false, false, false, false, 0, 6);
    LoadingIndicator? _loadingIndicator;
    LoadingIndicator? _loadingIndicatorLoadConfigData;
    bool _importStart;
    
    RadzenDataGrid<Auftragskopf>? _grid;
    int _count;
    private string GridHeight => $"height: {(Dimensions.Height <= 800 ? 800 : Dimensions.Height) - 550}px";
    IEnumerable<Auftragskopf>? CreatedAuftragskopfe;
    IList<Auftragskopf> SelectedAuftragskopfe { get; set; } = [];
    
    protected override async void OnAfterRender(bool firstRender)
    {
        if (!firstRender)
            return;
        await LoadConfigDataButton();
        StateHasChanged();
    }

    protected override bool ShouldRender()
    {
        return true;
    }

    private async Task ImportierenButton()
    {
        if (_loadingIndicator is null)
            return;

        await _loadingIndicator.Run();
    }

    private async Task Importieren()
    {
        if (_importStart)
            return;

        _importStart = true;

        try
        {
            _options.CurrentPercent = 1;

            var stopwatch = Stopwatch.StartNew();

            
            var result = Task.Run(async () => await Import(_fileContent));
            while (!result.IsCompleted)
                await Task.Delay(500);

            if (!result.Result.Any())
            {
                _importStart = false;
                return;
            }
            
            CreatedAuftragskopfe = result.Result;
            
            if (CreatedAuftragskopfe is not null)
                _count = await Task.FromResult(CreatedAuftragskopfe.Count());
            
            stopwatch.Stop();
            

            await DialogService.ShowInfo(Localizer["ImportOfOrdersWasSuccessfullyCompleted", _count, stopwatch.Elapsed]);
        }
        catch (Exception e)
        {
            DialogService.Close();
            await DialogService.ShowError(e.InnerException?.Message ?? e.Message);
            _importStart = false;
        }

        _importStart = false;
        StateHasChanged();
    }

    private async Task<IEnumerable<Auftragskopf>> Import(string xml)
    {
        return await EdifactExportService.ImportOrderFromEdifactAsync(xml);
    }

    private async Task LoadConfigDataButton()
    {
        if (_loadingIndicatorLoadConfigData is null)
            return;

        await _loadingIndicatorLoadConfigData.Run();
    }

    public async Task LoadConfigData()
    {
        try
        {
            ExportParameter = await ConfigurationService.GetConfigurationEdifactAsync();
        }
        catch (Exception e)
        {
            await DialogService.ShowError(e.InnerException?.Message ?? e.Message);
        }
    }

    bool _saveConfigDataStart;

    private async Task SaveConfigData()
    {
        if (_saveConfigDataStart)
            return;

        _saveConfigDataStart = true;
        try
        {
            await ConfigurationService.SetConfigurationEdifactAsync(ExportParameter);
        }
        catch (Exception e)
        {
            await DialogService.ShowError(e.InnerException?.Message ?? e.Message);
        }
        finally
        {
            _saveConfigDataStart = false;
        }
    }

    string _fileContent = string.Empty;

    private async Task HandleFileSelected(InputFileChangeEventArgs e)
    {
        var file = e.File;
        using var stream = file.OpenReadStream();
        using var reader = new StreamReader(stream);
        _fileContent = await reader.ReadToEndAsync();
    }
}