@inherits BasePage
<RadzenTextArea Style="@StyleForTextArea"
                aria-label="TextArea"
                Value="@LogData"
                ReadOnly="true"/>

<RadzenButton Size="ButtonSize.ExtraSmall" Text=@Localizer["Close"] Click="@(_ => Service?.CloseSide())" />

@code {
    [Inject] DialogService? Service { get; set; }
    [Parameter] public string LogData { get; set; } = string.Empty;
    string StyleForTextArea => $"width: 100%;height: {Dimensions.Height-130}px";
}