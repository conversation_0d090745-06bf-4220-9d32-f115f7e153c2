@page "/Jobs"
@using Quartz;
@using Quartz.Impl
@using Quartz.Impl.Matchers
@using QuartzScheduler
@using QuartzScheduler.Extensions
@using WinGng.RazorLib.Components.Common.LoadingPage
@using IScheduler = Quartz.IScheduler

@inherits BasePage

@inject ISchedulerFactory SchedulerFactory
@inject DialogService DialogService
@inject SeedData SeedData


<LoadingIndicator @ref="_loadingIndicator"
                  DoLoadDataCallback="LoadData"
                  Option="_options" DialogService="DialogService"/>

<RadzenColumn SizeMD=12 Style="min-width: 90%">

        <RadzenStack Gap="1rem" style="margin-bottom:1rem;" Orientation="Orientation.Horizontal" AlignItems="AlignItems.Center" JustifyContent="JustifyContent.Start">
            <RadzenButton Icon="refresh" Click="LoadDataButton" ButtonStyle="ButtonStyle.Primary">@Localizer["Reload"]</RadzenButton>
        </RadzenStack>
        <RadzenStack>
            <RadzenCard Variant="Variant.Filled">
                <RadzenText TextStyle="TextStyle.Subtitle1">@Localizer["Jobs"]</RadzenText>
                <RadzenDataGrid AllowColumnResize="true"
                                AllowFiltering="true" 
                                AllowPaging="true" 
                                AllowSorting="true"
                                FilterPopupRenderMode="PopupRenderMode.OnDemand"
                                FilterCaseSensitivity="FilterCaseSensitivity.CaseInsensitive"
                                Data="@_jobs" Density="Density.Compact" AllowAlternatingRows="false">
                    <Columns>
                    @if (_jobsToTrigger is not null)
                    {
                        <RadzenDataGridColumn Property="@nameof(JobDetailImpl.Name)" Title=@Localizer["Name"]/>
                        <RadzenDataGridColumn Title=@Localizer["Status"]>
                            <Template>
                                @GetTriggerStateDesc(context)
                            </Template>
                        </RadzenDataGridColumn>
                        <RadzenDataGridColumn Title=@Localizer["Data"]>
                            <Template>
                                @if (_jobsToTrigger.ContainsKey(context.Key))
                                {
                                    var trigger = _jobsToTrigger[context.Key];

                                    if (trigger is not null &&
                                        trigger.JobDataMap.ContainsKey(SeedData.KeyJobDataObjDataAsString))
                                    {
                                        @trigger.JobDataMap.GetString(SeedData.KeyJobDataObjDataAsString)
                                    }
                                }
                            </Template>
                        </RadzenDataGridColumn>
                        <RadzenDataGridColumn Title=@Localizer["Server"]>
                            <Template>
                                @GetTriggerServerName(context)
                            </Template>
                        </RadzenDataGridColumn>
                       
                        <RadzenDataGridColumn Title=@Localizer["StartTime"]>
                            <Template>
                                @if (_jobsToTrigger.ContainsKey(context.Key))
                                {
                                    var trigger = _jobsToTrigger[context.Key];
                                    @trigger?.StartTimeUtc.LocalDateTime
                                }
                            </Template>
                        </RadzenDataGridColumn>
                        <RadzenDataGridColumn Title=@Localizer["NextTriggerTime"]>
                            <Template>
                                @if (_jobsToTrigger.ContainsKey(context.Key))
                                {
                                    var trigger = _jobsToTrigger[context.Key];
                                    @trigger?.GetNextFireTimeUtc()?.LocalDateTime
                                }
                            </Template>
                        </RadzenDataGridColumn>
                        <RadzenDataGridColumn TItem="JobDetailImpl" Title="" Width="225px" Sortable="false" Frozen="true" OrderIndex="99">
                            <Template Context="job">
                                <RadzenStack Gap="0.5rem"  Orientation="Orientation.Horizontal">
                                    <RadzenButton  Icon="sprint"
                                                   Click="@(_ => RunJobNow(job))"
                                                   MouseEnter="@(args => ShowTooltip(args, Localizer["Execute"], new TooltipOptions() { Position = TooltipPosition.Left }))"/>
                                    <RadzenButton  Icon="stop_circle"
                                                   Click="@(_ => PauseJob(job))"
                                                   MouseEnter="@(args => ShowTooltip(args, Localizer["Stop"], new TooltipOptions() { Position = TooltipPosition.Left }))"
                                                   Visible="@(!IsStatusStopped(job))"/>
                                    <RadzenButton  Icon="start"
                                                   Click="@(_ => ResumeJob(job))"
                                                   MouseEnter="@(args => ShowTooltip(args, Localizer["Start"], new TooltipOptions() { Position = TooltipPosition.Left }))"
                                                   Visible="@(IsStatusStopped(job))"/>
                                    <RadzenButton  Icon="delete"
                                                   Click="@(_ => Delete(job))"
                                                   MouseEnter="@(args => ShowTooltip(args, Localizer["Delete"], new TooltipOptions() { Position = TooltipPosition.Left }))"/>
                                    <RadzenButton Icon="description"
                                                  Click="@(_ => OpenSideDialogForLog(job))"
                                                  MouseEnter="@(args => ShowTooltip(args, Localizer["Logging"], new TooltipOptions() { Position = TooltipPosition.Left }))"/>
                                </RadzenStack>
                            </Template>
                        </RadzenDataGridColumn>
                    }
                    </Columns>
                </RadzenDataGrid>
            </RadzenCard>
        </RadzenStack>
</RadzenColumn>



@code {
    private IQueryable<JobDetailImpl>? _jobs;
    private Dictionary<JobKey, ITrigger?>? _jobsToTrigger;
    private IScheduler? _scheduler;
    LoadingIndicator? _loadingIndicator;
    readonly LoadingIndicatorOptions _options = new(true, false, false, false);

    protected override async Task OnInitializedAsync()
    {
        if (_jobs is null)
            await LoadData();

        _scheduler = await SchedulerFactory.GetScheduler();
    }

    private async Task LoadDataButton()
    {
        if (_loadingIndicator is null)
            return;

        await _loadingIndicator.Run();
    }

    private async Task LoadData()
    {
        if(_scheduler is null)
            return;

        try
        {
            var jobKeys = await _scheduler.GetJobKeys(GroupMatcher<JobKey>.AnyGroup());

            var jobs = new List<JobDetailImpl>();
            _jobsToTrigger = new Dictionary<JobKey,ITrigger?>();
            foreach (var jobKey in jobKeys)
            {
                try
                {
                    var jobDetail = await _scheduler.GetJobDetail(jobKey);
                    if (jobDetail != null) jobs.Add((JobDetailImpl)jobDetail);

                    var trigger = await _scheduler.GetTriggersOfJob(jobKey);
                    _jobsToTrigger.Add(jobKey,trigger.FirstOrDefault());
                }
                catch (Exception e)
                {
                    await DialogService.ShowError(e.InnerException?.Message ?? e.Message);
                }
            }
        
            _jobs = jobs.AsQueryable();
        }
        catch (Exception e)
        {
            await DialogService.ShowError(e.InnerException?.Message ?? e.Message);
        }
    }

    async void ResumeAllJobs()
    {
        await _scheduler!.ResumeAll();
        await LoadDataButton();
    }

    async void ResumeJob(JobDetailImpl? job)
    {
        if(_scheduler is null)
            return;
        
        if(job is null)
            return;

        try
        {
            await _scheduler.ResumeJob(job.Key);
            await LoadDataButton();
        }
        catch (Exception e)
        {
            await DialogService.ShowError(e.InnerException?.Message ?? e.Message);
        }
    }
    
    async void Delete(JobDetailImpl? job)
    {
        if(_scheduler is null)
            return;
        
        if(job is null)
            return;

        try
        {
            await _scheduler.DeleteJob(job.Key);
            await LoadDataButton();
        }
        catch (Exception e)
        {
            await DialogService.ShowError(e.InnerException?.Message ?? e.Message);
        }
    }
    
    async void PauseJob(JobDetailImpl? job)
    {
        if(_scheduler is null)
            return;
        
        if(job is null)
            return;
        
        try
        {
            await _scheduler!.PauseJob(job.Key);
            await LoadDataButton();
        }
        catch (Exception e)
        {
            await DialogService.ShowError(e.InnerException?.Message ?? e.Message);
        }
    }
    
    async void RunJobNow(JobDetailImpl? job)
    {
        if(_scheduler is null)
            return;
        
        if(job is null)
            return;
        
        if(_jobsToTrigger is null)
            return;
        
        try
        {
            await SeedData.ExecuteNow(job);
            await LoadDataButton();
        }
        catch (Exception e)
        {
            await DialogService.ShowError(e.InnerException?.Message ?? e.Message);
        }
    }

    async Task CreateJobs()
    {
        JobViewModel data = await DialogService.OpenAsync<CreateJob>(Localizer["CreateTask"]);

        if (data?.TimeIntervallInSec is null)
            return;
        _ = await SeedData.CheckAndCreateJobsData(data.Name,
            "groupName",
            data.ServerName,
            data.TimeIntervallInSec.Value,
            "ExportDatevJob",
            data.SelectedFilePath);
        
        await LoadDataButton();
    }
    
    string GetTriggerStateDesc(JobDetailImpl job)
    {
        var triggerState = GetJobTriggerState(job);
        return triggerState switch
        {
            TriggerState.Normal => Localizer["Normal"],
            TriggerState.Paused => Localizer["Paused"],
            TriggerState.Complete => Localizer["Completed"],
            TriggerState.Error => Localizer["Error"],
            TriggerState.Blocked => Localizer["Blocked"],
            TriggerState.None => Localizer["NotAvailable"],
            _ => Localizer["Unknown"] 
        };
    }

    private string GetTriggerServerName(JobDetailImpl job)
    {
        if (_jobsToTrigger is null ||
            _jobsToTrigger?.ContainsKey(job.Key) is false)
                return string.Empty;
        
        var trigger = _jobsToTrigger?[job.Key];

        if (trigger is null)
            return string.Empty;
        
        return trigger.Key.GetServerName();
    }

    private bool IsStatusStopped(JobDetailImpl job)
    {
        var triggerState = GetJobTriggerState(job);
        return triggerState is TriggerState.Paused;
    }
    
    private TriggerState? GetJobTriggerState(JobDetailImpl job)
    {
        if (_scheduler is null)
            return null;
        
        if (_jobsToTrigger is null)
            return null;
        
        if (!_jobsToTrigger.ContainsKey(job.Key))
            return null;
        
        var trigger = _jobsToTrigger[job.Key];
        
        if(trigger is null)
            return null;
        
        try
        {
            return _scheduler.GetTriggerState(trigger.Key).GetAwaiter().GetResult();
        }
        catch (Exception e)
        {
            DialogService.ShowError(e.InnerException?.Message ?? e.Message).GetAwaiter().GetResult();
            return null;
        }
    }
    
    async Task OpenSideDialogForLog(JobDetailImpl? job)
    {
        if(job is null)
            return;

        if (_jobsToTrigger is null)
            return;
        
        if (!_jobsToTrigger.TryGetValue(job.Key, out var trigger))
            return;
        
        var logData = string.Empty;
        trigger?.JobDataMap.TryGetString(SeedData.KeyLog, out logData);
            
        await DialogService.OpenSideAsync<TaskLogSideContent>(Localizer["Logs"],
            new Dictionary<string, object>() { { "LogData", logData } },
            options: new SideDialogOptions
            {
                CloseDialogOnOverlayClick = false,
                Position = DialogPosition.Right,
                ShowMask = false
            });
    }
}