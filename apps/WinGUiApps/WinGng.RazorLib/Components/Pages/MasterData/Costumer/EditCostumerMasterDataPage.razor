@inherits BasePage
@page "/editCostumerMasterData"
@page "/editCostumerMasterData/{CostumerNumber:long}"

@using WingCore.domain.Models
@using WingCore.domain.Models.StammDbModels
@using Microsoft.AspNetCore.Authorization
@using WingCore.application.Contract.Services
@using WinGng.RazorLib.Components.Common.LoadingPage

@inject DialogService DialogService
@inject IKundenService KundenService
@inject ICountryService CountryService
@inject NavigationManager NavigationManager

@* <LoadingIndicator @ref="_loadingIndicator" *@
@*                   DoLoadDataCallback="GetCompanyMasterData" *@
@*                   Option="_options" DialogService="DialogService"/> *@

<RadzenContent Container="main">
    <ChildContent>
        <RadzenRow>
            @* <RadzenIcon Icon="assessment"> *@
            @* </RadzenIcon> *@
            <RadzenHeading Size="H1" style="display: inline-block" Text="@Localizer["CustomerData"]">
            </RadzenHeading>

        </RadzenRow>
        <RadzenRow Gap="1rem" style="font-size: large">
            <RadzenStack Orientation="Orientation.Horizontal" JustifyContent="JustifyContent.Start" Gap="1rem" Class="rz-mt-8 rz-mb-4">
                <RadzenButton ButtonType="ButtonType.Submit" Size="ButtonSize.Large" Icon="search" Text="@Localizer["SearchFor"]" Click="@OpenCustomerSearch"/>
                @if (IsSaved)
                {
                    <RadzenButton ButtonType="ButtonType.Submit" Size="ButtonSize.Large" Icon="save" Text="@Localizer["Save"]" Click="@SetCostumerData"/>
                }
            </RadzenStack>
        </RadzenRow>
        <RadzenRow Gap="1rem" class="rz-p-0 rz-p-lg-4">
            <RadzenColumn Size="12" SizeMD="6">
                    <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                        <RadzenFieldset AllowCollapse="true" Style="background-color:white">
                            <HeaderTemplate>
                                <RadzenStack Orientation="Orientation.Horizontal" Gap="0.25rem">
                                    <b>@Localizer["BasicData"]</b>
                                </RadzenStack>
                            </HeaderTemplate>
                            <ChildContent>
                                <RadzenStack Orientation="Orientation.Vertical" Gap="1rem">
                                    <RadzenRow Gap="1rem" style="font-size: large">
                                        <RadzenColumn Size="12" SizeSM="6">
                                            <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                                @Localizer["CustomerNo"]
                                                <RadzenNumeric @bind-Value="@CostumerMasterData.Kdnummer" ReadOnly="true"/>
                                            </RadzenStack>
                                        </RadzenColumn>
                                        <RadzenColumn Size="12" SizeSM="6">
                                            <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                                @Localizer["Matchcode"]
                                                <RadzenTextBox @bind-Value="@CostumerMasterData.Kdsbg"/>
                                            </RadzenStack>
                                        </RadzenColumn>
                                    </RadzenRow>
                                    <RadzenRow Gap="1rem" style="font-size: large">
                                        <RadzenColumn Size="12" SizeSM="6">
                                            <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                                @Localizer["Salutation"] 
                                                <RadzenTextBox @bind-Value="@CostumerMasterData.Kdanrede"/>
                                            </RadzenStack>
                                        </RadzenColumn>
                                        <RadzenColumn Size="12" SizeSM="6">
                                            <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                                @Localizer["ContactPerson"] 
                                                <RadzenTextBox @bind-Value="@CostumerMasterData.KdansprPartner"/>
                                            </RadzenStack>
                                        </RadzenColumn>
                                    </RadzenRow>
                                    <RadzenRow Gap="1rem" style="font-size: large">
                                        <RadzenColumn Size="12" SizeSM="6">
                                            <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                                @Localizer["Name"] 1
                                                <RadzenTextBox @bind-Value="@CostumerMasterData.Kdname1"/>
                                            </RadzenStack>
                                        </RadzenColumn>
                                        <RadzenColumn Size="12" SizeSM="6">
                                            <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                                @Localizer["Name"] 2
                                                <RadzenTextBox @bind-Value="@CostumerMasterData.Kdname2"/>
                                            </RadzenStack>
                                        </RadzenColumn>
                                        <RadzenColumn Size="12" SizeSM="6">
                                            <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                                @Localizer["Name"] 3 
                                                <RadzenTextBox @bind-Value="@CostumerMasterData.Kdname3"/>
                                            </RadzenStack>
                                        </RadzenColumn>
                                    </RadzenRow>
                                </RadzenStack>
                            </ChildContent>
                        </RadzenFieldset>
                    </RadzenStack>

                    <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                        <RadzenFieldset AllowCollapse="true" Style="background-color:white">
                            <HeaderTemplate>
                                <RadzenStack Orientation="Orientation.Horizontal" Gap="0.25rem">
                                    <b>@Localizer["Address"]</b>
                                </RadzenStack>
                            </HeaderTemplate>
                            <ChildContent>
                                <RadzenStack Orientation="Orientation.Vertical" Gap="1rem">
                                    <RadzenRow Gap="1rem" style="font-size: large">
                                        <RadzenColumn Size="12">
                                            <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                                @Localizer["Street"]
                                                <RadzenTextBox @bind-Value="@CostumerMasterData.Kdstrasse"/>
                                            </RadzenStack>
                                        </RadzenColumn>
                                    </RadzenRow>
                                    <RadzenRow Gap="1rem" style="font-size: large">
                                        <RadzenColumn Size="12" >
                                            <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                                @Localizer["ZipCodeCity"] 
                                                <RadzenStack Orientation="Orientation.Horizontal" Gap="6px">
                                                    <RadzenTextBox @bind-Value="@CostumerMasterData.Kdplz" Style="min-width: 30%"/>
                                                    <RadzenTextBox @bind-Value="@CostumerMasterData.Kdort" Style="min-width: 70%"/>
                                                </RadzenStack>
                                            </RadzenStack>
                                        </RadzenColumn>
                                    </RadzenRow>
                                    <RadzenRow Gap="1rem" style="font-size: large">
                                        <RadzenColumn Size="12" SizeSM="6">
                                            <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                                <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                                    @Localizer["Country"] 
                                                    <RadzenDropDown @bind-Value="@CostumerMasterData.Kdland" Data="@_allCountries" TextProperty="@nameof(LandKennung.Landesbezeichnung)" TValue="@string" ValueProperty="Landesbezeichnung" AllowClear=true  />
                                                </RadzenStack>
                                            </RadzenStack>
                                        </RadzenColumn>
                                        <RadzenColumn Size="12" SizeSM="6">
                                            <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                                 @Localizer["FederalState"]
                                                <RadzenDropDown @bind-Value=@CostumerMasterData.Kdbundesland Data=@_bundLand TextProperty="@nameof(BundLand.BlName)" ValueProperty="BlName" AllowClear="true"/>
                                            </RadzenStack>
                                        </RadzenColumn>
                                    </RadzenRow>
                                </RadzenStack>
                            </ChildContent>
                        </RadzenFieldset>
                    </RadzenStack>
            </RadzenColumn>

            <RadzenColumn Size="12" SizeMD="6">
                <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                    <RadzenFieldset AllowCollapse="true" Style="background-color:white">
                        <HeaderTemplate>
                            <RadzenStack Orientation="Orientation.Horizontal" Gap="0.25rem">
                                <b>@Localizer["Communication"]</b>
                            </RadzenStack>
                        </HeaderTemplate>
                        <ChildContent>
                            <RadzenStack Orientation="Orientation.Vertical" Gap="1rem">
                                <RadzenRow Gap="1rem" style="font-size: large">
                                    <RadzenColumn Size="12" SizeSM="6">
                                        <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                            @Localizer["Phone"] 
                                            <RadzenTextBox @bind-Value="@CostumerMasterData.Kdtelefon1"/>
                                        </RadzenStack>
                                    </RadzenColumn>
                                    <RadzenColumn Size="12" SizeSM="6">
                                        <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                             @Localizer["Fax"]
                                            <RadzenTextBox @bind-Value="@CostumerMasterData.Kdtelefax"/>
                                        </RadzenStack>
                                    </RadzenColumn>
                                </RadzenRow>
                                <RadzenRow Gap="1rem" style="font-size: large">
                                    <RadzenColumn Size="12" SizeSM="6">
                                        <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                            @Localizer["Email"] 
                                            <RadzenTextBox @bind-Value="@CostumerMasterData.Kdemail"/>
                                        </RadzenStack>
                                    </RadzenColumn>
                                    <RadzenColumn Size="12" SizeSM="6">
                                        <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                            @Localizer["Internet"] 
                                            <RadzenTextBox @bind-Value="@CostumerMasterData.Kdinternet"/>
                                        </RadzenStack>
                                    </RadzenColumn>
                                </RadzenRow>
                            </RadzenStack>
                        </ChildContent>
                    </RadzenFieldset>
                </RadzenStack>

                <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                    <RadzenFieldset AllowCollapse="true" Style="background-color:white">
                        <HeaderTemplate>
                            <RadzenStack Orientation="Orientation.Horizontal" Gap="0.25rem">
                                <b>@Localizer["VAT"]</b>
                            </RadzenStack>
                        </HeaderTemplate>
                        <ChildContent>
                            <RadzenRow Gap="1rem" style="font-size: large">
                                <RadzenColumn Size="12" SizeSM="6">
                                    <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                        @Localizer["IdentNumber"] 
                                        <RadzenTextBox @bind-Value="@CostumerMasterData.KdustIdnr"/>
                                    </RadzenStack>
                                </RadzenColumn>
                                <RadzenColumn Size="12" SizeSM="6">
                                    <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                    </RadzenStack>
                                </RadzenColumn>
                            </RadzenRow>
                            <RadzenRow Gap="1rem" style="font-size: large">
                                <RadzenColumn Size="12" SizeSM="6">
                                    <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                        @Localizer["ILN"] 
                                        <RadzenSwitch @bind-Value="@ValueForIln" Change=@(args => OnChange(args, "ILN"))/>
                                    </RadzenStack>
                                </RadzenColumn>
                                <RadzenColumn Size="12" SizeSM="6">
                                    <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                        @Localizer["Kdleh"] 
                                        <RadzenTextBox @bind-Value="@CostumerMasterData.Kdleh"/>
                                    </RadzenStack>
                                </RadzenColumn>
                            </RadzenRow>
                        </ChildContent>
                    </RadzenFieldset>
                </RadzenStack>
                <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                    <RadzenFieldset AllowCollapse="true" Style="background-color:white">
                        <HeaderTemplate>
                            <RadzenStack Orientation="Orientation.Horizontal" Gap="0.25rem">
                                <b>@Localizer["EInvoice"]</b>
                            </RadzenStack>
                        </HeaderTemplate>
                        <ChildContent>
                            <RadzenRow Gap="1rem" style="font-size: large">
                                <RadzenColumn Size="12" SizeSM="6">
                                    <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                        @Localizer["Email"]  
                                        <RadzenTextBox @bind-Value="@CostumerMasterData.KdEinvoiceMail"/>
                                    </RadzenStack>
                                </RadzenColumn>
                                <RadzenColumn Size="12" SizeSM="6">
                                    <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                    </RadzenStack>
                                </RadzenColumn>
                            </RadzenRow>
                        </ChildContent>
                    </RadzenFieldset>
                </RadzenStack>
            </RadzenColumn>
        </RadzenRow>
    </ChildContent>
</RadzenContent>

@code {
    [Parameter] public long CostumerNumber { get; set; }

    //LoadingIndicator? _loadingIndicator;
    readonly LoadingIndicatorOptions _options = new(false, false, false, false);

    Kunden CostumerMasterData { get; set; } = new Kunden();
    IEnumerable<BundLand> _bundLand = [];
    IEnumerable<LandKennung> _allCountries = [];

    bool ValueForIln { get; set; }
    bool IsSaved => CostumerMasterData.Id != 0;
    long KdbldKennungDefault { get; set; } = 0;

    protected override async Task OnInitializedAsync()
    {
        try
        {
            await ReloadCostumer(CostumerNumber);
            _bundLand = await CountryService.GetAllBundesLaenderAsync();
            _allCountries = await CountryService.GetAllCountries();
            _allCountries = _allCountries
                                .Where(l=> !string.IsNullOrWhiteSpace(l.IntraKennung))
                                .OrderBy(l => l.Landesbezeichnung);
        }
        catch (Exception e)
        {
            DialogService.Close();
            await DialogService.ShowError(e.InnerException?.Message ?? e.Message);
        }

        await base.OnInitializedAsync();
    }

    private async Task ReloadCostumer(long costumerNumber)
    {
        CostumerNumber = costumerNumber;
        CostumerMasterData = await KundenService.GetCustomerOverNumber(false, costumerNumber) ?? new Kunden();
        ValueForIln = CostumerMasterData.Iln ?? false;
        KdbldKennungDefault = CostumerMasterData.KdbldKennung ?? 0;

        StateHasChanged();
    }

    private async Task SetCostumerData()
    {
        try
        {
            await KundenService.Update(CostumerMasterData);
        }
        catch (Exception e)
        {
            await DialogService.ShowError(e.InnerException?.Message ?? e.Message);
        }
    }


    private async Task OpenCustomerSearch()
    {
        try
        {
            NavigationManager.NavigateTo($"/customerMasterDataSearch");
        }
        catch (Exception e)
        {
            DialogService.Close();
            await DialogService.ShowError(e.Message);
        }
    }

    void OnChange(bool? value, string name)
    {
        switch (name)
        {
            case "ILN":
                CostumerMasterData.Iln = value;
                break;
            default:
                break;
        }
    }

}