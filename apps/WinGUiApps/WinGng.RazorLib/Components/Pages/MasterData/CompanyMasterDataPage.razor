@inherits BasePage
@page "/companyMasterData"
@using WingCore.domain.Models.StammDbModels
@using WingCore.application.Contract
@using WingCore.application.Contract.Services
@using WinGng.RazorLib.Components.Common.LoadingPage

@inject DialogService DialogService
@inject ICompanyMasterDataService CompanyMasterDataService
@inject ICountryService CountryService
@inject ISelectedMandant SelectedMandant

<LoadingIndicator @ref="_loadingIndicator"
                  DoLoadDataCallback="GetCompanyMasterData"
                  Option="_options" DialogService="DialogService"/>
<LoadingIndicator @ref="_loadingIndicatorForSave"
                  DoLoadDataCallback="SetCompanyMasterData"
                  Option="_optionsForSave" DialogService="DialogService"/>
<RadzenContent Container="main" >
    <ChildContent>
        <RadzenRow>
            @* <RadzenIcon Icon="assessment"> *@
            @* </RadzenIcon> *@
            <RadzenHeading Size="H1" style="display: inline-block" Text="@Localizer["CompanyMasterData"]">
            </RadzenHeading>

        </RadzenRow>
        <RadzenRow Gap="1rem" style="font-size: large">
            <RadzenStack Orientation="Orientation.Horizontal" JustifyContent="JustifyContent.Start" Gap="1rem" Class="rz-mt-8 rz-mb-4">
                <RadzenButton ButtonType="ButtonType.Submit" Size="ButtonSize.Large" Icon="save" Text="@Localizer["Save"]" Click="@SetCompanyMasterDataButton"/>
            </RadzenStack>
        </RadzenRow>
        <RadzenRow Gap="1rem" class="rz-p-0 rz-p-lg-4">
            <RadzenColumn Size="12" SizeMD="6">
                <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                    <RadzenFieldset AllowCollapse="true" Style="background-color:white">
                        <HeaderTemplate>
                            <RadzenStack Orientation="Orientation.Horizontal" Gap="0.25rem">
                                <b>@Localizer["Address"]</b>
                            </RadzenStack>
                        </HeaderTemplate>
                        <ChildContent>
                            <RadzenStack Orientation="Orientation.Vertical" Gap="1rem">
                                <RadzenRow Gap="1rem" style="font-size: large">
                                    <RadzenColumn Size="12" SizeSM="6">
                                        <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                            @Localizer["Name"] 
                                            <RadzenTextBox @bind-Value="@CompanyMasterData.FName1"/>
                                        </RadzenStack>
                                    </RadzenColumn>
                                    <RadzenColumn Size="12" SizeSM="6">
                                        <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                            @Localizer["Name"] 2
                                            <RadzenTextBox @bind-Value="@CompanyMasterData.FName2"/>
                                        </RadzenStack>
                                    </RadzenColumn>
                                </RadzenRow>
                                <RadzenRow Gap="1rem" style="font-size: large">
                                    <RadzenColumn Size="12" SizeSM="6">
                                        <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                            @Localizer["Name"] 3
                                            <RadzenTextBox @bind-Value="@CompanyMasterData.FName3"/>
                                        </RadzenStack>
                                    </RadzenColumn>
                                    <RadzenColumn Size="12" SizeSM="6">
                                        <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                            @Localizer["Name"] 4
                                            <RadzenTextBox @bind-Value="@CompanyMasterData.FName4"/>
                                        </RadzenStack>
                                    </RadzenColumn>
                                </RadzenRow>
                                <RadzenRow Gap="1rem" style="font-size: large">
                                    <RadzenColumn Size="12" SizeSM="6">
                                        <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                            @Localizer["Street"] 
                                            <RadzenTextBox @bind-Value="@CompanyMasterData.FStrasse"/>
                                        </RadzenStack>
                                    </RadzenColumn>
                                    <RadzenColumn Size="12" SizeSM="6">
                                        <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                            @Localizer["ZipCodeCity"] 
                                            <RadzenStack Orientation="Orientation.Horizontal" Gap="6px">
                                                <RadzenTextBox @bind-Value="@CompanyMasterData.Fplz" Style="min-width: 30%"/>
                                                <RadzenTextBox @bind-Value="@CompanyMasterData.FOrt" Style="min-width: 70%"/>
                                            </RadzenStack>
                                        </RadzenStack>
                                    </RadzenColumn>
                                </RadzenRow>
                                <RadzenRow Gap="1rem" style="font-size: large">
                                    <RadzenColumn Size="12" SizeSM="6">
                                        <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                            @Localizer["FederalState"] 
                                            <RadzenDropDown @bind-Value=@CompanyMasterData.FBundesland Data=@_bundLand TextProperty="@nameof(BundLand.BlName)" ValueProperty="BlName" AllowClear="true" />
                                        </RadzenStack>
                                    </RadzenColumn>
                                    <RadzenColumn Size="12" SizeSM="6">
                                        <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                        </RadzenStack>
                                    </RadzenColumn>
                                </RadzenRow>
                            </RadzenStack>
                        </ChildContent>
                    </RadzenFieldset>
                </RadzenStack>
            </RadzenColumn>
            <RadzenColumn Size="12" SizeMD="6">
                <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                    <RadzenFieldset AllowCollapse="true" Style="background-color:white">
                        <HeaderTemplate>
                            <RadzenStack Orientation="Orientation.Horizontal" Gap="0.25rem">
                                <b>@Localizer["Communication"]</b>
                            </RadzenStack>
                        </HeaderTemplate>
                        <ChildContent>
                            <RadzenStack Orientation="Orientation.Vertical" Gap="1rem">
                                <RadzenRow Gap="1rem" style="font-size: large">
                                    <RadzenColumn Size="12" SizeSM="6">
                                        <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                            @Localizer["Phone"]
                                            <RadzenTextBox @bind-Value="@CompanyMasterData.FTelefon"/>
                                        </RadzenStack>
                                    </RadzenColumn>
                                    <RadzenColumn Size="12" SizeSM="6">
                                        <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                            @Localizer["Fax"] 
                                            <RadzenTextBox @bind-Value="@CompanyMasterData.FTelefax"/>
                                        </RadzenStack>
                                    </RadzenColumn>
                                </RadzenRow>
                                <RadzenRow Gap="1rem" style="font-size: large">
                                    <RadzenColumn Size="12" SizeSM="6">
                                        <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                            @Localizer["Email"] 
                                            <RadzenTextBox @bind-Value="@CompanyMasterData.FEmail"/>
                                        </RadzenStack>
                                    </RadzenColumn>
                                    <RadzenColumn Size="12" SizeSM="6">
                                        <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                            @Localizer["Internet"] 
                                            <RadzenTextBox @bind-Value="@CompanyMasterData.FInternet"/>
                                        </RadzenStack>
                                    </RadzenColumn>
                                </RadzenRow>
                            </RadzenStack>
                        </ChildContent>
                    </RadzenFieldset>
                </RadzenStack>
                <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                    <RadzenFieldset AllowCollapse="true" Style="background-color:white">
                        <HeaderTemplate>
                            <RadzenStack Orientation="Orientation.Horizontal" Gap="0.25rem">
                                <b>@Localizer["VAT"]</b>
                            </RadzenStack>
                        </HeaderTemplate>
                        <ChildContent>
                            <RadzenRow Gap="1rem" style="font-size: large">
                                <RadzenColumn Size="12" SizeSM="6">
                                    <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                        @Localizer["IdentNumber"]
                                        <RadzenTextBox @bind-Value="@CompanyMasterData.FIdentNr"/>
                                    </RadzenStack>
                                </RadzenColumn>
                                <RadzenColumn Size="12" SizeSM="6">
                                    <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                        @Localizer["ILN"] 
                                        <RadzenTextBox @bind-Value="@CompanyMasterData.IlnNummer"/>
                                    </RadzenStack>
                                </RadzenColumn>
                            </RadzenRow>
                        </ChildContent>
                    </RadzenFieldset>
                </RadzenStack>
            </RadzenColumn>
        </RadzenRow>
    </ChildContent>
</RadzenContent>

@code {
    LoadingIndicator? _loadingIndicator;
    LoadingIndicator? _loadingIndicatorForSave;
    readonly LoadingIndicatorOptions _options = new(true, false, false, false);
    readonly LoadingIndicatorOptions _optionsForSave = new(false, false, false, false);
    CompanyMasterData CompanyMasterData { get; set; } = new CompanyMasterData();

    IEnumerable<BundLand> _bundLand = [];

    private async Task GetCompanyMasterData()
    {
        try
        {
            var selectedMandant = await SelectedMandant.GetSelectedMandant();
            if(selectedMandant?.Mnr is null )
                return;
            CompanyMasterData = await SelectedMandant.GetCompanyMasterData() ?? await SelectedMandant.GetNewCompanyMasterData();//await CompanyMasterDataService.GetMasterData(selectedMandant.Mnr) ?? new CompanyMasterData();
            _bundLand = await CountryService.GetAllBundesLaenderAsync();
        }
        catch (Exception ex)
        {
            await DialogService.ShowError(ex.InnerException?.Message ?? ex.Message);
        }
    }
    
    private async Task SetCompanyMasterDataButton()
    {
        if (_loadingIndicatorForSave is not null)
            await _loadingIndicatorForSave.Run();
    }

    private async Task SetCompanyMasterData()
    {
        try
        {
            CompanyMasterDataService.SetOrUpdate(CompanyMasterData);
        }
        catch (Exception ex)
        {
            await DialogService.ShowError(ex.InnerException?.Message ?? ex.Message);
        }
    }

}