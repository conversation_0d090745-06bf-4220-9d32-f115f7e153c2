using WingCore.data.WaWiContext;

namespace WinGng.WebApp.Extensions;

public class DbContextLoaderService(IServiceProvider serviceProvider) : IHostedService, IDisposable
{
    private Timer? _timer;

    public Task StartAsync(CancellationToken cancellationToken)
    {
        _timer = new Timer(LoadDbContext, null, TimeSpan.Zero, TimeSpan.FromMinutes(30));
        return Task.CompletedTask;
    }

    private void LoadDbContext(object? state)
    {
        using var scope = serviceProvider.CreateScope();
        var wingNgDbContextFactory = scope.ServiceProvider.GetRequiredService<WingNgDbContextFactory>();
        wingNgDbContextFactory.CreateDbContext();
        StopAsync(CancellationToken.None);
    }

    public Task StopAsync(CancellationToken cancellationToken)
    {
        _timer?.Change(Timeout.Infinite, 0);
        return Task.CompletedTask;
    }

    public void Dispose()
    {
        _timer?.Dispose();
    }
}