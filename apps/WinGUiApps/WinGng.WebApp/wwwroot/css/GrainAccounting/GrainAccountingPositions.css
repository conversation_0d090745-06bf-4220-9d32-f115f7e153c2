body {
    background-color: #F2F2F2;
}

.rz-card {
    background-color: #FFFFFF;
    border: 1px solid #E5E5E5;
    border-radius: 6px;
    margin-bottom: 30px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.supplier-card {
    border-left: 4px solid #9793d2;
    margin-bottom: 20px;
}

.wgs-card {
    overflow: hidden;
}

.wgs-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid #F0F0F0;
    cursor: pointer;
}

.wgs-card-header h4 {
    margin: 0;
    font-weight: 500;
}

.card-actions {
    display: flex;
    align-items: center;
    gap: 8px;
}

.primary-button {
    background-color: var(--rz-primary) !important;
    color: white !important;
    border-radius: 4px !important;
    border: none !important;
}

.secondary-button {
    background-color: #c7c2fc !important;
    color: white !important;
    border-radius: 4px !important;
    border: none !important;
}

.outline-button {
    background-color: transparent !important;
    color: var(--rz-primary) !important;
    border-radius: 4px !important;
    border: 2px solid var(--rz-primary) !important;
    transition: all 0.3s ease !important;
}

.outline-button:hover {
    background-color: var(--rz-primary) !important;
    color: white !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
}

.icon-button {
    background-color: transparent !important;
    color: #7570c4 !important;
    border: none !important;
    padding: 4px !important;
}

.delete-button {
    color: #F55 !important;
}

.summary-sidebar {
    position: sticky;
    top: 20px;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    padding: 6px 0;
    font-size: 14px;
}

.summary-total {
    font-weight: bold;
    color: #9793d2;
    border-top: 1px solid #E5E5E5;
    padding-top: 12px;
    margin-top: 8px;
}

.data-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #F0F0F0;
}

.data-label {
    font-weight: 500;
}

.kontrakt-dropdown {
    width: 200px;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 12px;
}

.data-table th {
    text-align: left;
    padding: 8px;
    background-color: #F8F8F8;
    font-weight: 500;
}

.data-table td {
    padding: 8px;
    border-bottom: 1px solid #F0F0F0;
}

.highlight-row {
    background-color: #F5F5F5;
    font-weight: bold;
}

.total-row {
    font-weight: bold;
    color: #9793d2;
}

.action-buttons {
    display: flex;
    gap: 4px;
}

.add-button {
    display: flex;
    align-items: center;
    gap: 8px;
}

.colored-value {
    color: #9793d2;
    font-weight: 500;
}

.contract-section {
    display: flex;
    align-items: center;
    margin-top: 10px;
    gap: 10px;
}

.sidebar-box {
    background-color: white;
    border-radius: 6px;
    padding: 16px;
    margin-bottom: 40px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
}

.contract-item {
    padding: 12px;
    margin-bottom: 8px;
    border-radius: 4px;
    background-color: #f9f9f9;
}

.contract-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.contract-details {
    margin-top: 8px;
    font-size: 14px;
}

.price-tag {
    font-size: 16px;
    font-weight: 500;
    color: #9793d2;
}

.more-button {
    background: transparent !important;
    border: none !important;
    color: #9793d2 !important;
    padding: 4px 8px !important;
    font-size: 14px !important;
}

 .readonly-indicator {
    background-color: #e9ecef; /* Light gray background */
    padding: 8px 16px;
    border-radius: 4px;
    margin-bottom: 15px;
    font-weight: bold;
    color: #6c757d; /* Gray text */
    border: 1px solid #ced4da;
    display: flex;
    align-items: center;
}

.tooltip-container {
    position: relative;
    display: inline-block;
}

.speech-bubble {
    visibility: hidden;
    position: absolute;
    top: 125%;
    right: 0;
    transform: translateY(0) translateX(calc(-100% + 20px));
    background-color: #ffffff;
    color: #333333;
    text-align: left;
    border-radius: 8px;
    padding: 10px 15px;
    min-width: 200px;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
    z-index: 1;
    opacity: 0;
    transition: opacity 0.3s, transform 0.3s;
    font-size: 14px;
    pointer-events: none;
}

/* Speech bubble pointer */
.speech-bubble::after {
    content: "";
    position: absolute;
    bottom: 100%;
    right: 10px;
    border-width: 8px;
    border-style: solid;
    border-color: transparent transparent #ffffff transparent;
}

.tooltip-container:hover .speech-bubble {
    visibility: visible;
    opacity: 1;
    transform: translateY(5px) translateX(8px);
}