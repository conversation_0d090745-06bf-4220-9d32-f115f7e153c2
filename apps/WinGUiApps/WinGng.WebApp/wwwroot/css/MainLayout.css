/* MainLayout.css */

:root {
    /* IMPORTANT: Custom properties (variables) cannot have !important directly */
    /* Their importance is determined when they are *used* */
    --layout-background-color: #F1F4F7FF;
    --body-background-color: #ffffff;
    --border-color: #F1F4F7FF;
    --text-color: #212529;
    --sidebar-width-expanded: max-content;
    --sidebar-width-collapsed: 80px!important; /* Already important, keep as is */
    --header-height: 55px;
    --accent-color: #9980ff; /* Example accent color */
}

.rz-button.rz-button-lg{
    background-color: var(--accent-color) !important; /* Match layout */
}

.rz-sidebar-toggle {
    background-color: var(--layout-background-color) !important; /* Match layout */
    border: none !important; /* Remove default border */
    color: var(--text-color) !important; /* Match text color */
    font-size: 1.2rem !important; /* Adjust size if needed */
    padding: 1rem 1rem !important; /* Adjust padding if needed */
}

.rz-layout {
    background-color: var(--layout-background-color) !important; /* Applies to gaps if any */
    color: var(--text-color) !important;
}

.rz-header {
    background-color: var(--layout-background-color) !important;
    border-bottom: 1px solid var(--border-color) !important; /* Add border like image */
    height: var(--header-height) !important; /* Already important, keep as is */
    padding: 0 1rem !important; /* Already important, keep as is */
    box-shadow: none !important; /* Remove default shadow if any */
}

.rz-header .rz-label {
    font-weight: 500 !important; /* Slightly bolder header text */
    font-size: 1.1rem !important;
    margin-left: 0.5rem !important; /* Space after toggle */
}

/* Ensure the toggle button styles fit */
.rz-sidebar-toggle {
    /* Add custom styles if needed - make them !important */
}


.rz-sidebar {
    background-color: var(--layout-background-color) !important;
    border-right: 1px solid var(--border-color) !important;
    padding: 0 !important; /* Already important, keep as is */
    color: var(--text-color) !important; /* Match text color */
    width: var(--sidebar-width-expanded) !important; /* Auto-resize based on content */
    min-width: 50px !important; /* Minimum width to prevent too narrow sidebar */
    max-width: 400px !important; /* Maximum width to prevent too wide sidebar */
    transition: width 0.3s ease !important; /* Smooth transition */
    box-shadow: none !important;
    overflow-x: hidden !important; /* Hide horizontal scrollbar */
    overflow-y: auto !important; /* Allow vertical scrolling but hide scrollbar */
    scrollbar-width: none !important; /* Firefox */
    -ms-overflow-style: none !important; /* Internet Explorer 10+ */
}

/* Hide scrollbar for WebKit browsers */
.rz-sidebar::-webkit-scrollbar {
    display: none !important;
}

.rz-sidebar:not(.rz-sidebar-expanded) {
    width: var(--sidebar-width-collapsed) !important; /* Collapsed width */
}

/* Style the menu itself */
.rz-panel-menu {
    background-color: inherit !important; /* Inherit from sidebar */
    height: 100% !important; /* Take full height */
    width: max-content !important; /* Auto-resize based on content */
    min-width: 180px !important; /* Minimum width for readability */
    overflow: visible !important; /* Ensure content is not cut off */
}

/* Style menu items to prevent text cutoff */
.rz-panel-menu .rz-navigation-item {
    /* Adjust padding/margin/colors for items if needed */
    padding: 0.8rem 1rem !important; /* Example padding */
    white-space: nowrap !important; /* Prevent text wrapping */
    overflow: visible !important; /* Ensure text is not cut off */
}
.rz-panel-menu .rz-navigation-item-icon {
    margin-right: 1rem !important; /* Space between icon and text */
}

/* Ensure menu item text is fully visible */
.rz-navigation-item-text {
    white-space: nowrap !important; /* Prevent text wrapping */
    overflow: visible !important; /* Ensure text is not cut off */
}

/* Style submenu items to prevent cutoff */
.rz-panel-menu .rz-panel-menu-item {
    white-space: nowrap !important; /* Prevent text wrapping */
    overflow: visible !important; /* Ensure text is not cut off */
}

/* Ensure panel menu items expand to fit content */
.rz-panel-menu-item-content {
    width: max-content !important; /* Auto-resize based on content */
    min-width: 180px !important; /* Minimum width for readability */
}

/* Style the menu itself */
.rz-panel-menu {
    background-color: inherit !important; /* Inherit from sidebar */
    height: 100% !important; /* Take full height */
}

/* Style menu items */
.rz-panel-menu .rz-navigation-item {
    /* Adjust padding/margin/colors for items if needed */
    padding: 0.8rem 1rem !important; /* Example padding */
}
.rz-panel-menu .rz-navigation-item-icon {
    margin-right: 1rem !important; /* Space between icon and text */
}

/* Hide text when collapsed - target specific elements if needed */
.rz-sidebar:not(.rz-sidebar-expanded) .rz-navigation-item-text {
    display: none !important;
}

.rz-sidebar:not(.rz-sidebar-expanded) .rz-navigation-item-icon {
    margin-right: 0 !important; /* Remove margin when collapsed */
}

.rz-sidebar:not(.rz-sidebar-expanded) .rz-navigation-item {
    justify-content: center !important; /* Center icon when collapsed */
}


.rz-body {
    background-color: var(--body-background-color) !important;
    padding: 1.5rem !important; /* Already important, keep as is */
    border-radius: 30px !important; /* Rounded corners */
}

.rz-footer {
    display: none !important; /* Hide the footer as it's not in the target image */
    /* Or style it differently if you want to keep it */
    /* background-color: var(--layout-background-color) !important; */
    /* border-top: 1px solid var(--border-color) !important; */
    /* height: 30px !important; */ /* Already important, keep as is */
    /* padding: 0 1rem !important; */ /* Already important, keep as is */
    /* font-size: 0.8rem !important; */
    /* color: #6c757d !important; */
}

/* Optional: Remove profile menu from header to match image */
.rz-profile-menu {
    /* display: none !important; */
}

.rz-navigation-item-text {
    font-size: 1rem !important; /* Adjust font size */
    color: var(--text-color) !important; /* Match text color */
}
.rz-navigation-item-icon {
    font-size: 1.5rem !important; /* Adjust icon size */
    color: var(--text-color) !important; /* Match text color */
}

       /* Base state - no transitions or animations */
   .rz-navigation-item-link {
       background-color: var(--layout-background-color) !important;
       color: var(--text-color) !important;
       text-decoration: none !important;
       transition: none !important;
       animation: none !important;
   }

/* Hover state - subtle change with no animations */
.rz-navigation-item-link:hover {
    background-color: var(--layout-background-color) !important;
    color: var(--text-color) !important;
    text-decoration: none !important;
    transition: none !important;
    animation: none !important;
}

.rz-navigation-item-wrapper-active::before {
    background-color: var(--accent-color) !important; /* Change color of left border */
}

.rz-navigation-item {
    border-bottom: none !important; /* Remove bottom border */
}

.navmenu-container {
    padding-top: 1rem !important; /* Add some space at the top */
}