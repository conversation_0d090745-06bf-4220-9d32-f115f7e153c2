:root {
  --blue: #007bff;
  --indigo: #6610f2;
  --purple: #6f42c1;
  --pink: #e83e8c;
  --red: #dc3545;
  --orange: #fd7e14;
  --yellow: #ffc107;
  --green: #28a745;
  --teal: #20c997;
  --cyan: #17a2b8;
  --white: #ffffff;
  --gray: #6c757d;
  --gray-dark: #343a40;
  --primary: #ff6d41;
  --secondary: #479cc8;
  --success: #5dbf74;
  --info: #68d5c8;
  --warning: #e6c54f;
  --danger: #f9777f;
  --light: #e6ecef;
  --dark: #343a40;
  --breakpoint-xs: 0;
  --breakpoint-sm: 576px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 992px;
  --breakpoint-xl: 1200px;
  --font-family-sans-serif: "Source Sans Pro", -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
  --font-family-monospace: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace; }

@media print {
  *,
  *::before,
  *::after {
    text-shadow: none !important;
    box-shadow: none !important; }
  a:not(.btn) {
    text-decoration: underline; }
  abbr[title]::after {
    content: " (" attr(title) ")"; }
  pre {
    white-space: pre-wrap !important; }
  pre,
  blockquote {
    border: 1px solid #adb5bd;
    page-break-inside: avoid; }
  thead {
    display: table-header-group; }
  tr,
  img {
    page-break-inside: avoid; }
  p,
  h2,
  h3 {
    orphans: 3;
    widows: 3; }
  h2,
  h3 {
    page-break-after: avoid; }
  @page {
    size: a3; }
  body {
    min-width: 992px !important; }
  .container {
    min-width: 992px !important; }
  .navbar {
    display: none; }
  .badge {
    border: 1px solid #040404; }
  .table {
    border-collapse: collapse !important; }
    .table td,
    .table th {
      background-color: #ffffff !important; }
  .table-bordered th,
  .table-bordered td {
    border: 1px solid #dee2e6 !important; }
  .table-dark {
    color: inherit; }
    .table-dark th,
    .table-dark td,
    .table-dark thead th,
    .table-dark tbody + tbody {
      border-color: #dee2e6; }
  .table .thead-dark th {
    color: inherit;
    border-color: #dee2e6; } }

*,
*::before,
*::after {
  box-sizing: border-box; }

html {
  font-family: sans-serif;
  line-height: 1.15;
  -webkit-text-size-adjust: 100%;
  -webkit-tap-highlight-color: rgba(4, 4, 4, 0); }

article, aside, figcaption, figure, footer, header, hgroup, main, nav, section {
  display: block; }

body {
  margin: 0;
  font-family: "Source Sans Pro", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  color: #ffffff;
  text-align: left;
  background-color: #3a474d; }

[tabindex="-1"]:focus {
  outline: 0 !important; }

hr {
  box-sizing: content-box;
  height: 0;
  overflow: visible; }

h1, h2, h3, h4, h5, h6 {
  margin-top: 0;
  margin-bottom: 0.5rem; }

p {
  margin-top: 0;
  margin-bottom: 1rem; }

abbr[title],
abbr[data-original-title] {
  text-decoration: underline;
  text-decoration: underline dotted;
  cursor: help;
  border-bottom: 0;
  text-decoration-skip-ink: none; }

address {
  margin-bottom: 1rem;
  font-style: normal;
  line-height: inherit; }

ol,
ul,
dl {
  margin-top: 0;
  margin-bottom: 1rem; }

ol ol,
ul ul,
ol ul,
ul ol {
  margin-bottom: 0; }

dt {
  font-weight: 700; }

dd {
  margin-bottom: .5rem;
  margin-left: 0; }

blockquote {
  margin: 0 0 1rem; }

b,
strong {
  font-weight: bolder; }

small {
  font-size: 80%; }

sub,
sup {
  position: relative;
  font-size: 75%;
  line-height: 0;
  vertical-align: baseline; }

sub {
  bottom: -.25em; }

sup {
  top: -.5em; }

a {
  color: #4db9f2;
  text-decoration: none;
  background-color: transparent; }
  a:hover {
    color: #4db9f2;
    text-decoration: underline; }

a:not([href]):not([tabindex]) {
  color: inherit;
  text-decoration: none; }
  a:not([href]):not([tabindex]):hover, a:not([href]):not([tabindex]):focus {
    color: inherit;
    text-decoration: none; }
  a:not([href]):not([tabindex]):focus {
    outline: 0; }

pre,
code,
kbd,
samp {
  font-family: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  font-size: 1em; }

pre {
  margin-top: 0;
  margin-bottom: 1rem;
  overflow: auto; }

figure {
  margin: 0 0 1rem; }

img {
  vertical-align: middle;
  border-style: none; }

svg {
  overflow: hidden;
  vertical-align: middle; }

table {
  border-collapse: collapse; }

caption {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  color: #6c757d;
  text-align: left;
  caption-side: bottom; }

th {
  text-align: inherit; }

label {
  display: inline-block;
  margin-bottom: 0.5rem; }

button {
  border-radius: 0; }

button:focus {
  outline: 1px dotted;
  outline: 5px auto -webkit-focus-ring-color; }

input,
button,
select,
optgroup,
textarea {
  margin: 0;
  font-family: inherit;
  font-size: inherit;
  line-height: inherit; }

button,
input {
  overflow: visible; }

button,
select {
  text-transform: none; }

select {
  word-wrap: normal; }

button,
[type="button"],
[type="reset"],
[type="submit"] {
  -webkit-appearance: button; }

button:not(:disabled),
[type="button"]:not(:disabled),
[type="reset"]:not(:disabled),
[type="submit"]:not(:disabled) {
  cursor: pointer; }

button::-moz-focus-inner,
[type="button"]::-moz-focus-inner,
[type="reset"]::-moz-focus-inner,
[type="submit"]::-moz-focus-inner {
  padding: 0;
  border-style: none; }

input[type="radio"],
input[type="checkbox"] {
  box-sizing: border-box;
  padding: 0; }

input[type="date"],
input[type="time"],
input[type="datetime-local"],
input[type="month"] {
  -webkit-appearance: listbox; }

textarea {
  overflow: auto;
  resize: vertical; }

fieldset {
  min-width: 0;
  padding: 0;
  margin: 0;
  border: 0; }

legend {
  display: block;
  width: 100%;
  max-width: 100%;
  padding: 0;
  margin-bottom: .5rem;
  font-size: 1.5rem;
  line-height: inherit;
  color: inherit;
  white-space: normal; }

progress {
  vertical-align: baseline; }

[type="number"]::-webkit-inner-spin-button,
[type="number"]::-webkit-outer-spin-button {
  height: auto; }

[type="search"] {
  outline-offset: -2px;
  -webkit-appearance: none; }

[type="search"]::-webkit-search-decoration {
  -webkit-appearance: none; }

::-webkit-file-upload-button {
  font: inherit;
  -webkit-appearance: button; }

output {
  display: inline-block; }

summary {
  display: list-item;
  cursor: pointer; }

template {
  display: none; }

[hidden] {
  display: none !important; }

.container {
  width: 100%;
  padding-right: 15px;
  padding-left: 15px;
  margin-right: auto;
  margin-left: auto; }
  @media (min-width: 576px) {
    .container {
      max-width: 540px; } }
  @media (min-width: 768px) {
    .container {
      max-width: 720px; } }
  @media (min-width: 992px) {
    .container {
      max-width: 960px; } }
  @media (min-width: 1200px) {
    .container {
      max-width: 1140px; } }

.container-fluid {
  width: 100%;
  padding-right: 15px;
  padding-left: 15px;
  margin-right: auto;
  margin-left: auto; }

.row {
  display: flex;
  flex-wrap: wrap;
  margin-right: -15px;
  margin-left: -15px; }

.no-gutters {
  margin-right: 0;
  margin-left: 0; }
  .no-gutters > .col,
  .no-gutters > [class*="col-"] {
    padding-right: 0;
    padding-left: 0; }

.col-1, .col-2, .col-3, .col-4, .col-5, .col-6, .col-7, .col-8, .col-9, .col-10, .col-11, .col-12, .col,
.col-auto, .col-sm-1, .col-sm-2, .col-sm-3, .col-sm-4, .col-sm-5, .col-sm-6, .col-sm-7, .col-sm-8, .col-sm-9, .col-sm-10, .col-sm-11, .col-sm-12, .col-sm,
.col-sm-auto, .col-md-1, .col-md-2, .col-md-3, .col-md-4, .col-md-5, .col-md-6, .col-md-7, .col-md-8, .col-md-9, .col-md-10, .col-md-11, .col-md-12, .col-md,
.col-md-auto, .col-lg-1, .col-lg-2, .col-lg-3, .col-lg-4, .col-lg-5, .col-lg-6, .col-lg-7, .col-lg-8, .col-lg-9, .col-lg-10, .col-lg-11, .col-lg-12, .col-lg,
.col-lg-auto, .col-xl-1, .col-xl-2, .col-xl-3, .col-xl-4, .col-xl-5, .col-xl-6, .col-xl-7, .col-xl-8, .col-xl-9, .col-xl-10, .col-xl-11, .col-xl-12, .col-xl,
.col-xl-auto {
  position: relative;
  width: 100%;
  padding-right: 15px;
  padding-left: 15px; }

.col {
  flex-basis: 0;
  flex-grow: 1;
  max-width: 100%; }

.col-auto {
  flex: 0 0 auto;
  width: auto;
  max-width: 100%; }

.col-1 {
  flex: 0 0 8.33333%;
  max-width: 8.33333%; }

.col-2 {
  flex: 0 0 16.66667%;
  max-width: 16.66667%; }

.col-3 {
  flex: 0 0 25%;
  max-width: 25%; }

.col-4 {
  flex: 0 0 33.33333%;
  max-width: 33.33333%; }

.col-5 {
  flex: 0 0 41.66667%;
  max-width: 41.66667%; }

.col-6 {
  flex: 0 0 50%;
  max-width: 50%; }

.col-7 {
  flex: 0 0 58.33333%;
  max-width: 58.33333%; }

.col-8 {
  flex: 0 0 66.66667%;
  max-width: 66.66667%; }

.col-9 {
  flex: 0 0 75%;
  max-width: 75%; }

.col-10 {
  flex: 0 0 83.33333%;
  max-width: 83.33333%; }

.col-11 {
  flex: 0 0 91.66667%;
  max-width: 91.66667%; }

.col-12 {
  flex: 0 0 100%;
  max-width: 100%; }

.order-first {
  order: -1; }

.order-last {
  order: 13; }

.order-0 {
  order: 0; }

.order-1 {
  order: 1; }

.order-2 {
  order: 2; }

.order-3 {
  order: 3; }

.order-4 {
  order: 4; }

.order-5 {
  order: 5; }

.order-6 {
  order: 6; }

.order-7 {
  order: 7; }

.order-8 {
  order: 8; }

.order-9 {
  order: 9; }

.order-10 {
  order: 10; }

.order-11 {
  order: 11; }

.order-12 {
  order: 12; }

.offset-1 {
  margin-left: 8.33333%; }

.offset-2 {
  margin-left: 16.66667%; }

.offset-3 {
  margin-left: 25%; }

.offset-4 {
  margin-left: 33.33333%; }

.offset-5 {
  margin-left: 41.66667%; }

.offset-6 {
  margin-left: 50%; }

.offset-7 {
  margin-left: 58.33333%; }

.offset-8 {
  margin-left: 66.66667%; }

.offset-9 {
  margin-left: 75%; }

.offset-10 {
  margin-left: 83.33333%; }

.offset-11 {
  margin-left: 91.66667%; }

@media (min-width: 576px) {
  .col-sm {
    flex-basis: 0;
    flex-grow: 1;
    max-width: 100%; }
  .col-sm-auto {
    flex: 0 0 auto;
    width: auto;
    max-width: 100%; }
  .col-sm-1 {
    flex: 0 0 8.33333%;
    max-width: 8.33333%; }
  .col-sm-2 {
    flex: 0 0 16.66667%;
    max-width: 16.66667%; }
  .col-sm-3 {
    flex: 0 0 25%;
    max-width: 25%; }
  .col-sm-4 {
    flex: 0 0 33.33333%;
    max-width: 33.33333%; }
  .col-sm-5 {
    flex: 0 0 41.66667%;
    max-width: 41.66667%; }
  .col-sm-6 {
    flex: 0 0 50%;
    max-width: 50%; }
  .col-sm-7 {
    flex: 0 0 58.33333%;
    max-width: 58.33333%; }
  .col-sm-8 {
    flex: 0 0 66.66667%;
    max-width: 66.66667%; }
  .col-sm-9 {
    flex: 0 0 75%;
    max-width: 75%; }
  .col-sm-10 {
    flex: 0 0 83.33333%;
    max-width: 83.33333%; }
  .col-sm-11 {
    flex: 0 0 91.66667%;
    max-width: 91.66667%; }
  .col-sm-12 {
    flex: 0 0 100%;
    max-width: 100%; }
  .order-sm-first {
    order: -1; }
  .order-sm-last {
    order: 13; }
  .order-sm-0 {
    order: 0; }
  .order-sm-1 {
    order: 1; }
  .order-sm-2 {
    order: 2; }
  .order-sm-3 {
    order: 3; }
  .order-sm-4 {
    order: 4; }
  .order-sm-5 {
    order: 5; }
  .order-sm-6 {
    order: 6; }
  .order-sm-7 {
    order: 7; }
  .order-sm-8 {
    order: 8; }
  .order-sm-9 {
    order: 9; }
  .order-sm-10 {
    order: 10; }
  .order-sm-11 {
    order: 11; }
  .order-sm-12 {
    order: 12; }
  .offset-sm-0 {
    margin-left: 0; }
  .offset-sm-1 {
    margin-left: 8.33333%; }
  .offset-sm-2 {
    margin-left: 16.66667%; }
  .offset-sm-3 {
    margin-left: 25%; }
  .offset-sm-4 {
    margin-left: 33.33333%; }
  .offset-sm-5 {
    margin-left: 41.66667%; }
  .offset-sm-6 {
    margin-left: 50%; }
  .offset-sm-7 {
    margin-left: 58.33333%; }
  .offset-sm-8 {
    margin-left: 66.66667%; }
  .offset-sm-9 {
    margin-left: 75%; }
  .offset-sm-10 {
    margin-left: 83.33333%; }
  .offset-sm-11 {
    margin-left: 91.66667%; } }

@media (min-width: 768px) {
  .col-md {
    flex-basis: 0;
    flex-grow: 1;
    max-width: 100%; }
  .col-md-auto {
    flex: 0 0 auto;
    width: auto;
    max-width: 100%; }
  .col-md-1 {
    flex: 0 0 8.33333%;
    max-width: 8.33333%; }
  .col-md-2 {
    flex: 0 0 16.66667%;
    max-width: 16.66667%; }
  .col-md-3 {
    flex: 0 0 25%;
    max-width: 25%; }
  .col-md-4 {
    flex: 0 0 33.33333%;
    max-width: 33.33333%; }
  .col-md-5 {
    flex: 0 0 41.66667%;
    max-width: 41.66667%; }
  .col-md-6 {
    flex: 0 0 50%;
    max-width: 50%; }
  .col-md-7 {
    flex: 0 0 58.33333%;
    max-width: 58.33333%; }
  .col-md-8 {
    flex: 0 0 66.66667%;
    max-width: 66.66667%; }
  .col-md-9 {
    flex: 0 0 75%;
    max-width: 75%; }
  .col-md-10 {
    flex: 0 0 83.33333%;
    max-width: 83.33333%; }
  .col-md-11 {
    flex: 0 0 91.66667%;
    max-width: 91.66667%; }
  .col-md-12 {
    flex: 0 0 100%;
    max-width: 100%; }
  .order-md-first {
    order: -1; }
  .order-md-last {
    order: 13; }
  .order-md-0 {
    order: 0; }
  .order-md-1 {
    order: 1; }
  .order-md-2 {
    order: 2; }
  .order-md-3 {
    order: 3; }
  .order-md-4 {
    order: 4; }
  .order-md-5 {
    order: 5; }
  .order-md-6 {
    order: 6; }
  .order-md-7 {
    order: 7; }
  .order-md-8 {
    order: 8; }
  .order-md-9 {
    order: 9; }
  .order-md-10 {
    order: 10; }
  .order-md-11 {
    order: 11; }
  .order-md-12 {
    order: 12; }
  .offset-md-0 {
    margin-left: 0; }
  .offset-md-1 {
    margin-left: 8.33333%; }
  .offset-md-2 {
    margin-left: 16.66667%; }
  .offset-md-3 {
    margin-left: 25%; }
  .offset-md-4 {
    margin-left: 33.33333%; }
  .offset-md-5 {
    margin-left: 41.66667%; }
  .offset-md-6 {
    margin-left: 50%; }
  .offset-md-7 {
    margin-left: 58.33333%; }
  .offset-md-8 {
    margin-left: 66.66667%; }
  .offset-md-9 {
    margin-left: 75%; }
  .offset-md-10 {
    margin-left: 83.33333%; }
  .offset-md-11 {
    margin-left: 91.66667%; } }

@media (min-width: 992px) {
  .col-lg {
    flex-basis: 0;
    flex-grow: 1;
    max-width: 100%; }
  .col-lg-auto {
    flex: 0 0 auto;
    width: auto;
    max-width: 100%; }
  .col-lg-1 {
    flex: 0 0 8.33333%;
    max-width: 8.33333%; }
  .col-lg-2 {
    flex: 0 0 16.66667%;
    max-width: 16.66667%; }
  .col-lg-3 {
    flex: 0 0 25%;
    max-width: 25%; }
  .col-lg-4 {
    flex: 0 0 33.33333%;
    max-width: 33.33333%; }
  .col-lg-5 {
    flex: 0 0 41.66667%;
    max-width: 41.66667%; }
  .col-lg-6 {
    flex: 0 0 50%;
    max-width: 50%; }
  .col-lg-7 {
    flex: 0 0 58.33333%;
    max-width: 58.33333%; }
  .col-lg-8 {
    flex: 0 0 66.66667%;
    max-width: 66.66667%; }
  .col-lg-9 {
    flex: 0 0 75%;
    max-width: 75%; }
  .col-lg-10 {
    flex: 0 0 83.33333%;
    max-width: 83.33333%; }
  .col-lg-11 {
    flex: 0 0 91.66667%;
    max-width: 91.66667%; }
  .col-lg-12 {
    flex: 0 0 100%;
    max-width: 100%; }
  .order-lg-first {
    order: -1; }
  .order-lg-last {
    order: 13; }
  .order-lg-0 {
    order: 0; }
  .order-lg-1 {
    order: 1; }
  .order-lg-2 {
    order: 2; }
  .order-lg-3 {
    order: 3; }
  .order-lg-4 {
    order: 4; }
  .order-lg-5 {
    order: 5; }
  .order-lg-6 {
    order: 6; }
  .order-lg-7 {
    order: 7; }
  .order-lg-8 {
    order: 8; }
  .order-lg-9 {
    order: 9; }
  .order-lg-10 {
    order: 10; }
  .order-lg-11 {
    order: 11; }
  .order-lg-12 {
    order: 12; }
  .offset-lg-0 {
    margin-left: 0; }
  .offset-lg-1 {
    margin-left: 8.33333%; }
  .offset-lg-2 {
    margin-left: 16.66667%; }
  .offset-lg-3 {
    margin-left: 25%; }
  .offset-lg-4 {
    margin-left: 33.33333%; }
  .offset-lg-5 {
    margin-left: 41.66667%; }
  .offset-lg-6 {
    margin-left: 50%; }
  .offset-lg-7 {
    margin-left: 58.33333%; }
  .offset-lg-8 {
    margin-left: 66.66667%; }
  .offset-lg-9 {
    margin-left: 75%; }
  .offset-lg-10 {
    margin-left: 83.33333%; }
  .offset-lg-11 {
    margin-left: 91.66667%; } }

@media (min-width: 1200px) {
  .col-xl {
    flex-basis: 0;
    flex-grow: 1;
    max-width: 100%; }
  .col-xl-auto {
    flex: 0 0 auto;
    width: auto;
    max-width: 100%; }
  .col-xl-1 {
    flex: 0 0 8.33333%;
    max-width: 8.33333%; }
  .col-xl-2 {
    flex: 0 0 16.66667%;
    max-width: 16.66667%; }
  .col-xl-3 {
    flex: 0 0 25%;
    max-width: 25%; }
  .col-xl-4 {
    flex: 0 0 33.33333%;
    max-width: 33.33333%; }
  .col-xl-5 {
    flex: 0 0 41.66667%;
    max-width: 41.66667%; }
  .col-xl-6 {
    flex: 0 0 50%;
    max-width: 50%; }
  .col-xl-7 {
    flex: 0 0 58.33333%;
    max-width: 58.33333%; }
  .col-xl-8 {
    flex: 0 0 66.66667%;
    max-width: 66.66667%; }
  .col-xl-9 {
    flex: 0 0 75%;
    max-width: 75%; }
  .col-xl-10 {
    flex: 0 0 83.33333%;
    max-width: 83.33333%; }
  .col-xl-11 {
    flex: 0 0 91.66667%;
    max-width: 91.66667%; }
  .col-xl-12 {
    flex: 0 0 100%;
    max-width: 100%; }
  .order-xl-first {
    order: -1; }
  .order-xl-last {
    order: 13; }
  .order-xl-0 {
    order: 0; }
  .order-xl-1 {
    order: 1; }
  .order-xl-2 {
    order: 2; }
  .order-xl-3 {
    order: 3; }
  .order-xl-4 {
    order: 4; }
  .order-xl-5 {
    order: 5; }
  .order-xl-6 {
    order: 6; }
  .order-xl-7 {
    order: 7; }
  .order-xl-8 {
    order: 8; }
  .order-xl-9 {
    order: 9; }
  .order-xl-10 {
    order: 10; }
  .order-xl-11 {
    order: 11; }
  .order-xl-12 {
    order: 12; }
  .offset-xl-0 {
    margin-left: 0; }
  .offset-xl-1 {
    margin-left: 8.33333%; }
  .offset-xl-2 {
    margin-left: 16.66667%; }
  .offset-xl-3 {
    margin-left: 25%; }
  .offset-xl-4 {
    margin-left: 33.33333%; }
  .offset-xl-5 {
    margin-left: 41.66667%; }
  .offset-xl-6 {
    margin-left: 50%; }
  .offset-xl-7 {
    margin-left: 58.33333%; }
  .offset-xl-8 {
    margin-left: 66.66667%; }
  .offset-xl-9 {
    margin-left: 75%; }
  .offset-xl-10 {
    margin-left: 83.33333%; }
  .offset-xl-11 {
    margin-left: 91.66667%; } }

.form-control {
  display: block;
  width: 100%;
  height: 2.1875rem;
  padding: 0.375rem 0.75rem;
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  color: #495057;
  background-color: #ffffff;
  background-clip: padding-box;
  border: 1px solid #ced4da;
  border-radius: 4px;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out; }
  @media (prefers-reduced-motion: reduce) {
    .form-control {
      transition: none; } }
  .form-control::-ms-expand {
    background-color: transparent;
    border: 0; }
  .form-control:focus {
    color: #495057;
    background-color: #ffffff;
    border-color: #ffcfc1;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(255, 109, 65, 0.25); }
  .form-control::placeholder {
    color: #6c757d;
    opacity: 1; }
  .form-control:disabled, .form-control[readonly] {
    background-color: #e9ecef;
    opacity: 1; }

select.form-control:focus::-ms-value {
  color: #495057;
  background-color: #ffffff; }

.form-control-file,
.form-control-range {
  display: block;
  width: 100%; }

.col-form-label {
  padding-top: calc(0.375rem + 1px);
  padding-bottom: calc(0.375rem + 1px);
  margin-bottom: 0;
  font-size: inherit;
  line-height: 1.5; }

.col-form-label-lg {
  padding-top: calc(0.5rem + 1px);
  padding-bottom: calc(0.5rem + 1px);
  font-size: 1.25rem;
  line-height: 1.5; }

.col-form-label-sm {
  padding-top: calc(0.25rem + 1px);
  padding-bottom: calc(0.25rem + 1px);
  font-size: 0.875rem;
  line-height: 1.5; }

.form-control-plaintext {
  display: block;
  width: 100%;
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
  margin-bottom: 0;
  line-height: 1.5;
  color: #ffffff;
  background-color: transparent;
  border: solid transparent;
  border-width: 1px 0; }
  .form-control-plaintext.form-control-sm, .form-control-plaintext.form-control-lg {
    padding-right: 0;
    padding-left: 0; }

.form-control-sm {
  height: calc(1.5em + 0.5rem + 2px);
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  line-height: 1.5;
  border-radius: 0.2rem; }

.form-control-lg {
  height: calc(1.5em + 1rem + 2px);
  padding: 0.5rem 1rem;
  font-size: 1.25rem;
  line-height: 1.5;
  border-radius: 0.3rem; }

select.form-control[size], select.form-control[multiple] {
  height: auto; }

textarea.form-control {
  height: auto; }

.form-group {
  margin-bottom: 1rem; }

.form-text {
  display: block;
  margin-top: 0.25rem; }

.form-row {
  display: flex;
  flex-wrap: wrap;
  margin-right: -5px;
  margin-left: -5px; }
  .form-row > .col,
  .form-row > [class*="col-"] {
    padding-right: 5px;
    padding-left: 5px; }

.form-check {
  position: relative;
  display: block;
  padding-left: 1.25rem; }

.form-check-input {
  position: absolute;
  margin-top: 0.3rem;
  margin-left: -1.25rem; }
  .form-check-input:disabled ~ .form-check-label {
    color: #6c757d; }

.form-check-label {
  margin-bottom: 0; }

.form-check-inline {
  display: inline-flex;
  align-items: center;
  padding-left: 0;
  margin-right: 0.75rem; }
  .form-check-inline .form-check-input {
    position: static;
    margin-top: 0;
    margin-right: 0.3125rem;
    margin-left: 0; }

.valid-feedback {
  display: none;
  width: 100%;
  margin-top: 0.25rem;
  font-size: 80%;
  color: #5dbf74; }

.valid-tooltip {
  position: absolute;
  top: 100%;
  z-index: 5;
  display: none;
  max-width: 100%;
  padding: 0.25rem 0.5rem;
  margin-top: .1rem;
  font-size: 0.875rem;
  line-height: 1.5;
  color: #212529;
  background-color: rgba(93, 191, 116, 0.9);
  border-radius: 4px; }

.was-validated .form-control:valid, .form-control.is-valid {
  border-color: #5dbf74;
  padding-right: calc(1.5em + 0.75rem);
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%235dbf74' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: center right calc(0.375em + 0.1875rem);
  background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem); }
  .was-validated .form-control:valid:focus, .form-control.is-valid:focus {
    border-color: #5dbf74;
    box-shadow: 0 0 0 0.2rem rgba(93, 191, 116, 0.25); }
  .was-validated .form-control:valid ~ .valid-feedback,
  .was-validated .form-control:valid ~ .valid-tooltip, .form-control.is-valid ~ .valid-feedback,
  .form-control.is-valid ~ .valid-tooltip {
    display: block; }

.was-validated textarea.form-control:valid, textarea.form-control.is-valid {
  padding-right: calc(1.5em + 0.75rem);
  background-position: top calc(0.375em + 0.1875rem) right calc(0.375em + 0.1875rem); }

.was-validated .custom-select:valid, .custom-select.is-valid {
  border-color: #5dbf74;
  padding-right: calc((1em + 0.75rem) * 3 / 4 + 1.75rem);
  background: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 5'%3e%3cpath fill='%23343a40' d='M2 0L0 2h4zm0 5L0 3h4z'/%3e%3c/svg%3e") no-repeat right 0.75rem center/8px 10px, url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%235dbf74' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e") #ffffff no-repeat center right 1.75rem/calc(0.75em + 0.375rem) calc(0.75em + 0.375rem); }
  .was-validated .custom-select:valid:focus, .custom-select.is-valid:focus {
    border-color: #5dbf74;
    box-shadow: 0 0 0 0.2rem rgba(93, 191, 116, 0.25); }
  .was-validated .custom-select:valid ~ .valid-feedback,
  .was-validated .custom-select:valid ~ .valid-tooltip, .custom-select.is-valid ~ .valid-feedback,
  .custom-select.is-valid ~ .valid-tooltip {
    display: block; }

.was-validated .form-control-file:valid ~ .valid-feedback,
.was-validated .form-control-file:valid ~ .valid-tooltip, .form-control-file.is-valid ~ .valid-feedback,
.form-control-file.is-valid ~ .valid-tooltip {
  display: block; }

.was-validated .form-check-input:valid ~ .form-check-label, .form-check-input.is-valid ~ .form-check-label {
  color: #5dbf74; }

.was-validated .form-check-input:valid ~ .valid-feedback,
.was-validated .form-check-input:valid ~ .valid-tooltip, .form-check-input.is-valid ~ .valid-feedback,
.form-check-input.is-valid ~ .valid-tooltip {
  display: block; }

.was-validated .custom-control-input:valid ~ .custom-control-label, .custom-control-input.is-valid ~ .custom-control-label {
  color: #5dbf74; }
  .was-validated .custom-control-input:valid ~ .custom-control-label::before, .custom-control-input.is-valid ~ .custom-control-label::before {
    border-color: #5dbf74; }

.was-validated .custom-control-input:valid ~ .valid-feedback,
.was-validated .custom-control-input:valid ~ .valid-tooltip, .custom-control-input.is-valid ~ .valid-feedback,
.custom-control-input.is-valid ~ .valid-tooltip {
  display: block; }

.was-validated .custom-control-input:valid:checked ~ .custom-control-label::before, .custom-control-input.is-valid:checked ~ .custom-control-label::before {
  border-color: #82cd93;
  background-color: #82cd93; }

.was-validated .custom-control-input:valid:focus ~ .custom-control-label::before, .custom-control-input.is-valid:focus ~ .custom-control-label::before {
  box-shadow: 0 0 0 0.2rem rgba(93, 191, 116, 0.25); }

.was-validated .custom-control-input:valid:focus:not(:checked) ~ .custom-control-label::before, .custom-control-input.is-valid:focus:not(:checked) ~ .custom-control-label::before {
  border-color: #5dbf74; }

.was-validated .custom-file-input:valid ~ .custom-file-label, .custom-file-input.is-valid ~ .custom-file-label {
  border-color: #5dbf74; }

.was-validated .custom-file-input:valid ~ .valid-feedback,
.was-validated .custom-file-input:valid ~ .valid-tooltip, .custom-file-input.is-valid ~ .valid-feedback,
.custom-file-input.is-valid ~ .valid-tooltip {
  display: block; }

.was-validated .custom-file-input:valid:focus ~ .custom-file-label, .custom-file-input.is-valid:focus ~ .custom-file-label {
  border-color: #5dbf74;
  box-shadow: 0 0 0 0.2rem rgba(93, 191, 116, 0.25); }

.invalid-feedback {
  display: none;
  width: 100%;
  margin-top: 0.25rem;
  font-size: 80%;
  color: #f9777f; }

.invalid-tooltip {
  position: absolute;
  top: 100%;
  z-index: 5;
  display: none;
  max-width: 100%;
  padding: 0.25rem 0.5rem;
  margin-top: .1rem;
  font-size: 0.875rem;
  line-height: 1.5;
  color: #212529;
  background-color: rgba(249, 119, 127, 0.9);
  border-radius: 4px; }

.was-validated .form-control:invalid, .form-control.is-invalid {
  border-color: #f9777f;
  padding-right: calc(1.5em + 0.75rem);
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='%23f9777f' viewBox='-2 -2 7 7'%3e%3cpath stroke='%23f9777f' d='M0 0l3 3m0-3L0 3'/%3e%3ccircle r='.5'/%3e%3ccircle cx='3' r='.5'/%3e%3ccircle cy='3' r='.5'/%3e%3ccircle cx='3' cy='3' r='.5'/%3e%3c/svg%3E");
  background-repeat: no-repeat;
  background-position: center right calc(0.375em + 0.1875rem);
  background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem); }
  .was-validated .form-control:invalid:focus, .form-control.is-invalid:focus {
    border-color: #f9777f;
    box-shadow: 0 0 0 0.2rem rgba(249, 119, 127, 0.25); }
  .was-validated .form-control:invalid ~ .invalid-feedback,
  .was-validated .form-control:invalid ~ .invalid-tooltip, .form-control.is-invalid ~ .invalid-feedback,
  .form-control.is-invalid ~ .invalid-tooltip {
    display: block; }

.was-validated textarea.form-control:invalid, textarea.form-control.is-invalid {
  padding-right: calc(1.5em + 0.75rem);
  background-position: top calc(0.375em + 0.1875rem) right calc(0.375em + 0.1875rem); }

.was-validated .custom-select:invalid, .custom-select.is-invalid {
  border-color: #f9777f;
  padding-right: calc((1em + 0.75rem) * 3 / 4 + 1.75rem);
  background: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 5'%3e%3cpath fill='%23343a40' d='M2 0L0 2h4zm0 5L0 3h4z'/%3e%3c/svg%3e") no-repeat right 0.75rem center/8px 10px, url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='%23f9777f' viewBox='-2 -2 7 7'%3e%3cpath stroke='%23f9777f' d='M0 0l3 3m0-3L0 3'/%3e%3ccircle r='.5'/%3e%3ccircle cx='3' r='.5'/%3e%3ccircle cy='3' r='.5'/%3e%3ccircle cx='3' cy='3' r='.5'/%3e%3c/svg%3E") #ffffff no-repeat center right 1.75rem/calc(0.75em + 0.375rem) calc(0.75em + 0.375rem); }
  .was-validated .custom-select:invalid:focus, .custom-select.is-invalid:focus {
    border-color: #f9777f;
    box-shadow: 0 0 0 0.2rem rgba(249, 119, 127, 0.25); }
  .was-validated .custom-select:invalid ~ .invalid-feedback,
  .was-validated .custom-select:invalid ~ .invalid-tooltip, .custom-select.is-invalid ~ .invalid-feedback,
  .custom-select.is-invalid ~ .invalid-tooltip {
    display: block; }

.was-validated .form-control-file:invalid ~ .invalid-feedback,
.was-validated .form-control-file:invalid ~ .invalid-tooltip, .form-control-file.is-invalid ~ .invalid-feedback,
.form-control-file.is-invalid ~ .invalid-tooltip {
  display: block; }

.was-validated .form-check-input:invalid ~ .form-check-label, .form-check-input.is-invalid ~ .form-check-label {
  color: #f9777f; }

.was-validated .form-check-input:invalid ~ .invalid-feedback,
.was-validated .form-check-input:invalid ~ .invalid-tooltip, .form-check-input.is-invalid ~ .invalid-feedback,
.form-check-input.is-invalid ~ .invalid-tooltip {
  display: block; }

.was-validated .custom-control-input:invalid ~ .custom-control-label, .custom-control-input.is-invalid ~ .custom-control-label {
  color: #f9777f; }
  .was-validated .custom-control-input:invalid ~ .custom-control-label::before, .custom-control-input.is-invalid ~ .custom-control-label::before {
    border-color: #f9777f; }

.was-validated .custom-control-input:invalid ~ .invalid-feedback,
.was-validated .custom-control-input:invalid ~ .invalid-tooltip, .custom-control-input.is-invalid ~ .invalid-feedback,
.custom-control-input.is-invalid ~ .invalid-tooltip {
  display: block; }

.was-validated .custom-control-input:invalid:checked ~ .custom-control-label::before, .custom-control-input.is-invalid:checked ~ .custom-control-label::before {
  border-color: #fba8ad;
  background-color: #fba8ad; }

.was-validated .custom-control-input:invalid:focus ~ .custom-control-label::before, .custom-control-input.is-invalid:focus ~ .custom-control-label::before {
  box-shadow: 0 0 0 0.2rem rgba(249, 119, 127, 0.25); }

.was-validated .custom-control-input:invalid:focus:not(:checked) ~ .custom-control-label::before, .custom-control-input.is-invalid:focus:not(:checked) ~ .custom-control-label::before {
  border-color: #f9777f; }

.was-validated .custom-file-input:invalid ~ .custom-file-label, .custom-file-input.is-invalid ~ .custom-file-label {
  border-color: #f9777f; }

.was-validated .custom-file-input:invalid ~ .invalid-feedback,
.was-validated .custom-file-input:invalid ~ .invalid-tooltip, .custom-file-input.is-invalid ~ .invalid-feedback,
.custom-file-input.is-invalid ~ .invalid-tooltip {
  display: block; }

.was-validated .custom-file-input:invalid:focus ~ .custom-file-label, .custom-file-input.is-invalid:focus ~ .custom-file-label {
  border-color: #f9777f;
  box-shadow: 0 0 0 0.2rem rgba(249, 119, 127, 0.25); }

.form-inline {
  display: flex;
  flex-flow: row wrap;
  align-items: center; }
  .form-inline .form-check {
    width: 100%; }
  @media (min-width: 576px) {
    .form-inline label {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 0; }
    .form-inline .form-group {
      display: flex;
      flex: 0 0 auto;
      flex-flow: row wrap;
      align-items: center;
      margin-bottom: 0; }
    .form-inline .form-control {
      display: inline-block;
      width: auto;
      vertical-align: middle; }
    .form-inline .form-control-plaintext {
      display: inline-block; }
    .form-inline .input-group,
    .form-inline .custom-select {
      width: auto; }
    .form-inline .form-check {
      display: flex;
      align-items: center;
      justify-content: center;
      width: auto;
      padding-left: 0; }
    .form-inline .form-check-input {
      position: relative;
      flex-shrink: 0;
      margin-top: 0;
      margin-right: 0.25rem;
      margin-left: 0; }
    .form-inline .custom-control {
      align-items: center;
      justify-content: center; }
    .form-inline .custom-control-label {
      margin-bottom: 0; } }

.badge {
  display: inline-block;
  padding: 0.25em 0.4em;
  font-size: 75%;
  font-weight: 700;
  line-height: 1;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: 4px;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out; }
  @media (prefers-reduced-motion: reduce) {
    .badge {
      transition: none; } }
  a.badge:hover, a.badge:focus {
    text-decoration: none; }
  .badge:empty {
    display: none; }

.btn .badge {
  position: relative;
  top: -1px; }

.badge-pill {
  padding-right: 0.6em;
  padding-left: 0.6em;
  border-radius: 10rem; }

.badge-primary {
  color: #ffffff;
  background-color: #ff6d41; }
  a.badge-primary:hover, a.badge-primary:focus {
    color: #ffffff;
    background-color: #ff460e; }
  a.badge-primary:focus, a.badge-primary.focus {
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(255, 109, 65, 0.5); }

.badge-secondary {
  color: #ffffff;
  background-color: #479cc8; }
  a.badge-secondary:hover, a.badge-secondary:focus {
    color: #ffffff;
    background-color: #3381a9; }
  a.badge-secondary:focus, a.badge-secondary.focus {
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(71, 156, 200, 0.5); }

.badge-success {
  color: #212529;
  background-color: #5dbf74; }
  a.badge-success:hover, a.badge-success:focus {
    color: #212529;
    background-color: #42a75a; }
  a.badge-success:focus, a.badge-success.focus {
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(93, 191, 116, 0.5); }

.badge-info {
  color: #212529;
  background-color: #68d5c8; }
  a.badge-info:hover, a.badge-info:focus {
    color: #212529;
    background-color: #40cab9; }
  a.badge-info:focus, a.badge-info.focus {
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(104, 213, 200, 0.5); }

.badge-warning {
  color: #212529;
  background-color: #e6c54f; }
  a.badge-warning:hover, a.badge-warning:focus {
    color: #212529;
    background-color: #e0b622; }
  a.badge-warning:focus, a.badge-warning.focus {
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(230, 197, 79, 0.5); }

.badge-danger {
  color: #212529;
  background-color: #f9777f; }
  a.badge-danger:hover, a.badge-danger:focus {
    color: #212529;
    background-color: #f74651; }
  a.badge-danger:focus, a.badge-danger.focus {
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(249, 119, 127, 0.5); }

.badge-light {
  color: #212529;
  background-color: #e6ecef; }
  a.badge-light:hover, a.badge-light:focus {
    color: #212529;
    background-color: #c7d4db; }
  a.badge-light:focus, a.badge-light.focus {
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(230, 236, 239, 0.5); }

.badge-dark {
  color: #ffffff;
  background-color: #343a40; }
  a.badge-dark:hover, a.badge-dark:focus {
    color: #ffffff;
    background-color: #1d2124; }
  a.badge-dark:focus, a.badge-dark.focus {
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(52, 58, 64, 0.5); }

.alert {
  position: relative;
  padding: 0.75rem 1.25rem;
  margin-bottom: 1rem;
  border: 1px solid transparent;
  border-radius: 4px; }

.alert-heading {
  color: inherit; }

.alert-link {
  font-weight: 700; }

.alert-dismissible {
  padding-right: 4rem; }
  .alert-dismissible .close {
    position: absolute;
    top: 0;
    right: 0;
    padding: 0.75rem 1.25rem;
    color: inherit; }

.alert-primary {
  color: #873b24;
  background-color: #ffe2d9;
  border-color: #ffd6ca; }
  .alert-primary hr {
    border-top-color: #ffc2b1; }
  .alert-primary .alert-link {
    color: #5f2919; }

.alert-secondary {
  color: #27536a;
  background-color: #daebf4;
  border-color: #cbe3f0; }
  .alert-secondary hr {
    border-top-color: #b7d8ea; }
  .alert-secondary .alert-link {
    color: #193645; }

.alert-success {
  color: #32653e;
  background-color: #dff2e3;
  border-color: #d2edd8; }
  .alert-success hr {
    border-top-color: #c0e6c8; }
  .alert-success .alert-link {
    color: #214329; }

.alert-info {
  color: #38716a;
  background-color: #e1f7f4;
  border-color: #d5f3f0; }
  .alert-info hr {
    border-top-color: #c1ede9; }
  .alert-info .alert-link {
    color: #274f4a; }

.alert-warning {
  color: #7a682b;
  background-color: #faf3dc;
  border-color: #f8efce; }
  .alert-warning hr {
    border-top-color: #f5e8b8; }
  .alert-warning .alert-link {
    color: #54481e; }

.alert-danger {
  color: #834044;
  background-color: #fee4e5;
  border-color: #fdd9db; }
  .alert-danger hr {
    border-top-color: #fcc1c4; }
  .alert-danger .alert-link {
    color: #612f32; }

.alert-light {
  color: #7a7d7e;
  background-color: #fafbfc;
  border-color: #f8fafb; }
  .alert-light hr {
    border-top-color: #e8eef2; }
  .alert-light .alert-link {
    color: #616364; }

.alert-dark {
  color: #1d2023;
  background-color: #d6d8d9;
  border-color: #c6c8ca; }
  .alert-dark hr {
    border-top-color: #b9bbbe; }
  .alert-dark .alert-link {
    color: #060707; }

.media {
  display: flex;
  align-items: flex-start; }

.media-body {
  flex: 1; }

.align-baseline {
  vertical-align: baseline !important; }

.align-top {
  vertical-align: top !important; }

.align-middle {
  vertical-align: middle !important; }

.align-bottom {
  vertical-align: bottom !important; }

.align-text-bottom {
  vertical-align: text-bottom !important; }

.align-text-top {
  vertical-align: text-top !important; }

.bg-primary {
  background-color: #ff6d41 !important; }

a.bg-primary:hover, a.bg-primary:focus,
button.bg-primary:hover,
button.bg-primary:focus {
  background-color: #ff460e !important; }

.bg-secondary {
  background-color: #479cc8 !important; }

a.bg-secondary:hover, a.bg-secondary:focus,
button.bg-secondary:hover,
button.bg-secondary:focus {
  background-color: #3381a9 !important; }

.bg-success {
  background-color: #5dbf74 !important; }

a.bg-success:hover, a.bg-success:focus,
button.bg-success:hover,
button.bg-success:focus {
  background-color: #42a75a !important; }

.bg-info {
  background-color: #68d5c8 !important; }

a.bg-info:hover, a.bg-info:focus,
button.bg-info:hover,
button.bg-info:focus {
  background-color: #40cab9 !important; }

.bg-warning {
  background-color: #e6c54f !important; }

a.bg-warning:hover, a.bg-warning:focus,
button.bg-warning:hover,
button.bg-warning:focus {
  background-color: #e0b622 !important; }

.bg-danger {
  background-color: #f9777f !important; }

a.bg-danger:hover, a.bg-danger:focus,
button.bg-danger:hover,
button.bg-danger:focus {
  background-color: #f74651 !important; }

.bg-light {
  background-color: #e6ecef !important; }

a.bg-light:hover, a.bg-light:focus,
button.bg-light:hover,
button.bg-light:focus {
  background-color: #c7d4db !important; }

.bg-dark {
  background-color: #343a40 !important; }

a.bg-dark:hover, a.bg-dark:focus,
button.bg-dark:hover,
button.bg-dark:focus {
  background-color: #1d2124 !important; }

.bg-white {
  background-color: #ffffff !important; }

.bg-transparent {
  background-color: transparent !important; }

.border {
  border: 1px solid #dee2e6 !important; }

.border-top {
  border-top: 1px solid #dee2e6 !important; }

.border-right {
  border-right: 1px solid #dee2e6 !important; }

.border-bottom {
  border-bottom: 1px solid #dee2e6 !important; }

.border-left {
  border-left: 1px solid #dee2e6 !important; }

.border-0 {
  border: 0 !important; }

.border-top-0 {
  border-top: 0 !important; }

.border-right-0 {
  border-right: 0 !important; }

.border-bottom-0 {
  border-bottom: 0 !important; }

.border-left-0 {
  border-left: 0 !important; }

.border-primary {
  border-color: #ff6d41 !important; }

.border-secondary {
  border-color: #479cc8 !important; }

.border-success {
  border-color: #5dbf74 !important; }

.border-info {
  border-color: #68d5c8 !important; }

.border-warning {
  border-color: #e6c54f !important; }

.border-danger {
  border-color: #f9777f !important; }

.border-light {
  border-color: #e6ecef !important; }

.border-dark {
  border-color: #343a40 !important; }

.border-white {
  border-color: #ffffff !important; }

.rounded-sm {
  border-radius: 0.2rem !important; }

.rounded {
  border-radius: 4px !important; }

.rounded-top {
  border-top-left-radius: 4px !important;
  border-top-right-radius: 4px !important; }

.rounded-right {
  border-top-right-radius: 4px !important;
  border-bottom-right-radius: 4px !important; }

.rounded-bottom {
  border-bottom-right-radius: 4px !important;
  border-bottom-left-radius: 4px !important; }

.rounded-left {
  border-top-left-radius: 4px !important;
  border-bottom-left-radius: 4px !important; }

.rounded-lg {
  border-radius: 0.3rem !important; }

.rounded-circle {
  border-radius: 50% !important; }

.rounded-pill {
  border-radius: 50rem !important; }

.rounded-0 {
  border-radius: 0 !important; }

.clearfix::after {
  display: block;
  clear: both;
  content: ""; }

.d-none {
  display: none !important; }

.d-inline {
  display: inline !important; }

.d-inline-block {
  display: inline-block !important; }

.d-block {
  display: block !important; }

.d-table {
  display: table !important; }

.d-table-row {
  display: table-row !important; }

.d-table-cell {
  display: table-cell !important; }

.d-flex {
  display: flex !important; }

.d-inline-flex {
  display: inline-flex !important; }

@media (min-width: 576px) {
  .d-sm-none {
    display: none !important; }
  .d-sm-inline {
    display: inline !important; }
  .d-sm-inline-block {
    display: inline-block !important; }
  .d-sm-block {
    display: block !important; }
  .d-sm-table {
    display: table !important; }
  .d-sm-table-row {
    display: table-row !important; }
  .d-sm-table-cell {
    display: table-cell !important; }
  .d-sm-flex {
    display: flex !important; }
  .d-sm-inline-flex {
    display: inline-flex !important; } }

@media (min-width: 768px) {
  .d-md-none {
    display: none !important; }
  .d-md-inline {
    display: inline !important; }
  .d-md-inline-block {
    display: inline-block !important; }
  .d-md-block {
    display: block !important; }
  .d-md-table {
    display: table !important; }
  .d-md-table-row {
    display: table-row !important; }
  .d-md-table-cell {
    display: table-cell !important; }
  .d-md-flex {
    display: flex !important; }
  .d-md-inline-flex {
    display: inline-flex !important; } }

@media (min-width: 992px) {
  .d-lg-none {
    display: none !important; }
  .d-lg-inline {
    display: inline !important; }
  .d-lg-inline-block {
    display: inline-block !important; }
  .d-lg-block {
    display: block !important; }
  .d-lg-table {
    display: table !important; }
  .d-lg-table-row {
    display: table-row !important; }
  .d-lg-table-cell {
    display: table-cell !important; }
  .d-lg-flex {
    display: flex !important; }
  .d-lg-inline-flex {
    display: inline-flex !important; } }

@media (min-width: 1200px) {
  .d-xl-none {
    display: none !important; }
  .d-xl-inline {
    display: inline !important; }
  .d-xl-inline-block {
    display: inline-block !important; }
  .d-xl-block {
    display: block !important; }
  .d-xl-table {
    display: table !important; }
  .d-xl-table-row {
    display: table-row !important; }
  .d-xl-table-cell {
    display: table-cell !important; }
  .d-xl-flex {
    display: flex !important; }
  .d-xl-inline-flex {
    display: inline-flex !important; } }

@media print {
  .d-print-none {
    display: none !important; }
  .d-print-inline {
    display: inline !important; }
  .d-print-inline-block {
    display: inline-block !important; }
  .d-print-block {
    display: block !important; }
  .d-print-table {
    display: table !important; }
  .d-print-table-row {
    display: table-row !important; }
  .d-print-table-cell {
    display: table-cell !important; }
  .d-print-flex {
    display: flex !important; }
  .d-print-inline-flex {
    display: inline-flex !important; } }

.embed-responsive {
  position: relative;
  display: block;
  width: 100%;
  padding: 0;
  overflow: hidden; }
  .embed-responsive::before {
    display: block;
    content: ""; }
  .embed-responsive .embed-responsive-item,
  .embed-responsive iframe,
  .embed-responsive embed,
  .embed-responsive object,
  .embed-responsive video {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: 0; }

.embed-responsive-21by9::before {
  padding-top: 42.85714%; }

.embed-responsive-16by9::before {
  padding-top: 56.25%; }

.embed-responsive-4by3::before {
  padding-top: 75%; }

.embed-responsive-1by1::before {
  padding-top: 100%; }

.flex-row {
  flex-direction: row !important; }

.flex-column {
  flex-direction: column !important; }

.flex-row-reverse {
  flex-direction: row-reverse !important; }

.flex-column-reverse {
  flex-direction: column-reverse !important; }

.flex-wrap {
  flex-wrap: wrap !important; }

.flex-nowrap {
  flex-wrap: nowrap !important; }

.flex-wrap-reverse {
  flex-wrap: wrap-reverse !important; }

.flex-fill {
  flex: 1 1 auto !important; }

.flex-grow-0 {
  flex-grow: 0 !important; }

.flex-grow-1 {
  flex-grow: 1 !important; }

.flex-shrink-0 {
  flex-shrink: 0 !important; }

.flex-shrink-1 {
  flex-shrink: 1 !important; }

.justify-content-start {
  justify-content: flex-start !important; }

.justify-content-end {
  justify-content: flex-end !important; }

.justify-content-center {
  justify-content: center !important; }

.justify-content-between {
  justify-content: space-between !important; }

.justify-content-around {
  justify-content: space-around !important; }

.align-items-start {
  align-items: flex-start !important; }

.align-items-end {
  align-items: flex-end !important; }

.align-items-center {
  align-items: center !important; }

.align-items-baseline {
  align-items: baseline !important; }

.align-items-stretch {
  align-items: stretch !important; }

.align-content-start {
  align-content: flex-start !important; }

.align-content-end {
  align-content: flex-end !important; }

.align-content-center {
  align-content: center !important; }

.align-content-between {
  align-content: space-between !important; }

.align-content-around {
  align-content: space-around !important; }

.align-content-stretch {
  align-content: stretch !important; }

.align-self-auto {
  align-self: auto !important; }

.align-self-start {
  align-self: flex-start !important; }

.align-self-end {
  align-self: flex-end !important; }

.align-self-center {
  align-self: center !important; }

.align-self-baseline {
  align-self: baseline !important; }

.align-self-stretch {
  align-self: stretch !important; }

@media (min-width: 576px) {
  .flex-sm-row {
    flex-direction: row !important; }
  .flex-sm-column {
    flex-direction: column !important; }
  .flex-sm-row-reverse {
    flex-direction: row-reverse !important; }
  .flex-sm-column-reverse {
    flex-direction: column-reverse !important; }
  .flex-sm-wrap {
    flex-wrap: wrap !important; }
  .flex-sm-nowrap {
    flex-wrap: nowrap !important; }
  .flex-sm-wrap-reverse {
    flex-wrap: wrap-reverse !important; }
  .flex-sm-fill {
    flex: 1 1 auto !important; }
  .flex-sm-grow-0 {
    flex-grow: 0 !important; }
  .flex-sm-grow-1 {
    flex-grow: 1 !important; }
  .flex-sm-shrink-0 {
    flex-shrink: 0 !important; }
  .flex-sm-shrink-1 {
    flex-shrink: 1 !important; }
  .justify-content-sm-start {
    justify-content: flex-start !important; }
  .justify-content-sm-end {
    justify-content: flex-end !important; }
  .justify-content-sm-center {
    justify-content: center !important; }
  .justify-content-sm-between {
    justify-content: space-between !important; }
  .justify-content-sm-around {
    justify-content: space-around !important; }
  .align-items-sm-start {
    align-items: flex-start !important; }
  .align-items-sm-end {
    align-items: flex-end !important; }
  .align-items-sm-center {
    align-items: center !important; }
  .align-items-sm-baseline {
    align-items: baseline !important; }
  .align-items-sm-stretch {
    align-items: stretch !important; }
  .align-content-sm-start {
    align-content: flex-start !important; }
  .align-content-sm-end {
    align-content: flex-end !important; }
  .align-content-sm-center {
    align-content: center !important; }
  .align-content-sm-between {
    align-content: space-between !important; }
  .align-content-sm-around {
    align-content: space-around !important; }
  .align-content-sm-stretch {
    align-content: stretch !important; }
  .align-self-sm-auto {
    align-self: auto !important; }
  .align-self-sm-start {
    align-self: flex-start !important; }
  .align-self-sm-end {
    align-self: flex-end !important; }
  .align-self-sm-center {
    align-self: center !important; }
  .align-self-sm-baseline {
    align-self: baseline !important; }
  .align-self-sm-stretch {
    align-self: stretch !important; } }

@media (min-width: 768px) {
  .flex-md-row {
    flex-direction: row !important; }
  .flex-md-column {
    flex-direction: column !important; }
  .flex-md-row-reverse {
    flex-direction: row-reverse !important; }
  .flex-md-column-reverse {
    flex-direction: column-reverse !important; }
  .flex-md-wrap {
    flex-wrap: wrap !important; }
  .flex-md-nowrap {
    flex-wrap: nowrap !important; }
  .flex-md-wrap-reverse {
    flex-wrap: wrap-reverse !important; }
  .flex-md-fill {
    flex: 1 1 auto !important; }
  .flex-md-grow-0 {
    flex-grow: 0 !important; }
  .flex-md-grow-1 {
    flex-grow: 1 !important; }
  .flex-md-shrink-0 {
    flex-shrink: 0 !important; }
  .flex-md-shrink-1 {
    flex-shrink: 1 !important; }
  .justify-content-md-start {
    justify-content: flex-start !important; }
  .justify-content-md-end {
    justify-content: flex-end !important; }
  .justify-content-md-center {
    justify-content: center !important; }
  .justify-content-md-between {
    justify-content: space-between !important; }
  .justify-content-md-around {
    justify-content: space-around !important; }
  .align-items-md-start {
    align-items: flex-start !important; }
  .align-items-md-end {
    align-items: flex-end !important; }
  .align-items-md-center {
    align-items: center !important; }
  .align-items-md-baseline {
    align-items: baseline !important; }
  .align-items-md-stretch {
    align-items: stretch !important; }
  .align-content-md-start {
    align-content: flex-start !important; }
  .align-content-md-end {
    align-content: flex-end !important; }
  .align-content-md-center {
    align-content: center !important; }
  .align-content-md-between {
    align-content: space-between !important; }
  .align-content-md-around {
    align-content: space-around !important; }
  .align-content-md-stretch {
    align-content: stretch !important; }
  .align-self-md-auto {
    align-self: auto !important; }
  .align-self-md-start {
    align-self: flex-start !important; }
  .align-self-md-end {
    align-self: flex-end !important; }
  .align-self-md-center {
    align-self: center !important; }
  .align-self-md-baseline {
    align-self: baseline !important; }
  .align-self-md-stretch {
    align-self: stretch !important; } }

@media (min-width: 992px) {
  .flex-lg-row {
    flex-direction: row !important; }
  .flex-lg-column {
    flex-direction: column !important; }
  .flex-lg-row-reverse {
    flex-direction: row-reverse !important; }
  .flex-lg-column-reverse {
    flex-direction: column-reverse !important; }
  .flex-lg-wrap {
    flex-wrap: wrap !important; }
  .flex-lg-nowrap {
    flex-wrap: nowrap !important; }
  .flex-lg-wrap-reverse {
    flex-wrap: wrap-reverse !important; }
  .flex-lg-fill {
    flex: 1 1 auto !important; }
  .flex-lg-grow-0 {
    flex-grow: 0 !important; }
  .flex-lg-grow-1 {
    flex-grow: 1 !important; }
  .flex-lg-shrink-0 {
    flex-shrink: 0 !important; }
  .flex-lg-shrink-1 {
    flex-shrink: 1 !important; }
  .justify-content-lg-start {
    justify-content: flex-start !important; }
  .justify-content-lg-end {
    justify-content: flex-end !important; }
  .justify-content-lg-center {
    justify-content: center !important; }
  .justify-content-lg-between {
    justify-content: space-between !important; }
  .justify-content-lg-around {
    justify-content: space-around !important; }
  .align-items-lg-start {
    align-items: flex-start !important; }
  .align-items-lg-end {
    align-items: flex-end !important; }
  .align-items-lg-center {
    align-items: center !important; }
  .align-items-lg-baseline {
    align-items: baseline !important; }
  .align-items-lg-stretch {
    align-items: stretch !important; }
  .align-content-lg-start {
    align-content: flex-start !important; }
  .align-content-lg-end {
    align-content: flex-end !important; }
  .align-content-lg-center {
    align-content: center !important; }
  .align-content-lg-between {
    align-content: space-between !important; }
  .align-content-lg-around {
    align-content: space-around !important; }
  .align-content-lg-stretch {
    align-content: stretch !important; }
  .align-self-lg-auto {
    align-self: auto !important; }
  .align-self-lg-start {
    align-self: flex-start !important; }
  .align-self-lg-end {
    align-self: flex-end !important; }
  .align-self-lg-center {
    align-self: center !important; }
  .align-self-lg-baseline {
    align-self: baseline !important; }
  .align-self-lg-stretch {
    align-self: stretch !important; } }

@media (min-width: 1200px) {
  .flex-xl-row {
    flex-direction: row !important; }
  .flex-xl-column {
    flex-direction: column !important; }
  .flex-xl-row-reverse {
    flex-direction: row-reverse !important; }
  .flex-xl-column-reverse {
    flex-direction: column-reverse !important; }
  .flex-xl-wrap {
    flex-wrap: wrap !important; }
  .flex-xl-nowrap {
    flex-wrap: nowrap !important; }
  .flex-xl-wrap-reverse {
    flex-wrap: wrap-reverse !important; }
  .flex-xl-fill {
    flex: 1 1 auto !important; }
  .flex-xl-grow-0 {
    flex-grow: 0 !important; }
  .flex-xl-grow-1 {
    flex-grow: 1 !important; }
  .flex-xl-shrink-0 {
    flex-shrink: 0 !important; }
  .flex-xl-shrink-1 {
    flex-shrink: 1 !important; }
  .justify-content-xl-start {
    justify-content: flex-start !important; }
  .justify-content-xl-end {
    justify-content: flex-end !important; }
  .justify-content-xl-center {
    justify-content: center !important; }
  .justify-content-xl-between {
    justify-content: space-between !important; }
  .justify-content-xl-around {
    justify-content: space-around !important; }
  .align-items-xl-start {
    align-items: flex-start !important; }
  .align-items-xl-end {
    align-items: flex-end !important; }
  .align-items-xl-center {
    align-items: center !important; }
  .align-items-xl-baseline {
    align-items: baseline !important; }
  .align-items-xl-stretch {
    align-items: stretch !important; }
  .align-content-xl-start {
    align-content: flex-start !important; }
  .align-content-xl-end {
    align-content: flex-end !important; }
  .align-content-xl-center {
    align-content: center !important; }
  .align-content-xl-between {
    align-content: space-between !important; }
  .align-content-xl-around {
    align-content: space-around !important; }
  .align-content-xl-stretch {
    align-content: stretch !important; }
  .align-self-xl-auto {
    align-self: auto !important; }
  .align-self-xl-start {
    align-self: flex-start !important; }
  .align-self-xl-end {
    align-self: flex-end !important; }
  .align-self-xl-center {
    align-self: center !important; }
  .align-self-xl-baseline {
    align-self: baseline !important; }
  .align-self-xl-stretch {
    align-self: stretch !important; } }

.float-left {
  float: left !important; }

.float-right {
  float: right !important; }

.float-none {
  float: none !important; }

@media (min-width: 576px) {
  .float-sm-left {
    float: left !important; }
  .float-sm-right {
    float: right !important; }
  .float-sm-none {
    float: none !important; } }

@media (min-width: 768px) {
  .float-md-left {
    float: left !important; }
  .float-md-right {
    float: right !important; }
  .float-md-none {
    float: none !important; } }

@media (min-width: 992px) {
  .float-lg-left {
    float: left !important; }
  .float-lg-right {
    float: right !important; }
  .float-lg-none {
    float: none !important; } }

@media (min-width: 1200px) {
  .float-xl-left {
    float: left !important; }
  .float-xl-right {
    float: right !important; }
  .float-xl-none {
    float: none !important; } }

.overflow-auto {
  overflow: auto !important; }

.overflow-hidden {
  overflow: hidden !important; }

.position-static {
  position: static !important; }

.position-relative {
  position: relative !important; }

.position-absolute {
  position: absolute !important; }

.position-fixed {
  position: fixed !important; }

.position-sticky {
  position: sticky !important; }

.fixed-top {
  position: fixed;
  top: 0;
  right: 0;
  left: 0;
  z-index: 1030; }

.fixed-bottom {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1030; }

@supports (position: sticky) {
  .sticky-top {
    position: sticky;
    top: 0;
    z-index: 1020; } }

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0; }

.sr-only-focusable:active, .sr-only-focusable:focus {
  position: static;
  width: auto;
  height: auto;
  overflow: visible;
  clip: auto;
  white-space: normal; }

.shadow-sm {
  box-shadow: 0 0.125rem 0.25rem rgba(4, 4, 4, 0.075) !important; }

.shadow {
  box-shadow: 0 0.5rem 1rem rgba(4, 4, 4, 0.15) !important; }

.shadow-lg {
  box-shadow: 0 1rem 3rem rgba(4, 4, 4, 0.175) !important; }

.shadow-none {
  box-shadow: none !important; }

.w-25 {
  width: 25% !important; }

.w-50 {
  width: 50% !important; }

.w-75 {
  width: 75% !important; }

.w-100 {
  width: 100% !important; }

.w-auto {
  width: auto !important; }

.h-25 {
  height: 25% !important; }

.h-50 {
  height: 50% !important; }

.h-75 {
  height: 75% !important; }

.h-100 {
  height: 100% !important; }

.h-auto {
  height: auto !important; }

.mw-100 {
  max-width: 100% !important; }

.mh-100 {
  max-height: 100% !important; }

.min-vw-100 {
  min-width: 100vw !important; }

.min-vh-100 {
  min-height: 100vh !important; }

.vw-100 {
  width: 100vw !important; }

.vh-100 {
  height: 100vh !important; }

.stretched-link::after {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1;
  pointer-events: auto;
  content: "";
  background-color: rgba(0, 0, 0, 0); }

.m-0 {
  margin: 0 !important; }

.mt-0,
.my-0 {
  margin-top: 0 !important; }

.mr-0,
.mx-0 {
  margin-right: 0 !important; }

.mb-0,
.my-0 {
  margin-bottom: 0 !important; }

.ml-0,
.mx-0 {
  margin-left: 0 !important; }

.m-1 {
  margin: 0.25rem !important; }

.mt-1,
.my-1 {
  margin-top: 0.25rem !important; }

.mr-1,
.mx-1 {
  margin-right: 0.25rem !important; }

.mb-1,
.my-1 {
  margin-bottom: 0.25rem !important; }

.ml-1,
.mx-1 {
  margin-left: 0.25rem !important; }

.m-2 {
  margin: 0.5rem !important; }

.mt-2,
.my-2 {
  margin-top: 0.5rem !important; }

.mr-2,
.mx-2 {
  margin-right: 0.5rem !important; }

.mb-2,
.my-2 {
  margin-bottom: 0.5rem !important; }

.ml-2,
.mx-2 {
  margin-left: 0.5rem !important; }

.m-3 {
  margin: 1rem !important; }

.mt-3,
.my-3 {
  margin-top: 1rem !important; }

.mr-3,
.mx-3 {
  margin-right: 1rem !important; }

.mb-3,
.my-3 {
  margin-bottom: 1rem !important; }

.ml-3,
.mx-3 {
  margin-left: 1rem !important; }

.m-4 {
  margin: 1.5rem !important; }

.mt-4,
.my-4 {
  margin-top: 1.5rem !important; }

.mr-4,
.mx-4 {
  margin-right: 1.5rem !important; }

.mb-4,
.my-4 {
  margin-bottom: 1.5rem !important; }

.ml-4,
.mx-4 {
  margin-left: 1.5rem !important; }

.m-5 {
  margin: 3rem !important; }

.mt-5,
.my-5 {
  margin-top: 3rem !important; }

.mr-5,
.mx-5 {
  margin-right: 3rem !important; }

.mb-5,
.my-5 {
  margin-bottom: 3rem !important; }

.ml-5,
.mx-5 {
  margin-left: 3rem !important; }

.p-0 {
  padding: 0 !important; }

.pt-0,
.py-0 {
  padding-top: 0 !important; }

.pr-0,
.px-0 {
  padding-right: 0 !important; }

.pb-0,
.py-0 {
  padding-bottom: 0 !important; }

.pl-0,
.px-0 {
  padding-left: 0 !important; }

.p-1 {
  padding: 0.25rem !important; }

.pt-1,
.py-1 {
  padding-top: 0.25rem !important; }

.pr-1,
.px-1 {
  padding-right: 0.25rem !important; }

.pb-1,
.py-1 {
  padding-bottom: 0.25rem !important; }

.pl-1,
.px-1 {
  padding-left: 0.25rem !important; }

.p-2 {
  padding: 0.5rem !important; }

.pt-2,
.py-2 {
  padding-top: 0.5rem !important; }

.pr-2,
.px-2 {
  padding-right: 0.5rem !important; }

.pb-2,
.py-2 {
  padding-bottom: 0.5rem !important; }

.pl-2,
.px-2 {
  padding-left: 0.5rem !important; }

.p-3 {
  padding: 1rem !important; }

.pt-3,
.py-3 {
  padding-top: 1rem !important; }

.pr-3,
.px-3 {
  padding-right: 1rem !important; }

.pb-3,
.py-3 {
  padding-bottom: 1rem !important; }

.pl-3,
.px-3 {
  padding-left: 1rem !important; }

.p-4 {
  padding: 1.5rem !important; }

.pt-4,
.py-4 {
  padding-top: 1.5rem !important; }

.pr-4,
.px-4 {
  padding-right: 1.5rem !important; }

.pb-4,
.py-4 {
  padding-bottom: 1.5rem !important; }

.pl-4,
.px-4 {
  padding-left: 1.5rem !important; }

.p-5 {
  padding: 3rem !important; }

.pt-5,
.py-5 {
  padding-top: 3rem !important; }

.pr-5,
.px-5 {
  padding-right: 3rem !important; }

.pb-5,
.py-5 {
  padding-bottom: 3rem !important; }

.pl-5,
.px-5 {
  padding-left: 3rem !important; }

.m-n1 {
  margin: -0.25rem !important; }

.mt-n1,
.my-n1 {
  margin-top: -0.25rem !important; }

.mr-n1,
.mx-n1 {
  margin-right: -0.25rem !important; }

.mb-n1,
.my-n1 {
  margin-bottom: -0.25rem !important; }

.ml-n1,
.mx-n1 {
  margin-left: -0.25rem !important; }

.m-n2 {
  margin: -0.5rem !important; }

.mt-n2,
.my-n2 {
  margin-top: -0.5rem !important; }

.mr-n2,
.mx-n2 {
  margin-right: -0.5rem !important; }

.mb-n2,
.my-n2 {
  margin-bottom: -0.5rem !important; }

.ml-n2,
.mx-n2 {
  margin-left: -0.5rem !important; }

.m-n3 {
  margin: -1rem !important; }

.mt-n3,
.my-n3 {
  margin-top: -1rem !important; }

.mr-n3,
.mx-n3 {
  margin-right: -1rem !important; }

.mb-n3,
.my-n3 {
  margin-bottom: -1rem !important; }

.ml-n3,
.mx-n3 {
  margin-left: -1rem !important; }

.m-n4 {
  margin: -1.5rem !important; }

.mt-n4,
.my-n4 {
  margin-top: -1.5rem !important; }

.mr-n4,
.mx-n4 {
  margin-right: -1.5rem !important; }

.mb-n4,
.my-n4 {
  margin-bottom: -1.5rem !important; }

.ml-n4,
.mx-n4 {
  margin-left: -1.5rem !important; }

.m-n5 {
  margin: -3rem !important; }

.mt-n5,
.my-n5 {
  margin-top: -3rem !important; }

.mr-n5,
.mx-n5 {
  margin-right: -3rem !important; }

.mb-n5,
.my-n5 {
  margin-bottom: -3rem !important; }

.ml-n5,
.mx-n5 {
  margin-left: -3rem !important; }

.m-auto {
  margin: auto !important; }

.mt-auto,
.my-auto {
  margin-top: auto !important; }

.mr-auto,
.mx-auto {
  margin-right: auto !important; }

.mb-auto,
.my-auto {
  margin-bottom: auto !important; }

.ml-auto,
.mx-auto {
  margin-left: auto !important; }

@media (min-width: 576px) {
  .m-sm-0 {
    margin: 0 !important; }
  .mt-sm-0,
  .my-sm-0 {
    margin-top: 0 !important; }
  .mr-sm-0,
  .mx-sm-0 {
    margin-right: 0 !important; }
  .mb-sm-0,
  .my-sm-0 {
    margin-bottom: 0 !important; }
  .ml-sm-0,
  .mx-sm-0 {
    margin-left: 0 !important; }
  .m-sm-1 {
    margin: 0.25rem !important; }
  .mt-sm-1,
  .my-sm-1 {
    margin-top: 0.25rem !important; }
  .mr-sm-1,
  .mx-sm-1 {
    margin-right: 0.25rem !important; }
  .mb-sm-1,
  .my-sm-1 {
    margin-bottom: 0.25rem !important; }
  .ml-sm-1,
  .mx-sm-1 {
    margin-left: 0.25rem !important; }
  .m-sm-2 {
    margin: 0.5rem !important; }
  .mt-sm-2,
  .my-sm-2 {
    margin-top: 0.5rem !important; }
  .mr-sm-2,
  .mx-sm-2 {
    margin-right: 0.5rem !important; }
  .mb-sm-2,
  .my-sm-2 {
    margin-bottom: 0.5rem !important; }
  .ml-sm-2,
  .mx-sm-2 {
    margin-left: 0.5rem !important; }
  .m-sm-3 {
    margin: 1rem !important; }
  .mt-sm-3,
  .my-sm-3 {
    margin-top: 1rem !important; }
  .mr-sm-3,
  .mx-sm-3 {
    margin-right: 1rem !important; }
  .mb-sm-3,
  .my-sm-3 {
    margin-bottom: 1rem !important; }
  .ml-sm-3,
  .mx-sm-3 {
    margin-left: 1rem !important; }
  .m-sm-4 {
    margin: 1.5rem !important; }
  .mt-sm-4,
  .my-sm-4 {
    margin-top: 1.5rem !important; }
  .mr-sm-4,
  .mx-sm-4 {
    margin-right: 1.5rem !important; }
  .mb-sm-4,
  .my-sm-4 {
    margin-bottom: 1.5rem !important; }
  .ml-sm-4,
  .mx-sm-4 {
    margin-left: 1.5rem !important; }
  .m-sm-5 {
    margin: 3rem !important; }
  .mt-sm-5,
  .my-sm-5 {
    margin-top: 3rem !important; }
  .mr-sm-5,
  .mx-sm-5 {
    margin-right: 3rem !important; }
  .mb-sm-5,
  .my-sm-5 {
    margin-bottom: 3rem !important; }
  .ml-sm-5,
  .mx-sm-5 {
    margin-left: 3rem !important; }
  .p-sm-0 {
    padding: 0 !important; }
  .pt-sm-0,
  .py-sm-0 {
    padding-top: 0 !important; }
  .pr-sm-0,
  .px-sm-0 {
    padding-right: 0 !important; }
  .pb-sm-0,
  .py-sm-0 {
    padding-bottom: 0 !important; }
  .pl-sm-0,
  .px-sm-0 {
    padding-left: 0 !important; }
  .p-sm-1 {
    padding: 0.25rem !important; }
  .pt-sm-1,
  .py-sm-1 {
    padding-top: 0.25rem !important; }
  .pr-sm-1,
  .px-sm-1 {
    padding-right: 0.25rem !important; }
  .pb-sm-1,
  .py-sm-1 {
    padding-bottom: 0.25rem !important; }
  .pl-sm-1,
  .px-sm-1 {
    padding-left: 0.25rem !important; }
  .p-sm-2 {
    padding: 0.5rem !important; }
  .pt-sm-2,
  .py-sm-2 {
    padding-top: 0.5rem !important; }
  .pr-sm-2,
  .px-sm-2 {
    padding-right: 0.5rem !important; }
  .pb-sm-2,
  .py-sm-2 {
    padding-bottom: 0.5rem !important; }
  .pl-sm-2,
  .px-sm-2 {
    padding-left: 0.5rem !important; }
  .p-sm-3 {
    padding: 1rem !important; }
  .pt-sm-3,
  .py-sm-3 {
    padding-top: 1rem !important; }
  .pr-sm-3,
  .px-sm-3 {
    padding-right: 1rem !important; }
  .pb-sm-3,
  .py-sm-3 {
    padding-bottom: 1rem !important; }
  .pl-sm-3,
  .px-sm-3 {
    padding-left: 1rem !important; }
  .p-sm-4 {
    padding: 1.5rem !important; }
  .pt-sm-4,
  .py-sm-4 {
    padding-top: 1.5rem !important; }
  .pr-sm-4,
  .px-sm-4 {
    padding-right: 1.5rem !important; }
  .pb-sm-4,
  .py-sm-4 {
    padding-bottom: 1.5rem !important; }
  .pl-sm-4,
  .px-sm-4 {
    padding-left: 1.5rem !important; }
  .p-sm-5 {
    padding: 3rem !important; }
  .pt-sm-5,
  .py-sm-5 {
    padding-top: 3rem !important; }
  .pr-sm-5,
  .px-sm-5 {
    padding-right: 3rem !important; }
  .pb-sm-5,
  .py-sm-5 {
    padding-bottom: 3rem !important; }
  .pl-sm-5,
  .px-sm-5 {
    padding-left: 3rem !important; }
  .m-sm-n1 {
    margin: -0.25rem !important; }
  .mt-sm-n1,
  .my-sm-n1 {
    margin-top: -0.25rem !important; }
  .mr-sm-n1,
  .mx-sm-n1 {
    margin-right: -0.25rem !important; }
  .mb-sm-n1,
  .my-sm-n1 {
    margin-bottom: -0.25rem !important; }
  .ml-sm-n1,
  .mx-sm-n1 {
    margin-left: -0.25rem !important; }
  .m-sm-n2 {
    margin: -0.5rem !important; }
  .mt-sm-n2,
  .my-sm-n2 {
    margin-top: -0.5rem !important; }
  .mr-sm-n2,
  .mx-sm-n2 {
    margin-right: -0.5rem !important; }
  .mb-sm-n2,
  .my-sm-n2 {
    margin-bottom: -0.5rem !important; }
  .ml-sm-n2,
  .mx-sm-n2 {
    margin-left: -0.5rem !important; }
  .m-sm-n3 {
    margin: -1rem !important; }
  .mt-sm-n3,
  .my-sm-n3 {
    margin-top: -1rem !important; }
  .mr-sm-n3,
  .mx-sm-n3 {
    margin-right: -1rem !important; }
  .mb-sm-n3,
  .my-sm-n3 {
    margin-bottom: -1rem !important; }
  .ml-sm-n3,
  .mx-sm-n3 {
    margin-left: -1rem !important; }
  .m-sm-n4 {
    margin: -1.5rem !important; }
  .mt-sm-n4,
  .my-sm-n4 {
    margin-top: -1.5rem !important; }
  .mr-sm-n4,
  .mx-sm-n4 {
    margin-right: -1.5rem !important; }
  .mb-sm-n4,
  .my-sm-n4 {
    margin-bottom: -1.5rem !important; }
  .ml-sm-n4,
  .mx-sm-n4 {
    margin-left: -1.5rem !important; }
  .m-sm-n5 {
    margin: -3rem !important; }
  .mt-sm-n5,
  .my-sm-n5 {
    margin-top: -3rem !important; }
  .mr-sm-n5,
  .mx-sm-n5 {
    margin-right: -3rem !important; }
  .mb-sm-n5,
  .my-sm-n5 {
    margin-bottom: -3rem !important; }
  .ml-sm-n5,
  .mx-sm-n5 {
    margin-left: -3rem !important; }
  .m-sm-auto {
    margin: auto !important; }
  .mt-sm-auto,
  .my-sm-auto {
    margin-top: auto !important; }
  .mr-sm-auto,
  .mx-sm-auto {
    margin-right: auto !important; }
  .mb-sm-auto,
  .my-sm-auto {
    margin-bottom: auto !important; }
  .ml-sm-auto,
  .mx-sm-auto {
    margin-left: auto !important; } }

@media (min-width: 768px) {
  .m-md-0 {
    margin: 0 !important; }
  .mt-md-0,
  .my-md-0 {
    margin-top: 0 !important; }
  .mr-md-0,
  .mx-md-0 {
    margin-right: 0 !important; }
  .mb-md-0,
  .my-md-0 {
    margin-bottom: 0 !important; }
  .ml-md-0,
  .mx-md-0 {
    margin-left: 0 !important; }
  .m-md-1 {
    margin: 0.25rem !important; }
  .mt-md-1,
  .my-md-1 {
    margin-top: 0.25rem !important; }
  .mr-md-1,
  .mx-md-1 {
    margin-right: 0.25rem !important; }
  .mb-md-1,
  .my-md-1 {
    margin-bottom: 0.25rem !important; }
  .ml-md-1,
  .mx-md-1 {
    margin-left: 0.25rem !important; }
  .m-md-2 {
    margin: 0.5rem !important; }
  .mt-md-2,
  .my-md-2 {
    margin-top: 0.5rem !important; }
  .mr-md-2,
  .mx-md-2 {
    margin-right: 0.5rem !important; }
  .mb-md-2,
  .my-md-2 {
    margin-bottom: 0.5rem !important; }
  .ml-md-2,
  .mx-md-2 {
    margin-left: 0.5rem !important; }
  .m-md-3 {
    margin: 1rem !important; }
  .mt-md-3,
  .my-md-3 {
    margin-top: 1rem !important; }
  .mr-md-3,
  .mx-md-3 {
    margin-right: 1rem !important; }
  .mb-md-3,
  .my-md-3 {
    margin-bottom: 1rem !important; }
  .ml-md-3,
  .mx-md-3 {
    margin-left: 1rem !important; }
  .m-md-4 {
    margin: 1.5rem !important; }
  .mt-md-4,
  .my-md-4 {
    margin-top: 1.5rem !important; }
  .mr-md-4,
  .mx-md-4 {
    margin-right: 1.5rem !important; }
  .mb-md-4,
  .my-md-4 {
    margin-bottom: 1.5rem !important; }
  .ml-md-4,
  .mx-md-4 {
    margin-left: 1.5rem !important; }
  .m-md-5 {
    margin: 3rem !important; }
  .mt-md-5,
  .my-md-5 {
    margin-top: 3rem !important; }
  .mr-md-5,
  .mx-md-5 {
    margin-right: 3rem !important; }
  .mb-md-5,
  .my-md-5 {
    margin-bottom: 3rem !important; }
  .ml-md-5,
  .mx-md-5 {
    margin-left: 3rem !important; }
  .p-md-0 {
    padding: 0 !important; }
  .pt-md-0,
  .py-md-0 {
    padding-top: 0 !important; }
  .pr-md-0,
  .px-md-0 {
    padding-right: 0 !important; }
  .pb-md-0,
  .py-md-0 {
    padding-bottom: 0 !important; }
  .pl-md-0,
  .px-md-0 {
    padding-left: 0 !important; }
  .p-md-1 {
    padding: 0.25rem !important; }
  .pt-md-1,
  .py-md-1 {
    padding-top: 0.25rem !important; }
  .pr-md-1,
  .px-md-1 {
    padding-right: 0.25rem !important; }
  .pb-md-1,
  .py-md-1 {
    padding-bottom: 0.25rem !important; }
  .pl-md-1,
  .px-md-1 {
    padding-left: 0.25rem !important; }
  .p-md-2 {
    padding: 0.5rem !important; }
  .pt-md-2,
  .py-md-2 {
    padding-top: 0.5rem !important; }
  .pr-md-2,
  .px-md-2 {
    padding-right: 0.5rem !important; }
  .pb-md-2,
  .py-md-2 {
    padding-bottom: 0.5rem !important; }
  .pl-md-2,
  .px-md-2 {
    padding-left: 0.5rem !important; }
  .p-md-3 {
    padding: 1rem !important; }
  .pt-md-3,
  .py-md-3 {
    padding-top: 1rem !important; }
  .pr-md-3,
  .px-md-3 {
    padding-right: 1rem !important; }
  .pb-md-3,
  .py-md-3 {
    padding-bottom: 1rem !important; }
  .pl-md-3,
  .px-md-3 {
    padding-left: 1rem !important; }
  .p-md-4 {
    padding: 1.5rem !important; }
  .pt-md-4,
  .py-md-4 {
    padding-top: 1.5rem !important; }
  .pr-md-4,
  .px-md-4 {
    padding-right: 1.5rem !important; }
  .pb-md-4,
  .py-md-4 {
    padding-bottom: 1.5rem !important; }
  .pl-md-4,
  .px-md-4 {
    padding-left: 1.5rem !important; }
  .p-md-5 {
    padding: 3rem !important; }
  .pt-md-5,
  .py-md-5 {
    padding-top: 3rem !important; }
  .pr-md-5,
  .px-md-5 {
    padding-right: 3rem !important; }
  .pb-md-5,
  .py-md-5 {
    padding-bottom: 3rem !important; }
  .pl-md-5,
  .px-md-5 {
    padding-left: 3rem !important; }
  .m-md-n1 {
    margin: -0.25rem !important; }
  .mt-md-n1,
  .my-md-n1 {
    margin-top: -0.25rem !important; }
  .mr-md-n1,
  .mx-md-n1 {
    margin-right: -0.25rem !important; }
  .mb-md-n1,
  .my-md-n1 {
    margin-bottom: -0.25rem !important; }
  .ml-md-n1,
  .mx-md-n1 {
    margin-left: -0.25rem !important; }
  .m-md-n2 {
    margin: -0.5rem !important; }
  .mt-md-n2,
  .my-md-n2 {
    margin-top: -0.5rem !important; }
  .mr-md-n2,
  .mx-md-n2 {
    margin-right: -0.5rem !important; }
  .mb-md-n2,
  .my-md-n2 {
    margin-bottom: -0.5rem !important; }
  .ml-md-n2,
  .mx-md-n2 {
    margin-left: -0.5rem !important; }
  .m-md-n3 {
    margin: -1rem !important; }
  .mt-md-n3,
  .my-md-n3 {
    margin-top: -1rem !important; }
  .mr-md-n3,
  .mx-md-n3 {
    margin-right: -1rem !important; }
  .mb-md-n3,
  .my-md-n3 {
    margin-bottom: -1rem !important; }
  .ml-md-n3,
  .mx-md-n3 {
    margin-left: -1rem !important; }
  .m-md-n4 {
    margin: -1.5rem !important; }
  .mt-md-n4,
  .my-md-n4 {
    margin-top: -1.5rem !important; }
  .mr-md-n4,
  .mx-md-n4 {
    margin-right: -1.5rem !important; }
  .mb-md-n4,
  .my-md-n4 {
    margin-bottom: -1.5rem !important; }
  .ml-md-n4,
  .mx-md-n4 {
    margin-left: -1.5rem !important; }
  .m-md-n5 {
    margin: -3rem !important; }
  .mt-md-n5,
  .my-md-n5 {
    margin-top: -3rem !important; }
  .mr-md-n5,
  .mx-md-n5 {
    margin-right: -3rem !important; }
  .mb-md-n5,
  .my-md-n5 {
    margin-bottom: -3rem !important; }
  .ml-md-n5,
  .mx-md-n5 {
    margin-left: -3rem !important; }
  .m-md-auto {
    margin: auto !important; }
  .mt-md-auto,
  .my-md-auto {
    margin-top: auto !important; }
  .mr-md-auto,
  .mx-md-auto {
    margin-right: auto !important; }
  .mb-md-auto,
  .my-md-auto {
    margin-bottom: auto !important; }
  .ml-md-auto,
  .mx-md-auto {
    margin-left: auto !important; } }

@media (min-width: 992px) {
  .m-lg-0 {
    margin: 0 !important; }
  .mt-lg-0,
  .my-lg-0 {
    margin-top: 0 !important; }
  .mr-lg-0,
  .mx-lg-0 {
    margin-right: 0 !important; }
  .mb-lg-0,
  .my-lg-0 {
    margin-bottom: 0 !important; }
  .ml-lg-0,
  .mx-lg-0 {
    margin-left: 0 !important; }
  .m-lg-1 {
    margin: 0.25rem !important; }
  .mt-lg-1,
  .my-lg-1 {
    margin-top: 0.25rem !important; }
  .mr-lg-1,
  .mx-lg-1 {
    margin-right: 0.25rem !important; }
  .mb-lg-1,
  .my-lg-1 {
    margin-bottom: 0.25rem !important; }
  .ml-lg-1,
  .mx-lg-1 {
    margin-left: 0.25rem !important; }
  .m-lg-2 {
    margin: 0.5rem !important; }
  .mt-lg-2,
  .my-lg-2 {
    margin-top: 0.5rem !important; }
  .mr-lg-2,
  .mx-lg-2 {
    margin-right: 0.5rem !important; }
  .mb-lg-2,
  .my-lg-2 {
    margin-bottom: 0.5rem !important; }
  .ml-lg-2,
  .mx-lg-2 {
    margin-left: 0.5rem !important; }
  .m-lg-3 {
    margin: 1rem !important; }
  .mt-lg-3,
  .my-lg-3 {
    margin-top: 1rem !important; }
  .mr-lg-3,
  .mx-lg-3 {
    margin-right: 1rem !important; }
  .mb-lg-3,
  .my-lg-3 {
    margin-bottom: 1rem !important; }
  .ml-lg-3,
  .mx-lg-3 {
    margin-left: 1rem !important; }
  .m-lg-4 {
    margin: 1.5rem !important; }
  .mt-lg-4,
  .my-lg-4 {
    margin-top: 1.5rem !important; }
  .mr-lg-4,
  .mx-lg-4 {
    margin-right: 1.5rem !important; }
  .mb-lg-4,
  .my-lg-4 {
    margin-bottom: 1.5rem !important; }
  .ml-lg-4,
  .mx-lg-4 {
    margin-left: 1.5rem !important; }
  .m-lg-5 {
    margin: 3rem !important; }
  .mt-lg-5,
  .my-lg-5 {
    margin-top: 3rem !important; }
  .mr-lg-5,
  .mx-lg-5 {
    margin-right: 3rem !important; }
  .mb-lg-5,
  .my-lg-5 {
    margin-bottom: 3rem !important; }
  .ml-lg-5,
  .mx-lg-5 {
    margin-left: 3rem !important; }
  .p-lg-0 {
    padding: 0 !important; }
  .pt-lg-0,
  .py-lg-0 {
    padding-top: 0 !important; }
  .pr-lg-0,
  .px-lg-0 {
    padding-right: 0 !important; }
  .pb-lg-0,
  .py-lg-0 {
    padding-bottom: 0 !important; }
  .pl-lg-0,
  .px-lg-0 {
    padding-left: 0 !important; }
  .p-lg-1 {
    padding: 0.25rem !important; }
  .pt-lg-1,
  .py-lg-1 {
    padding-top: 0.25rem !important; }
  .pr-lg-1,
  .px-lg-1 {
    padding-right: 0.25rem !important; }
  .pb-lg-1,
  .py-lg-1 {
    padding-bottom: 0.25rem !important; }
  .pl-lg-1,
  .px-lg-1 {
    padding-left: 0.25rem !important; }
  .p-lg-2 {
    padding: 0.5rem !important; }
  .pt-lg-2,
  .py-lg-2 {
    padding-top: 0.5rem !important; }
  .pr-lg-2,
  .px-lg-2 {
    padding-right: 0.5rem !important; }
  .pb-lg-2,
  .py-lg-2 {
    padding-bottom: 0.5rem !important; }
  .pl-lg-2,
  .px-lg-2 {
    padding-left: 0.5rem !important; }
  .p-lg-3 {
    padding: 1rem !important; }
  .pt-lg-3,
  .py-lg-3 {
    padding-top: 1rem !important; }
  .pr-lg-3,
  .px-lg-3 {
    padding-right: 1rem !important; }
  .pb-lg-3,
  .py-lg-3 {
    padding-bottom: 1rem !important; }
  .pl-lg-3,
  .px-lg-3 {
    padding-left: 1rem !important; }
  .p-lg-4 {
    padding: 1.5rem !important; }
  .pt-lg-4,
  .py-lg-4 {
    padding-top: 1.5rem !important; }
  .pr-lg-4,
  .px-lg-4 {
    padding-right: 1.5rem !important; }
  .pb-lg-4,
  .py-lg-4 {
    padding-bottom: 1.5rem !important; }
  .pl-lg-4,
  .px-lg-4 {
    padding-left: 1.5rem !important; }
  .p-lg-5 {
    padding: 3rem !important; }
  .pt-lg-5,
  .py-lg-5 {
    padding-top: 3rem !important; }
  .pr-lg-5,
  .px-lg-5 {
    padding-right: 3rem !important; }
  .pb-lg-5,
  .py-lg-5 {
    padding-bottom: 3rem !important; }
  .pl-lg-5,
  .px-lg-5 {
    padding-left: 3rem !important; }
  .m-lg-n1 {
    margin: -0.25rem !important; }
  .mt-lg-n1,
  .my-lg-n1 {
    margin-top: -0.25rem !important; }
  .mr-lg-n1,
  .mx-lg-n1 {
    margin-right: -0.25rem !important; }
  .mb-lg-n1,
  .my-lg-n1 {
    margin-bottom: -0.25rem !important; }
  .ml-lg-n1,
  .mx-lg-n1 {
    margin-left: -0.25rem !important; }
  .m-lg-n2 {
    margin: -0.5rem !important; }
  .mt-lg-n2,
  .my-lg-n2 {
    margin-top: -0.5rem !important; }
  .mr-lg-n2,
  .mx-lg-n2 {
    margin-right: -0.5rem !important; }
  .mb-lg-n2,
  .my-lg-n2 {
    margin-bottom: -0.5rem !important; }
  .ml-lg-n2,
  .mx-lg-n2 {
    margin-left: -0.5rem !important; }
  .m-lg-n3 {
    margin: -1rem !important; }
  .mt-lg-n3,
  .my-lg-n3 {
    margin-top: -1rem !important; }
  .mr-lg-n3,
  .mx-lg-n3 {
    margin-right: -1rem !important; }
  .mb-lg-n3,
  .my-lg-n3 {
    margin-bottom: -1rem !important; }
  .ml-lg-n3,
  .mx-lg-n3 {
    margin-left: -1rem !important; }
  .m-lg-n4 {
    margin: -1.5rem !important; }
  .mt-lg-n4,
  .my-lg-n4 {
    margin-top: -1.5rem !important; }
  .mr-lg-n4,
  .mx-lg-n4 {
    margin-right: -1.5rem !important; }
  .mb-lg-n4,
  .my-lg-n4 {
    margin-bottom: -1.5rem !important; }
  .ml-lg-n4,
  .mx-lg-n4 {
    margin-left: -1.5rem !important; }
  .m-lg-n5 {
    margin: -3rem !important; }
  .mt-lg-n5,
  .my-lg-n5 {
    margin-top: -3rem !important; }
  .mr-lg-n5,
  .mx-lg-n5 {
    margin-right: -3rem !important; }
  .mb-lg-n5,
  .my-lg-n5 {
    margin-bottom: -3rem !important; }
  .ml-lg-n5,
  .mx-lg-n5 {
    margin-left: -3rem !important; }
  .m-lg-auto {
    margin: auto !important; }
  .mt-lg-auto,
  .my-lg-auto {
    margin-top: auto !important; }
  .mr-lg-auto,
  .mx-lg-auto {
    margin-right: auto !important; }
  .mb-lg-auto,
  .my-lg-auto {
    margin-bottom: auto !important; }
  .ml-lg-auto,
  .mx-lg-auto {
    margin-left: auto !important; } }

@media (min-width: 1200px) {
  .m-xl-0 {
    margin: 0 !important; }
  .mt-xl-0,
  .my-xl-0 {
    margin-top: 0 !important; }
  .mr-xl-0,
  .mx-xl-0 {
    margin-right: 0 !important; }
  .mb-xl-0,
  .my-xl-0 {
    margin-bottom: 0 !important; }
  .ml-xl-0,
  .mx-xl-0 {
    margin-left: 0 !important; }
  .m-xl-1 {
    margin: 0.25rem !important; }
  .mt-xl-1,
  .my-xl-1 {
    margin-top: 0.25rem !important; }
  .mr-xl-1,
  .mx-xl-1 {
    margin-right: 0.25rem !important; }
  .mb-xl-1,
  .my-xl-1 {
    margin-bottom: 0.25rem !important; }
  .ml-xl-1,
  .mx-xl-1 {
    margin-left: 0.25rem !important; }
  .m-xl-2 {
    margin: 0.5rem !important; }
  .mt-xl-2,
  .my-xl-2 {
    margin-top: 0.5rem !important; }
  .mr-xl-2,
  .mx-xl-2 {
    margin-right: 0.5rem !important; }
  .mb-xl-2,
  .my-xl-2 {
    margin-bottom: 0.5rem !important; }
  .ml-xl-2,
  .mx-xl-2 {
    margin-left: 0.5rem !important; }
  .m-xl-3 {
    margin: 1rem !important; }
  .mt-xl-3,
  .my-xl-3 {
    margin-top: 1rem !important; }
  .mr-xl-3,
  .mx-xl-3 {
    margin-right: 1rem !important; }
  .mb-xl-3,
  .my-xl-3 {
    margin-bottom: 1rem !important; }
  .ml-xl-3,
  .mx-xl-3 {
    margin-left: 1rem !important; }
  .m-xl-4 {
    margin: 1.5rem !important; }
  .mt-xl-4,
  .my-xl-4 {
    margin-top: 1.5rem !important; }
  .mr-xl-4,
  .mx-xl-4 {
    margin-right: 1.5rem !important; }
  .mb-xl-4,
  .my-xl-4 {
    margin-bottom: 1.5rem !important; }
  .ml-xl-4,
  .mx-xl-4 {
    margin-left: 1.5rem !important; }
  .m-xl-5 {
    margin: 3rem !important; }
  .mt-xl-5,
  .my-xl-5 {
    margin-top: 3rem !important; }
  .mr-xl-5,
  .mx-xl-5 {
    margin-right: 3rem !important; }
  .mb-xl-5,
  .my-xl-5 {
    margin-bottom: 3rem !important; }
  .ml-xl-5,
  .mx-xl-5 {
    margin-left: 3rem !important; }
  .p-xl-0 {
    padding: 0 !important; }
  .pt-xl-0,
  .py-xl-0 {
    padding-top: 0 !important; }
  .pr-xl-0,
  .px-xl-0 {
    padding-right: 0 !important; }
  .pb-xl-0,
  .py-xl-0 {
    padding-bottom: 0 !important; }
  .pl-xl-0,
  .px-xl-0 {
    padding-left: 0 !important; }
  .p-xl-1 {
    padding: 0.25rem !important; }
  .pt-xl-1,
  .py-xl-1 {
    padding-top: 0.25rem !important; }
  .pr-xl-1,
  .px-xl-1 {
    padding-right: 0.25rem !important; }
  .pb-xl-1,
  .py-xl-1 {
    padding-bottom: 0.25rem !important; }
  .pl-xl-1,
  .px-xl-1 {
    padding-left: 0.25rem !important; }
  .p-xl-2 {
    padding: 0.5rem !important; }
  .pt-xl-2,
  .py-xl-2 {
    padding-top: 0.5rem !important; }
  .pr-xl-2,
  .px-xl-2 {
    padding-right: 0.5rem !important; }
  .pb-xl-2,
  .py-xl-2 {
    padding-bottom: 0.5rem !important; }
  .pl-xl-2,
  .px-xl-2 {
    padding-left: 0.5rem !important; }
  .p-xl-3 {
    padding: 1rem !important; }
  .pt-xl-3,
  .py-xl-3 {
    padding-top: 1rem !important; }
  .pr-xl-3,
  .px-xl-3 {
    padding-right: 1rem !important; }
  .pb-xl-3,
  .py-xl-3 {
    padding-bottom: 1rem !important; }
  .pl-xl-3,
  .px-xl-3 {
    padding-left: 1rem !important; }
  .p-xl-4 {
    padding: 1.5rem !important; }
  .pt-xl-4,
  .py-xl-4 {
    padding-top: 1.5rem !important; }
  .pr-xl-4,
  .px-xl-4 {
    padding-right: 1.5rem !important; }
  .pb-xl-4,
  .py-xl-4 {
    padding-bottom: 1.5rem !important; }
  .pl-xl-4,
  .px-xl-4 {
    padding-left: 1.5rem !important; }
  .p-xl-5 {
    padding: 3rem !important; }
  .pt-xl-5,
  .py-xl-5 {
    padding-top: 3rem !important; }
  .pr-xl-5,
  .px-xl-5 {
    padding-right: 3rem !important; }
  .pb-xl-5,
  .py-xl-5 {
    padding-bottom: 3rem !important; }
  .pl-xl-5,
  .px-xl-5 {
    padding-left: 3rem !important; }
  .m-xl-n1 {
    margin: -0.25rem !important; }
  .mt-xl-n1,
  .my-xl-n1 {
    margin-top: -0.25rem !important; }
  .mr-xl-n1,
  .mx-xl-n1 {
    margin-right: -0.25rem !important; }
  .mb-xl-n1,
  .my-xl-n1 {
    margin-bottom: -0.25rem !important; }
  .ml-xl-n1,
  .mx-xl-n1 {
    margin-left: -0.25rem !important; }
  .m-xl-n2 {
    margin: -0.5rem !important; }
  .mt-xl-n2,
  .my-xl-n2 {
    margin-top: -0.5rem !important; }
  .mr-xl-n2,
  .mx-xl-n2 {
    margin-right: -0.5rem !important; }
  .mb-xl-n2,
  .my-xl-n2 {
    margin-bottom: -0.5rem !important; }
  .ml-xl-n2,
  .mx-xl-n2 {
    margin-left: -0.5rem !important; }
  .m-xl-n3 {
    margin: -1rem !important; }
  .mt-xl-n3,
  .my-xl-n3 {
    margin-top: -1rem !important; }
  .mr-xl-n3,
  .mx-xl-n3 {
    margin-right: -1rem !important; }
  .mb-xl-n3,
  .my-xl-n3 {
    margin-bottom: -1rem !important; }
  .ml-xl-n3,
  .mx-xl-n3 {
    margin-left: -1rem !important; }
  .m-xl-n4 {
    margin: -1.5rem !important; }
  .mt-xl-n4,
  .my-xl-n4 {
    margin-top: -1.5rem !important; }
  .mr-xl-n4,
  .mx-xl-n4 {
    margin-right: -1.5rem !important; }
  .mb-xl-n4,
  .my-xl-n4 {
    margin-bottom: -1.5rem !important; }
  .ml-xl-n4,
  .mx-xl-n4 {
    margin-left: -1.5rem !important; }
  .m-xl-n5 {
    margin: -3rem !important; }
  .mt-xl-n5,
  .my-xl-n5 {
    margin-top: -3rem !important; }
  .mr-xl-n5,
  .mx-xl-n5 {
    margin-right: -3rem !important; }
  .mb-xl-n5,
  .my-xl-n5 {
    margin-bottom: -3rem !important; }
  .ml-xl-n5,
  .mx-xl-n5 {
    margin-left: -3rem !important; }
  .m-xl-auto {
    margin: auto !important; }
  .mt-xl-auto,
  .my-xl-auto {
    margin-top: auto !important; }
  .mr-xl-auto,
  .mx-xl-auto {
    margin-right: auto !important; }
  .mb-xl-auto,
  .my-xl-auto {
    margin-bottom: auto !important; }
  .ml-xl-auto,
  .mx-xl-auto {
    margin-left: auto !important; } }

.text-monospace {
  font-family: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace !important; }

.text-justify {
  text-align: justify !important; }

.text-wrap {
  white-space: normal !important; }

.text-nowrap {
  white-space: nowrap !important; }

.text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap; }

.text-left {
  text-align: left !important; }

.text-right {
  text-align: right !important; }

.text-center {
  text-align: center !important; }

@media (min-width: 576px) {
  .text-sm-left {
    text-align: left !important; }
  .text-sm-right {
    text-align: right !important; }
  .text-sm-center {
    text-align: center !important; } }

@media (min-width: 768px) {
  .text-md-left {
    text-align: left !important; }
  .text-md-right {
    text-align: right !important; }
  .text-md-center {
    text-align: center !important; } }

@media (min-width: 992px) {
  .text-lg-left {
    text-align: left !important; }
  .text-lg-right {
    text-align: right !important; }
  .text-lg-center {
    text-align: center !important; } }

@media (min-width: 1200px) {
  .text-xl-left {
    text-align: left !important; }
  .text-xl-right {
    text-align: right !important; }
  .text-xl-center {
    text-align: center !important; } }

.text-lowercase {
  text-transform: lowercase !important; }

.text-uppercase {
  text-transform: uppercase !important; }

.text-capitalize {
  text-transform: capitalize !important; }

.font-weight-light {
  font-weight: 300 !important; }

.font-weight-lighter {
  font-weight: lighter !important; }

.font-weight-normal {
  font-weight: 400 !important; }

.font-weight-bold {
  font-weight: 700 !important; }

.font-weight-bolder {
  font-weight: bolder !important; }

.font-italic {
  font-style: italic !important; }

.text-white {
  color: #ffffff !important; }

.text-primary {
  color: #ff6d41 !important; }

a.text-primary:hover, a.text-primary:focus {
  color: #f43800 !important; }

.text-secondary {
  color: #479cc8 !important; }

a.text-secondary:hover, a.text-secondary:focus {
  color: #2d7296 !important; }

.text-success {
  color: #5dbf74 !important; }

a.text-success:hover, a.text-success:focus {
  color: #3b9550 !important; }

.text-info {
  color: #68d5c8 !important; }

a.text-info:hover, a.text-info:focus {
  color: #34bcac !important; }

.text-warning {
  color: #e6c54f !important; }

a.text-warning:hover, a.text-warning:focus {
  color: #cca51d !important; }

.text-danger {
  color: #f9777f !important; }

a.text-danger:hover, a.text-danger:focus {
  color: #f62e3a !important; }

.text-light {
  color: #e6ecef !important; }

a.text-light:hover, a.text-light:focus {
  color: #b7c9d1 !important; }

.text-dark {
  color: #343a40 !important; }

a.text-dark:hover, a.text-dark:focus {
  color: #121416 !important; }

.text-body {
  color: #ffffff !important; }

.text-muted {
  color: #6c757d !important; }

.text-black-50 {
  color: rgba(4, 4, 4, 0.5) !important; }

.text-white-50 {
  color: rgba(255, 255, 255, 0.5) !important; }

.text-hide {
  font: 0/0 a;
  color: transparent;
  text-shadow: none;
  background-color: transparent;
  border: 0; }

.text-decoration-none {
  text-decoration: none !important; }

.text-break {
  word-break: break-word !important;
  overflow-wrap: break-word !important; }

.text-reset {
  color: inherit !important; }

.visible {
  visibility: visible !important; }

.invisible {
  visibility: hidden !important; }

@font-face {
  font-family: 'Material Icons';
  font-style: normal;
  font-weight: 400;
  src: local("Material Icons"), local("MaterialIcons-Regular"), url("../fonts/MaterialIcons-Regular.woff") format("woff"); }

@font-face {
  font-family: 'Source Sans Pro';
  font-weight: 200;
  font-style: normal;
  font-stretch: normal;
  src: local("SourceSansPro-ExtraLight"), local("Source Sans Pro"), url("../fonts/SourceSansPro-ExtraLight.woff") format("woff"); }

@font-face {
  font-family: 'Source Sans Pro';
  font-weight: 300;
  font-style: normal;
  font-stretch: normal;
  src: local("SourceSansPro-Light"), local("Source Sans Pro"), url("../fonts/SourceSansPro-Light.woff") format("woff"); }

@font-face {
  font-family: 'Source Sans Pro';
  font-weight: 400;
  font-style: normal;
  font-stretch: normal;
  src: local("SourceSansPro-Regular"), local("Source Sans Pro"), url("../fonts/SourceSansPro-Regular.woff") format("woff"); }

@font-face {
  font-family: 'Source Sans Pro';
  font-weight: 600;
  font-style: normal;
  font-stretch: normal;
  src: local("SourceSansPro-Semibold"), local("Source Sans Pro Semibold"), url("../fonts/SourceSansPro-Semibold.woff") format("woff"); }

@font-face {
  font-family: 'Source Sans Pro';
  font-weight: 700;
  font-style: normal;
  font-stretch: normal;
  src: local("SourceSansPro-Bold"), local("Source Sans Pro Bold"), url("../fonts/SourceSansPro-Bold.woff") format("woff"); }

@font-face {
  font-family: 'Source Sans Pro';
  font-weight: 900;
  font-style: normal;
  font-stretch: normal;
  src: local("SourceSansPro-Black"), local("Source Sans Pro Black"), url("../fonts/SourceSansPro-Black.woff") format("woff"); }

@font-face {
  font-family: 'Source Sans Pro';
  font-weight: 200;
  font-style: italic;
  font-stretch: normal;
  src: local("SourceSansPro-ExtraLightIt"), local("Source Sans Pro"), url("../fonts/SourceSansPro-ExtraLightIt.woff") format("woff"); }

@font-face {
  font-family: 'Source Sans Pro';
  font-weight: 300;
  font-style: italic;
  font-stretch: normal;
  src: local("SourceSansPro-LightIt"), local("Source Sans Pro"), url("../fonts/SourceSansPro-LightIt.woff") format("woff"); }

@font-face {
  font-family: 'Source Sans Pro';
  font-weight: 400;
  font-style: italic;
  font-stretch: normal;
  src: local("SourceSansPro-It"), local("Source Sans Pro"), url("../fonts/SourceSansPro-It.woff") format("woff"); }

@font-face {
  font-family: 'Source Sans Pro';
  font-weight: 600;
  font-style: italic;
  font-stretch: normal;
  src: local("SourceSansPro-SemiboldIt"), local("Source Sans Pro"), url("../fonts/SourceSansPro-SemiboldIt.woff") format("woff"); }

@font-face {
  font-family: 'Source Sans Pro';
  font-weight: 700;
  font-style: italic;
  font-stretch: normal;
  src: local("SourceSansPro-BoldIt"), local("Source Sans Pro"), url("../fonts/SourceSansPro-BoldIt.woff") format("woff"); }

@font-face {
  font-family: 'Source Sans Pro';
  font-weight: 900;
  font-style: italic;
  font-stretch: normal;
  src: local("SourceSansPro-BlackIt"), local("Source Sans Pro"), url("../fonts/SourceSansPro-BlackIt.woff") format("woff"); }

h1 {
  font-size: 2.25rem;
  font-weight: bold; }

h2 {
  font-size: 1.5rem;
  font-weight: 600; }

h3 {
  font-size: 1.25rem;
  font-weight: normal; }

h4 {
  font-size: 1.125rem;
  font-weight: 600; }

h5 {
  font-size: 1rem;
  font-weight: 600; }

h6 {
  font-size: 0.875rem;
  font-weight: 600; }

p {
  line-height: 1.38; }

label {
  margin-bottom: 0; }

.rzi, .rz-menuitem .rz-menuitem-icon, .rz-fileupload-row .rz-button .rzi-close, .rz-fileupload-row .rz-paginator-element .rzi-close,
.rz-fileupload-row .rz-button .rzi-times,
.rz-fileupload-row .rz-paginator-element .rzi-times,
.rz-fileupload-row .rz-button .rz-icon-trash,
.rz-fileupload-row .rz-paginator-element .rz-icon-trash, .rz-datatable .rzi-chevron-circle-right, .rz-datatable .rzi-chevron-circle-down, .rz-sortable-column .rzi-grid-sort, .rz-datatable-header .rzi-plus, .rz-datatable-loading-content .rzi-circle-o-notch, .rz-column-drag {
  font-family: 'Material Icons';
  font-weight: normal;
  font-style: normal;
  font-size: 1.5em;
  /* Preferred icon size */
  display: inline-block;
  width: 1em;
  height: 1em;
  line-height: 1;
  text-transform: none;
  letter-spacing: normal;
  word-wrap: normal;
  white-space: nowrap;
  direction: ltr;
  /* Support for all WebKit browsers. */
  -webkit-font-smoothing: antialiased;
  /* Support for Safari and Chrome. */
  text-rendering: optimizeLegibility;
  /* Support for Firefox. */
  -moz-osx-font-smoothing: grayscale;
  /* Support for IE. */
  font-feature-settings: 'liga'; }

.rz-helper-hidden-accessible {
  opacity: 0;
  height: 0;
  overflow: hidden; }

.rz-scrollbar-measure {
  width: 100px;
  height: 100px;
  overflow: scroll;
  position: absolute;
  top: -9999px; }

.rz-helper-hidden {
  display: none; }

@media (max-width: 768px) {
  body {
    overflow-x: hidden; }
  .header,
  .footer,
  .body {
    width: 100vw; } }

rz-button[type],
p-button[type] {
  -webkit-appearance: none; }

.rz-button, .rz-paginator-element {
  -webkit-appearance: none;
  border-radius: 4px;
  color: #ffffff;
  border: none;
  outline: none;
  line-height: initial;
  font-size: 1.0625rem;
  box-shadow: none; }
  .rz-button:focus, .rz-paginator-element:focus {
    outline: none; }
  .rz-button:not(.rz-state-disabled), .rz-paginator-element:not(.rz-state-disabled) {
    cursor: pointer; }
    .rz-button:not(.rz-state-disabled):hover:not(:active), .rz-paginator-element:not(.rz-state-disabled):hover:not(:active) {
      background-image: linear-gradient(rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.1));
      box-shadow: inset 0 -3px 0 0 rgba(255, 255, 255, 0.2); }
    .rz-button:not(.rz-state-disabled):active, .rz-paginator-element:not(.rz-state-disabled):active {
      background-image: linear-gradient(rgba(0, 0, 0, 0.1), rgba(0, 0, 0, 0.1));
      box-shadow: inset 0 3px 0 0 rgba(0, 0, 0, 0.1); }
  .rz-button.rz-state-disabled, .rz-state-disabled.rz-paginator-element {
    opacity: 0.2; }
  .rz-button .rz-button-text, .rz-paginator-element .rz-button-text {
    vertical-align: middle;
    line-height: 1.5rem; }
  .rz-button .rzi, .rz-paginator-element .rzi, .rz-button .rz-menuitem .rz-menuitem-icon, .rz-menuitem .rz-button .rz-menuitem-icon, .rz-paginator-element .rz-menuitem .rz-menuitem-icon, .rz-menuitem .rz-paginator-element .rz-menuitem-icon, .rz-fileupload-row .rz-button .rzi-close, .rz-fileupload-row .rz-paginator-element .rzi-close,
  .rz-fileupload-row .rz-button .rzi-times,
  .rz-fileupload-row .rz-paginator-element .rzi-times,
  .rz-fileupload-row .rz-button .rz-icon-trash,
  .rz-fileupload-row .rz-paginator-element .rz-icon-trash, .rz-button .rz-datatable .rzi-chevron-circle-right, .rz-datatable .rz-button .rzi-chevron-circle-right, .rz-paginator-element .rz-datatable .rzi-chevron-circle-right, .rz-datatable .rz-paginator-element .rzi-chevron-circle-right, .rz-button .rz-datatable .rzi-chevron-circle-down, .rz-datatable .rz-button .rzi-chevron-circle-down, .rz-paginator-element .rz-datatable .rzi-chevron-circle-down, .rz-datatable .rz-paginator-element .rzi-chevron-circle-down, .rz-button .rz-sortable-column .rzi-grid-sort, .rz-sortable-column .rz-button .rzi-grid-sort, .rz-paginator-element .rz-sortable-column .rzi-grid-sort, .rz-sortable-column .rz-paginator-element .rzi-grid-sort, .rz-button .rz-datatable-header .rzi-plus, .rz-datatable-header .rz-button .rzi-plus, .rz-paginator-element .rz-datatable-header .rzi-plus, .rz-datatable-header .rz-paginator-element .rzi-plus, .rz-button .rz-datatable-loading-content .rzi-circle-o-notch, .rz-datatable-loading-content .rz-button .rzi-circle-o-notch, .rz-paginator-element .rz-datatable-loading-content .rzi-circle-o-notch, .rz-datatable-loading-content .rz-paginator-element .rzi-circle-o-notch, .rz-button .rz-column-drag, .rz-paginator-element .rz-column-drag {
    vertical-align: middle; }
  .rz-button.btn-primary, .btn-primary.rz-paginator-element {
    background-color: #ff6d41; }
  .rz-button.btn-light, .btn-light.rz-paginator-element {
    background-color: #e6ecef;
    color: #3a474d; }
  .rz-button.btn-secondary, .btn-secondary.rz-paginator-element {
    background-color: #479cc8; }
  .rz-button.btn-info, .btn-info.rz-paginator-element {
    background-color: #68d5c8; }
  .rz-button.btn-warning, .btn-warning.rz-paginator-element {
    background-color: #e6c54f; }
  .rz-button.btn-error, .btn-error.rz-paginator-element {
    background-color: #f9777f; }
  .rz-button.btn-danger, .btn-danger.rz-paginator-element {
    background-color: #f9777f; }
  .rz-button.btn-success, .btn-success.rz-paginator-element {
    background-color: #5dbf74; }
  .rz-button-md, .rz-splitbutton .rz-button-text-icon-left,
  .rz-splitbutton .rz-button-text-only, .rz-splitbutton-menubutton, .rz-selectbutton .rz-button, .rz-selectbutton .rz-paginator-element, .rz-fileupload-choose, .rz-date-filter-buttons .rz-button, .rz-date-filter-buttons .rz-paginator-element {
    font-size: 1.0625rem;
    font-weight: normal;
    padding: 0 2rem;
    height: 2.1875rem; }
    .rz-button-md.rz-button-text-icon-left, .rz-splitbutton .rz-button-text-icon-left, .rz-button-text-icon-left.rz-splitbutton-menubutton, .rz-selectbutton .rz-button-text-icon-left.rz-button, .rz-selectbutton .rz-button-text-icon-left.rz-paginator-element, .rz-button-text-icon-left.rz-fileupload-choose, .rz-date-filter-buttons .rz-button-text-icon-left.rz-button, .rz-date-filter-buttons .rz-button-text-icon-left.rz-paginator-element {
      padding: 0 1.375rem; }
    .rz-button-md.rz-button-icon-only, .rz-splitbutton .rz-button-icon-only.rz-button-text-icon-left, .rz-splitbutton .rz-button-text-icon-left.rz-paginator-element,
    .rz-splitbutton .rz-button-icon-only.rz-button-text-only, .rz-splitbutton .rz-button-text-only.rz-paginator-element, .rz-button-icon-only.rz-splitbutton-menubutton, .rz-splitbutton-menubutton.rz-paginator-element, .rz-selectbutton .rz-button-icon-only.rz-button, .rz-selectbutton .rz-paginator-element, .rz-button-icon-only.rz-fileupload-choose, .rz-fileupload-choose.rz-paginator-element, .rz-date-filter-buttons .rz-button-icon-only.rz-button, .rz-date-filter-buttons .rz-paginator-element, .rz-button-md.rz-paginator-element {
      padding: 0 0.5rem; }
    .rz-button-md .rzi, .rz-splitbutton .rz-button-text-icon-left .rzi, .rz-splitbutton .rz-button-text-only .rzi, .rz-splitbutton-menubutton .rzi, .rz-selectbutton .rz-button .rzi, .rz-selectbutton .rz-paginator-element .rzi, .rz-fileupload-choose .rzi, .rz-date-filter-buttons .rz-button .rzi, .rz-date-filter-buttons .rz-paginator-element .rzi, .rz-button-md .rz-menuitem .rz-menuitem-icon, .rz-menuitem .rz-button-md .rz-menuitem-icon, .rz-splitbutton .rz-button-text-icon-left .rz-menuitem .rz-menuitem-icon, .rz-menuitem .rz-splitbutton .rz-button-text-icon-left .rz-menuitem-icon, .rz-splitbutton .rz-button-text-only .rz-menuitem .rz-menuitem-icon, .rz-menuitem .rz-splitbutton .rz-button-text-only .rz-menuitem-icon, .rz-splitbutton-menubutton .rz-menuitem .rz-menuitem-icon, .rz-menuitem .rz-splitbutton-menubutton .rz-menuitem-icon, .rz-selectbutton .rz-button .rz-menuitem .rz-menuitem-icon, .rz-menuitem .rz-selectbutton .rz-button .rz-menuitem-icon, .rz-selectbutton .rz-paginator-element .rz-menuitem .rz-menuitem-icon, .rz-menuitem .rz-selectbutton .rz-paginator-element .rz-menuitem-icon, .rz-fileupload-choose .rz-menuitem .rz-menuitem-icon, .rz-menuitem .rz-fileupload-choose .rz-menuitem-icon, .rz-date-filter-buttons .rz-button .rz-menuitem .rz-menuitem-icon, .rz-menuitem .rz-date-filter-buttons .rz-button .rz-menuitem-icon, .rz-date-filter-buttons .rz-paginator-element .rz-menuitem .rz-menuitem-icon, .rz-menuitem .rz-date-filter-buttons .rz-paginator-element .rz-menuitem-icon, .rz-button-md .rz-fileupload-row .rz-button .rzi-close, .rz-fileupload-row .rz-button .rz-button-md .rzi-close, .rz-splitbutton .rz-button-text-icon-left .rz-fileupload-row .rz-button .rzi-close, .rz-fileupload-row .rz-button .rz-splitbutton .rz-button-text-icon-left .rzi-close, .rz-splitbutton .rz-button-text-only .rz-fileupload-row .rz-button .rzi-close, .rz-fileupload-row .rz-button .rz-splitbutton .rz-button-text-only .rzi-close, .rz-splitbutton-menubutton .rz-fileupload-row .rz-button .rzi-close, .rz-fileupload-row .rz-button .rz-splitbutton-menubutton .rzi-close, .rz-selectbutton .rz-fileupload-row .rz-button .rzi-close, .rz-fileupload-row .rz-selectbutton .rz-button .rzi-close, .rz-selectbutton .rz-paginator-element .rz-fileupload-row .rz-button .rzi-close, .rz-fileupload-row .rz-button .rz-selectbutton .rz-paginator-element .rzi-close, .rz-fileupload-choose .rz-fileupload-row .rz-button .rzi-close, .rz-fileupload-row .rz-button .rz-fileupload-choose .rzi-close, .rz-date-filter-buttons .rz-fileupload-row .rz-button .rzi-close, .rz-fileupload-row .rz-date-filter-buttons .rz-button .rzi-close, .rz-date-filter-buttons .rz-paginator-element .rz-fileupload-row .rz-button .rzi-close, .rz-fileupload-row .rz-button .rz-date-filter-buttons .rz-paginator-element .rzi-close, .rz-button-md .rz-fileupload-row .rz-paginator-element .rzi-close, .rz-fileupload-row .rz-paginator-element .rz-button-md .rzi-close, .rz-splitbutton .rz-button-text-icon-left .rz-fileupload-row .rz-paginator-element .rzi-close, .rz-fileupload-row .rz-paginator-element .rz-splitbutton .rz-button-text-icon-left .rzi-close, .rz-splitbutton .rz-button-text-only .rz-fileupload-row .rz-paginator-element .rzi-close, .rz-fileupload-row .rz-paginator-element .rz-splitbutton .rz-button-text-only .rzi-close, .rz-splitbutton-menubutton .rz-fileupload-row .rz-paginator-element .rzi-close, .rz-fileupload-row .rz-paginator-element .rz-splitbutton-menubutton .rzi-close, .rz-selectbutton .rz-button .rz-fileupload-row .rz-paginator-element .rzi-close, .rz-fileupload-row .rz-paginator-element .rz-selectbutton .rz-button .rzi-close, .rz-selectbutton .rz-fileupload-row .rz-paginator-element .rzi-close, .rz-fileupload-row .rz-selectbutton .rz-paginator-element .rzi-close, .rz-fileupload-choose .rz-fileupload-row .rz-paginator-element .rzi-close, .rz-fileupload-row .rz-paginator-element .rz-fileupload-choose .rzi-close, .rz-date-filter-buttons .rz-button .rz-fileupload-row .rz-paginator-element .rzi-close, .rz-fileupload-row .rz-paginator-element .rz-date-filter-buttons .rz-button .rzi-close, .rz-date-filter-buttons .rz-fileupload-row .rz-paginator-element .rzi-close, .rz-fileupload-row .rz-date-filter-buttons .rz-paginator-element .rzi-close,
    .rz-button-md .rz-fileupload-row .rz-button .rzi-times, .rz-fileupload-row .rz-button .rz-button-md .rzi-times,
    .rz-splitbutton .rz-button-text-icon-left .rz-fileupload-row .rz-button .rzi-times, .rz-fileupload-row .rz-button .rz-splitbutton .rz-button-text-icon-left .rzi-times,
    .rz-splitbutton .rz-button-text-only .rz-fileupload-row .rz-button .rzi-times, .rz-fileupload-row .rz-button .rz-splitbutton .rz-button-text-only .rzi-times,
    .rz-splitbutton-menubutton .rz-fileupload-row .rz-button .rzi-times, .rz-fileupload-row .rz-button .rz-splitbutton-menubutton .rzi-times,
    .rz-selectbutton .rz-fileupload-row .rz-button .rzi-times, .rz-fileupload-row .rz-selectbutton .rz-button .rzi-times,
    .rz-selectbutton .rz-paginator-element .rz-fileupload-row .rz-button .rzi-times, .rz-fileupload-row .rz-button .rz-selectbutton .rz-paginator-element .rzi-times,
    .rz-fileupload-choose .rz-fileupload-row .rz-button .rzi-times, .rz-fileupload-row .rz-button .rz-fileupload-choose .rzi-times,
    .rz-date-filter-buttons .rz-fileupload-row .rz-button .rzi-times, .rz-fileupload-row .rz-date-filter-buttons .rz-button .rzi-times,
    .rz-date-filter-buttons .rz-paginator-element .rz-fileupload-row .rz-button .rzi-times, .rz-fileupload-row .rz-button .rz-date-filter-buttons .rz-paginator-element .rzi-times,
    .rz-button-md .rz-fileupload-row .rz-paginator-element .rzi-times, .rz-fileupload-row .rz-paginator-element .rz-button-md .rzi-times,
    .rz-splitbutton .rz-button-text-icon-left .rz-fileupload-row .rz-paginator-element .rzi-times, .rz-fileupload-row .rz-paginator-element .rz-splitbutton .rz-button-text-icon-left .rzi-times,
    .rz-splitbutton .rz-button-text-only .rz-fileupload-row .rz-paginator-element .rzi-times, .rz-fileupload-row .rz-paginator-element .rz-splitbutton .rz-button-text-only .rzi-times,
    .rz-splitbutton-menubutton .rz-fileupload-row .rz-paginator-element .rzi-times, .rz-fileupload-row .rz-paginator-element .rz-splitbutton-menubutton .rzi-times,
    .rz-selectbutton .rz-button .rz-fileupload-row .rz-paginator-element .rzi-times, .rz-fileupload-row .rz-paginator-element .rz-selectbutton .rz-button .rzi-times,
    .rz-selectbutton .rz-fileupload-row .rz-paginator-element .rzi-times, .rz-fileupload-row .rz-selectbutton .rz-paginator-element .rzi-times,
    .rz-fileupload-choose .rz-fileupload-row .rz-paginator-element .rzi-times, .rz-fileupload-row .rz-paginator-element .rz-fileupload-choose .rzi-times,
    .rz-date-filter-buttons .rz-button .rz-fileupload-row .rz-paginator-element .rzi-times, .rz-fileupload-row .rz-paginator-element .rz-date-filter-buttons .rz-button .rzi-times,
    .rz-date-filter-buttons .rz-fileupload-row .rz-paginator-element .rzi-times, .rz-fileupload-row .rz-date-filter-buttons .rz-paginator-element .rzi-times,
    .rz-button-md .rz-fileupload-row .rz-button .rz-icon-trash, .rz-fileupload-row .rz-button .rz-button-md .rz-icon-trash,
    .rz-splitbutton .rz-button-text-icon-left .rz-fileupload-row .rz-button .rz-icon-trash, .rz-fileupload-row .rz-button .rz-splitbutton .rz-button-text-icon-left .rz-icon-trash,
    .rz-splitbutton .rz-button-text-only .rz-fileupload-row .rz-button .rz-icon-trash, .rz-fileupload-row .rz-button .rz-splitbutton .rz-button-text-only .rz-icon-trash,
    .rz-splitbutton-menubutton .rz-fileupload-row .rz-button .rz-icon-trash, .rz-fileupload-row .rz-button .rz-splitbutton-menubutton .rz-icon-trash,
    .rz-selectbutton .rz-fileupload-row .rz-button .rz-icon-trash, .rz-fileupload-row .rz-selectbutton .rz-button .rz-icon-trash,
    .rz-selectbutton .rz-paginator-element .rz-fileupload-row .rz-button .rz-icon-trash, .rz-fileupload-row .rz-button .rz-selectbutton .rz-paginator-element .rz-icon-trash,
    .rz-fileupload-choose .rz-fileupload-row .rz-button .rz-icon-trash, .rz-fileupload-row .rz-button .rz-fileupload-choose .rz-icon-trash,
    .rz-date-filter-buttons .rz-fileupload-row .rz-button .rz-icon-trash, .rz-fileupload-row .rz-date-filter-buttons .rz-button .rz-icon-trash,
    .rz-date-filter-buttons .rz-paginator-element .rz-fileupload-row .rz-button .rz-icon-trash, .rz-fileupload-row .rz-button .rz-date-filter-buttons .rz-paginator-element .rz-icon-trash,
    .rz-button-md .rz-fileupload-row .rz-paginator-element .rz-icon-trash, .rz-fileupload-row .rz-paginator-element .rz-button-md .rz-icon-trash,
    .rz-splitbutton .rz-button-text-icon-left .rz-fileupload-row .rz-paginator-element .rz-icon-trash, .rz-fileupload-row .rz-paginator-element .rz-splitbutton .rz-button-text-icon-left .rz-icon-trash,
    .rz-splitbutton .rz-button-text-only .rz-fileupload-row .rz-paginator-element .rz-icon-trash, .rz-fileupload-row .rz-paginator-element .rz-splitbutton .rz-button-text-only .rz-icon-trash,
    .rz-splitbutton-menubutton .rz-fileupload-row .rz-paginator-element .rz-icon-trash, .rz-fileupload-row .rz-paginator-element .rz-splitbutton-menubutton .rz-icon-trash,
    .rz-selectbutton .rz-button .rz-fileupload-row .rz-paginator-element .rz-icon-trash, .rz-fileupload-row .rz-paginator-element .rz-selectbutton .rz-button .rz-icon-trash,
    .rz-selectbutton .rz-fileupload-row .rz-paginator-element .rz-icon-trash, .rz-fileupload-row .rz-selectbutton .rz-paginator-element .rz-icon-trash,
    .rz-fileupload-choose .rz-fileupload-row .rz-paginator-element .rz-icon-trash, .rz-fileupload-row .rz-paginator-element .rz-fileupload-choose .rz-icon-trash,
    .rz-date-filter-buttons .rz-button .rz-fileupload-row .rz-paginator-element .rz-icon-trash, .rz-fileupload-row .rz-paginator-element .rz-date-filter-buttons .rz-button .rz-icon-trash,
    .rz-date-filter-buttons .rz-fileupload-row .rz-paginator-element .rz-icon-trash, .rz-fileupload-row .rz-date-filter-buttons .rz-paginator-element .rz-icon-trash, .rz-button-md .rz-datatable .rzi-chevron-circle-right, .rz-datatable .rz-button-md .rzi-chevron-circle-right, .rz-splitbutton .rz-button-text-icon-left .rz-datatable .rzi-chevron-circle-right, .rz-datatable .rz-splitbutton .rz-button-text-icon-left .rzi-chevron-circle-right, .rz-splitbutton .rz-button-text-only .rz-datatable .rzi-chevron-circle-right, .rz-datatable .rz-splitbutton .rz-button-text-only .rzi-chevron-circle-right, .rz-splitbutton-menubutton .rz-datatable .rzi-chevron-circle-right, .rz-datatable .rz-splitbutton-menubutton .rzi-chevron-circle-right, .rz-selectbutton .rz-button .rz-datatable .rzi-chevron-circle-right, .rz-datatable .rz-selectbutton .rz-button .rzi-chevron-circle-right, .rz-selectbutton .rz-paginator-element .rz-datatable .rzi-chevron-circle-right, .rz-datatable .rz-selectbutton .rz-paginator-element .rzi-chevron-circle-right, .rz-fileupload-choose .rz-datatable .rzi-chevron-circle-right, .rz-datatable .rz-fileupload-choose .rzi-chevron-circle-right, .rz-date-filter-buttons .rz-button .rz-datatable .rzi-chevron-circle-right, .rz-datatable .rz-date-filter-buttons .rz-button .rzi-chevron-circle-right, .rz-date-filter-buttons .rz-paginator-element .rz-datatable .rzi-chevron-circle-right, .rz-datatable .rz-date-filter-buttons .rz-paginator-element .rzi-chevron-circle-right, .rz-button-md .rz-datatable .rzi-chevron-circle-down, .rz-datatable .rz-button-md .rzi-chevron-circle-down, .rz-splitbutton .rz-button-text-icon-left .rz-datatable .rzi-chevron-circle-down, .rz-datatable .rz-splitbutton .rz-button-text-icon-left .rzi-chevron-circle-down, .rz-splitbutton .rz-button-text-only .rz-datatable .rzi-chevron-circle-down, .rz-datatable .rz-splitbutton .rz-button-text-only .rzi-chevron-circle-down, .rz-splitbutton-menubutton .rz-datatable .rzi-chevron-circle-down, .rz-datatable .rz-splitbutton-menubutton .rzi-chevron-circle-down, .rz-selectbutton .rz-button .rz-datatable .rzi-chevron-circle-down, .rz-datatable .rz-selectbutton .rz-button .rzi-chevron-circle-down, .rz-selectbutton .rz-paginator-element .rz-datatable .rzi-chevron-circle-down, .rz-datatable .rz-selectbutton .rz-paginator-element .rzi-chevron-circle-down, .rz-fileupload-choose .rz-datatable .rzi-chevron-circle-down, .rz-datatable .rz-fileupload-choose .rzi-chevron-circle-down, .rz-date-filter-buttons .rz-button .rz-datatable .rzi-chevron-circle-down, .rz-datatable .rz-date-filter-buttons .rz-button .rzi-chevron-circle-down, .rz-date-filter-buttons .rz-paginator-element .rz-datatable .rzi-chevron-circle-down, .rz-datatable .rz-date-filter-buttons .rz-paginator-element .rzi-chevron-circle-down, .rz-button-md .rz-sortable-column .rzi-grid-sort, .rz-sortable-column .rz-button-md .rzi-grid-sort, .rz-splitbutton .rz-button-text-icon-left .rz-sortable-column .rzi-grid-sort, .rz-sortable-column .rz-splitbutton .rz-button-text-icon-left .rzi-grid-sort, .rz-splitbutton .rz-button-text-only .rz-sortable-column .rzi-grid-sort, .rz-sortable-column .rz-splitbutton .rz-button-text-only .rzi-grid-sort, .rz-splitbutton-menubutton .rz-sortable-column .rzi-grid-sort, .rz-sortable-column .rz-splitbutton-menubutton .rzi-grid-sort, .rz-selectbutton .rz-button .rz-sortable-column .rzi-grid-sort, .rz-sortable-column .rz-selectbutton .rz-button .rzi-grid-sort, .rz-selectbutton .rz-paginator-element .rz-sortable-column .rzi-grid-sort, .rz-sortable-column .rz-selectbutton .rz-paginator-element .rzi-grid-sort, .rz-fileupload-choose .rz-sortable-column .rzi-grid-sort, .rz-sortable-column .rz-fileupload-choose .rzi-grid-sort, .rz-date-filter-buttons .rz-button .rz-sortable-column .rzi-grid-sort, .rz-sortable-column .rz-date-filter-buttons .rz-button .rzi-grid-sort, .rz-date-filter-buttons .rz-paginator-element .rz-sortable-column .rzi-grid-sort, .rz-sortable-column .rz-date-filter-buttons .rz-paginator-element .rzi-grid-sort, .rz-button-md .rz-datatable-header .rzi-plus, .rz-datatable-header .rz-button-md .rzi-plus, .rz-splitbutton .rz-button-text-icon-left .rz-datatable-header .rzi-plus, .rz-datatable-header .rz-splitbutton .rz-button-text-icon-left .rzi-plus, .rz-splitbutton .rz-button-text-only .rz-datatable-header .rzi-plus, .rz-datatable-header .rz-splitbutton .rz-button-text-only .rzi-plus, .rz-splitbutton-menubutton .rz-datatable-header .rzi-plus, .rz-datatable-header .rz-splitbutton-menubutton .rzi-plus, .rz-selectbutton .rz-button .rz-datatable-header .rzi-plus, .rz-datatable-header .rz-selectbutton .rz-button .rzi-plus, .rz-selectbutton .rz-paginator-element .rz-datatable-header .rzi-plus, .rz-datatable-header .rz-selectbutton .rz-paginator-element .rzi-plus, .rz-fileupload-choose .rz-datatable-header .rzi-plus, .rz-datatable-header .rz-fileupload-choose .rzi-plus, .rz-date-filter-buttons .rz-button .rz-datatable-header .rzi-plus, .rz-datatable-header .rz-date-filter-buttons .rz-button .rzi-plus, .rz-date-filter-buttons .rz-paginator-element .rz-datatable-header .rzi-plus, .rz-datatable-header .rz-date-filter-buttons .rz-paginator-element .rzi-plus, .rz-button-md .rz-datatable-loading-content .rzi-circle-o-notch, .rz-datatable-loading-content .rz-button-md .rzi-circle-o-notch, .rz-splitbutton .rz-button-text-icon-left .rz-datatable-loading-content .rzi-circle-o-notch, .rz-datatable-loading-content .rz-splitbutton .rz-button-text-icon-left .rzi-circle-o-notch, .rz-splitbutton .rz-button-text-only .rz-datatable-loading-content .rzi-circle-o-notch, .rz-datatable-loading-content .rz-splitbutton .rz-button-text-only .rzi-circle-o-notch, .rz-splitbutton-menubutton .rz-datatable-loading-content .rzi-circle-o-notch, .rz-datatable-loading-content .rz-splitbutton-menubutton .rzi-circle-o-notch, .rz-selectbutton .rz-button .rz-datatable-loading-content .rzi-circle-o-notch, .rz-datatable-loading-content .rz-selectbutton .rz-button .rzi-circle-o-notch, .rz-selectbutton .rz-paginator-element .rz-datatable-loading-content .rzi-circle-o-notch, .rz-datatable-loading-content .rz-selectbutton .rz-paginator-element .rzi-circle-o-notch, .rz-fileupload-choose .rz-datatable-loading-content .rzi-circle-o-notch, .rz-datatable-loading-content .rz-fileupload-choose .rzi-circle-o-notch, .rz-date-filter-buttons .rz-button .rz-datatable-loading-content .rzi-circle-o-notch, .rz-datatable-loading-content .rz-date-filter-buttons .rz-button .rzi-circle-o-notch, .rz-date-filter-buttons .rz-paginator-element .rz-datatable-loading-content .rzi-circle-o-notch, .rz-datatable-loading-content .rz-date-filter-buttons .rz-paginator-element .rzi-circle-o-notch, .rz-button-md .rz-column-drag, .rz-splitbutton .rz-button-text-icon-left .rz-column-drag, .rz-splitbutton .rz-button-text-only .rz-column-drag, .rz-splitbutton-menubutton .rz-column-drag, .rz-selectbutton .rz-button .rz-column-drag, .rz-selectbutton .rz-paginator-element .rz-column-drag, .rz-fileupload-choose .rz-column-drag, .rz-date-filter-buttons .rz-button .rz-column-drag, .rz-date-filter-buttons .rz-paginator-element .rz-column-drag {
      font-size: 1.0625rem;
      height: auto;
      line-height: 2.1875rem;
      width: 1.25rem; }
    .rz-button-md .rz-button-text, .rz-splitbutton .rz-button-text-icon-left .rz-button-text, .rz-splitbutton .rz-button-text-only .rz-button-text, .rz-splitbutton-menubutton .rz-button-text, .rz-selectbutton .rz-button .rz-button-text, .rz-selectbutton .rz-paginator-element .rz-button-text, .rz-fileupload-choose .rz-button-text, .rz-date-filter-buttons .rz-button .rz-button-text, .rz-date-filter-buttons .rz-paginator-element .rz-button-text {
      line-height: 2.1875rem; }
  .rz-button-sm, .rz-fileupload .rz-button, .rz-fileupload .rz-paginator-element, .rz-paginator-element {
    font-size: 0.875rem;
    padding: 0 2.875rem;
    height: 1.75rem; }
    .rz-button-sm.rz-button-text-icon-left, .rz-fileupload .rz-button-text-icon-left.rz-button, .rz-button-text-icon-left.rz-paginator-element {
      padding: 0 1.375rem; }
    .rz-button-sm.rz-button-icon-only, .rz-fileupload .rz-button-icon-only.rz-button, .rz-fileupload .rz-paginator-element, .rz-paginator-element {
      padding: 0 0.3125rem; }
    .rz-button-sm .rzi, .rz-fileupload .rz-button .rzi, .rz-fileupload .rz-paginator-element .rzi, .rz-paginator-element .rzi, .rz-button-sm .rz-menuitem .rz-menuitem-icon, .rz-menuitem .rz-button-sm .rz-menuitem-icon, .rz-fileupload .rz-button .rz-menuitem .rz-menuitem-icon, .rz-menuitem .rz-fileupload .rz-button .rz-menuitem-icon, .rz-paginator-element .rz-menuitem .rz-menuitem-icon, .rz-menuitem .rz-paginator-element .rz-menuitem-icon, .rz-button-sm .rz-fileupload-row .rz-button .rzi-close, .rz-fileupload-row .rz-button .rz-button-sm .rzi-close, .rz-fileupload .rz-fileupload-row .rz-button .rzi-close, .rz-fileupload-row .rz-fileupload .rz-button .rzi-close, .rz-paginator-element .rz-fileupload-row .rz-button .rzi-close, .rz-fileupload-row .rz-paginator-element .rzi-close,
    .rz-button-sm .rz-fileupload-row .rz-button .rzi-times, .rz-fileupload-row .rz-button .rz-button-sm .rzi-times,
    .rz-fileupload .rz-fileupload-row .rz-button .rzi-times, .rz-fileupload-row .rz-fileupload .rz-button .rzi-times,
    .rz-paginator-element .rz-fileupload-row .rz-button .rzi-times,
    .rz-fileupload-row .rz-paginator-element .rzi-times,
    .rz-button-sm .rz-fileupload-row .rz-button .rz-icon-trash, .rz-fileupload-row .rz-button .rz-button-sm .rz-icon-trash,
    .rz-fileupload .rz-fileupload-row .rz-button .rz-icon-trash, .rz-fileupload-row .rz-fileupload .rz-button .rz-icon-trash,
    .rz-paginator-element .rz-fileupload-row .rz-button .rz-icon-trash,
    .rz-fileupload-row .rz-paginator-element .rz-icon-trash, .rz-button-sm .rz-datatable .rzi-chevron-circle-right, .rz-datatable .rz-button-sm .rzi-chevron-circle-right, .rz-fileupload .rz-button .rz-datatable .rzi-chevron-circle-right, .rz-datatable .rz-fileupload .rz-button .rzi-chevron-circle-right, .rz-paginator-element .rz-datatable .rzi-chevron-circle-right, .rz-datatable .rz-paginator-element .rzi-chevron-circle-right, .rz-button-sm .rz-datatable .rzi-chevron-circle-down, .rz-datatable .rz-button-sm .rzi-chevron-circle-down, .rz-fileupload .rz-button .rz-datatable .rzi-chevron-circle-down, .rz-datatable .rz-fileupload .rz-button .rzi-chevron-circle-down, .rz-paginator-element .rz-datatable .rzi-chevron-circle-down, .rz-datatable .rz-paginator-element .rzi-chevron-circle-down, .rz-button-sm .rz-sortable-column .rzi-grid-sort, .rz-sortable-column .rz-button-sm .rzi-grid-sort, .rz-fileupload .rz-button .rz-sortable-column .rzi-grid-sort, .rz-sortable-column .rz-fileupload .rz-button .rzi-grid-sort, .rz-paginator-element .rz-sortable-column .rzi-grid-sort, .rz-sortable-column .rz-paginator-element .rzi-grid-sort, .rz-button-sm .rz-datatable-header .rzi-plus, .rz-datatable-header .rz-button-sm .rzi-plus, .rz-fileupload .rz-button .rz-datatable-header .rzi-plus, .rz-datatable-header .rz-fileupload .rz-button .rzi-plus, .rz-paginator-element .rz-datatable-header .rzi-plus, .rz-datatable-header .rz-paginator-element .rzi-plus, .rz-button-sm .rz-datatable-loading-content .rzi-circle-o-notch, .rz-datatable-loading-content .rz-button-sm .rzi-circle-o-notch, .rz-fileupload .rz-button .rz-datatable-loading-content .rzi-circle-o-notch, .rz-datatable-loading-content .rz-fileupload .rz-button .rzi-circle-o-notch, .rz-paginator-element .rz-datatable-loading-content .rzi-circle-o-notch, .rz-datatable-loading-content .rz-paginator-element .rzi-circle-o-notch, .rz-button-sm .rz-column-drag, .rz-fileupload .rz-button .rz-column-drag, .rz-fileupload .rz-paginator-element .rz-column-drag, .rz-paginator-element .rz-column-drag {
      font-size: 0.9375rem;
      height: auto;
      line-height: 1.75rem;
      width: 1.125rem; }
    .rz-button-sm .rz-button-text, .rz-fileupload .rz-button .rz-button-text, .rz-fileupload .rz-paginator-element .rz-button-text, .rz-paginator-element .rz-button-text {
      line-height: 1.75rem; }

.rz-textbox:hover:not(:focus), .rz-textarea:hover:not(:focus), .mask:hover:not(:focus), .rz-dropdown:hover:not(:focus), .rz-multiselect:hover:not(:focus), .rz-calendar .rz-inputtext:hover:not(:focus), .rz-spinner:hover:not(:focus), .rz-lookup-search input:hover:not(:focus), .rz-colorpicker:hover:not(:focus), .rz-chkbox-box:hover:not(.rz-state-disabled), .rz-radiobutton-box:hover:not(.rz-state-disabled), .rz-autocomplete:hover {
  box-shadow: inset 0 4px 7px 0 rgba(0, 0, 0, 0.03);
  border: 1px solid #2f3a40; }

.rz-textbox:disabled, .rz-textarea:disabled, .mask:disabled, .rz-dropdown:disabled, .rz-multiselect:disabled, .rz-calendar .rz-inputtext:disabled, .rz-spinner:disabled, .rz-lookup-search input:disabled, .rz-colorpicker:disabled, .rz-chkbox-box.rz-state-disabled, .rz-radiobutton-box.rz-state-disabled, .rz-state-disabled.rz-dropdown, .rz-state-disabled.rz-multiselect, .rz-autocomplete-input:disabled, .rz-listbox.rz-state-disabled, .rz-calendar.rz-state-disabled .rz-inputtext, .rz-spinner.rz-state-disabled {
  color: #868e96;
  box-shadow: inset 0 4px 3px 0 rgba(0, 0, 0, 0.03);
  background-color: rgba(136, 152, 155, 0.06);
  border: solid 1px rgba(47, 58, 64, 0.53); }
  .rz-textbox:disabled::placeholder, .rz-textarea:disabled::placeholder, .mask:disabled::placeholder, .rz-dropdown:disabled::placeholder, .rz-multiselect:disabled::placeholder, .rz-calendar .rz-inputtext:disabled::placeholder, .rz-spinner:disabled::placeholder, .rz-lookup-search input:disabled::placeholder, .rz-colorpicker:disabled::placeholder, .rz-chkbox-box.rz-state-disabled::placeholder, .rz-radiobutton-box.rz-state-disabled::placeholder, .rz-state-disabled.rz-dropdown::placeholder, .rz-state-disabled.rz-multiselect::placeholder, .rz-autocomplete-input:disabled::placeholder, .rz-listbox.rz-state-disabled::placeholder, .rz-calendar.rz-state-disabled .rz-inputtext::placeholder, .rz-spinner.rz-state-disabled::placeholder {
    color: #868e96; }
  .rz-textbox:disabled .rz-inputtext, .rz-textarea:disabled .rz-inputtext, .mask:disabled .rz-inputtext, .rz-dropdown:disabled .rz-inputtext, .rz-multiselect:disabled .rz-inputtext, .rz-calendar .rz-inputtext:disabled .rz-inputtext, .rz-spinner:disabled .rz-inputtext, .rz-lookup-search input:disabled .rz-inputtext, .rz-colorpicker:disabled .rz-inputtext, .rz-chkbox-box.rz-state-disabled .rz-inputtext, .rz-radiobutton-box.rz-state-disabled .rz-inputtext, .rz-state-disabled.rz-dropdown .rz-inputtext, .rz-state-disabled.rz-multiselect .rz-inputtext, .rz-autocomplete-input:disabled .rz-inputtext, .rz-listbox.rz-state-disabled .rz-inputtext, .rz-calendar.rz-state-disabled .rz-inputtext .rz-inputtext, .rz-spinner.rz-state-disabled .rz-inputtext {
    background-color: rgba(136, 152, 155, 0.06);
    color: #868e96; }

input {
  color: #ffffff;
  font-size: 1rem; }
  input::placeholder {
    color: #6c757d; }

.rz-textbox, .rz-textarea, .mask, .rz-dropdown, .rz-multiselect, .rz-calendar .rz-inputtext, .rz-spinner, .rz-lookup-search input, .rz-colorpicker {
  padding: 0.1875rem 0.625rem; }

.rz-textbox, .rz-textarea, .mask, .rz-dropdown, .rz-multiselect, .rz-calendar .rz-inputtext, .rz-spinner, .rz-lookup-search input, .rz-colorpicker {
  border: 1px solid #2f3a40;
  border-radius: 4px;
  box-shadow: inset 0 4px 3px 0 rgba(0, 0, 0, 0.03);
  background-color: #323f45; }

.rz-textbox, .rz-textarea, .mask, .rz-dropdown, .rz-multiselect, .rz-calendar .rz-inputtext, .rz-spinner, .rz-lookup-search input, .rz-colorpicker {
  height: 2.1875rem;
  line-height: 1.5;
  color: #ffffff;
  font-size: 1rem; }

@media (max-width: 768px) {
  .rz-textbox, .rz-textarea, .mask, .rz-dropdown, .rz-multiselect, .rz-calendar .rz-inputtext, .rz-spinner, .rz-lookup-search input, .rz-colorpicker,
  input {
    font-size: 1rem; } }

.header {
  background-color: #5d717b;
  min-height: 3.125rem;
  border-bottom: 1px solid #435863;
  color: #ffffff;
  box-shadow: none; }
  .header.fixed {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 2; }

.footer {
  padding: 1rem;
  border-top: 1px solid #435863;
  background-color: #5d717b;
  color: #ffffff; }
  .footer.fixed {
    position: fixed;
    z-index: 2;
    bottom: 0;
    left: 0;
    right: 0; }

.rz-sidebar {
  background-color: #5d717b;
  border-right: 1px solid #eaeef0;
  position: fixed;
  overflow: auto;
  left: 0;
  top: 0;
  bottom: 0;
  z-index: 1;
  width: 250px; }

.rz-card {
  padding: 1.25rem 1.5625rem;
  border-radius: 4px;
  box-shadow: 0 6px 14px 0 rgba(0, 0, 0, 0.06);
  background-color: #5d717b; }
  .rz-card h1,
  .rz-card h2,
  .rz-card h3,
  .rz-card h4,
  .rz-card h5,
  .rz-card h6 {
    margin-bottom: 0.8125rem; }
  .rz-card p {
    margin-bottom: 0; }

.body {
  padding: 1rem; }

.rz-badge {
  color: #ffffff;
  display: inline-block;
  padding: .25em .4em;
  font-size: 75%;
  font-weight: 700;
  line-height: 1;
  text-align: center;
  white-space: nowrap; }

.rz-badge-primary {
  background-color: #ff6d41; }

.rz-badge-light {
  background-color: #e6ecef;
  color: #3a474d; }

.rz-badge-secondary {
  background-color: #479cc8; }

.rz-badge-info {
  background-color: #68d5c8; }

.rz-badge-warning {
  background-color: #e6c54f; }

.rz-badge-error {
  background-color: #f9777f; }

.rz-badge-danger {
  background-color: #f9777f; }

.rz-badge-success {
  background-color: #5dbf74; }

.rz-badge-pill {
  border-radius: 10rem; }

.rz-accordion {
  box-shadow: none; }

.rz-accordion-header {
  background-color: #5d717b; }
  .rz-accordion-header a[role='tab'] {
    color: #ffffff;
    line-height: 1.5rem;
    text-decoration: none;
    color: inherit;
    display: flex;
    align-items: center;
    font-weight: 600; }
  .rz-accordion-header:hover {
    color: #4db9f2; }

.rz-accordion-toggle-icon {
  width: 1.5rem;
  height: 1.5rem;
  font-size: 1.5rem;
  margin: 0 0.375rem 0 0; }
  .rz-accordion-toggle-icon.rzi-chevron-right:before {
    content: 'arrow_right'; }
  .rz-accordion-toggle-icon.rzi-chevron-down:before {
    content: 'arrow_drop_down'; }

.rz-state-active a[role='tab'] {
  color: #4db9f2; }

.rz-accordion-content-wrapper-overflown {
  overflow: hidden; }

.rz-accordion-content {
  background-color: #5d717b;
  padding: 0.5rem 0.5rem 0.5rem 2rem; }

.rz-panel {
  background: #5d717b;
  border-radius: 4px;
  padding: 0.625rem 1.25rem;
  box-shadow: none;
  display: flex;
  flex-direction: column; }

.rz-panel-titlebar {
  display: flex;
  justify-content: space-between; }

.rz-panel-content {
  margin: 1.4375rem 0 0 0; }

.rz-panel-content-wrapper[aria-hidden='true'] {
  display: none; }

.rz-panel-title {
  line-height: 1.5rem;
  font-weight: 600; }

.rz-panel-titlebar-toggler {
  width: 1.625rem;
  height: 1.625rem;
  border-radius: 2px;
  background-color: #3a474d;
  color: inherit;
  display: flex;
  align-items: center;
  justify-content: center; }
  .rz-panel-titlebar-toggler:hover {
    text-decoration: none;
    color: #4db9f2; }
  .rz-panel-titlebar-toggler .rzi-minus:before {
    content: 'remove'; }
  .rz-panel-titlebar-toggler .rzi-plus:before {
    content: 'add'; }

.sidebar-toggle {
  -webkit-appearance: none;
  border: none;
  padding: 0.8125rem;
  margin: 0 1rem 0 0;
  border-right: 1px solid #435863;
  background-color: #5d717b;
  color: #ffffff; }
  .sidebar-toggle:focus {
    outline: none; }
  .sidebar-toggle:hover {
    color: #479cc8;
    background: #5d717b;
    border-radius: 0; }
  .sidebar-toggle .rzi, .sidebar-toggle .rz-menuitem .rz-menuitem-icon, .rz-menuitem .sidebar-toggle .rz-menuitem-icon, .sidebar-toggle .rz-fileupload-row .rz-button .rzi-close, .rz-fileupload-row .rz-button .sidebar-toggle .rzi-close, .sidebar-toggle .rz-fileupload-row .rz-paginator-element .rzi-close, .rz-fileupload-row .rz-paginator-element .sidebar-toggle .rzi-close,
  .sidebar-toggle .rz-fileupload-row .rz-button .rzi-times, .rz-fileupload-row .rz-button .sidebar-toggle .rzi-times,
  .sidebar-toggle .rz-fileupload-row .rz-paginator-element .rzi-times, .rz-fileupload-row .rz-paginator-element .sidebar-toggle .rzi-times,
  .sidebar-toggle .rz-fileupload-row .rz-button .rz-icon-trash, .rz-fileupload-row .rz-button .sidebar-toggle .rz-icon-trash,
  .sidebar-toggle .rz-fileupload-row .rz-paginator-element .rz-icon-trash, .rz-fileupload-row .rz-paginator-element .sidebar-toggle .rz-icon-trash, .sidebar-toggle .rz-datatable .rzi-chevron-circle-right, .rz-datatable .sidebar-toggle .rzi-chevron-circle-right, .sidebar-toggle .rz-datatable .rzi-chevron-circle-down, .rz-datatable .sidebar-toggle .rzi-chevron-circle-down, .sidebar-toggle .rz-sortable-column .rzi-grid-sort, .rz-sortable-column .sidebar-toggle .rzi-grid-sort, .sidebar-toggle .rz-datatable-header .rzi-plus, .rz-datatable-header .sidebar-toggle .rzi-plus, .sidebar-toggle .rz-datatable-loading-content .rzi-circle-o-notch, .rz-datatable-loading-content .sidebar-toggle .rzi-circle-o-notch, .sidebar-toggle .rz-column-drag {
    width: 1.5rem;
    height: 1.5rem; }

.rz-navigation-item-link {
  display: flex;
  align-items: center;
  cursor: default; }
  .rz-navigation-item-link:hover {
    text-decoration: none; }

.rz-navigation-item-text {
  flex: auto; }

.rz-context-menu .rz-menu {
  flex-direction: column; }

.rz-context-menu .rz-menu, .rz-context-menu .rz-navigation-menu {
  box-shadow: 0 10px 8px 0 rgba(58, 71, 77, 0.06);
  border: 1px solid #2f3a40; }

.rz-menu:not(.rz-profile-menu) {
  list-style: none;
  margin: 0;
  padding: 0;
  display: flex;
  background-color: #5d717b; }
  .rz-menu:not(.rz-profile-menu) .rz-navigation-item-link {
    color: rgba(255, 255, 255, 0.5);
    white-space: nowrap; }
  .rz-menu:not(.rz-profile-menu) .rz-navigation-item-wrapper {
    padding: 0.8125rem 0.625rem; }
    .rz-menu:not(.rz-profile-menu) .rz-navigation-item-wrapper:hover .rz-navigation-item-link {
      color: #4db9f2; }
  .rz-menu:not(.rz-profile-menu) .rz-navigation-item-wrapper-active .rz-navigation-item-link {
    color: #ffffff; }
  .rz-menu:not(.rz-profile-menu) > .rz-navigation-item > .rz-navigation-item-wrapper-active:before {
    position: absolute;
    content: '';
    bottom: -2px;
    height: 1px;
    left: 0;
    right: 0;
    background-color: #ffffff;
    margin-left: 0.75rem; }
  .rz-menu:not(.rz-profile-menu) .rz-navigation-item {
    position: relative; }
  .rz-menu:not(.rz-profile-menu) .rz-navigation-menu {
    list-style: none;
    overflow: hidden;
    position: absolute;
    padding: 0;
    margin: 0;
    min-width: 100%;
    box-shadow: 0 10px 8px 0 rgba(58, 71, 77, 0.06);
    border-radius: 2px;
    background-color: #5d717b; }
    .rz-menu:not(.rz-profile-menu) .rz-navigation-menu .rz-navigation-item-wrapper {
      padding: 0.375rem 1.25rem; }
      .rz-menu:not(.rz-profile-menu) .rz-navigation-menu .rz-navigation-item-wrapper:hover {
        background-color: #479cc8; }
        .rz-menu:not(.rz-profile-menu) .rz-navigation-menu .rz-navigation-item-wrapper:hover .rz-navigation-item-link {
          color: #ffffff; }
    .rz-menu:not(.rz-profile-menu) .rz-navigation-menu .rz-navigation-item-link {
      color: rgba(255, 255, 255, 0.5);
      white-space: nowrap; }
    .rz-menu:not(.rz-profile-menu) .rz-navigation-menu .rz-navigation-menu {
      position: static;
      box-shadow: none; }
      .rz-menu:not(.rz-profile-menu) .rz-navigation-menu .rz-navigation-menu .rz-navigation-item-link {
        color: rgba(255, 255, 255, 0.5);
        margin-left: 0.75rem; }
      .rz-menu:not(.rz-profile-menu) .rz-navigation-menu .rz-navigation-menu .rz-navigation-menu .rz-navigation-item-link {
        margin-left: 1.5rem; }
      .rz-menu:not(.rz-profile-menu) .rz-navigation-menu .rz-navigation-menu .rz-navigation-menu .rz-navigation-menu .rz-navigation-item-link {
        margin-left: 2.25rem; }
  .rz-menu:not(.rz-profile-menu) .rzi:not(.rz-navigation-item-icon-children), .rz-menu:not(.rz-profile-menu) .rz-menuitem .rz-menuitem-icon:not(.rz-navigation-item-icon-children), .rz-menuitem .rz-menu:not(.rz-profile-menu) .rz-menuitem-icon:not(.rz-navigation-item-icon-children), .rz-menu:not(.rz-profile-menu) .rz-fileupload-row .rz-button .rzi-close:not(.rz-navigation-item-icon-children), .rz-fileupload-row .rz-button .rz-menu:not(.rz-profile-menu) .rzi-close:not(.rz-navigation-item-icon-children), .rz-menu:not(.rz-profile-menu) .rz-fileupload-row .rz-paginator-element .rzi-close:not(.rz-navigation-item-icon-children), .rz-fileupload-row .rz-paginator-element .rz-menu:not(.rz-profile-menu) .rzi-close:not(.rz-navigation-item-icon-children),
  .rz-menu:not(.rz-profile-menu) .rz-fileupload-row .rz-button .rzi-times:not(.rz-navigation-item-icon-children), .rz-fileupload-row .rz-button .rz-menu:not(.rz-profile-menu) .rzi-times:not(.rz-navigation-item-icon-children),
  .rz-menu:not(.rz-profile-menu) .rz-fileupload-row .rz-paginator-element .rzi-times:not(.rz-navigation-item-icon-children), .rz-fileupload-row .rz-paginator-element .rz-menu:not(.rz-profile-menu) .rzi-times:not(.rz-navigation-item-icon-children),
  .rz-menu:not(.rz-profile-menu) .rz-fileupload-row .rz-button .rz-icon-trash:not(.rz-navigation-item-icon-children), .rz-fileupload-row .rz-button .rz-menu:not(.rz-profile-menu) .rz-icon-trash:not(.rz-navigation-item-icon-children),
  .rz-menu:not(.rz-profile-menu) .rz-fileupload-row .rz-paginator-element .rz-icon-trash:not(.rz-navigation-item-icon-children), .rz-fileupload-row .rz-paginator-element .rz-menu:not(.rz-profile-menu) .rz-icon-trash:not(.rz-navigation-item-icon-children), .rz-menu:not(.rz-profile-menu) .rz-datatable .rzi-chevron-circle-right:not(.rz-navigation-item-icon-children), .rz-datatable .rz-menu:not(.rz-profile-menu) .rzi-chevron-circle-right:not(.rz-navigation-item-icon-children), .rz-menu:not(.rz-profile-menu) .rz-datatable .rzi-chevron-circle-down:not(.rz-navigation-item-icon-children), .rz-datatable .rz-menu:not(.rz-profile-menu) .rzi-chevron-circle-down:not(.rz-navigation-item-icon-children), .rz-menu:not(.rz-profile-menu) .rz-sortable-column .rzi-grid-sort:not(.rz-navigation-item-icon-children), .rz-sortable-column .rz-menu:not(.rz-profile-menu) .rzi-grid-sort:not(.rz-navigation-item-icon-children), .rz-menu:not(.rz-profile-menu) .rz-datatable-header .rzi-plus:not(.rz-navigation-item-icon-children), .rz-datatable-header .rz-menu:not(.rz-profile-menu) .rzi-plus:not(.rz-navigation-item-icon-children), .rz-menu:not(.rz-profile-menu) .rz-datatable-loading-content .rzi-circle-o-notch:not(.rz-navigation-item-icon-children), .rz-datatable-loading-content .rz-menu:not(.rz-profile-menu) .rzi-circle-o-notch:not(.rz-navigation-item-icon-children), .rz-menu:not(.rz-profile-menu) .rz-column-drag:not(.rz-navigation-item-icon-children) {
    margin: 0 0.625rem 0 0; }

.rz-menu-toggle-item {
  display: none;
  padding: 0.8125rem 0.625rem;
  text-align: right;
  width: 100%;
  height: 100%; }

.rz-menu-toggle {
  appearance: none;
  background-color: transparent;
  border: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0; }
  .rz-menu-toggle:active {
    background-color: transparent;
    color: inherit; }

@media (max-width: 768px) {
  .rz-menu:not(.rz-profile-menu).rz-menu-closed .rz-navigation-item {
    display: none; }
  .rz-menu:not(.rz-profile-menu) .rz-menu-toggle .rzi, .rz-menu:not(.rz-profile-menu) .rz-menu-toggle .rz-menuitem .rz-menuitem-icon, .rz-menuitem .rz-menu:not(.rz-profile-menu) .rz-menu-toggle .rz-menuitem-icon, .rz-menu:not(.rz-profile-menu) .rz-menu-toggle .rz-fileupload-row .rz-button .rzi-close, .rz-fileupload-row .rz-button .rz-menu:not(.rz-profile-menu) .rz-menu-toggle .rzi-close, .rz-menu:not(.rz-profile-menu) .rz-menu-toggle .rz-fileupload-row .rz-paginator-element .rzi-close, .rz-fileupload-row .rz-paginator-element .rz-menu:not(.rz-profile-menu) .rz-menu-toggle .rzi-close,
  .rz-menu:not(.rz-profile-menu) .rz-menu-toggle .rz-fileupload-row .rz-button .rzi-times, .rz-fileupload-row .rz-button .rz-menu:not(.rz-profile-menu) .rz-menu-toggle .rzi-times,
  .rz-menu:not(.rz-profile-menu) .rz-menu-toggle .rz-fileupload-row .rz-paginator-element .rzi-times, .rz-fileupload-row .rz-paginator-element .rz-menu:not(.rz-profile-menu) .rz-menu-toggle .rzi-times,
  .rz-menu:not(.rz-profile-menu) .rz-menu-toggle .rz-fileupload-row .rz-button .rz-icon-trash, .rz-fileupload-row .rz-button .rz-menu:not(.rz-profile-menu) .rz-menu-toggle .rz-icon-trash,
  .rz-menu:not(.rz-profile-menu) .rz-menu-toggle .rz-fileupload-row .rz-paginator-element .rz-icon-trash, .rz-fileupload-row .rz-paginator-element .rz-menu:not(.rz-profile-menu) .rz-menu-toggle .rz-icon-trash, .rz-menu:not(.rz-profile-menu) .rz-menu-toggle .rz-datatable .rzi-chevron-circle-right, .rz-datatable .rz-menu:not(.rz-profile-menu) .rz-menu-toggle .rzi-chevron-circle-right, .rz-menu:not(.rz-profile-menu) .rz-menu-toggle .rz-datatable .rzi-chevron-circle-down, .rz-datatable .rz-menu:not(.rz-profile-menu) .rz-menu-toggle .rzi-chevron-circle-down, .rz-menu:not(.rz-profile-menu) .rz-menu-toggle .rz-sortable-column .rzi-grid-sort, .rz-sortable-column .rz-menu:not(.rz-profile-menu) .rz-menu-toggle .rzi-grid-sort, .rz-menu:not(.rz-profile-menu) .rz-menu-toggle .rz-datatable-header .rzi-plus, .rz-datatable-header .rz-menu:not(.rz-profile-menu) .rz-menu-toggle .rzi-plus, .rz-menu:not(.rz-profile-menu) .rz-menu-toggle .rz-datatable-loading-content .rzi-circle-o-notch, .rz-datatable-loading-content .rz-menu:not(.rz-profile-menu) .rz-menu-toggle .rzi-circle-o-notch, .rz-menu:not(.rz-profile-menu) .rz-menu-toggle .rz-column-drag {
    margin: 0; }
  .rz-menu:not(.rz-profile-menu).rz-menu-open {
    display: block; }
    .rz-menu:not(.rz-profile-menu).rz-menu-open .rz-navigation-item {
      background-color: inherit; }
    .rz-menu:not(.rz-profile-menu).rz-menu-open .rz-navigation-menu {
      position: static;
      box-shadow: none; }
  .rz-menu:not(.rz-profile-menu) .rz-menu-toggle-item {
    display: block; }
  .rz-menu:not(.rz-profile-menu) .rz-navigation-item-wrapper-active:before {
    display: none !important; } }

.rz-panel-menu {
  list-style: none;
  padding: 0;
  margin-bottom: 0;
  overflow: auto;
  color: #ffffff;
  background-color: #5d717b; }
  .rz-panel-menu .rz-navigation-item {
    border-bottom: solid 1px rgba(54, 61, 64, 0.1); }
  .rz-panel-menu .rz-navigation-item-active {
    background-color: #435863; }
  .rz-panel-menu .rz-navigation-item-wrapper {
    position: relative;
    line-height: 1.575;
    padding: 0.625rem 0; }
    .rz-panel-menu .rz-navigation-item-wrapper:hover {
      background-color: #435863;
      color: #ffffff; }
  .rz-panel-menu .rz-navigation-item-wrapper-active {
    background-color: #435863; }
    .rz-panel-menu .rz-navigation-item-wrapper-active:before {
      position: absolute;
      content: '';
      top: 0;
      bottom: 0;
      width: 4px;
      background-color: #479cc8; }
  .rz-panel-menu .rz-navigation-item-link {
    padding: 0 1.25rem 0 1.25rem;
    color: inherit;
    cursor: pointer; }
  .rz-panel-menu a.rz-navigation-item-link {
    cursor: pointer; }
  .rz-panel-menu .rz-navigation-item-text {
    flex: auto; }
  .rz-panel-menu .rz-navigation-item-icon-children {
    font-size: 1.5rem; }
  .rz-panel-menu .rz-navigation-item-icon {
    height: 1.575;
    width: 1.5rem;
    color: rgba(58, 71, 77, 0.6);
    margin: 0 0.9375rem 0 0; }
  .rz-panel-menu .rz-navigation-menu {
    list-style: none;
    padding: 0;
    margin-bottom: 0;
    overflow: hidden; }
    .rz-panel-menu .rz-navigation-menu .rz-navigation-item-active {
      background-color: #e9eaec;
      color: #3a474d; }
    .rz-panel-menu .rz-navigation-menu .rz-navigation-item-wrapper {
      padding: 0.25rem 0;
      background-color: #435863; }
      .rz-panel-menu .rz-navigation-menu .rz-navigation-item-wrapper:hover {
        background-color: #e9eaec;
        color: #3a474d; }
    .rz-panel-menu .rz-navigation-menu .rz-navigation-item-icon {
      margin: 0 0.75rem 0 -2.25rem; }
    .rz-panel-menu .rz-navigation-menu .rz-navigation-menu .rz-navigation-item-link {
      padding-left: 0.75rem; }
    .rz-panel-menu .rz-navigation-menu .rz-navigation-menu .rz-navigation-item-active,
    .rz-panel-menu .rz-navigation-menu .rz-navigation-menu .rz-navigation-item-wrapper-active {
      background-color: #dddfe1;
      color: #3a474d; }
    .rz-panel-menu .rz-navigation-menu .rz-navigation-menu .rz-navigation-item-wrapper {
      background-color: #e9eaec;
      color: #000; }
      .rz-panel-menu .rz-navigation-menu .rz-navigation-menu .rz-navigation-item-wrapper:hover {
        background-color: #dddfe1;
        color: #3a474d; }
    .rz-panel-menu .rz-navigation-menu .rz-navigation-item {
      border-bottom: none;
      font-size: 0.875rem; }
      .rz-panel-menu .rz-navigation-menu .rz-navigation-item .rz-navigation-item-link {
        padding-left: 3.625rem; }

ul.rz-profile-menu {
  list-style: none;
  margin-bottom: 0;
  padding: 0.5625rem 1.25rem;
  display: inline-block;
  background-color: #5d717b;
  border-left: 1px solid #435863;
  position: relative;
  z-index: 1; }
  ul.rz-profile-menu .rz-navigation-item-icon-children {
    color: #ffffff; }
  ul.rz-profile-menu .rz-navigation-menu {
    border-radius: 2px;
    background-color: #5d717b;
    box-shadow: 0 10px 8px 0 rgba(58, 71, 77, 0.06);
    overflow: hidden;
    margin-bottom: 0;
    padding: 0;
    position: absolute;
    text-align: left;
    top: calc(100% - 2px);
    min-width: 100%;
    right: 0;
    white-space: nowrap; }
    ul.rz-profile-menu .rz-navigation-menu .rz-navigation-item-wrapper {
      padding: 0.375rem 1.25rem; }
      ul.rz-profile-menu .rz-navigation-menu .rz-navigation-item-wrapper:hover {
        background-color: #479cc8;
        color: #ffffff; }
        ul.rz-profile-menu .rz-navigation-menu .rz-navigation-item-wrapper:hover .rz-navigation-item-link {
          color: #ffffff; }
        ul.rz-profile-menu .rz-navigation-menu .rz-navigation-item-wrapper:hover .rz-navigation-item-text {
          color: #ffffff; }
    ul.rz-profile-menu .rz-navigation-menu .rz-navigation-item-link,
    ul.rz-profile-menu .rz-navigation-menu .rz-navigation-item-text {
      color: rgba(255, 255, 255, 0.5); }
    ul.rz-profile-menu .rz-navigation-menu .rzi, ul.rz-profile-menu .rz-navigation-menu .rz-menuitem .rz-menuitem-icon, .rz-menuitem ul.rz-profile-menu .rz-navigation-menu .rz-menuitem-icon, ul.rz-profile-menu .rz-navigation-menu .rz-fileupload-row .rz-button .rzi-close, .rz-fileupload-row .rz-button ul.rz-profile-menu .rz-navigation-menu .rzi-close, ul.rz-profile-menu .rz-navigation-menu .rz-fileupload-row .rz-paginator-element .rzi-close, .rz-fileupload-row .rz-paginator-element ul.rz-profile-menu .rz-navigation-menu .rzi-close,
    ul.rz-profile-menu .rz-navigation-menu .rz-fileupload-row .rz-button .rzi-times, .rz-fileupload-row .rz-button ul.rz-profile-menu .rz-navigation-menu .rzi-times,
    ul.rz-profile-menu .rz-navigation-menu .rz-fileupload-row .rz-paginator-element .rzi-times, .rz-fileupload-row .rz-paginator-element ul.rz-profile-menu .rz-navigation-menu .rzi-times,
    ul.rz-profile-menu .rz-navigation-menu .rz-fileupload-row .rz-button .rz-icon-trash, .rz-fileupload-row .rz-button ul.rz-profile-menu .rz-navigation-menu .rz-icon-trash,
    ul.rz-profile-menu .rz-navigation-menu .rz-fileupload-row .rz-paginator-element .rz-icon-trash, .rz-fileupload-row .rz-paginator-element ul.rz-profile-menu .rz-navigation-menu .rz-icon-trash, ul.rz-profile-menu .rz-navigation-menu .rz-datatable .rzi-chevron-circle-right, .rz-datatable ul.rz-profile-menu .rz-navigation-menu .rzi-chevron-circle-right, ul.rz-profile-menu .rz-navigation-menu .rz-datatable .rzi-chevron-circle-down, .rz-datatable ul.rz-profile-menu .rz-navigation-menu .rzi-chevron-circle-down, ul.rz-profile-menu .rz-navigation-menu .rz-sortable-column .rzi-grid-sort, .rz-sortable-column ul.rz-profile-menu .rz-navigation-menu .rzi-grid-sort, ul.rz-profile-menu .rz-navigation-menu .rz-datatable-header .rzi-plus, .rz-datatable-header ul.rz-profile-menu .rz-navigation-menu .rzi-plus, ul.rz-profile-menu .rz-navigation-menu .rz-datatable-loading-content .rzi-circle-o-notch, .rz-datatable-loading-content ul.rz-profile-menu .rz-navigation-menu .rzi-circle-o-notch, ul.rz-profile-menu .rz-navigation-menu .rz-column-drag {
      width: 1.125rem;
      height: 1.125rem;
      font-size: 1.125rem;
      margin: 0 0.625rem 0 0; }

.rz-gravatar {
  width: 2rem;
  height: 2rem;
  display: inline-block;
  border-radius: 50%; }

.rz-steps ul {
  list-style: none;
  padding: 0; }

.rz-steps .rz-menuitem-link {
  display: inline-flex;
  align-items: center;
  color: #ffffff;
  cursor: pointer; }
  .rz-steps .rz-menuitem-link:hover {
    text-decoration: none;
    color: #479cc8 !important; }

.rz-steps .rz-state-disabled .rz-menuitem-link {
  color: rgba(255, 255, 255, 0.5);
  cursor: default; }
  .rz-steps .rz-state-disabled .rz-menuitem-link:hover {
    color: rgba(255, 255, 255, 0.5) !important; }

.rz-steps .rz-state-highlight .rz-steps-title {
  color: #479cc8; }

.rz-steps .rz-state-highlight .rz-steps-number {
  background: #479cc8;
  color: #ffffff; }

.rz-steps-item {
  display: inline-block; }

.rz-steps-title {
  margin: 0 0.8125rem; }

.rz-steps-number {
  text-align: center;
  line-height: 1;
  color: #414141;
  padding: 0.4375rem 0;
  width: 2rem;
  height: 2rem;
  background-color: #e6ecef;
  border-radius: 50%; }

.rz-steps-buttons {
  display: flex;
  justify-content: space-between; }

.rz-steps-next,
.rz-steps-prev {
  display: inline-flex;
  align-items: center;
  color: #88989b !important; }
  .rz-steps-next:not(.rz-state-disabled):hover,
  .rz-steps-prev:not(.rz-state-disabled):hover {
    cursor: pointer;
    color: #479cc8 !important; }
  .rz-steps-next:hover,
  .rz-steps-prev:hover {
    text-decoration: none; }
  .rz-steps-next.rz-state-disabled,
  .rz-steps-prev.rz-state-disabled {
    color: rgba(136, 152, 155, 0.5) !important; }
  .rz-steps-next .rzi, .rz-steps-next .rz-menuitem .rz-menuitem-icon, .rz-menuitem .rz-steps-next .rz-menuitem-icon, .rz-steps-next .rz-fileupload-row .rz-button .rzi-close, .rz-fileupload-row .rz-button .rz-steps-next .rzi-close, .rz-steps-next .rz-fileupload-row .rz-paginator-element .rzi-close, .rz-fileupload-row .rz-paginator-element .rz-steps-next .rzi-close,
  .rz-steps-next .rz-fileupload-row .rz-button .rzi-times, .rz-fileupload-row .rz-button .rz-steps-next .rzi-times,
  .rz-steps-next .rz-fileupload-row .rz-paginator-element .rzi-times, .rz-fileupload-row .rz-paginator-element .rz-steps-next .rzi-times,
  .rz-steps-next .rz-fileupload-row .rz-button .rz-icon-trash, .rz-fileupload-row .rz-button .rz-steps-next .rz-icon-trash,
  .rz-steps-next .rz-fileupload-row .rz-paginator-element .rz-icon-trash, .rz-fileupload-row .rz-paginator-element .rz-steps-next .rz-icon-trash, .rz-steps-next .rz-datatable .rzi-chevron-circle-right, .rz-datatable .rz-steps-next .rzi-chevron-circle-right, .rz-steps-next .rz-datatable .rzi-chevron-circle-down, .rz-datatable .rz-steps-next .rzi-chevron-circle-down, .rz-steps-next .rz-sortable-column .rzi-grid-sort, .rz-sortable-column .rz-steps-next .rzi-grid-sort, .rz-steps-next .rz-datatable-header .rzi-plus, .rz-datatable-header .rz-steps-next .rzi-plus, .rz-steps-next .rz-datatable-loading-content .rzi-circle-o-notch, .rz-datatable-loading-content .rz-steps-next .rzi-circle-o-notch, .rz-steps-next .rz-column-drag,
  .rz-steps-prev .rzi,
  .rz-steps-prev .rz-menuitem .rz-menuitem-icon,
  .rz-menuitem .rz-steps-prev .rz-menuitem-icon,
  .rz-steps-prev .rz-fileupload-row .rz-button .rzi-close,
  .rz-fileupload-row .rz-button .rz-steps-prev .rzi-close,
  .rz-steps-prev .rz-fileupload-row .rz-paginator-element .rzi-close,
  .rz-fileupload-row .rz-paginator-element .rz-steps-prev .rzi-close,
  .rz-steps-prev .rz-fileupload-row .rz-button .rzi-times,
  .rz-fileupload-row .rz-button .rz-steps-prev .rzi-times,
  .rz-steps-prev .rz-fileupload-row .rz-paginator-element .rzi-times,
  .rz-fileupload-row .rz-paginator-element .rz-steps-prev .rzi-times,
  .rz-steps-prev .rz-fileupload-row .rz-button .rz-icon-trash,
  .rz-fileupload-row .rz-button .rz-steps-prev .rz-icon-trash,
  .rz-steps-prev .rz-fileupload-row .rz-paginator-element .rz-icon-trash,
  .rz-fileupload-row .rz-paginator-element .rz-steps-prev .rz-icon-trash,
  .rz-steps-prev .rz-datatable .rzi-chevron-circle-right,
  .rz-datatable .rz-steps-prev .rzi-chevron-circle-right,
  .rz-steps-prev .rz-datatable .rzi-chevron-circle-down,
  .rz-datatable .rz-steps-prev .rzi-chevron-circle-down,
  .rz-steps-prev .rz-sortable-column .rzi-grid-sort,
  .rz-sortable-column .rz-steps-prev .rzi-grid-sort,
  .rz-steps-prev .rz-datatable-header .rzi-plus,
  .rz-datatable-header .rz-steps-prev .rzi-plus,
  .rz-steps-prev .rz-datatable-loading-content .rzi-circle-o-notch,
  .rz-datatable-loading-content .rz-steps-prev .rzi-circle-o-notch,
  .rz-steps-prev .rz-column-drag {
    font-size: 1.25rem; }

.rz-textarea {
  padding: 0.1875rem 0.625rem;
  height: auto; }

.rz-checkbox-list-vertical .rz-checkbox {
  display: flex;
  align-items: center;
  margin: 1rem 0;
  cursor: pointer; }

.rz-checkbox-list-horizontal .rz-checkbox {
  display: inline-flex;
  align-items: center; }

.rz-chkbox-label {
  margin: 0 1rem 0 0.5rem;
  cursor: pointer; }

.rz-chkbox {
  display: inline-block;
  vertical-align: middle;
  position: relative;
  width: 1.25rem;
  height: 1.25rem; }
  .rz-chkbox .rz-helper-hidden-accessible {
    opacity: 0;
    height: 0;
    overflow: hidden; }

.rz-chkbox-box {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #2f3a40;
  border-radius: 2px;
  box-shadow: inset 0 4px 3px 0 rgba(0, 0, 0, 0.03);
  background-color: #323f45; }
  .rz-chkbox-box .rzi, .rz-chkbox-box .rz-menuitem .rz-menuitem-icon, .rz-menuitem .rz-chkbox-box .rz-menuitem-icon, .rz-chkbox-box .rz-fileupload-row .rz-button .rzi-close, .rz-fileupload-row .rz-button .rz-chkbox-box .rzi-close, .rz-chkbox-box .rz-fileupload-row .rz-paginator-element .rzi-close, .rz-fileupload-row .rz-paginator-element .rz-chkbox-box .rzi-close,
  .rz-chkbox-box .rz-fileupload-row .rz-button .rzi-times, .rz-fileupload-row .rz-button .rz-chkbox-box .rzi-times,
  .rz-chkbox-box .rz-fileupload-row .rz-paginator-element .rzi-times, .rz-fileupload-row .rz-paginator-element .rz-chkbox-box .rzi-times,
  .rz-chkbox-box .rz-fileupload-row .rz-button .rz-icon-trash, .rz-fileupload-row .rz-button .rz-chkbox-box .rz-icon-trash,
  .rz-chkbox-box .rz-fileupload-row .rz-paginator-element .rz-icon-trash, .rz-fileupload-row .rz-paginator-element .rz-chkbox-box .rz-icon-trash, .rz-chkbox-box .rz-datatable .rzi-chevron-circle-right, .rz-datatable .rz-chkbox-box .rzi-chevron-circle-right, .rz-chkbox-box .rz-datatable .rzi-chevron-circle-down, .rz-datatable .rz-chkbox-box .rzi-chevron-circle-down, .rz-chkbox-box .rz-sortable-column .rzi-grid-sort, .rz-sortable-column .rz-chkbox-box .rzi-grid-sort, .rz-chkbox-box .rz-datatable-header .rzi-plus, .rz-datatable-header .rz-chkbox-box .rzi-plus, .rz-chkbox-box .rz-datatable-loading-content .rzi-circle-o-notch, .rz-datatable-loading-content .rz-chkbox-box .rzi-circle-o-notch, .rz-chkbox-box .rz-column-drag {
    width: 0.875rem;
    height: 0.875rem;
    font-size: 0.875rem;
    color: #ffffff;
    vertical-align: middle; }
  .rz-chkbox-box .rzi-check:before {
    content: 'check'; }
  .rz-chkbox-box .rzi-times:before {
    content: 'close'; }
  .rz-chkbox-box.rz-state-active {
    background-color: #3f8cb3;
    border: 1px solid #2f3a40; }
    .rz-chkbox-box.rz-state-active:hover {
      background-color: #59a6cd;
      border: 1px solid #2f3a40; }

.rz-switch {
  position: relative;
  display: inline-block;
  width: 3rem;
  height: 1.75rem; }

.rz-switch-circle {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0; }

.rz-switch-circle.rz-disabled {
  background: #ced4da !important; }

.rz-switch .rz-switch-circle {
  background: #ced4da;
  transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s;
  border-radius: 30px; }

.rz-switch.rz-switch-checked .rz-switch-circle {
  background: #479cc8; }

.rz-switch .rz-switch-circle:before {
  background: #ffffff;
  width: 1.25rem;
  height: 1.25rem;
  left: 0.25rem;
  margin-top: -0.625rem;
  border-radius: 50%;
  transition-duration: 0.2s; }

.rz-switch-circle:before {
  position: absolute;
  content: "";
  top: 50%; }

.rz-switch.p-rz-switch-checked .rz-switch-circle:before {
  background: #ffffff; }

.rz-switch.rz-switch-checked .rz-switch-circle:before {
  transform: translateX(1.25rem); }

.rz-radio-button-list-vertical .rz-radio-btn {
  display: flex;
  margin: 1rem 0; }

.rz-radio-button-list-horizontal .rz-radio-btn {
  display: inline-flex;
  align-items: center; }

.rz-radiobutton {
  white-space: nowrap;
  display: inline-flex;
  align-items: center;
  cursor: pointer; }

.rz-radiobutton-label {
  margin: 0 1rem 0 0.5rem; }

.rz-radiobutton {
  display: inline-block;
  position: relative;
  vertical-align: middle;
  width: 1.25rem;
  height: 1.25rem; }
  .rz-radiobutton .rz-helper-hidden-accessible {
    opacity: 0;
    height: 0;
    overflow: hidden; }

.rz-radiobutton-box {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: inherit;
  border: 1px solid #2f3a40;
  border-radius: 50%;
  box-shadow: inset 0 4px 3px 0 rgba(0, 0, 0, 0.03);
  background-color: #323f45; }
  .rz-radiobutton-box:active {
    background-color: #3f8cb3;
    box-shadow: inset 0 3px 0 0 rgba(0, 0, 0, 0.1); }
  .rz-radiobutton-box .rzi, .rz-radiobutton-box .rz-menuitem .rz-menuitem-icon, .rz-menuitem .rz-radiobutton-box .rz-menuitem-icon, .rz-radiobutton-box .rz-fileupload-row .rz-button .rzi-close, .rz-fileupload-row .rz-button .rz-radiobutton-box .rzi-close, .rz-radiobutton-box .rz-fileupload-row .rz-paginator-element .rzi-close, .rz-fileupload-row .rz-paginator-element .rz-radiobutton-box .rzi-close,
  .rz-radiobutton-box .rz-fileupload-row .rz-button .rzi-times, .rz-fileupload-row .rz-button .rz-radiobutton-box .rzi-times,
  .rz-radiobutton-box .rz-fileupload-row .rz-paginator-element .rzi-times, .rz-fileupload-row .rz-paginator-element .rz-radiobutton-box .rzi-times,
  .rz-radiobutton-box .rz-fileupload-row .rz-button .rz-icon-trash, .rz-fileupload-row .rz-button .rz-radiobutton-box .rz-icon-trash,
  .rz-radiobutton-box .rz-fileupload-row .rz-paginator-element .rz-icon-trash, .rz-fileupload-row .rz-paginator-element .rz-radiobutton-box .rz-icon-trash, .rz-radiobutton-box .rz-datatable .rzi-chevron-circle-right, .rz-datatable .rz-radiobutton-box .rzi-chevron-circle-right, .rz-radiobutton-box .rz-datatable .rzi-chevron-circle-down, .rz-datatable .rz-radiobutton-box .rzi-chevron-circle-down, .rz-radiobutton-box .rz-sortable-column .rzi-grid-sort, .rz-sortable-column .rz-radiobutton-box .rzi-grid-sort, .rz-radiobutton-box .rz-datatable-header .rzi-plus, .rz-datatable-header .rz-radiobutton-box .rzi-plus, .rz-radiobutton-box .rz-datatable-loading-content .rzi-circle-o-notch, .rz-datatable-loading-content .rz-radiobutton-box .rzi-circle-o-notch, .rz-radiobutton-box .rz-column-drag {
    width: 0.5rem;
    height: 0.5rem;
    color: #ffffff; }
  .rz-radiobutton-box .rzi-circle-on {
    border-radius: 50%;
    vertical-align: middle;
    background-color: #ffffff;
    box-shadow: inset 0 4px 7px 0 rgba(0, 0, 0, 0.03); }
    .rz-radiobutton-box .rzi-circle-on:hover {
      background-color: #fafafa; }
  .rz-radiobutton-box.rz-state-active {
    background-color: #3f8cb3;
    border: 1px solid #2f3a40; }
    .rz-radiobutton-box.rz-state-active:hover {
      background-color: #59a6cd;
      box-shadow: inset 0 -3px 0 0 rgba(255, 255, 255, 0.2); }

.rz-fieldset {
  border: 1px solid #242c30; }

.rz-fieldset-content {
  padding: 1.25rem; }

.rz-fieldset-content-wrapper {
  overflow: hidden; }

.rz-fieldset-legend-text {
  vertical-align: middle; }

.rz-fieldset-toggler {
  vertical-align: middle;
  width: 1.125rem;
  height: 1.125rem;
  background-color: #323f45;
  color: #88989b;
  border: none; }
  .rz-fieldset-toggler.rzi, .rz-menuitem .rz-fieldset-toggler.rz-menuitem-icon, .rz-fileupload-row .rz-button .rz-fieldset-toggler.rzi-close, .rz-fileupload-row .rz-paginator-element .rz-fieldset-toggler.rzi-close,
  .rz-fileupload-row .rz-button .rz-fieldset-toggler.rzi-times,
  .rz-fileupload-row .rz-paginator-element .rz-fieldset-toggler.rzi-times,
  .rz-fileupload-row .rz-button .rz-fieldset-toggler.rz-icon-trash,
  .rz-fileupload-row .rz-paginator-element .rz-fieldset-toggler.rz-icon-trash, .rz-datatable .rz-fieldset-toggler.rzi-chevron-circle-right, .rz-datatable .rz-fieldset-toggler.rzi-chevron-circle-down, .rz-sortable-column .rz-fieldset-toggler.rzi-grid-sort, .rz-datatable-header .rz-fieldset-toggler.rzi-plus, .rz-datatable-loading-content .rz-fieldset-toggler.rzi-circle-o-notch, .rz-fieldset-toggler.rz-column-drag {
    text-align: center;
    font-size: 1rem;
    line-height: 1.125rem;
    margin: 0 0.5rem 0 0; }
  .rz-fieldset-toggler.rzi-minus:before {
    content: 'remove'; }
  .rz-fieldset-toggler.rzi-plus:before {
    content: 'add'; }

.rz-fieldset-legend {
  margin: 0 0 0 1rem;
  width: auto;
  color: #88989b;
  font-size: 0.875rem;
  padding: 0 1rem; }
  .rz-fieldset-legend a {
    color: inherit;
    text-decoration: none; }

.rz-dropdown, .rz-multiselect {
  display: inline-block;
  position: relative;
  overflow: hidden; }
  .rz-dropdown .rz-helper-hidden-accessible, .rz-multiselect .rz-helper-hidden-accessible {
    opacity: 0;
    height: 0;
    overflow: hidden; }
  .rz-dropdown .rz-placeholder, .rz-multiselect .rz-placeholder {
    color: #6c757d; }

.rz-dropdown.rz-dropdown-open {
  background-color: #5d717b;
  border: 1px solid #45565d; }

.rz-multiselect.rz-state-focus {
  background-color: #5d717b;
  border: 1px solid #45565d; }

.rz-dropdown-trigger, .rz-multiselect-trigger {
  position: absolute;
  display: flex;
  align-items: center;
  right: 0;
  top: 0;
  bottom: 0; }
  .rz-dropdown-trigger .rzi, .rz-multiselect-trigger .rzi, .rz-dropdown-trigger .rz-menuitem .rz-menuitem-icon, .rz-menuitem .rz-dropdown-trigger .rz-menuitem-icon, .rz-multiselect-trigger .rz-menuitem .rz-menuitem-icon, .rz-menuitem .rz-multiselect-trigger .rz-menuitem-icon, .rz-dropdown-trigger .rz-fileupload-row .rz-button .rzi-close, .rz-fileupload-row .rz-button .rz-dropdown-trigger .rzi-close, .rz-multiselect-trigger .rz-fileupload-row .rz-button .rzi-close, .rz-fileupload-row .rz-button .rz-multiselect-trigger .rzi-close, .rz-dropdown-trigger .rz-fileupload-row .rz-paginator-element .rzi-close, .rz-fileupload-row .rz-paginator-element .rz-dropdown-trigger .rzi-close, .rz-multiselect-trigger .rz-fileupload-row .rz-paginator-element .rzi-close, .rz-fileupload-row .rz-paginator-element .rz-multiselect-trigger .rzi-close,
  .rz-dropdown-trigger .rz-fileupload-row .rz-button .rzi-times, .rz-fileupload-row .rz-button .rz-dropdown-trigger .rzi-times,
  .rz-multiselect-trigger .rz-fileupload-row .rz-button .rzi-times, .rz-fileupload-row .rz-button .rz-multiselect-trigger .rzi-times,
  .rz-dropdown-trigger .rz-fileupload-row .rz-paginator-element .rzi-times, .rz-fileupload-row .rz-paginator-element .rz-dropdown-trigger .rzi-times,
  .rz-multiselect-trigger .rz-fileupload-row .rz-paginator-element .rzi-times, .rz-fileupload-row .rz-paginator-element .rz-multiselect-trigger .rzi-times,
  .rz-dropdown-trigger .rz-fileupload-row .rz-button .rz-icon-trash, .rz-fileupload-row .rz-button .rz-dropdown-trigger .rz-icon-trash,
  .rz-multiselect-trigger .rz-fileupload-row .rz-button .rz-icon-trash, .rz-fileupload-row .rz-button .rz-multiselect-trigger .rz-icon-trash,
  .rz-dropdown-trigger .rz-fileupload-row .rz-paginator-element .rz-icon-trash, .rz-fileupload-row .rz-paginator-element .rz-dropdown-trigger .rz-icon-trash,
  .rz-multiselect-trigger .rz-fileupload-row .rz-paginator-element .rz-icon-trash, .rz-fileupload-row .rz-paginator-element .rz-multiselect-trigger .rz-icon-trash, .rz-dropdown-trigger .rz-datatable .rzi-chevron-circle-right, .rz-datatable .rz-dropdown-trigger .rzi-chevron-circle-right, .rz-multiselect-trigger .rz-datatable .rzi-chevron-circle-right, .rz-datatable .rz-multiselect-trigger .rzi-chevron-circle-right, .rz-dropdown-trigger .rz-datatable .rzi-chevron-circle-down, .rz-datatable .rz-dropdown-trigger .rzi-chevron-circle-down, .rz-multiselect-trigger .rz-datatable .rzi-chevron-circle-down, .rz-datatable .rz-multiselect-trigger .rzi-chevron-circle-down, .rz-dropdown-trigger .rz-sortable-column .rzi-grid-sort, .rz-sortable-column .rz-dropdown-trigger .rzi-grid-sort, .rz-multiselect-trigger .rz-sortable-column .rzi-grid-sort, .rz-sortable-column .rz-multiselect-trigger .rzi-grid-sort, .rz-dropdown-trigger .rz-datatable-header .rzi-plus, .rz-datatable-header .rz-dropdown-trigger .rzi-plus, .rz-multiselect-trigger .rz-datatable-header .rzi-plus, .rz-datatable-header .rz-multiselect-trigger .rzi-plus, .rz-dropdown-trigger .rz-datatable-loading-content .rzi-circle-o-notch, .rz-datatable-loading-content .rz-dropdown-trigger .rzi-circle-o-notch, .rz-multiselect-trigger .rz-datatable-loading-content .rzi-circle-o-notch, .rz-datatable-loading-content .rz-multiselect-trigger .rzi-circle-o-notch, .rz-dropdown-trigger .rz-column-drag, .rz-multiselect-trigger .rz-column-drag {
    width: 1.1875rem;
    height: 1.1875rem;
    font-size: 1.1875rem;
    margin: 0 0.5rem 0 0; }
  .rz-dropdown-trigger .rzi-chevron-down:before, .rz-multiselect-trigger .rzi-chevron-down:before {
    content: 'arrow_drop_down'; }

.rz-dropdown-clear-icon {
  position: absolute;
  right: 1.6875rem;
  top: 0;
  height: 100%;
  display: flex;
  align-items: center;
  font-size: 1.1875rem; }
  .rz-dropdown-clear-icon:before {
    content: 'close'; }

.rz-dropdown-panel, .rz-multiselect-panel, .rz-autocomplete-panel, .rz-splitbutton-menu, .rz-html-editor-dropdown-items {
  position: absolute;
  transform: translateY(-4px);
  background-color: #5d717b;
  border-radius: 4px;
  border: 1px solid #45565d;
  border-top-right-radius: 0;
  border-top-left-radius: 0; }

.rz-dropdown-panel {
  box-sizing: content-box;
  padding: 0 0.625rem; }

.rz-multiselect-panel {
  box-sizing: content-box;
  padding: 0 0.625rem; }
  .rz-multiselect-panel .rz-chkbox {
    margin: 0 0.5rem 0 0; }

.rz-dropdown-items, .rz-multiselect-items, .rz-autocomplete-items {
  list-style: none;
  padding: 0;
  margin: 0; }
  .rz-dropdown-items li, .rz-multiselect-items li, .rz-autocomplete-items li {
    /* The 'No results found' item has no CSS class */ }

.rz-dropdown-items-wrapper,
.rz-multiselect-items-wrapper {
  overflow: auto;
  margin: 0 -0.625rem; }

.rz-multiselect-items-wrapper {
  overflow: auto; }

.rz-dropdown-filter-container {
  padding: 0.5rem 0;
  border-bottom: 1px solid rgba(58, 71, 77, 0.2); }

.rz-dropdown-filter, .rz-multiselect-filter-container .rz-inputtext {
  background-color: transparent;
  color: #ffffff; }

.rz-multiselect-header {
  display: flex;
  align-items: center;
  padding: 0.25rem 0.625rem;
  margin: 0 -0.625rem; }

.rz-dropdown-item, .rz-dropdown-items li, .rz-multiselect-items li, .rz-autocomplete-items li, .rz-multiselect-item, .rz-autocomplete-list-item, .rz-menuitem {
  padding: 0.25rem 0.625rem;
  cursor: default;
  font-size: 1rem; }
  .rz-dropdown-item.rz-state-highlight, .rz-dropdown-items li.rz-state-highlight, .rz-multiselect-items li.rz-state-highlight, .rz-autocomplete-items li.rz-state-highlight, .rz-state-highlight.rz-multiselect-item, .rz-state-highlight.rz-autocomplete-list-item, .rz-state-highlight.rz-menuitem {
    background-color: #479cc8;
    color: #ffffff;
    box-shadow: 0 8px 10px 0 rgba(0, 0, 0, 0.01);
    border-radius: 0; }
  .rz-dropdown-item:hover, .rz-dropdown-items li:hover, .rz-multiselect-items li:hover, .rz-autocomplete-items li:hover, .rz-multiselect-item:hover, .rz-autocomplete-list-item:hover, .rz-menuitem:hover {
    background-color: #59a6cd;
    color: #ffffff;
    border-radius: 0; }

.rz-multiselect-close {
  display: none; }

.rz-multiselect-filter-container {
  flex: auto;
  border-bottom: 1px solid rgba(58, 71, 77, 0.2); }

.rz-multiselect-item {
  display: flex;
  align-items: center; }

.rz-multiselect-label-container,
.rz-dropdown-label {
  display: block;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  margin-right: 1.1875rem; }

.rz-clear .rz-multiselect-label-container,
.rz-clear .rz-dropdown-label {
  margin-right: 2.375rem; }

.rz-autocomplete {
  display: inline-block;
  border: 1px solid #2f3a40;
  border-radius: 4px;
  overflow: hidden; }

.rz-autocomplete-input {
  padding: 0.1875rem 0.625rem;
  background-color: #323f45;
  box-shadow: inset 0 4px 3px 0 rgba(0, 0, 0, 0.03);
  border: none;
  height: 2.1875rem;
  line-height: 1.5;
  width: 100%; }
  .rz-autocomplete-input:focus {
    outline: none; }
  .rz-autocomplete-input:disabled {
    border: none; }

.rz-autocomplete-panel {
  overflow: auto;
  box-sizing: content-box;
  transform: translate(-1px, -4px); }

.rz-listbox {
  display: inline-flex;
  flex-direction: column;
  background-color: #5d717b;
  border: none;
  border-radius: 4px; }
  .rz-listbox .rz-chkbox {
    margin: 0 0.5rem 0 0; }
  .rz-listbox:not(.rz-state-disabled) .rz-listbox-item:hover:hover {
    background-color: #59a6cd;
    color: #ffffff;
    border-radius: 4px; }

.rz-listbox-list {
  margin: 0;
  padding: 0; }

.rz-listbox-item {
  cursor: default;
  padding: 0.25rem 0.625rem;
  margin: 1px 0;
  cursor: default;
  font-size: 1rem; }
  .rz-listbox-item.rz-state-highlight {
    background-color: #479cc8;
    color: #ffffff;
    box-shadow: 0 8px 10px 0 rgba(0, 0, 0, 0.01);
    border-radius: 4px; }

.rz-listbox-header {
  display: flex;
  align-items: center;
  padding: 0.5625rem 1.25rem 0.625rem 1.125rem;
  border-bottom: 1px solid rgba(58, 71, 77, 0.2); }

.rz-listbox-list-wrapper {
  flex: auto;
  overflow: auto;
  padding: 0.5rem; }

.rz-dropdown-filter-container, .rz-multiselect-filter-container, .rz-listbox-filter-container {
  display: flex;
  flex-direction: row-reverse;
  align-items: center; }
  .rz-dropdown-filter-container .rz-inputtext, .rz-multiselect-filter-container .rz-inputtext, .rz-listbox-filter-container .rz-inputtext {
    flex: auto;
    width: 0;
    border: none; }
    .rz-dropdown-filter-container .rz-inputtext:focus, .rz-multiselect-filter-container .rz-inputtext:focus, .rz-listbox-filter-container .rz-inputtext:focus {
      outline: none; }
  .rz-dropdown-filter-container .rzi-search, .rz-multiselect-filter-container .rzi-search, .rz-listbox-filter-container .rzi-search {
    width: 1.25rem;
    height: 1.25rem;
    line-height: 1.25rem;
    font-size: 1.25rem; }
    .rz-dropdown-filter-container .rzi-search:before, .rz-multiselect-filter-container .rzi-search:before, .rz-listbox-filter-container .rzi-search:before {
      content: 'search'; }

.rz-listbox-filter-container {
  flex: auto; }
  .rz-listbox-filter-container .rz-inputtext {
    background-color: transparent; }

.rz-splitbutton {
  display: inline-flex; }
  .rz-splitbutton .rz-button-text-icon-left,
  .rz-splitbutton .rz-button-text-only {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    padding: 0 0.625rem; }
  .rz-splitbutton .rz-button, .rz-splitbutton .rz-paginator-element {
    background-color: #ff6d41; }

.rz-splitbutton-menu {
  display: none;
  position: absolute;
  min-width: 10rem;
  border-top-right-radius: 4px;
  box-shadow: 0 6px 14px 0 rgba(0, 0, 0, 0.06); }

.rz-menu-list {
  list-style: none;
  padding: 0;
  margin: 0; }

.rz-menuitem:hover:first-child {
  border-top-right-radius: 4px; }

.rz-menuitem .rz-menuitem-link {
  color: inherit;
  display: block; }
  .rz-menuitem .rz-menuitem-link:hover {
    text-decoration: none; }

.rz-menuitem .rz-menuitem-icon {
  vertical-align: top; }

.rz-splitbutton-menubutton {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-left: solid 1px #e5613a; }
  .rz-splitbutton-menubutton .rz-button-text {
    display: none; }
  .rz-splitbutton-menubutton .rzi-chevron-down:before {
    content: 'arrow_drop_down'; }

.rz-splitter {
  display: flex;
  flex-wrap: nowrap;
  width: 100%;
  height: 100%; }
  .rz-splitter > .rz-splitter-bar {
    flex: 0 0 auto;
    position: relative;
    text-align: center;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    background-color: #4A5A63;
    opacity: 0.4;
    user-select: none; }
    .rz-splitter > .rz-splitter-bar > .rz-collapse {
      display: table; }
      .rz-splitter > .rz-splitter-bar > .rz-collapse:before {
        font-family: 'Material Icons';
        line-height: normal;
        display: table-cell; }
      .rz-splitter > .rz-splitter-bar > .rz-collapse:hover {
        cursor: pointer; }
    .rz-splitter > .rz-splitter-bar > .rz-resize {
      border: 1px solid #fff;
      border-radius: 1px; }
    .rz-splitter > .rz-splitter-bar > .rz-expand {
      display: table; }
      .rz-splitter > .rz-splitter-bar > .rz-expand:before {
        font-family: 'Material Icons';
        line-height: normal;
        display: table-cell; }
      .rz-splitter > .rz-splitter-bar > .rz-expand:hover {
        cursor: pointer; }
    .rz-splitter > .rz-splitter-bar-resizable:hover {
      background-color: #4A5A63;
      opacity: 0.8; }
    .rz-splitter > .rz-splitter-bar-resizable:active {
      background-color: #479CC8;
      opacity: 0.8; }
      .rz-splitter > .rz-splitter-bar-resizable:active > .rz-expand, .rz-splitter > .rz-splitter-bar-resizable:active > .rz-resize, .rz-splitter > .rz-splitter-bar-resizable:active > .rz-collapse {
        color: #fff; }
      .rz-splitter > .rz-splitter-bar-resizable:active > .rz-resize {
        border: 1px solid #fff; }
    .rz-splitter > .rz-splitter-bar-resizable:disabled {
      opacity: 0.2; }
  .rz-splitter-horizontal {
    flex-direction: row; }
    .rz-splitter-horizontal > .rz-splitter-bar {
      flex-direction: column;
      width: 8px; }
      .rz-splitter-horizontal > .rz-splitter-bar > .rz-collapse:before {
        content: 'arrow_left'; }
      .rz-splitter-horizontal > .rz-splitter-bar > .rz-resize {
        height: 16px;
        margin: 2px 0; }
      .rz-splitter-horizontal > .rz-splitter-bar > .rz-expand:before {
        content: 'arrow_right'; }
      .rz-splitter-horizontal > .rz-splitter-bar-resizable:hover {
        cursor: col-resize; }
  .rz-splitter-vertical {
    flex-direction: column; }
    .rz-splitter-vertical > .rz-splitter-bar {
      flex-direction: row;
      height: 8px; }
      .rz-splitter-vertical > .rz-splitter-bar > .rz-collapse:before {
        content: 'arrow_drop_up'; }
      .rz-splitter-vertical > .rz-splitter-bar > .rz-resize {
        width: 16px;
        margin: 0 2px; }
      .rz-splitter-vertical > .rz-splitter-bar > .rz-expand:before {
        content: 'arrow_drop_down'; }
      .rz-splitter-vertical > .rz-splitter-bar-resizable:hover {
        cursor: row-resize; }
  .rz-splitter-pane {
    overflow: hidden;
    position: relative;
    flex: 0 0 auto; }
    .rz-splitter-pane-collapsed {
      flex: 0 1 0% !important;
      overflow: hidden !important;
      display: block !important; }
    .rz-splitter-pane-lastresizable {
      flex: 1 1 auto; }

.rz-slider {
  position: relative;
  display: inline-block;
  border: 1px solid #2f3a40;
  border-radius: 4px;
  background-color: #323f45; }
  .rz-slider.rz-state-disabled {
    background-color: #6d787d;
    border: solid 1px rgba(47, 58, 64, 0.53); }
    .rz-slider.rz-state-disabled .rz-slider-range {
      background-color: rgba(211, 217, 219, 0.5);
      border: solid 1px rgba(211, 217, 219, 0.3); }
    .rz-slider.rz-state-disabled .rz-slider-handle {
      background-color: #6d787d; }

.rz-slider-horizontal {
  height: 0.8125rem;
  width: 10rem; }

.rz-slider-range {
  position: absolute;
  top: -1px;
  bottom: -1px;
  left: -1px;
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;
  background-color: #5d717b;
  border: 1px solid #5d717b; }

.rz-slider-handle {
  position: absolute;
  transform: translateY(-50%);
  top: 50%;
  margin-left: -0.5rem;
  background-color: #479cc8;
  border-radius: 2px;
  width: 1rem;
  height: 1.75rem; }
  .rz-slider-handle:before {
    display: inline-block;
    color: #ffffff;
    transform: rotate(90deg);
    vertical-align: middle;
    font-family: 'Material Icons';
    content: 'drag_handle'; }

.rz-slider:not(.rz-state-disabled) .rz-slider-handle:hover {
  background-color: #59a6cd;
  box-shadow: inset 0 -3px 0 0 rgba(255, 255, 255, 0.2); }

.rz-rating {
  display: inline-block; }
  .rz-rating.rz-state-disabled .rzi, .rz-rating.rz-state-disabled .rz-menuitem .rz-menuitem-icon, .rz-menuitem .rz-rating.rz-state-disabled .rz-menuitem-icon, .rz-rating.rz-state-disabled .rz-fileupload-row .rz-button .rzi-close, .rz-fileupload-row .rz-button .rz-rating.rz-state-disabled .rzi-close, .rz-rating.rz-state-disabled .rz-fileupload-row .rz-paginator-element .rzi-close, .rz-fileupload-row .rz-paginator-element .rz-rating.rz-state-disabled .rzi-close,
  .rz-rating.rz-state-disabled .rz-fileupload-row .rz-button .rzi-times, .rz-fileupload-row .rz-button .rz-rating.rz-state-disabled .rzi-times,
  .rz-rating.rz-state-disabled .rz-fileupload-row .rz-paginator-element .rzi-times, .rz-fileupload-row .rz-paginator-element .rz-rating.rz-state-disabled .rzi-times,
  .rz-rating.rz-state-disabled .rz-fileupload-row .rz-button .rz-icon-trash, .rz-fileupload-row .rz-button .rz-rating.rz-state-disabled .rz-icon-trash,
  .rz-rating.rz-state-disabled .rz-fileupload-row .rz-paginator-element .rz-icon-trash, .rz-fileupload-row .rz-paginator-element .rz-rating.rz-state-disabled .rz-icon-trash, .rz-rating.rz-state-disabled .rz-datatable .rzi-chevron-circle-right, .rz-datatable .rz-rating.rz-state-disabled .rzi-chevron-circle-right, .rz-rating.rz-state-disabled .rz-datatable .rzi-chevron-circle-down, .rz-datatable .rz-rating.rz-state-disabled .rzi-chevron-circle-down, .rz-rating.rz-state-disabled .rz-sortable-column .rzi-grid-sort, .rz-sortable-column .rz-rating.rz-state-disabled .rzi-grid-sort, .rz-rating.rz-state-disabled .rz-datatable-header .rzi-plus, .rz-datatable-header .rz-rating.rz-state-disabled .rzi-plus, .rz-rating.rz-state-disabled .rz-datatable-loading-content .rzi-circle-o-notch, .rz-datatable-loading-content .rz-rating.rz-state-disabled .rzi-circle-o-notch, .rz-rating.rz-state-disabled .rz-column-drag {
    opacity: 0.3;
    color: #88989b; }
  .rz-rating a {
    text-decoration: none;
    cursor: default;
    outline: none; }
  .rz-rating .rzi, .rz-rating .rz-menuitem .rz-menuitem-icon, .rz-menuitem .rz-rating .rz-menuitem-icon, .rz-rating .rz-fileupload-row .rz-button .rzi-close, .rz-fileupload-row .rz-button .rz-rating .rzi-close, .rz-rating .rz-fileupload-row .rz-paginator-element .rzi-close, .rz-fileupload-row .rz-paginator-element .rz-rating .rzi-close,
  .rz-rating .rz-fileupload-row .rz-button .rzi-times, .rz-fileupload-row .rz-button .rz-rating .rzi-times,
  .rz-rating .rz-fileupload-row .rz-paginator-element .rzi-times, .rz-fileupload-row .rz-paginator-element .rz-rating .rzi-times,
  .rz-rating .rz-fileupload-row .rz-button .rz-icon-trash, .rz-fileupload-row .rz-button .rz-rating .rz-icon-trash,
  .rz-rating .rz-fileupload-row .rz-paginator-element .rz-icon-trash, .rz-fileupload-row .rz-paginator-element .rz-rating .rz-icon-trash, .rz-rating .rz-datatable .rzi-chevron-circle-right, .rz-datatable .rz-rating .rzi-chevron-circle-right, .rz-rating .rz-datatable .rzi-chevron-circle-down, .rz-datatable .rz-rating .rzi-chevron-circle-down, .rz-rating .rz-sortable-column .rzi-grid-sort, .rz-sortable-column .rz-rating .rzi-grid-sort, .rz-rating .rz-datatable-header .rzi-plus, .rz-datatable-header .rz-rating .rzi-plus, .rz-rating .rz-datatable-loading-content .rzi-circle-o-notch, .rz-datatable-loading-content .rz-rating .rzi-circle-o-notch, .rz-rating .rz-column-drag {
    color: #88989b; }
  .rz-rating .rzi-ban:before {
    content: "not_interested"; }
  .rz-rating .rzi-star-o {
    opacity: 0.2; }
    .rz-rating .rzi-star-o:before {
      content: "star_border"; }
  .rz-rating .rzi-star {
    color: #ff6d41; }
    .rz-rating .rzi-star:before {
      content: "star"; }

.rz-rating:not(.rz-state-disabled):not(.rz-state-readonly) .rzi-star-o {
  cursor: pointer; }
  .rz-rating:not(.rz-state-disabled):not(.rz-state-readonly) .rzi-star-o:hover {
    color: #ff6d41; }
    .rz-rating:not(.rz-state-disabled):not(.rz-state-readonly) .rzi-star-o:hover:before {
      content: "star"; }

.rz-selectbutton {
  display: inline-block; }
  .rz-selectbutton .rz-button, .rz-selectbutton .rz-paginator-element {
    margin-left: -1px;
    display: inline-block;
    border-radius: 0;
    background-color: #ffffff;
    color: #3a3a3a;
    border: solid 1px #dadfe2; }
    .rz-selectbutton .rz-button:first-child, .rz-selectbutton .rz-paginator-element:first-child {
      border-top-left-radius: 4px;
      border-bottom-left-radius: 4px; }
    .rz-selectbutton .rz-button:last-child, .rz-selectbutton .rz-paginator-element:last-child {
      border-top-right-radius: 4px;
      border-bottom-right-radius: 4px; }
    .rz-selectbutton .rz-button.rz-state-active, .rz-selectbutton .rz-state-active.rz-paginator-element {
      background-color: #479cc8;
      color: #ffffff;
      border: solid 1px #459cca; }

.rz-calendar {
  display: inline-block;
  position: relative; }
  .rz-calendar .rz-inputtext {
    width: 100%;
    line-height: 1.5rem;
    padding-right: 1.125rem; }
  .rz-calendar:not(.rz-state-disabled):hover .rz-datepicker-trigger {
    box-shadow: none;
    color: #479cc8; }

.rz-calendar-inline {
  border: 1px solid #2f3a40; }

.rz-datepicker-trigger {
  box-shadow: none;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  right: 0.625rem;
  background-color: transparent;
  padding: 0;
  vertical-align: text-top;
  color: #88989b;
  width: 1.125rem;
  height: 1.25rem;
  font-size: 1.25rem; }
  .rz-datepicker-trigger.rz-state-disabled {
    border: none;
    box-shadow: none; }
  .rz-datepicker-trigger:hover {
    background-color: transparent; }
  .rz-datepicker-trigger:active {
    box-shadow: none !important;
    background-image: none !important; }
  .rz-datepicker-trigger .rzi-calendar {
    font-size: inherit;
    vertical-align: top; }
    .rz-datepicker-trigger .rzi-calendar:before {
      content: 'calendar_today'; }
  .rz-datepicker-trigger .rz-button-text {
    display: none; }

.rz-datepicker {
  background-color: #5d717b; }
  .rz-datepicker:not(.rz-datepicker-inline) {
    box-sizing: content-box;
    margin: 0;
    position: absolute;
    border: 1px solid #2f3a40;
    box-shadow: 0 6px 14px 0 rgba(0, 0, 0, 0.06);
    border-radius: 4px; }

.rz-calendar {
  display: inline-block; }

.rz-datepicker-inline {
  position: static; }
  .rz-datepicker-inline .rz-datepicker-group {
    display: inline-block; }

.rz-datepicker-header {
  position: relative;
  line-height: 2.5rem;
  background-color: #5d717b;
  border-bottom: 1px solid rgba(58, 71, 77, 0.2);
  color: #3a474d;
  padding: 0 0.875rem; }

.rz-datepicker-prev {
  float: left;
  height: 2.5rem; }
  .rz-datepicker-prev .rzi-chevron-left {
    vertical-align: text-top;
    color: #3a474d; }
    .rz-datepicker-prev .rzi-chevron-left:before {
      content: 'chevron_left'; }

.rz-datepicker-next {
  float: right;
  height: 2.5rem; }
  .rz-datepicker-next .rzi-chevron-right {
    vertical-align: text-top;
    color: #3a474d; }
    .rz-datepicker-next .rzi-chevron-right:before {
      content: 'chevron_right'; }

.rz-datepicker-title {
  text-align: center; }

.rz-datepicker-calendar {
  table-layout: fixed;
  border-collapse: collapse; }
  .rz-datepicker-calendar th {
    font-weight: normal;
    font-size: 0.6875rem;
    padding: 0.5rem 0.875rem;
    text-align: center; }
  .rz-datepicker-calendar td {
    text-align: center;
    border-top: 1px solid rgba(58, 71, 77, 0.2);
    padding: 0; }
    .rz-datepicker-calendar td .rz-state-default {
      display: block;
      padding: 0.5rem 0.875rem;
      color: #ffffff;
      font-size: 0.875rem; }
      .rz-datepicker-calendar td .rz-state-default:hover {
        text-decoration: none;
        color: #ffffff;
        cursor: default; }
    .rz-datepicker-calendar td .rz-state-active {
      color: #ffffff;
      background-color: #479cc8;
      padding: 0.5rem 0.875rem; }
      .rz-datepicker-calendar td .rz-state-active:hover {
        color: #ffffff; }
  .rz-datepicker-calendar .rz-state-disabled {
    opacity: 0.5; }

.rz-timepicker {
  display: flex;
  align-items: center;
  justify-content: center;
  border-top: 1px solid rgba(58, 71, 77, 0.2);
  padding: 0.5rem;
  color: #ffffff; }
  .rz-timepicker .rzi-chevron-up:before {
    content: 'expand_less'; }
  .rz-timepicker .rzi-chevron-down:before {
    content: 'expand_more'; }
  .rz-timepicker .rz-separator {
    margin: 0 0.5rem; }
    .rz-timepicker .rz-separator a {
      display: none; }

.rz-hour-picker,
.rz-minute-picker,
.rz-second-picker {
  background-color: #5d717b;
  width: 4rem; }

.rz-spinner {
  display: inline-block;
  position: relative;
  padding: 0px; }
  .rz-spinner input[type='number'],
  .rz-spinner input[type='text'] {
    -moz-appearance: textfield;
    width: 100%;
    height: 100%;
    border: none;
    background-color: transparent;
    line-height: 1.5rem;
    text-align: inherit;
    padding: 1px 12px 1px 0.5rem; }
    .rz-spinner input[type='number']::-webkit-inner-spin-button, .rz-spinner input[type='number']::-webkit-outer-spin-button,
    .rz-spinner input[type='text']::-webkit-inner-spin-button,
    .rz-spinner input[type='text']::-webkit-outer-spin-button {
      -webkit-appearance: none;
      margin: 0; }
  .rz-spinner.rz-state-disabled .rz-spinner-button {
    background-color: rgba(136, 152, 155, 0.2);
    color: #88989b; }
    .rz-spinner.rz-state-disabled .rz-spinner-button:active, .rz-spinner.rz-state-disabled .rz-spinner-button:hover {
      box-shadow: none; }

.rz-spinner-button {
  position: absolute;
  right: 3px;
  padding: 0;
  width: 0.875rem;
  height: 0.875rem;
  border-radius: 2px;
  background-color: #479cc8;
  color: #ffffff; }
  .rz-spinner-button:hover {
    background-color: #479cc8; }
  .rz-spinner-button .rzi, .rz-spinner-button .rz-menuitem .rz-menuitem-icon, .rz-menuitem .rz-spinner-button .rz-menuitem-icon, .rz-spinner-button .rz-fileupload-row .rz-button .rzi-close, .rz-fileupload-row .rz-button .rz-spinner-button .rzi-close, .rz-spinner-button .rz-fileupload-row .rz-paginator-element .rzi-close, .rz-fileupload-row .rz-paginator-element .rz-spinner-button .rzi-close,
  .rz-spinner-button .rz-fileupload-row .rz-button .rzi-times, .rz-fileupload-row .rz-button .rz-spinner-button .rzi-times,
  .rz-spinner-button .rz-fileupload-row .rz-paginator-element .rzi-times, .rz-fileupload-row .rz-paginator-element .rz-spinner-button .rzi-times,
  .rz-spinner-button .rz-fileupload-row .rz-button .rz-icon-trash, .rz-fileupload-row .rz-button .rz-spinner-button .rz-icon-trash,
  .rz-spinner-button .rz-fileupload-row .rz-paginator-element .rz-icon-trash, .rz-fileupload-row .rz-paginator-element .rz-spinner-button .rz-icon-trash, .rz-spinner-button .rz-datatable .rzi-chevron-circle-right, .rz-datatable .rz-spinner-button .rzi-chevron-circle-right, .rz-spinner-button .rz-datatable .rzi-chevron-circle-down, .rz-datatable .rz-spinner-button .rzi-chevron-circle-down, .rz-spinner-button .rz-sortable-column .rzi-grid-sort, .rz-sortable-column .rz-spinner-button .rzi-grid-sort, .rz-spinner-button .rz-datatable-header .rzi-plus, .rz-datatable-header .rz-spinner-button .rzi-plus, .rz-spinner-button .rz-datatable-loading-content .rzi-circle-o-notch, .rz-datatable-loading-content .rz-spinner-button .rzi-circle-o-notch, .rz-spinner-button .rz-column-drag {
    font-size: 0.875rem;
    vertical-align: top; }
  .rz-spinner-button .rzi-caret-up:before {
    content: 'expand_less'; }
  .rz-spinner-button .rzi-caret-down:before {
    content: 'expand_more'; }

.rz-spinner-up {
  top: 2px; }

.rz-spinner-down {
  bottom: 2px; }

.rz-fileupload {
  display: inline-block; }
  .rz-fileupload .rz-button, .rz-fileupload .rz-paginator-element {
    vertical-align: middle;
    -webkit-appearance: none !important; }
    .rz-fileupload .rz-button:not(.rz-fileupload-choose), .rz-fileupload .rz-paginator-element:not(.rz-fileupload-choose) {
      background-color: #ff6d41; }
    .rz-fileupload .rz-button[disabled], .rz-fileupload .rz-paginator-element[disabled] {
      opacity: 0.5; }
    .rz-fileupload .rz-button .rzi, .rz-fileupload .rz-paginator-element .rzi, .rz-fileupload .rz-button .rz-menuitem .rz-menuitem-icon, .rz-menuitem .rz-fileupload .rz-button .rz-menuitem-icon, .rz-fileupload .rz-paginator-element .rz-menuitem .rz-menuitem-icon, .rz-menuitem .rz-fileupload .rz-paginator-element .rz-menuitem-icon, .rz-fileupload .rz-fileupload-row .rz-button .rzi-close, .rz-fileupload-row .rz-fileupload .rz-button .rzi-close, .rz-fileupload .rz-fileupload-row .rz-paginator-element .rzi-close, .rz-fileupload-row .rz-fileupload .rz-paginator-element .rzi-close,
    .rz-fileupload .rz-fileupload-row .rz-button .rzi-times, .rz-fileupload-row .rz-fileupload .rz-button .rzi-times,
    .rz-fileupload .rz-fileupload-row .rz-paginator-element .rzi-times, .rz-fileupload-row .rz-fileupload .rz-paginator-element .rzi-times,
    .rz-fileupload .rz-fileupload-row .rz-button .rz-icon-trash, .rz-fileupload-row .rz-fileupload .rz-button .rz-icon-trash,
    .rz-fileupload .rz-fileupload-row .rz-paginator-element .rz-icon-trash, .rz-fileupload-row .rz-fileupload .rz-paginator-element .rz-icon-trash, .rz-fileupload .rz-button .rz-datatable .rzi-chevron-circle-right, .rz-datatable .rz-fileupload .rz-button .rzi-chevron-circle-right, .rz-fileupload .rz-paginator-element .rz-datatable .rzi-chevron-circle-right, .rz-datatable .rz-fileupload .rz-paginator-element .rzi-chevron-circle-right, .rz-fileupload .rz-button .rz-datatable .rzi-chevron-circle-down, .rz-datatable .rz-fileupload .rz-button .rzi-chevron-circle-down, .rz-fileupload .rz-paginator-element .rz-datatable .rzi-chevron-circle-down, .rz-datatable .rz-fileupload .rz-paginator-element .rzi-chevron-circle-down, .rz-fileupload .rz-button .rz-sortable-column .rzi-grid-sort, .rz-sortable-column .rz-fileupload .rz-button .rzi-grid-sort, .rz-fileupload .rz-paginator-element .rz-sortable-column .rzi-grid-sort, .rz-sortable-column .rz-fileupload .rz-paginator-element .rzi-grid-sort, .rz-fileupload .rz-button .rz-datatable-header .rzi-plus, .rz-datatable-header .rz-fileupload .rz-button .rzi-plus, .rz-fileupload .rz-paginator-element .rz-datatable-header .rzi-plus, .rz-datatable-header .rz-fileupload .rz-paginator-element .rzi-plus, .rz-fileupload .rz-button .rz-datatable-loading-content .rzi-circle-o-notch, .rz-datatable-loading-content .rz-fileupload .rz-button .rzi-circle-o-notch, .rz-fileupload .rz-paginator-element .rz-datatable-loading-content .rzi-circle-o-notch, .rz-datatable-loading-content .rz-fileupload .rz-paginator-element .rzi-circle-o-notch, .rz-fileupload .rz-button .rz-column-drag, .rz-fileupload .rz-paginator-element .rz-column-drag {
      display: none; }

.rz-fileupload-choose {
  position: relative;
  display: inline-block;
  overflow: hidden;
  vertical-align: middle;
  background-color: #479cc8; }
  .rz-fileupload-choose .rzi, .rz-fileupload-choose .rz-menuitem .rz-menuitem-icon, .rz-menuitem .rz-fileupload-choose .rz-menuitem-icon, .rz-fileupload-choose .rz-fileupload-row .rz-button .rzi-close, .rz-fileupload-row .rz-button .rz-fileupload-choose .rzi-close, .rz-fileupload-choose .rz-fileupload-row .rz-paginator-element .rzi-close, .rz-fileupload-row .rz-paginator-element .rz-fileupload-choose .rzi-close,
  .rz-fileupload-choose .rz-fileupload-row .rz-button .rzi-times, .rz-fileupload-row .rz-button .rz-fileupload-choose .rzi-times,
  .rz-fileupload-choose .rz-fileupload-row .rz-paginator-element .rzi-times, .rz-fileupload-row .rz-paginator-element .rz-fileupload-choose .rzi-times,
  .rz-fileupload-choose .rz-fileupload-row .rz-button .rz-icon-trash, .rz-fileupload-row .rz-button .rz-fileupload-choose .rz-icon-trash,
  .rz-fileupload-choose .rz-fileupload-row .rz-paginator-element .rz-icon-trash, .rz-fileupload-row .rz-paginator-element .rz-fileupload-choose .rz-icon-trash, .rz-fileupload-choose .rz-datatable .rzi-chevron-circle-right, .rz-datatable .rz-fileupload-choose .rzi-chevron-circle-right, .rz-fileupload-choose .rz-datatable .rzi-chevron-circle-down, .rz-datatable .rz-fileupload-choose .rzi-chevron-circle-down, .rz-fileupload-choose .rz-sortable-column .rzi-grid-sort, .rz-sortable-column .rz-fileupload-choose .rzi-grid-sort, .rz-fileupload-choose .rz-datatable-header .rzi-plus, .rz-datatable-header .rz-fileupload-choose .rzi-plus, .rz-fileupload-choose .rz-datatable-loading-content .rzi-circle-o-notch, .rz-datatable-loading-content .rz-fileupload-choose .rzi-circle-o-notch, .rz-fileupload-choose .rz-column-drag {
    display: none; }
  .rz-fileupload-choose input[type='file'] {
    position: absolute;
    top: 0;
    right: 0;
    margin: 0;
    opacity: 0;
    min-height: 100%; }

.rz-fileupload-row {
  display: flex;
  align-items: center;
  justify-content: space-between; }
  .rz-fileupload-row > div {
    margin: 0 0.75rem; }
  .rz-fileupload-row .rz-button-text {
    display: none; }
  .rz-fileupload-row .rz-button, .rz-fileupload-row .rz-paginator-element {
    background-color: #dadfe2;
    color: #ffffff; }
    .rz-fileupload-row .rz-button .rzi-close, .rz-fileupload-row .rz-paginator-element .rzi-close,
    .rz-fileupload-row .rz-button .rzi-times,
    .rz-fileupload-row .rz-paginator-element .rzi-times,
    .rz-fileupload-row .rz-button .rz-icon-trash,
    .rz-fileupload-row .rz-paginator-element .rz-icon-trash {
      display: block; }
      .rz-fileupload-row .rz-button .rzi-close:before, .rz-fileupload-row .rz-paginator-element .rzi-close:before,
      .rz-fileupload-row .rz-button .rzi-times:before,
      .rz-fileupload-row .rz-paginator-element .rzi-times:before,
      .rz-fileupload-row .rz-button .rz-icon-trash:before,
      .rz-fileupload-row .rz-paginator-element .rz-icon-trash:before {
        content: 'close'; }

.rz-fileupload-buttonbar {
  position: relative;
  background-color: #2f3d43;
  padding: 0.75rem; }
  .rz-fileupload-buttonbar .rz-button:nth-child(3), .rz-fileupload-buttonbar .rz-paginator-element:nth-child(3) {
    float: right;
    background-color: #dadfe2;
    color: #3a474d; }

.rz-fileupload-files {
  background-color: #5d717b;
  padding: 1rem 0; }

.rz-datatable {
  position: relative;
  box-shadow: 0 8px 10px 0 rgba(0, 0, 0, 0.01);
  border: 1px solid #45565d;
  background-color: #5d717b;
  border-bottom-right-radius: 4px;
  border-bottom-left-radius: 4px; }
  .rz-datatable .rz-col-icon {
    text-align: center;
    vertical-align: middle;
    width: 2rem;
    padding: 0; }
  .rz-datatable .rzi-chevron-circle-right {
    vertical-align: top; }
    .rz-datatable .rzi-chevron-circle-right:before {
      content: 'arrow_right'; }
  .rz-datatable .rzi-chevron-circle-down {
    vertical-align: top; }
    .rz-datatable .rzi-chevron-circle-down:before {
      content: 'arrow_drop_down'; }
  .rz-datatable.rz-has-template > .rz-datatable-scrollable-wrapper > .rz-datatable-scrollable-view > .rz-datatable-scrollable-body > .rz-datatable-scrollable-table-wrapper > table > .rz-datatable-scrollable-colgroup col:first-child {
    width: 2rem; }

.rz-unselectable-text {
  user-select: none; }

.rz-datatable-tablewrapper > table,
.rz-datatable-scrollable-header-box > table,
.rz-datatable-scrollable-table-wrapper > table,
.rz-datatable-scrollable-footer-box > table {
  table-layout: fixed;
  border-collapse: collapse;
  width: 100%; }

.rz-resizable-column {
  position: relative; }

.rz-column-resizer-helper {
  position: absolute;
  display: none;
  width: 0.25rem;
  background-color: #479cc8; }

.rz-datatable-reorder-indicator-up {
  position: absolute; }
  .rz-datatable-reorder-indicator-up:before {
    content: 'arrow_drop_down'; }

.rz-datatable-reorder-indicator-down {
  position: absolute; }
  .rz-datatable-reorder-indicator-down:before {
    content: 'arrow_drop_up'; }

.rz-column-resizer {
  position: absolute;
  top: 0;
  right: 0;
  height: 100%;
  cursor: col-resize;
  width: 0.5rem; }

.rz-rowgroup-header .fa,
.rz-row-toggler {
  color: #ffffff; }

.rz-datatable-scrollable-footer {
  background-color: #2f3d43;
  border-top: 1px solid #45565d; }

.rz-datatable-thead .rzi, .rz-datatable-thead .rz-menuitem .rz-menuitem-icon, .rz-menuitem .rz-datatable-thead .rz-menuitem-icon, .rz-datatable-thead .rz-fileupload-row .rz-button .rzi-close, .rz-fileupload-row .rz-button .rz-datatable-thead .rzi-close, .rz-datatable-thead .rz-fileupload-row .rz-paginator-element .rzi-close, .rz-fileupload-row .rz-paginator-element .rz-datatable-thead .rzi-close,
.rz-datatable-thead .rz-fileupload-row .rz-button .rzi-times, .rz-fileupload-row .rz-button .rz-datatable-thead .rzi-times,
.rz-datatable-thead .rz-fileupload-row .rz-paginator-element .rzi-times, .rz-fileupload-row .rz-paginator-element .rz-datatable-thead .rzi-times,
.rz-datatable-thead .rz-fileupload-row .rz-button .rz-icon-trash, .rz-fileupload-row .rz-button .rz-datatable-thead .rz-icon-trash,
.rz-datatable-thead .rz-fileupload-row .rz-paginator-element .rz-icon-trash, .rz-fileupload-row .rz-paginator-element .rz-datatable-thead .rz-icon-trash, .rz-datatable-thead .rz-datatable .rzi-chevron-circle-right, .rz-datatable .rz-datatable-thead .rzi-chevron-circle-right, .rz-datatable-thead .rz-datatable .rzi-chevron-circle-down, .rz-datatable .rz-datatable-thead .rzi-chevron-circle-down, .rz-datatable-thead .rz-sortable-column .rzi-grid-sort, .rz-sortable-column .rz-datatable-thead .rzi-grid-sort, .rz-datatable-thead .rz-datatable-header .rzi-plus, .rz-datatable-header .rz-datatable-thead .rzi-plus, .rz-datatable-thead .rz-datatable-loading-content .rzi-circle-o-notch, .rz-datatable-loading-content .rz-datatable-thead .rzi-circle-o-notch, .rz-datatable-thead .rz-column-drag, .rz-grid-table thead .rzi, .rz-grid-table thead .rz-menuitem .rz-menuitem-icon, .rz-menuitem .rz-grid-table thead .rz-menuitem-icon, .rz-grid-table thead .rz-fileupload-row .rz-button .rzi-close, .rz-fileupload-row .rz-button .rz-grid-table thead .rzi-close, .rz-grid-table thead .rz-fileupload-row .rz-paginator-element .rzi-close, .rz-fileupload-row .rz-paginator-element .rz-grid-table thead .rzi-close,
.rz-grid-table thead .rz-fileupload-row .rz-button .rzi-times, .rz-fileupload-row .rz-button .rz-grid-table thead .rzi-times,
.rz-grid-table thead .rz-fileupload-row .rz-paginator-element .rzi-times, .rz-fileupload-row .rz-paginator-element .rz-grid-table thead .rzi-times,
.rz-grid-table thead .rz-fileupload-row .rz-button .rz-icon-trash, .rz-fileupload-row .rz-button .rz-grid-table thead .rz-icon-trash,
.rz-grid-table thead .rz-fileupload-row .rz-paginator-element .rz-icon-trash, .rz-fileupload-row .rz-paginator-element .rz-grid-table thead .rz-icon-trash, .rz-grid-table thead .rz-datatable .rzi-chevron-circle-right, .rz-datatable .rz-grid-table thead .rzi-chevron-circle-right, .rz-grid-table thead .rz-datatable .rzi-chevron-circle-down, .rz-datatable .rz-grid-table thead .rzi-chevron-circle-down, .rz-grid-table thead .rz-sortable-column .rzi-grid-sort, .rz-sortable-column .rz-grid-table thead .rzi-grid-sort, .rz-grid-table thead .rz-datatable-header .rzi-plus, .rz-datatable-header .rz-grid-table thead .rzi-plus, .rz-grid-table thead .rz-datatable-loading-content .rzi-circle-o-notch, .rz-datatable-loading-content .rz-grid-table thead .rzi-circle-o-notch, .rz-grid-table thead .rz-column-drag {
  color: #ffffff; }

.rz-datatable-thead th, .rz-grid-table thead th {
  background-color: #2f3d43;
  padding: 0.5rem 0 0 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis; }
  .rz-datatable-thead th:not(:last-child), .rz-grid-table thead th:not(:last-child) {
    border-right: 1px solid #45565d; }
  .rz-datatable-thead th .rz-column-title, .rz-grid-table thead th .rz-column-title {
    display: inline-block;
    font-size: 0.75rem;
    text-transform: uppercase;
    color: #88989b;
    padding: 0 0 0.625rem 0.625rem;
    font-weight: normal; }

.rz-datatable-tfoot td, .rz-grid-table tfoot td {
  background-color: #2f3d43;
  font-size: 0.875rem;
  color: #ffffff;
  padding: 0.5rem 0.625rem 0.5rem 0.625rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis; }
  .rz-datatable-tfoot td:not(:last-child), .rz-grid-table tfoot td:not(:last-child) {
    border-right: 1px solid #45565d; }

.rz-datatable-scrollable-header {
  background-color: #2f3d43;
  border-bottom: 1px solid #45565d; }

.rz-datatable-scrollable-body {
  overflow: auto;
  border-top: none;
  flex: auto;
  border-bottom-right-radius: 4px;
  border-bottom-left-radius: 4px; }

.rz-has-paginator .rz-datatable-scrollable-body {
  border-radius: 0; }

.rz-sortable-column {
  cursor: pointer; }
  .rz-sortable-column:focus {
    outline: none; }
  .rz-sortable-column.rz-state-active {
    background-color: rgba(4, 4, 4, 0.1); }
  .rz-sortable-column .rzi-grid-sort {
    width: 1rem;
    height: 1rem;
    font-size: 1rem;
    vertical-align: middle; }
  .rz-sortable-column .rzi-sort {
    color: #88989b; }
    .rz-sortable-column .rzi-sort:before {
      content: 'sort'; }
  .rz-sortable-column .rzi-sort-asc {
    color: #88989b; }
    .rz-sortable-column .rzi-sort-asc:before {
      content: 'arrow_drop_up'; }
  .rz-sortable-column .rzi-sort-desc {
    color: #88989b; }
    .rz-sortable-column .rzi-sort-desc:before {
      content: 'arrow_drop_down'; }

.rz-datatable-odd > td {
  background-color: #586c75; }

.rz-datatable-even > td {
  background-color: #5d717b; }

.rz-datatable-data .rz-cell-data .rz-button-sm, .rz-datatable-data .rz-cell-data .rz-fileupload .rz-button, .rz-fileupload .rz-datatable-data .rz-cell-data .rz-button, .rz-datatable-data .rz-cell-data .rz-paginator-element, .rz-grid-table .rz-cell-data .rz-button-sm, .rz-grid-table .rz-cell-data .rz-fileupload .rz-button, .rz-fileupload .rz-grid-table .rz-cell-data .rz-button, .rz-grid-table .rz-cell-data .rz-paginator-element {
  line-height: 1.25rem;
  padding: 0 0.6875rem;
  height: 1.25rem;
  font-size: 0.75rem; }
  .rz-datatable-data .rz-cell-data .rz-button-sm .rz-button-icon-left, .rz-datatable-data .rz-cell-data .rz-fileupload .rz-button .rz-button-icon-left, .rz-fileupload .rz-datatable-data .rz-cell-data .rz-button .rz-button-icon-left, .rz-datatable-data .rz-cell-data .rz-paginator-element .rz-button-icon-left, .rz-grid-table .rz-cell-data .rz-button-sm .rz-button-icon-left, .rz-grid-table .rz-cell-data .rz-fileupload .rz-button .rz-button-icon-left, .rz-fileupload .rz-grid-table .rz-cell-data .rz-button .rz-button-icon-left, .rz-grid-table .rz-cell-data .rz-paginator-element .rz-button-icon-left {
    font-size: 0.75rem;
    line-height: 1.25rem;
    width: 0.75rem; }
  .rz-datatable-data .rz-cell-data .rz-button-sm .rz-button-text, .rz-datatable-data .rz-cell-data .rz-fileupload .rz-button .rz-button-text, .rz-fileupload .rz-datatable-data .rz-cell-data .rz-button .rz-button-text, .rz-datatable-data .rz-cell-data .rz-paginator-element .rz-button-text, .rz-grid-table .rz-cell-data .rz-button-sm .rz-button-text, .rz-grid-table .rz-cell-data .rz-fileupload .rz-button .rz-button-text, .rz-fileupload .rz-grid-table .rz-cell-data .rz-button .rz-button-text, .rz-grid-table .rz-cell-data .rz-paginator-element .rz-button-text {
    line-height: 1.25rem; }
  .rz-datatable-data .rz-cell-data .rz-button-sm.rz-button-icon-only, .rz-datatable-data .rz-cell-data .rz-fileupload .rz-button-icon-only.rz-button, .rz-fileupload .rz-datatable-data .rz-cell-data .rz-button-icon-only.rz-button, .rz-datatable-data .rz-cell-data .rz-fileupload .rz-paginator-element, .rz-fileupload .rz-datatable-data .rz-cell-data .rz-paginator-element, .rz-datatable-data .rz-cell-data .rz-paginator-element, .rz-grid-table .rz-cell-data .rz-button-sm.rz-button-icon-only, .rz-grid-table .rz-cell-data .rz-fileupload .rz-button-icon-only.rz-button, .rz-fileupload .rz-grid-table .rz-cell-data .rz-button-icon-only.rz-button, .rz-grid-table .rz-cell-data .rz-fileupload .rz-paginator-element, .rz-fileupload .rz-grid-table .rz-cell-data .rz-paginator-element, .rz-grid-table .rz-cell-data .rz-paginator-element {
    padding: 0 0.25rem; }

.rz-datatable-data td, .rz-grid-table td {
  padding: 0.5rem 0.625rem 0.5rem 0.625rem;
  border-bottom: 1px solid #45565d; }
  .rz-datatable-data td:not(:last-child), .rz-grid-table td:not(:last-child) {
    border-right: 1px solid #45565d; }
  .rz-datatable-data td .rz-cell-data, .rz-grid-table td .rz-cell-data {
    color: #ffffff;
    font-size: 0.875rem;
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap; }

.rz-datatable-data tr td:first-child, .rz-grid-table tr td:first-child {
  border-left: none; }

.rz-datatable-data tr td:last-child, .rz-grid-table tr td:last-child {
  border-right: none; }

.rz-datatable-data tr:first-child td, .rz-grid-table tr:first-child td {
  border-top: none; }

.rz-datatable-data tr:last-child td, .rz-grid-table tr:last-child td {
  border-bottom: none; }

.rz-datatable-reflow .rz-datatable-data td > .rz-column-title {
  display: none; }

.rz-datatable-scrollable {
  display: flex;
  flex-direction: column; }
  .rz-datatable-scrollable.rz-has-height > .rz-datatable-scrollable-wrapper {
    height: 0; }
    .rz-datatable-scrollable.rz-has-height > .rz-datatable-scrollable-wrapper > .rz-datatable-scrollable-view {
      height: 0; }

.rz-datatable-scrollable-wrapper {
  display: flex;
  flex-direction: column;
  flex: auto; }

.rz-datatable-scrollable-view {
  display: flex;
  flex: auto;
  flex-direction: column;
  overflow: hidden; }

.rz-datatable-header {
  background-color: #5d717b;
  padding: 0.625rem;
  border-bottom: 1px solid #45565d; }
  .rz-datatable-header .rzi-plus {
    font-size: 0.875rem; }
    .rz-datatable-header .rzi-plus:before {
      content: 'add'; }

.rz-cell-filter {
  padding: 0.25rem 0.625rem;
  background-color: #4a5a63;
  border-top: 1px solid #45565d;
  font-size: 0.8125rem;
  font-weight: normal; }
  .rz-cell-filter .rz-cell-filter-label {
    display: flex;
    flex: auto;
    align-items: center; }
    .rz-cell-filter .rz-cell-filter-label > .rzi, .rz-cell-filter .rz-menuitem .rz-cell-filter-label > .rz-menuitem-icon, .rz-menuitem .rz-cell-filter .rz-cell-filter-label > .rz-menuitem-icon, .rz-cell-filter .rz-fileupload-row .rz-button .rz-cell-filter-label > .rzi-close, .rz-fileupload-row .rz-button .rz-cell-filter .rz-cell-filter-label > .rzi-close, .rz-cell-filter .rz-fileupload-row .rz-paginator-element .rz-cell-filter-label > .rzi-close, .rz-fileupload-row .rz-paginator-element .rz-cell-filter .rz-cell-filter-label > .rzi-close,
    .rz-cell-filter .rz-fileupload-row .rz-button .rz-cell-filter-label > .rzi-times, .rz-fileupload-row .rz-button .rz-cell-filter .rz-cell-filter-label > .rzi-times,
    .rz-cell-filter .rz-fileupload-row .rz-paginator-element .rz-cell-filter-label > .rzi-times, .rz-fileupload-row .rz-paginator-element .rz-cell-filter .rz-cell-filter-label > .rzi-times,
    .rz-cell-filter .rz-fileupload-row .rz-button .rz-cell-filter-label > .rz-icon-trash, .rz-fileupload-row .rz-button .rz-cell-filter .rz-cell-filter-label > .rz-icon-trash,
    .rz-cell-filter .rz-fileupload-row .rz-paginator-element .rz-cell-filter-label > .rz-icon-trash, .rz-fileupload-row .rz-paginator-element .rz-cell-filter .rz-cell-filter-label > .rz-icon-trash, .rz-cell-filter .rz-datatable .rz-cell-filter-label > .rzi-chevron-circle-right, .rz-datatable .rz-cell-filter .rz-cell-filter-label > .rzi-chevron-circle-right, .rz-cell-filter .rz-datatable .rz-cell-filter-label > .rzi-chevron-circle-down, .rz-datatable .rz-cell-filter .rz-cell-filter-label > .rzi-chevron-circle-down, .rz-cell-filter .rz-sortable-column .rz-cell-filter-label > .rzi-grid-sort, .rz-sortable-column .rz-cell-filter .rz-cell-filter-label > .rzi-grid-sort, .rz-cell-filter .rz-datatable-header .rz-cell-filter-label > .rzi-plus, .rz-datatable-header .rz-cell-filter .rz-cell-filter-label > .rzi-plus, .rz-cell-filter .rz-datatable-loading-content .rz-cell-filter-label > .rzi-circle-o-notch, .rz-datatable-loading-content .rz-cell-filter .rz-cell-filter-label > .rzi-circle-o-notch, .rz-cell-filter .rz-cell-filter-label > .rz-column-drag {
      width: 0.875rem;
      height: 0.875rem;
      font-size: 0.8125rem;
      margin: 0 0.3125rem 0 0;
      color: #88989b; }
      .rz-cell-filter .rz-cell-filter-label > .rzi.rz-cell-filter-clear, .rz-cell-filter .rz-menuitem .rz-cell-filter-label > .rz-cell-filter-clear.rz-menuitem-icon, .rz-menuitem .rz-cell-filter .rz-cell-filter-label > .rz-cell-filter-clear.rz-menuitem-icon, .rz-cell-filter .rz-fileupload-row .rz-button .rz-cell-filter-label > .rz-cell-filter-clear.rzi-close, .rz-fileupload-row .rz-button .rz-cell-filter .rz-cell-filter-label > .rz-cell-filter-clear.rzi-close, .rz-cell-filter .rz-fileupload-row .rz-paginator-element .rz-cell-filter-label > .rz-cell-filter-clear.rzi-close, .rz-fileupload-row .rz-paginator-element .rz-cell-filter .rz-cell-filter-label > .rz-cell-filter-clear.rzi-close,
      .rz-cell-filter .rz-fileupload-row .rz-button .rz-cell-filter-label > .rz-cell-filter-clear.rzi-times, .rz-fileupload-row .rz-button .rz-cell-filter .rz-cell-filter-label > .rz-cell-filter-clear.rzi-times,
      .rz-cell-filter .rz-fileupload-row .rz-paginator-element .rz-cell-filter-label > .rz-cell-filter-clear.rzi-times, .rz-fileupload-row .rz-paginator-element .rz-cell-filter .rz-cell-filter-label > .rz-cell-filter-clear.rzi-times,
      .rz-cell-filter .rz-fileupload-row .rz-button .rz-cell-filter-label > .rz-cell-filter-clear.rz-icon-trash, .rz-fileupload-row .rz-button .rz-cell-filter .rz-cell-filter-label > .rz-cell-filter-clear.rz-icon-trash,
      .rz-cell-filter .rz-fileupload-row .rz-paginator-element .rz-cell-filter-label > .rz-cell-filter-clear.rz-icon-trash, .rz-fileupload-row .rz-paginator-element .rz-cell-filter .rz-cell-filter-label > .rz-cell-filter-clear.rz-icon-trash, .rz-cell-filter .rz-datatable .rz-cell-filter-label > .rz-cell-filter-clear.rzi-chevron-circle-right, .rz-datatable .rz-cell-filter .rz-cell-filter-label > .rz-cell-filter-clear.rzi-chevron-circle-right, .rz-cell-filter .rz-datatable .rz-cell-filter-label > .rz-cell-filter-clear.rzi-chevron-circle-down, .rz-datatable .rz-cell-filter .rz-cell-filter-label > .rz-cell-filter-clear.rzi-chevron-circle-down, .rz-cell-filter .rz-sortable-column .rz-cell-filter-label > .rz-cell-filter-clear.rzi-grid-sort, .rz-sortable-column .rz-cell-filter .rz-cell-filter-label > .rz-cell-filter-clear.rzi-grid-sort, .rz-cell-filter .rz-datatable-header .rz-cell-filter-label > .rz-cell-filter-clear.rzi-plus, .rz-datatable-header .rz-cell-filter .rz-cell-filter-label > .rz-cell-filter-clear.rzi-plus, .rz-cell-filter .rz-datatable-loading-content .rz-cell-filter-label > .rz-cell-filter-clear.rzi-circle-o-notch, .rz-datatable-loading-content .rz-cell-filter .rz-cell-filter-label > .rz-cell-filter-clear.rzi-circle-o-notch, .rz-cell-filter .rz-cell-filter-label > .rz-cell-filter-clear.rz-column-drag {
        margin-left: auto; }
    .rz-cell-filter .rz-cell-filter-label .rz-spinner {
      border: none;
      padding: 0;
      height: auto;
      background-color: transparent;
      box-shadow: none;
      line-height: 1;
      flex: auto; }
      .rz-cell-filter .rz-cell-filter-label .rz-spinner input {
        line-height: 1; }
        .rz-cell-filter .rz-cell-filter-label .rz-spinner input:focus {
          outline: none;
          color: #479cc8; }
      .rz-cell-filter .rz-cell-filter-label .rz-spinner button {
        display: none; }
    .rz-cell-filter .rz-cell-filter-label .rz-chkbox-box.rz-state-active {
      background-color: #323f45; }
    .rz-cell-filter .rz-cell-filter-label > input {
      width: 0;
      flex: auto;
      border: none;
      box-shadow: none;
      padding: 0;
      background-color: transparent;
      color: #88989b;
      line-height: 1; }
      .rz-cell-filter .rz-cell-filter-label > input:focus {
        outline: none;
        color: #479cc8; }

.rz-selectable .rz-datatable-even.rz-state-highlight > td,
.rz-selectable .rz-datatable-odd.rz-state-highlight > td {
  background-color: #479cc8; }

.rz-selectable .rz-datatable-even.rz-state-highlight .rz-cell-data,
.rz-selectable .rz-datatable-odd.rz-state-highlight .rz-cell-data {
  color: #ffffff; }

.rz-selectable .rz-datatable-even.rz-state-highlight > .rzi, .rz-selectable .rz-menuitem .rz-datatable-even.rz-state-highlight > .rz-menuitem-icon, .rz-menuitem .rz-selectable .rz-datatable-even.rz-state-highlight > .rz-menuitem-icon, .rz-selectable .rz-fileupload-row .rz-button .rz-datatable-even.rz-state-highlight > .rzi-close, .rz-fileupload-row .rz-button .rz-selectable .rz-datatable-even.rz-state-highlight > .rzi-close, .rz-selectable .rz-fileupload-row .rz-paginator-element .rz-datatable-even.rz-state-highlight > .rzi-close, .rz-fileupload-row .rz-paginator-element .rz-selectable .rz-datatable-even.rz-state-highlight > .rzi-close,
.rz-selectable .rz-fileupload-row .rz-button .rz-datatable-even.rz-state-highlight > .rzi-times, .rz-fileupload-row .rz-button .rz-selectable .rz-datatable-even.rz-state-highlight > .rzi-times,
.rz-selectable .rz-fileupload-row .rz-paginator-element .rz-datatable-even.rz-state-highlight > .rzi-times, .rz-fileupload-row .rz-paginator-element .rz-selectable .rz-datatable-even.rz-state-highlight > .rzi-times,
.rz-selectable .rz-fileupload-row .rz-button .rz-datatable-even.rz-state-highlight > .rz-icon-trash, .rz-fileupload-row .rz-button .rz-selectable .rz-datatable-even.rz-state-highlight > .rz-icon-trash,
.rz-selectable .rz-fileupload-row .rz-paginator-element .rz-datatable-even.rz-state-highlight > .rz-icon-trash, .rz-fileupload-row .rz-paginator-element .rz-selectable .rz-datatable-even.rz-state-highlight > .rz-icon-trash, .rz-selectable .rz-datatable .rz-datatable-even.rz-state-highlight > .rzi-chevron-circle-right, .rz-datatable .rz-selectable .rz-datatable-even.rz-state-highlight > .rzi-chevron-circle-right, .rz-selectable .rz-datatable .rz-datatable-even.rz-state-highlight > .rzi-chevron-circle-down, .rz-datatable .rz-selectable .rz-datatable-even.rz-state-highlight > .rzi-chevron-circle-down, .rz-selectable .rz-sortable-column .rz-datatable-even.rz-state-highlight > .rzi-grid-sort, .rz-sortable-column .rz-selectable .rz-datatable-even.rz-state-highlight > .rzi-grid-sort, .rz-selectable .rz-datatable-header .rz-datatable-even.rz-state-highlight > .rzi-plus, .rz-datatable-header .rz-selectable .rz-datatable-even.rz-state-highlight > .rzi-plus, .rz-selectable .rz-datatable-loading-content .rz-datatable-even.rz-state-highlight > .rzi-circle-o-notch, .rz-datatable-loading-content .rz-selectable .rz-datatable-even.rz-state-highlight > .rzi-circle-o-notch, .rz-selectable .rz-datatable-even.rz-state-highlight > .rz-column-drag,
.rz-selectable .rz-datatable-odd.rz-state-highlight > .rzi,
.rz-selectable .rz-menuitem .rz-datatable-odd.rz-state-highlight > .rz-menuitem-icon,
.rz-menuitem .rz-selectable .rz-datatable-odd.rz-state-highlight > .rz-menuitem-icon,
.rz-selectable .rz-fileupload-row .rz-button .rz-datatable-odd.rz-state-highlight > .rzi-close,
.rz-fileupload-row .rz-button .rz-selectable .rz-datatable-odd.rz-state-highlight > .rzi-close,
.rz-selectable .rz-fileupload-row .rz-paginator-element .rz-datatable-odd.rz-state-highlight > .rzi-close,
.rz-fileupload-row .rz-paginator-element .rz-selectable .rz-datatable-odd.rz-state-highlight > .rzi-close,
.rz-selectable .rz-fileupload-row .rz-button .rz-datatable-odd.rz-state-highlight > .rzi-times,
.rz-fileupload-row .rz-button .rz-selectable .rz-datatable-odd.rz-state-highlight > .rzi-times,
.rz-selectable .rz-fileupload-row .rz-paginator-element .rz-datatable-odd.rz-state-highlight > .rzi-times,
.rz-fileupload-row .rz-paginator-element .rz-selectable .rz-datatable-odd.rz-state-highlight > .rzi-times,
.rz-selectable .rz-fileupload-row .rz-button .rz-datatable-odd.rz-state-highlight > .rz-icon-trash,
.rz-fileupload-row .rz-button .rz-selectable .rz-datatable-odd.rz-state-highlight > .rz-icon-trash,
.rz-selectable .rz-fileupload-row .rz-paginator-element .rz-datatable-odd.rz-state-highlight > .rz-icon-trash,
.rz-fileupload-row .rz-paginator-element .rz-selectable .rz-datatable-odd.rz-state-highlight > .rz-icon-trash,
.rz-selectable .rz-datatable .rz-datatable-odd.rz-state-highlight > .rzi-chevron-circle-right,
.rz-datatable .rz-selectable .rz-datatable-odd.rz-state-highlight > .rzi-chevron-circle-right,
.rz-selectable .rz-datatable .rz-datatable-odd.rz-state-highlight > .rzi-chevron-circle-down,
.rz-datatable .rz-selectable .rz-datatable-odd.rz-state-highlight > .rzi-chevron-circle-down,
.rz-selectable .rz-sortable-column .rz-datatable-odd.rz-state-highlight > .rzi-grid-sort,
.rz-sortable-column .rz-selectable .rz-datatable-odd.rz-state-highlight > .rzi-grid-sort,
.rz-selectable .rz-datatable-header .rz-datatable-odd.rz-state-highlight > .rzi-plus,
.rz-datatable-header .rz-selectable .rz-datatable-odd.rz-state-highlight > .rzi-plus,
.rz-selectable .rz-datatable-loading-content .rz-datatable-odd.rz-state-highlight > .rzi-circle-o-notch,
.rz-datatable-loading-content .rz-selectable .rz-datatable-odd.rz-state-highlight > .rzi-circle-o-notch,
.rz-selectable .rz-datatable-odd.rz-state-highlight > .rz-column-drag {
  color: #ffffff; }

.rz-selectable .rz-datatable-even:hover:not(.rz-state-highlight) > td,
.rz-selectable .rz-datatable-odd:hover:not(.rz-state-highlight) > td {
  background-color: rgba(69, 86, 93, 0.5); }

.rz-selectable .rz-datatable-even:hover:not(.rz-state-highlight) .rz-cell-data,
.rz-selectable .rz-datatable-odd:hover:not(.rz-state-highlight) .rz-cell-data {
  color: #479cc8; }

.rz-cell-filter-content {
  display: flex;
  flex: auto;
  align-items: center; }

.rz-cell-filter-content {
  color: #88989b;
  min-height: 1.375rem; }

.rz-date-filter {
  display: flex;
  align-items: center;
  background-color: #4a5a63; }
  .rz-date-filter .rz-listbox {
    margin: 1.25rem; }
  .rz-date-filter .rz-datepicker {
    border-left: 1px solid #45565d; }

.rz-expanded-row > td {
  border-bottom: none;
  background-color: rgba(69, 86, 93, 0.5); }
  .rz-expanded-row > td .rz-cell-data,
  .rz-expanded-row > td .rz-row-toggler {
    color: #479cc8; }

.rz-expanded-row-template {
  background-color: #5d717b;
  padding: 0.625rem;
  border: none;
  border-radius: 2px; }

.rz-expanded-row-content > td {
  padding-top: 0;
  background-color: #5d717b; }

.rz-rowgroup-header a:hover {
  text-decoration: none; }

.rz-rowgroup-header td {
  border-top: 1px solid #45565d;
  border-bottom: 1px solid #45565d; }

.rz-date-filter-buttons {
  display: flex;
  justify-content: space-between;
  padding: 0.325rem 1.25rem;
  background-color: #5d717b;
  border-top: 1px solid rgba(151, 151, 151, 0.2); }

.rz-clear-filter {
  background-color: #e6ecef;
  color: #88989b; }

.rz-apply-filter {
  background-color: #479cc8; }

.rz-datatable-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(136, 152, 155, 0.5);
  z-index: 2; }

.rz-datatable-loading-content {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: 1; }
  .rz-datatable-loading-content .rzi-circle-o-notch {
    animation: rotation 1s linear infinite; }
    .rz-datatable-loading-content .rzi-circle-o-notch:before {
      content: 'refresh'; }

@keyframes rotation {
  from {
    transform: rotate(0deg); }
  to {
    transform: rotate(360deg); } }

@media (max-width: 768px) {
  .rz-datatable-reflow .rz-datatable-tablewrapper > table,
  .rz-datatable-reflow .rz-datatable-scrollable-header-box > table,
  .rz-datatable-reflow .rz-datatable-scrollable-table-wrapper > table {
    table-layout: auto; }
  .rz-datatable-reflow .rz-datatable-thead th {
    display: none; }
  .rz-datatable-reflow .rz-datatable-odd > td,
  .rz-datatable-reflow .rz-datatable-even > td {
    display: block;
    width: 100% !important;
    text-align: left !important;
    border: none; }
    .rz-datatable-reflow .rz-datatable-odd > td .rz-column-title,
    .rz-datatable-reflow .rz-datatable-even > td .rz-column-title {
      display: block; } }

.rz-grid-filter {
  padding: 0 1rem; }
  .rz-grid-filter .rz-dropdown,
  .rz-grid-filter .rz-spinner,
  .rz-grid-filter .rz-textbox,
  .rz-grid-filter .rz-grid-filter-label {
    display: block;
    margin: 1rem 0; }

.rz-grid-filter-buttons {
  display: flex;
  justify-content: space-between;
  margin: 1rem; }

.rz-grid-filter-icon {
  color: #88989b;
  margin: 5px 5px 0 0;
  float: right;
  font-size: 1rem; }
  .rz-grid-filter-icon:hover {
    cursor: pointer; }
  .rz-grid-filter-icon:after {
    content: 'filter_alt'; }

.rz-grid-filter-active {
  color: #479cc8 !important; }

.rz-data-grid {
  display: flex;
  flex-direction: column; }

.rz-data-grid-data {
  overflow: auto;
  flex: 1; }

.rz-grid-table td, .rz-grid-table th {
  padding: .5rem; }

.rz-grid-table thead th {
  position: sticky;
  top: 0;
  z-index: 1; }

.rz-grid-table thead tr:nth-of-type(2) th {
  top: 35px; }

.rz-grid-table-fixed {
  table-layout: fixed; }
  .rz-grid-table-fixed .rz-frozen-cell {
    position: -webkit-sticky;
    position: sticky;
    background: #2f3d43;
    z-index: 1; }

.rz-grid-table tfoot, .rz-grid-table tfoot td {
  position: sticky;
  bottom: 0;
  z-index: 1; }

.rz-grid-table {
  overflow: auto;
  width: 100%;
  position: relative;
  border-collapse: separate;
  border-spacing: 0; }
  .rz-grid-table th {
    white-space: nowrap;
    overflow: hidden; }
  .rz-grid-table td {
    white-space: nowrap;
    overflow: hidden; }

.rz-grid-table tbody > div {
  display: table-row; }

.rz-column-drag {
  cursor: grab;
  font-size: small; }
  .rz-column-drag:after {
    content: 'more_vert'; }
  .rz-column-drag:active {
    cursor: grabbing; }

.rz-column-draggable {
  background-color: #2f3d43; }

.rz-group-header {
  background-color: #2f3d43;
  padding: 15px;
  border-bottom: 1px solid #45565d; }

.rz-group-header-item {
  border: 1px solid #45565d;
  padding: 10px;
  margin: 5px;
  width: fit-content;
  float: left; }

.rz-group-header-item-title {
  margin-right: 5px; }

.rz-paginator {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #5d717b;
  padding: 0.625rem; }
  .rz-paginator .rzi-step-backward:before {
    content: 'first_page'; }
  .rz-paginator .rzi-caret-left:before {
    content: 'navigate_before'; }
  .rz-paginator .rzi-caret-right:before {
    content: 'navigate_next'; }
  .rz-paginator .rzi-step-forward:before {
    content: 'last_page'; }
  .rz-paginator .rz-dropdown {
    width: 80px;
    margin: 0 0 0 0.625rem;
    overflow: visible; }
  .rz-paginator .rz-dropdown-items-wrapper {
    width: 76px; }

.rz-paginator-element:hover,
.rz-paginator-page:hover {
  color: inherit; }

.rz-paginator-bottom {
  border-top: 1px solid #45565d;
  border-bottom-right-radius: 4px;
  border-bottom-left-radius: 4px; }

.rz-paginator-first {
  margin: 0 0.625rem 0 0; }

.rz-paginator-prev {
  margin-right: auto; }

.rz-paginator-next {
  margin-left: auto; }

.rz-paginator-last {
  margin: 0 0 0 0.625rem; }

.rz-paginator-first,
.rz-paginator-prev {
  background-color: #88989b;
  color: #515f65; }

.rz-paginator-element:hover {
  text-decoration: none; }

.rz-paginator-last,
.rz-paginator-next {
  background-color: #479cc8;
  color: #ffffff; }
  .rz-paginator-last:hover,
  .rz-paginator-next:hover {
    color: #ffffff; }

.rz-paginator-page {
  display: inline-block;
  margin: 0 0.3125rem;
  background-color: #88989b;
  border-radius: 4px;
  padding: 0.3125rem 0.6875rem;
  color: #515f65; }
  .rz-paginator-page.rz-state-active {
    background-color: #3a474d;
    color: #ffffff;
    border: 1px solid #3a474d;
    padding: 0.25rem 0.625rem; }

@media (max-width: 768px) {
  .rz-paginator-page:not(.rz-state-active) {
    display: none; } }

.rz-overlaypanel {
  position: absolute;
  overflow: hidden;
  box-shadow: 0 6px 14px 0 rgba(0, 0, 0, 0.06);
  border: 1px solid #2f3a40;
  border-radius: 4px;
  background-color: #3a474d; }

.rz-tree {
  display: inline-block;
  overflow: auto; }

.rz-tree .rz-treenode.rz-treenode-leaf > .rz-treenode-content > .rz-tree-toggler {
  visibility: hidden; }

.rz-tree-toggler {
  cursor: pointer; }
  .rz-tree-toggler.rzi-caret-right:before {
    content: "arrow_right"; }
  .rz-tree-toggler.rzi-caret-down:before {
    content: "arrow_drop_down"; }

.rz-treenode-content {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 0.375rem 0.25rem;
  margin: 1px 0; }
  .rz-treenode-content:not(.rz-treenode-content-selected):hover {
    background-color: #59a6cd;
    color: #ffffff;
    border-radius: 2px; }

.rz-tree-container,
.rz-treenode-children {
  list-style: none;
  padding: 0;
  margin: 0; }

.rz-treenode-children {
  padding: 0 0 0 1rem; }

.rz-treenode-content-selected {
  border-radius: 2px;
  color: #ffffff;
  background-color: #479cc8; }

.rz-datalist,
.rz-datagrid {
  background-color: #5d717b;
  border-bottom-right-radius: 4px;
  border-bottom-left-radius: 4px;
  box-shadow: 0 8px 10px 0 rgba(0, 0, 0, 0.01);
  border: 1px solid #45565d; }

.rz-datalist-data > li, .rz-g > div {
  border-radius: 4px;
  border: none;
  box-shadow: 0 22px 64px 0 rgba(0, 0, 0, 0.22);
  padding: 1rem;
  background-color: #3a474d; }

.rz-datalist-data {
  list-style: none;
  padding: 0.6875rem;
  margin: 0; }
  .rz-datalist-data > li {
    margin: 0.6875rem 0; }
    .rz-datalist-data > li:first-child {
      margin-top: 0; }
    .rz-datalist-data > li:last-child {
      margin-bottom: 0; }

.rz-g {
  display: flex;
  flex-wrap: wrap; }
  .rz-g > div {
    flex: auto;
    margin: 0.6875rem; }

.rz-scheduler {
  display: flex;
  height: 400px;
  flex-direction: column;
  border-radius: 4px;
  border: 1px solid #88989b;
  box-shadow: 0 8px 10px 0 rgba(0, 0, 0, 0.01);
  color: #88989b;
  background: #5d717b; }
  .rz-scheduler .rz-event-list-btn {
    position: absolute;
    color: #479cc8; }
    .rz-scheduler .rz-event-list-btn:hover {
      cursor: pointer;
      text-decoration: underline; }

.rz-slot {
  display: flex;
  height: 1.5em;
  border-top: 1px solid #88989b;
  border-left: 1px solid #88989b; }

.rz-slot-hours .rz-slot-header {
  height: 1.5em;
  text-align: right;
  padding: 0 4px;
  width: 80px;
  border-right: 1px solid #88989b;
  white-space: nowrap; }

.rz-slot-minor {
  border-top: 1px solid rgba(223, 231, 235, 0.2); }

.rz-day-view .rz-slot,
.rz-slots:first-child .rz-slot {
  border-left: none; }

.rz-event {
  position: absolute;
  padding: 2px 1px; }

.rz-event-content {
  background: #68d5c8;
  border-radius: 4px;
  color: #ffffff;
  height: 100%;
  padding: 4px;
  font-size: 0.6875rem;
  line-height: 12px;
  overflow: hidden; }

.rz-events {
  position: relative; }

.rz-month-view .rz-view-content {
  flex: 1;
  display: flex;
  flex-direction: column; }

.rz-slot-title {
  text-align: right;
  padding: 0 11px; }

.rz-scheduler-nav {
  display: flex;
  justify-content: space-between;
  padding: 0.625rem;
  background: #2f3d43; }

.rz-view-header {
  border-top: none;
  border-bottom: none;
  background-color: #4a5a63;
  text-transform: uppercase;
  display: flex; }
  .rz-view-header .rz-slot-header {
    flex: 1;
    text-align: center;
    font-size: 0.6875rem;
    padding: 0.5rem 0; }
  .rz-view-header .rz-slot-hour-header {
    flex: none;
    width: 80px; }

.rz-view {
  display: flex;
  flex-direction: column;
  flex: 1; }

.rz-view-content {
  flex: auto;
  display: flex;
  overflow: auto;
  height: 0; }

.rz-week-view-content {
  flex: 1;
  display: flex; }

.rz-slots {
  flex: 1; }

.rz-week {
  flex: 1; }
  .rz-week .rz-slots {
    display: flex;
    height: 100%; }
  .rz-week:first-child .rz-slot {
    border-top: none; }

.rz-month-view .rz-slot {
  flex: 1;
  height: 100%; }
  .rz-month-view .rz-slot:first-child {
    border-left: none; }

.rz-day-view .rz-slot:nth-of-type(2),
.rz-week-view .rz-slot:nth-of-type(2) {
  border-top: none; }

.rz-week-view .rz-view-header,
.rz-day-view .rz-view-header {
  padding-right: 15px; }

.rz-scheduler-nav-views {
  display: flex; }
  .rz-scheduler-nav-views .rz-button, .rz-scheduler-nav-views .rz-paginator-element {
    background: #ffffff;
    color: #88989b;
    text-transform: uppercase;
    padding: 0 0.875rem;
    font-size: 0.6875rem;
    line-height: 1.75rem;
    border: solid 1px #dadfe2;
    border-radius: 0; }
    .rz-scheduler-nav-views .rz-button:first-child, .rz-scheduler-nav-views .rz-paginator-element:first-child {
      border-top-left-radius: 4px;
      border-bottom-left-radius: 4px;
      border-right: none; }
    .rz-scheduler-nav-views .rz-button:last-child, .rz-scheduler-nav-views .rz-paginator-element:last-child {
      border-top-right-radius: 4px;
      border-bottom-right-radius: 4px;
      border-left: none; }
  .rz-scheduler-nav-views .rz-state-active {
    background-color: #479cc8;
    border-color: #479cc8;
    color: #ffffff; }

.rz-scheduler-nav-prev-next {
  display: flex; }
  .rz-scheduler-nav-prev-next .rz-today {
    margin-left: 16px; }
  .rz-scheduler-nav-prev-next .rz-prev {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0; }
  .rz-scheduler-nav-prev-next .rz-next {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0; }
  .rz-scheduler-nav-prev-next .rz-button, .rz-scheduler-nav-prev-next .rz-paginator-element {
    background-color: #479cc8;
    color: #ffffff;
    text-transform: uppercase;
    padding: 0 0.875rem;
    font-size: 0.6875rem; }

.rz-event-list .rz-event {
  position: static; }

.rz-tabview {
  display: flex;
  flex-direction: column; }

.rz-tabview-nav {
  list-style: none;
  display: flex;
  padding: 0;
  margin: 0; }
  .rz-tabview-nav li {
    border: none;
    border-top-width: 2px;
    border-bottom-color: #2f3d43;
    background-color: #2f3d43;
    border-radius: 4px 4px 0 0; }
    .rz-tabview-nav li:hover:not(.rz-tabview-selected):not(.rz-state-disabled) {
      background-color: #2f3d43;
      border-top-color: #479cc8; }
      .rz-tabview-nav li:hover:not(.rz-tabview-selected):not(.rz-state-disabled) a {
        color: #479cc8; }
    .rz-tabview-nav li a {
      display: inline-flex;
      align-items: center;
      color: rgba(255, 255, 255, 0.5);
      padding: 0.375rem 1.875rem;
      font-weight: 600; }
      .rz-tabview-nav li a:hover {
        text-decoration: none; }
    .rz-tabview-nav li.rz-state-disabled {
      opacity: 0.5; }
  .rz-tabview-nav .rz-tabview-selected {
    background-color: #5d717b;
    border-bottom-color: #5d717b;
    margin-bottom: -1px;
    position: relative;
    border-top-color: #479cc8; }
    .rz-tabview-nav .rz-tabview-selected a {
      color: #ffffff; }

.rz-tabview-panels {
  background-color: #5d717b;
  border: none;
  border-radius: 0 0 4px 4px;
  box-shadow: 0 8px 10px 0 rgba(0, 0, 0, 0.01);
  flex: 1;
  overflow: auto; }

@media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) {
  .rz-tabview-panels {
    flex: auto; } }

.rz-tabview-panel {
  padding: 1.25rem; }

.rz-tabview-left-icon {
  font-size: 1.0625rem; }

.rz-tooltip {
  position: absolute;
  transition: top 0.2s, left 0.2s;
  top: 0;
  left: 0; }

.rz-tooltip-content {
  background: #84d2c8;
  color: #fff;
  box-shadow: 0 6px 14px 0 rgba(0, 0, 0, 0.15);
  padding: 0.25rem 1rem;
  border-radius: 4px;
  white-space: nowrap; }

.rz-tooltip .rz-top-tooltip-content {
  margin-bottom: 15px; }

.rz-tooltip .rz-top-tooltip-content:after {
  content: ' ';
  position: absolute;
  width: 8px;
  height: 8px;
  bottom: 0;
  background-color: inherit;
  transform-origin: center;
  transform: translate(-50%, -11px) rotate(45deg);
  border-bottom: inherit;
  border-right: inherit; }

.rz-tooltip .rz-bottom-tooltip-content {
  margin-top: -8px; }

.rz-tooltip .rz-bottom-tooltip-content:after {
  content: ' ';
  position: absolute;
  width: 8px;
  height: 8px;
  top: 0;
  background-color: inherit;
  transform-origin: center;
  transform: translate(-50%, -11px) rotate(45deg);
  border-bottom: inherit;
  border-right: inherit; }

.rz-tooltip .rz-left-tooltip-content {
  margin-right: 8px; }

.rz-tooltip .rz-left-tooltip-content:after {
  content: ' ';
  position: absolute;
  width: 8px;
  height: 8px;
  top: 20px;
  right: 0;
  background-color: inherit;
  transform-origin: center;
  transform: translate(-50%, -11px) rotate(45deg);
  border-bottom: inherit;
  border-right: inherit; }

.rz-tooltip .rz-right-tooltip-content {
  margin-left: 0px; }

.rz-tooltip .rz-right-tooltip-content:after {
  content: ' ';
  position: absolute;
  width: 8px;
  height: 8px;
  top: 20px;
  left: 0;
  background-color: inherit;
  transform-origin: center;
  transform: translate(-50%, -11px) rotate(45deg);
  border-bottom: inherit;
  border-right: inherit; }

.rz-dialog {
  position: fixed;
  overflow: hidden;
  background-color: #3a474d;
  box-shadow: 0 22px 64px 0 rgba(0, 0, 0, 0.22);
  border-radius: 4px; }

.rz-dialog-titlebar {
  background-color: #28363c;
  padding: 0.6875rem 1.25rem;
  font-size: 1.25rem;
  line-height: 1.875rem;
  border-bottom: none; }

.rz-dialog-titlebar-close {
  float: right; }
  .rz-dialog-titlebar-close .rzi-times {
    font-size: 1.25rem;
    color: #88989b;
    vertical-align: middle; }
    .rz-dialog-titlebar-close .rzi-times:before {
      content: 'close'; }

.rz-dialog-content {
  padding: 1.25rem;
  overflow: auto; }

.rz-confirmdialog {
  top: 50%;
  left: 50%; }

.rz-dialog-mask {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background-color: #3a474d;
  opacity: 0.5; }

.rz-dialog-footer {
  text-align: right;
  border: 0 none;
  padding: 1em; }
  .rz-dialog-footer .rz-button, .rz-dialog-footer .rz-paginator-element {
    text-align: left !important;
    padding: 6px; }
  .rz-dialog-footer .rz-button:first-child, .rz-dialog-footer .rz-paginator-element:first-child {
    background-color: #ff6d41;
    width: 80px; }
  .rz-dialog-footer .rz-button:last-child, .rz-dialog-footer .rz-paginator-element:last-child {
    background-color: #479cc8;
    width: 105px; }

@media (max-width: 768px) {
  .rz-dialog:not(.rz-confirmdialog) {
    width: 100% !important;
    top: 0px !important; }
  .rz-dialog-content {
    -webkit-overflow-scrolling: touch; }
  .no-scroll {
    padding-right: 0; } }

.no-scroll {
  overflow: hidden;
  padding-right: 15px; }

.rz-dialog-wrapper {
  display: flex;
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  bottom: 0;
  z-index: 1001;
  align-items: center;
  justify-content: center; }
  .rz-dialog-wrapper .rz-dialog {
    max-height: 100%;
    display: flex;
    flex-direction: column;
    position: static; }
  .rz-dialog-wrapper .rz-dialog-content {
    flex: 0 1 auto;
    overflow: auto; }

.rz-growl {
  position: fixed;
  top: 100px;
  right: 20px;
  min-width: 20rem; }

.rz-growl-item {
  border-radius: 4px;
  padding: 1rem 1.25rem; }

.rz-growl-message-success .rz-growl-item {
  background-color: #5dbf74; }

.rz-growl-message-warn .rz-growl-item {
  background-color: #e6c54f; }

.rz-growl-message-error .rz-growl-item {
  background-color: #f9777f; }

.rz-growl-message-info .rz-growl-item {
  background-color: #68d5c8; }

.rz-growl-item {
  margin: 1rem 0;
  color: #ffffff; }
  .rz-growl-item p {
    color: inherit; }

.rz-growl-icon-close {
  float: right; }
  .rz-growl-icon-close:before {
    content: "close"; }

.rz-growl-image {
  float: left;
  margin: 0 0.625rem 0 0; }
  .rz-growl-image.rzi-check:before {
    content: "check"; }
  .rz-growl-image.rzi-exclamation-triangle:before {
    content: "warning"; }
  .rz-growl-image.rzi-info-circle:before {
    content: "info"; }
  .rz-growl-image.rzi-times:before {
    content: "error"; }

.rz-growl-title {
  font-weight: bold; }

.rz-growl-message p {
  display: inline-block;
  margin-bottom: 0; }

.rz-form .rz-textbox,
.rz-form .rz-lookup,
.rz-form .rz-dropdown,
.rz-form .rz-multiselect,
.rz-form .rz-calendar,
.rz-form .rz-spinner,
.rz-form .textarea {
  width: 100%; }

.rz-form .rz-messages-error {
  position: absolute; }

.rz-messages-error {
  display: inline-block;
  color: #f9777f;
  font-size: 0.75rem; }

.rz-message-popup {
  position: absolute;
  background-color: #f9777f;
  transform: translateY(16px);
  box-shadow: 0 6px 14px 0 rgba(0, 0, 0, 0.15);
  padding: 4px 12px;
  border-radius: 4px;
  color: #ffffff;
  pointer-events: none; }
  .rz-message-popup:before {
    content: '';
    border: 6px solid transparent;
    border-bottom-color: #f9777f;
    border-left-color: #f9777f;
    position: absolute;
    top: 0;
    left: 0;
    transform: translate(6px, -100%); }

.rz-template-form .row {
  margin: 0; }

body:not(.rz-default-scrollbars)::-webkit-scrollbar {
  background-color: #5d717b; }

body:not(.rz-default-scrollbars)::-webkit-scrollbar-thumb {
  background: #3a474d;
  border: 4px solid rgba(0, 0, 0, 0);
  background-clip: padding-box;
  border-radius: 8px; }

body:not(.rz-default-scrollbars)::-webkit-scrollbar-corner {
  background-color: #5d717b; }

body:not(.rz-default-scrollbars) ::-webkit-scrollbar {
  background-color: #5d717b; }

body:not(.rz-default-scrollbars) ::-webkit-scrollbar-thumb {
  background: #3a474d;
  border: 4px solid rgba(0, 0, 0, 0);
  background-clip: padding-box;
  border-radius: 8px; }

body:not(.rz-default-scrollbars) ::-webkit-scrollbar-corner {
  background-color: #5d717b; }

.login {
  display: flex;
  flex-direction: column;
  justify-content: space-between; }
  .login .register {
    text-align: center;
    background-color: #5d717b;
    padding: 1rem; }
    .login .register .rz-button, .login .register .rz-paginator-element {
      margin: 0 1rem; }
  .login .login-buttons {
    display: flex;
    justify-content: space-between;
    align-items: center; }
    .login .login-buttons a {
      color: #4db9f2;
      cursor: pointer; }
      .login .login-buttons a:hover {
        color: #4db9f2;
        text-decoration: underline; }

.rz-lookup-panel {
  background-color: #5d717b;
  padding: 1rem; }

.rz-lookup-search {
  display: flex;
  margin-bottom: 1rem; }
  .rz-lookup-search input {
    flex: auto;
    margin-right: 1rem; }

.rz-lookup-select {
  margin-top: 1rem;
  display: flex;
  justify-content: flex-end; }

.rz-lookup {
  display: inline-block; }
  .rz-lookup .rz-dropdown {
    width: 100%; }

.ssrsviewer {
  display: flex; }
  .ssrsviewer iframe {
    flex: auto; }

.rz-map {
  height: 10rem;
  padding: 1.25rem;
  background-color: #ffffff;
  box-shadow: 0 8px 10px 0 rgba(0, 0, 0, 0.01);
  border-radius: 4px; }

.rz-map-container {
  height: 100%; }

.rz-gauge,
.rz-arc-gauge {
  position: relative;
  display: inline-block;
  width: 300px;
  height: 300px; }

.rz-gauge .rz-line,
.rz-gauge .rz-tick {
  stroke: #fff; }

.rz-gauge .rz-tick-text {
  font-size: 0.875rem;
  fill: #fff; }

.rz-arc-gauge .rz-line,
.rz-arc-gauge .rz-tick {
  stroke: #fff; }

.rz-arc-gauge .rz-tick-text {
  font-size: 0.875rem;
  fill: #fff; }

.rz-gauge-value {
  position: absolute;
  transform: translateX(-50%);
  padding: 0.5rem; }

.rz-arc-gauge-value {
  position: absolute;
  transform: translate(-50%, -50%);
  padding: 0.5rem; }

.rz-gauge-pointer {
  fill: #fff; }

.rz-arc-gauge-scale-value {
  fill: #479cc8; }

.rz-arc-gauge-scale {
  fill: #fff; }

.rz-progressbar {
  border-radius: 4px;
  height: 1.25rem;
  position: relative;
  background-color: #5d717b;
  text-align: center;
  display: flex;
  align-items: center; }

.rz-progressbar-value {
  border-radius: 4px;
  position: absolute;
  background-color: #479cc8;
  height: 100%;
  width: 100%; }

.rz-progressbar-label {
  position: relative;
  width: 100%;
  text-align: center;
  font-size: 1rem;
  line-height: 1rem; }

.rz-progressbar-indeterminate {
  overflow: hidden; }
  .rz-progressbar-indeterminate .rz-progressbar-value {
    background-color: transparent; }
    .rz-progressbar-indeterminate .rz-progressbar-value:before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      bottom: 0;
      background-color: #479cc8;
      will-change: left, right;
      animation: 2.1s cubic-bezier(0.65, 0.815, 0.735, 0.395) infinite rz-progressbar-indeterminate-anim; }
    .rz-progressbar-indeterminate .rz-progressbar-value:after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      bottom: 0;
      background-color: #479cc8;
      will-change: left, right;
      animation: 2.1s cubic-bezier(0.165, 0.84, 0.44, 1) infinite rz-progressbar-indeterminate-anim-short;
      animation-delay: 1.15s; }

@keyframes rz-progressbar-indeterminate-anim {
  0% {
    left: -35%;
    right: 100%; }
  100%,
  60% {
    left: 100%;
    right: -90%; } }

@keyframes rz-progressbar-indeterminate-anim-short {
  0% {
    left: -200%;
    right: 100%; }
  100%,
  60% {
    left: 107%;
    right: -8%; } }

.chart,
.pie {
  height: 200px; }
  .chart .ngx-charts,
  .pie .ngx-charts {
    overflow: visible !important; }
    .chart .ngx-charts .gridline-path,
    .pie .ngx-charts .gridline-path {
      stroke: rgba(255, 255, 255, 0.2); }
  .chart .chart-legend .legend-labels,
  .pie .chart-legend .legend-labels {
    background-color: transparent; }
  .chart .legend-title,
  .pie .legend-title {
    display: none; }
  .chart .tick text,
  .pie .tick text {
    fill: rgba(255, 255, 255, 0.5);
    font-size: 0.875rem !important; }

.rz-chart {
  position: relative;
  height: 300px; }

.rz-area-series {
  fill-opacity: 0.8; }

.rz-scheme-palette .rz-series-0 {
  fill: #003f5c;
  stroke: #003f5c; }

.rz-scheme-palette .rz-series-0-tooltip .rz-chart-tooltip-content {
  border: 1px solid #003f5c; }

.rz-scheme-palette .rz-series-item-0 {
  fill: #003f5c;
  stroke: #003f5c; }
  .rz-scheme-palette .rz-series-item-0 .rz-chart-tooltip-content {
    border: 1px solid #003f5c; }

.rz-scheme-palette .rz-series-1 {
  fill: #2f4b7c;
  stroke: #2f4b7c; }

.rz-scheme-palette .rz-series-1-tooltip .rz-chart-tooltip-content {
  border: 1px solid #2f4b7c; }

.rz-scheme-palette .rz-series-item-1 {
  fill: #2f4b7c;
  stroke: #2f4b7c; }
  .rz-scheme-palette .rz-series-item-1 .rz-chart-tooltip-content {
    border: 1px solid #2f4b7c; }

.rz-scheme-palette .rz-series-2 {
  fill: #665191;
  stroke: #665191; }

.rz-scheme-palette .rz-series-2-tooltip .rz-chart-tooltip-content {
  border: 1px solid #665191; }

.rz-scheme-palette .rz-series-item-2 {
  fill: #665191;
  stroke: #665191; }
  .rz-scheme-palette .rz-series-item-2 .rz-chart-tooltip-content {
    border: 1px solid #665191; }

.rz-scheme-palette .rz-series-3 {
  fill: #a05195;
  stroke: #a05195; }

.rz-scheme-palette .rz-series-3-tooltip .rz-chart-tooltip-content {
  border: 1px solid #a05195; }

.rz-scheme-palette .rz-series-item-3 {
  fill: #a05195;
  stroke: #a05195; }
  .rz-scheme-palette .rz-series-item-3 .rz-chart-tooltip-content {
    border: 1px solid #a05195; }

.rz-scheme-palette .rz-series-4 {
  fill: #d45087;
  stroke: #d45087; }

.rz-scheme-palette .rz-series-4-tooltip .rz-chart-tooltip-content {
  border: 1px solid #d45087; }

.rz-scheme-palette .rz-series-item-4 {
  fill: #d45087;
  stroke: #d45087; }
  .rz-scheme-palette .rz-series-item-4 .rz-chart-tooltip-content {
    border: 1px solid #d45087; }

.rz-scheme-palette .rz-series-5 {
  fill: #f95d6a;
  stroke: #f95d6a; }

.rz-scheme-palette .rz-series-5-tooltip .rz-chart-tooltip-content {
  border: 1px solid #f95d6a; }

.rz-scheme-palette .rz-series-item-5 {
  fill: #f95d6a;
  stroke: #f95d6a; }
  .rz-scheme-palette .rz-series-item-5 .rz-chart-tooltip-content {
    border: 1px solid #f95d6a; }

.rz-scheme-palette .rz-series-6 {
  fill: #ff7c43;
  stroke: #ff7c43; }

.rz-scheme-palette .rz-series-6-tooltip .rz-chart-tooltip-content {
  border: 1px solid #ff7c43; }

.rz-scheme-palette .rz-series-item-6 {
  fill: #ff7c43;
  stroke: #ff7c43; }
  .rz-scheme-palette .rz-series-item-6 .rz-chart-tooltip-content {
    border: 1px solid #ff7c43; }

.rz-scheme-palette .rz-series-7 {
  fill: #ffa600;
  stroke: #ffa600; }

.rz-scheme-palette .rz-series-7-tooltip .rz-chart-tooltip-content {
  border: 1px solid #ffa600; }

.rz-scheme-palette .rz-series-item-7 {
  fill: #ffa600;
  stroke: #ffa600; }
  .rz-scheme-palette .rz-series-item-7 .rz-chart-tooltip-content {
    border: 1px solid #ffa600; }

.rz-scheme-pastel .rz-series-0 {
  fill: #0479cc;
  stroke: #0479cc; }

.rz-scheme-pastel .rz-series-0-tooltip .rz-chart-tooltip-content {
  border: 1px solid #0479cc; }

.rz-scheme-pastel .rz-series-item-0 {
  fill: #0479cc;
  stroke: #0479cc; }
  .rz-scheme-pastel .rz-series-item-0 .rz-chart-tooltip-content {
    border: 1px solid #0479cc; }

.rz-scheme-pastel .rz-series-1 {
  fill: #68d5c8;
  stroke: #68d5c8; }

.rz-scheme-pastel .rz-series-1-tooltip .rz-chart-tooltip-content {
  border: 1px solid #68d5c8; }

.rz-scheme-pastel .rz-series-item-1 {
  fill: #68d5c8;
  stroke: #68d5c8; }
  .rz-scheme-pastel .rz-series-item-1 .rz-chart-tooltip-content {
    border: 1px solid #68d5c8; }

.rz-scheme-pastel .rz-series-2 {
  fill: #ff6d41;
  stroke: #ff6d41; }

.rz-scheme-pastel .rz-series-2-tooltip .rz-chart-tooltip-content {
  border: 1px solid #ff6d41; }

.rz-scheme-pastel .rz-series-item-2 {
  fill: #ff6d41;
  stroke: #ff6d41; }
  .rz-scheme-pastel .rz-series-item-2 .rz-chart-tooltip-content {
    border: 1px solid #ff6d41; }

.rz-scheme-pastel .rz-series-3 {
  fill: #cb6992;
  stroke: #cb6992; }

.rz-scheme-pastel .rz-series-3-tooltip .rz-chart-tooltip-content {
  border: 1px solid #cb6992; }

.rz-scheme-pastel .rz-series-item-3 {
  fill: #cb6992;
  stroke: #cb6992; }
  .rz-scheme-pastel .rz-series-item-3 .rz-chart-tooltip-content {
    border: 1px solid #cb6992; }

.rz-scheme-pastel .rz-series-4 {
  fill: #e6c54f;
  stroke: #e6c54f; }

.rz-scheme-pastel .rz-series-4-tooltip .rz-chart-tooltip-content {
  border: 1px solid #e6c54f; }

.rz-scheme-pastel .rz-series-item-4 {
  fill: #e6c54f;
  stroke: #e6c54f; }
  .rz-scheme-pastel .rz-series-item-4 .rz-chart-tooltip-content {
    border: 1px solid #e6c54f; }

.rz-scheme-pastel .rz-series-5 {
  fill: #f9777f;
  stroke: #f9777f; }

.rz-scheme-pastel .rz-series-5-tooltip .rz-chart-tooltip-content {
  border: 1px solid #f9777f; }

.rz-scheme-pastel .rz-series-item-5 {
  fill: #f9777f;
  stroke: #f9777f; }
  .rz-scheme-pastel .rz-series-item-5 .rz-chart-tooltip-content {
    border: 1px solid #f9777f; }

.rz-scheme-pastel .rz-series-6 {
  fill: #5dbf74;
  stroke: #5dbf74; }

.rz-scheme-pastel .rz-series-6-tooltip .rz-chart-tooltip-content {
  border: 1px solid #5dbf74; }

.rz-scheme-pastel .rz-series-item-6 {
  fill: #5dbf74;
  stroke: #5dbf74; }
  .rz-scheme-pastel .rz-series-item-6 .rz-chart-tooltip-content {
    border: 1px solid #5dbf74; }

.rz-scheme-pastel .rz-series-7 {
  fill: #4db9f2;
  stroke: #4db9f2; }

.rz-scheme-pastel .rz-series-7-tooltip .rz-chart-tooltip-content {
  border: 1px solid #4db9f2; }

.rz-scheme-pastel .rz-series-item-7 {
  fill: #4db9f2;
  stroke: #4db9f2; }
  .rz-scheme-pastel .rz-series-item-7 .rz-chart-tooltip-content {
    border: 1px solid #4db9f2; }

.rz-scheme-monochrome .rz-series-0 {
  fill: #004c6d;
  stroke: #004c6d; }

.rz-scheme-monochrome .rz-series-0-tooltip .rz-chart-tooltip-content {
  border: 1px solid #004c6d; }

.rz-scheme-monochrome .rz-series-item-0 {
  fill: #004c6d;
  stroke: #004c6d; }
  .rz-scheme-monochrome .rz-series-item-0 .rz-chart-tooltip-content {
    border: 1px solid #004c6d; }

.rz-scheme-monochrome .rz-series-1 {
  fill: #296080;
  stroke: #296080; }

.rz-scheme-monochrome .rz-series-1-tooltip .rz-chart-tooltip-content {
  border: 1px solid #296080; }

.rz-scheme-monochrome .rz-series-item-1 {
  fill: #296080;
  stroke: #296080; }
  .rz-scheme-monochrome .rz-series-item-1 .rz-chart-tooltip-content {
    border: 1px solid #296080; }

.rz-scheme-monochrome .rz-series-2 {
  fill: #437594;
  stroke: #437594; }

.rz-scheme-monochrome .rz-series-2-tooltip .rz-chart-tooltip-content {
  border: 1px solid #437594; }

.rz-scheme-monochrome .rz-series-item-2 {
  fill: #437594;
  stroke: #437594; }
  .rz-scheme-monochrome .rz-series-item-2 .rz-chart-tooltip-content {
    border: 1px solid #437594; }

.rz-scheme-monochrome .rz-series-3 {
  fill: #5d8ba9;
  stroke: #5d8ba9; }

.rz-scheme-monochrome .rz-series-3-tooltip .rz-chart-tooltip-content {
  border: 1px solid #5d8ba9; }

.rz-scheme-monochrome .rz-series-item-3 {
  fill: #5d8ba9;
  stroke: #5d8ba9; }
  .rz-scheme-monochrome .rz-series-item-3 .rz-chart-tooltip-content {
    border: 1px solid #5d8ba9; }

.rz-scheme-monochrome .rz-series-4 {
  fill: #75a1be;
  stroke: #75a1be; }

.rz-scheme-monochrome .rz-series-4-tooltip .rz-chart-tooltip-content {
  border: 1px solid #75a1be; }

.rz-scheme-monochrome .rz-series-item-4 {
  fill: #75a1be;
  stroke: #75a1be; }
  .rz-scheme-monochrome .rz-series-item-4 .rz-chart-tooltip-content {
    border: 1px solid #75a1be; }

.rz-scheme-monochrome .rz-series-5 {
  fill: #8eb8d3;
  stroke: #8eb8d3; }

.rz-scheme-monochrome .rz-series-5-tooltip .rz-chart-tooltip-content {
  border: 1px solid #8eb8d3; }

.rz-scheme-monochrome .rz-series-item-5 {
  fill: #8eb8d3;
  stroke: #8eb8d3; }
  .rz-scheme-monochrome .rz-series-item-5 .rz-chart-tooltip-content {
    border: 1px solid #8eb8d3; }

.rz-scheme-monochrome .rz-series-6 {
  fill: #a7cfe9;
  stroke: #a7cfe9; }

.rz-scheme-monochrome .rz-series-6-tooltip .rz-chart-tooltip-content {
  border: 1px solid #a7cfe9; }

.rz-scheme-monochrome .rz-series-item-6 {
  fill: #a7cfe9;
  stroke: #a7cfe9; }
  .rz-scheme-monochrome .rz-series-item-6 .rz-chart-tooltip-content {
    border: 1px solid #a7cfe9; }

.rz-scheme-monochrome .rz-series-7 {
  fill: #c1e7ff;
  stroke: #c1e7ff; }

.rz-scheme-monochrome .rz-series-7-tooltip .rz-chart-tooltip-content {
  border: 1px solid #c1e7ff; }

.rz-scheme-monochrome .rz-series-item-7 {
  fill: #c1e7ff;
  stroke: #c1e7ff; }
  .rz-scheme-monochrome .rz-series-item-7 .rz-chart-tooltip-content {
    border: 1px solid #c1e7ff; }

.rz-scheme-divergent .rz-series-0 {
  fill: #00876c;
  stroke: #00876c; }

.rz-scheme-divergent .rz-series-0-tooltip .rz-chart-tooltip-content {
  border: 1px solid #00876c; }

.rz-scheme-divergent .rz-series-item-0 {
  fill: #00876c;
  stroke: #00876c; }
  .rz-scheme-divergent .rz-series-item-0 .rz-chart-tooltip-content {
    border: 1px solid #00876c; }

.rz-scheme-divergent .rz-series-1 {
  fill: #57a18b;
  stroke: #57a18b; }

.rz-scheme-divergent .rz-series-1-tooltip .rz-chart-tooltip-content {
  border: 1px solid #57a18b; }

.rz-scheme-divergent .rz-series-item-1 {
  fill: #57a18b;
  stroke: #57a18b; }
  .rz-scheme-divergent .rz-series-item-1 .rz-chart-tooltip-content {
    border: 1px solid #57a18b; }

.rz-scheme-divergent .rz-series-2 {
  fill: #8cbcac;
  stroke: #8cbcac; }

.rz-scheme-divergent .rz-series-2-tooltip .rz-chart-tooltip-content {
  border: 1px solid #8cbcac; }

.rz-scheme-divergent .rz-series-item-2 {
  fill: #8cbcac;
  stroke: #8cbcac; }
  .rz-scheme-divergent .rz-series-item-2 .rz-chart-tooltip-content {
    border: 1px solid #8cbcac; }

.rz-scheme-divergent .rz-series-3 {
  fill: #bed6ce;
  stroke: #bed6ce; }

.rz-scheme-divergent .rz-series-3-tooltip .rz-chart-tooltip-content {
  border: 1px solid #bed6ce; }

.rz-scheme-divergent .rz-series-item-3 {
  fill: #bed6ce;
  stroke: #bed6ce; }
  .rz-scheme-divergent .rz-series-item-3 .rz-chart-tooltip-content {
    border: 1px solid #bed6ce; }

.rz-scheme-divergent .rz-series-4 {
  fill: #f1f1f1;
  stroke: #f1f1f1; }

.rz-scheme-divergent .rz-series-4-tooltip .rz-chart-tooltip-content {
  border: 1px solid #f1f1f1; }

.rz-scheme-divergent .rz-series-item-4 {
  fill: #f1f1f1;
  stroke: #f1f1f1; }
  .rz-scheme-divergent .rz-series-item-4 .rz-chart-tooltip-content {
    border: 1px solid #f1f1f1; }

.rz-scheme-divergent .rz-series-5 {
  fill: #f1c6c6;
  stroke: #f1c6c6; }

.rz-scheme-divergent .rz-series-5-tooltip .rz-chart-tooltip-content {
  border: 1px solid #f1c6c6; }

.rz-scheme-divergent .rz-series-item-5 {
  fill: #f1c6c6;
  stroke: #f1c6c6; }
  .rz-scheme-divergent .rz-series-item-5 .rz-chart-tooltip-content {
    border: 1px solid #f1c6c6; }

.rz-scheme-divergent .rz-series-6 {
  fill: #ec9c9d;
  stroke: #ec9c9d; }

.rz-scheme-divergent .rz-series-6-tooltip .rz-chart-tooltip-content {
  border: 1px solid #ec9c9d; }

.rz-scheme-divergent .rz-series-item-6 {
  fill: #ec9c9d;
  stroke: #ec9c9d; }
  .rz-scheme-divergent .rz-series-item-6 .rz-chart-tooltip-content {
    border: 1px solid #ec9c9d; }

.rz-scheme-divergent .rz-series-7 {
  fill: #e27076;
  stroke: #e27076; }

.rz-scheme-divergent .rz-series-7-tooltip .rz-chart-tooltip-content {
  border: 1px solid #e27076; }

.rz-scheme-divergent .rz-series-item-7 {
  fill: #e27076;
  stroke: #e27076; }
  .rz-scheme-divergent .rz-series-item-7 .rz-chart-tooltip-content {
    border: 1px solid #e27076; }

.rz-scheme-divergent .rz-series-8 {
  fill: #d43d51;
  stroke: #d43d51; }

.rz-scheme-divergent .rz-series-8-tooltip .rz-chart-tooltip-content {
  border: 1px solid #d43d51; }

.rz-scheme-divergent .rz-series-item-8 {
  fill: #d43d51;
  stroke: #d43d51; }
  .rz-scheme-divergent .rz-series-item-8 .rz-chart-tooltip-content {
    border: 1px solid #d43d51; }

.rz-marker {
  stroke: #fff; }

.rz-area-series .rz-marker {
  fill-opacity: 1; }

.rz-axis {
  stroke: #a0a0a0;
  font-size: 0.875em; }

.rz-axis .rz-grid-line {
  stroke: rgba(255, 255, 255, 0.2); }

.rz-tick-text {
  stroke: none;
  fill: rgba(255, 255, 255, 0.5); }

.rz-value-axis .rz-tick-text {
  text-anchor: end; }

.rz-category-axis .rz-tick-text {
  text-anchor: middle; }

.rz-axis .rz-axis-title {
  stroke: none;
  text-anchor: middle; }

.rz-donut-title {
  text-anchor: middle; }

.rz-donut-content {
  height: 100%;
  text-align: center;
  display: flex;
  flex-direction: column;
  justify-content: center; }

.rz-legend {
  position: absolute;
  display: flex; }

.rz-legend-right {
  right: 0;
  top: 0;
  bottom: 0;
  align-items: center; }

.rz-legend-left {
  left: 0;
  top: 0;
  bottom: 0;
  align-items: center; }

.rz-legend-top {
  top: 0;
  left: 0;
  right: 0;
  justify-content: center; }

.rz-legend-bottom {
  bottom: 0;
  left: 0;
  right: 0;
  justify-content: center; }

.rz-legend-items {
  padding: 0;
  margin: 0;
  overflow-y: auto;
  overflow-x: hidden;
  max-height: 100%; }

.rz-legend-item {
  align-items: center;
  display: flex; }

.rz-legend-top .rz-legend-item,
.rz-legend-bottom .rz-legend-item {
  display: inline-flex; }

.rz-legend-item-text {
  padding: 4px; }

.rz-chart-tooltip {
  position: absolute;
  transform: translate(-50%, -100%);
  transition: top 0.2s, left 0.2s;
  top: 0;
  left: 0; }

.rz-chart-tooltip-content {
  background: rgba(93, 113, 123, 0.95);
  color: #ffffff;
  box-shadow: 0 6px 14px 0 rgba(0, 0, 0, 0.15);
  padding: 0.25rem 1rem;
  border-radius: 4px;
  white-space: nowrap; }

.rz-chart-tooltip:not(.rz-pie-tooltip) .rz-chart-tooltip-content {
  margin-bottom: 15px; }
  .rz-chart-tooltip:not(.rz-pie-tooltip) .rz-chart-tooltip-content:after {
    content: ' ';
    position: absolute;
    width: 8px;
    height: 8px;
    left: 50%;
    bottom: 0;
    background-color: inherit;
    transform-origin: center;
    transform: translate(-50%, -11px) rotate(45deg);
    border-bottom: inherit;
    border-right: inherit; }

.rz-link .rz-link-text {
  vertical-align: middle; }

.rz-link .rzi, .rz-link .rz-menuitem .rz-menuitem-icon, .rz-menuitem .rz-link .rz-menuitem-icon, .rz-link .rz-fileupload-row .rz-button .rzi-close, .rz-fileupload-row .rz-button .rz-link .rzi-close, .rz-link .rz-fileupload-row .rz-paginator-element .rzi-close, .rz-fileupload-row .rz-paginator-element .rz-link .rzi-close,
.rz-link .rz-fileupload-row .rz-button .rzi-times, .rz-fileupload-row .rz-button .rz-link .rzi-times,
.rz-link .rz-fileupload-row .rz-paginator-element .rzi-times, .rz-fileupload-row .rz-paginator-element .rz-link .rzi-times,
.rz-link .rz-fileupload-row .rz-button .rz-icon-trash, .rz-fileupload-row .rz-button .rz-link .rz-icon-trash,
.rz-link .rz-fileupload-row .rz-paginator-element .rz-icon-trash, .rz-fileupload-row .rz-paginator-element .rz-link .rz-icon-trash, .rz-link .rz-datatable .rzi-chevron-circle-right, .rz-datatable .rz-link .rzi-chevron-circle-right, .rz-link .rz-datatable .rzi-chevron-circle-down, .rz-datatable .rz-link .rzi-chevron-circle-down, .rz-link .rz-sortable-column .rzi-grid-sort, .rz-sortable-column .rz-link .rzi-grid-sort, .rz-link .rz-datatable-header .rzi-plus, .rz-datatable-header .rz-link .rzi-plus, .rz-link .rz-datatable-loading-content .rzi-circle-o-notch, .rz-datatable-loading-content .rz-link .rzi-circle-o-notch, .rz-link .rz-column-drag {
  font-size: inherit;
  vertical-align: middle; }

.rz-state-highlight .link {
  color: #ffffff; }

.rz-html-editor {
  display: flex;
  flex-direction: column;
  border-radius: 4px;
  border: 1px solid #2f3a40; }

.rz-html-editor-content {
  flex: 1;
  overflow: auto;
  padding: 0.5rem;
  outline: none;
  background-color: #323f45; }

.rz-html-editor-toolbar {
  border-bottom: 1px solid #2f3a40;
  display: flex;
  line-height: 1rem;
  flex-wrap: wrap;
  background-color: #5d717b; }
  .rz-html-editor-toolbar .rzi, .rz-html-editor-toolbar .rz-menuitem .rz-menuitem-icon, .rz-menuitem .rz-html-editor-toolbar .rz-menuitem-icon, .rz-html-editor-toolbar .rz-fileupload-row .rz-button .rzi-close, .rz-fileupload-row .rz-button .rz-html-editor-toolbar .rzi-close, .rz-html-editor-toolbar .rz-fileupload-row .rz-paginator-element .rzi-close, .rz-fileupload-row .rz-paginator-element .rz-html-editor-toolbar .rzi-close,
  .rz-html-editor-toolbar .rz-fileupload-row .rz-button .rzi-times, .rz-fileupload-row .rz-button .rz-html-editor-toolbar .rzi-times,
  .rz-html-editor-toolbar .rz-fileupload-row .rz-paginator-element .rzi-times, .rz-fileupload-row .rz-paginator-element .rz-html-editor-toolbar .rzi-times,
  .rz-html-editor-toolbar .rz-fileupload-row .rz-button .rz-icon-trash, .rz-fileupload-row .rz-button .rz-html-editor-toolbar .rz-icon-trash,
  .rz-html-editor-toolbar .rz-fileupload-row .rz-paginator-element .rz-icon-trash, .rz-fileupload-row .rz-paginator-element .rz-html-editor-toolbar .rz-icon-trash, .rz-html-editor-toolbar .rz-datatable .rzi-chevron-circle-right, .rz-datatable .rz-html-editor-toolbar .rzi-chevron-circle-right, .rz-html-editor-toolbar .rz-datatable .rzi-chevron-circle-down, .rz-datatable .rz-html-editor-toolbar .rzi-chevron-circle-down, .rz-html-editor-toolbar .rz-sortable-column .rzi-grid-sort, .rz-sortable-column .rz-html-editor-toolbar .rzi-grid-sort, .rz-html-editor-toolbar .rz-datatable-header .rzi-plus, .rz-datatable-header .rz-html-editor-toolbar .rzi-plus, .rz-html-editor-toolbar .rz-datatable-loading-content .rzi-circle-o-notch, .rz-datatable-loading-content .rz-html-editor-toolbar .rzi-circle-o-notch, .rz-html-editor-toolbar .rz-column-drag {
    font-size: 1rem; }
  .rz-html-editor-toolbar > * {
    margin: 0.5rem; }

.rz-html-editor-colorpicker {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background: #5d717b;
  padding: 0.625rem; }
  .rz-html-editor-colorpicker .rz-colorpicker-trigger {
    color: inherit;
    background-color: #5d717b; }
  .rz-html-editor-colorpicker .rz-colorpicker-value {
    display: none; }
  .rz-html-editor-colorpicker .rz-colorpicker {
    border: none;
    box-shadow: none;
    padding: 0;
    height: auto; }
    .rz-html-editor-colorpicker .rz-colorpicker:hover {
      border: none;
      box-shadow: none; }

.rz-html-editor-color {
  border: none;
  display: flex;
  flex-direction: column;
  background: inherit;
  color: inherit;
  appearance: none;
  padding: 0;
  position: relative; }
  .rz-html-editor-color:disabled {
    color: #868e96; }

.rz-html-editor-color-value {
  position: absolute;
  bottom: -4px;
  height: 4px;
  width: 100%; }

.rz-html-editor-button {
  color: #ffffff;
  appearance: none;
  border: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background: #5d717b;
  padding: 0.625rem; }
  .rz-html-editor-button.rz-selected {
    background-color: #479cc8;
    color: #ffffff;
    border-radius: 4px; }
  .rz-html-editor-button:disabled {
    color: #868e96; }

.rz-html-editor-dropdown {
  display: inline-flex;
  padding: 0.625rem;
  align-items: center;
  cursor: pointer; }
  .rz-html-editor-dropdown.rz-disabled {
    color: #868e96;
    cursor: default; }

.rz-html-editor-dropdown-item {
  cursor: default;
  font-size: 1rem;
  padding: 0.25rem 0.625rem;
  white-space: nowrap; }
  .rz-html-editor-dropdown-item:hover {
    background-color: #59a6cd;
    color: #ffffff;
    border-radius: 0; }
  .rz-html-editor-dropdown-item.rz-selected {
    background-color: #479cc8;
    color: #ffffff; }

.rz-html-editor-dropdown-trigger {
  border: none;
  appearance: none;
  padding: 0;
  display: inline-flex;
  align-items: center;
  color: inherit;
  background-color: inherit; }
  .rz-html-editor-dropdown-trigger .rzi:before, .rz-html-editor-dropdown-trigger .rz-menuitem .rz-menuitem-icon:before, .rz-menuitem .rz-html-editor-dropdown-trigger .rz-menuitem-icon:before, .rz-html-editor-dropdown-trigger .rz-fileupload-row .rz-button .rzi-close:before, .rz-fileupload-row .rz-button .rz-html-editor-dropdown-trigger .rzi-close:before, .rz-html-editor-dropdown-trigger .rz-fileupload-row .rz-paginator-element .rzi-close:before, .rz-fileupload-row .rz-paginator-element .rz-html-editor-dropdown-trigger .rzi-close:before,
  .rz-html-editor-dropdown-trigger .rz-fileupload-row .rz-button .rzi-times:before, .rz-fileupload-row .rz-button .rz-html-editor-dropdown-trigger .rzi-times:before,
  .rz-html-editor-dropdown-trigger .rz-fileupload-row .rz-paginator-element .rzi-times:before, .rz-fileupload-row .rz-paginator-element .rz-html-editor-dropdown-trigger .rzi-times:before,
  .rz-html-editor-dropdown-trigger .rz-fileupload-row .rz-button .rz-icon-trash:before, .rz-fileupload-row .rz-button .rz-html-editor-dropdown-trigger .rz-icon-trash:before,
  .rz-html-editor-dropdown-trigger .rz-fileupload-row .rz-paginator-element .rz-icon-trash:before, .rz-fileupload-row .rz-paginator-element .rz-html-editor-dropdown-trigger .rz-icon-trash:before, .rz-html-editor-dropdown-trigger .rz-datatable .rzi-chevron-circle-right:before, .rz-datatable .rz-html-editor-dropdown-trigger .rzi-chevron-circle-right:before, .rz-html-editor-dropdown-trigger .rz-datatable .rzi-chevron-circle-down:before, .rz-datatable .rz-html-editor-dropdown-trigger .rzi-chevron-circle-down:before, .rz-html-editor-dropdown-trigger .rz-sortable-column .rzi-grid-sort:before, .rz-sortable-column .rz-html-editor-dropdown-trigger .rzi-grid-sort:before, .rz-html-editor-dropdown-trigger .rz-datatable-header .rzi-plus:before, .rz-datatable-header .rz-html-editor-dropdown-trigger .rzi-plus:before, .rz-html-editor-dropdown-trigger .rz-datatable-loading-content .rzi-circle-o-notch:before, .rz-datatable-loading-content .rz-html-editor-dropdown-trigger .rzi-circle-o-notch:before, .rz-html-editor-dropdown-trigger .rz-column-drag:before {
    content: 'arrow_drop_down'; }

.rz-html-editor-dropdown-items {
  display: none; }

.rz-html-editor-dialog-item {
  margin-bottom: 1rem; }
  .rz-html-editor-dialog-item label:first-child {
    display: block; }

.rz-html-editor-dialog-buttons {
  text-align: right; }

.rz-html-editor-separator {
  width: 1px;
  background-color: #ffffff; }

.rz-colorpicker {
  display: inline-flex;
  align-items: center;
  cursor: pointer; }
  .rz-colorpicker.rz-disabled {
    color: #868e96;
    cursor: default; }

.rz-colorpicker-trigger {
  border: none;
  appearance: none;
  padding: 0;
  display: inline-flex;
  align-items: center;
  color: #ffffff;
  background-color: inherit; }
  .rz-colorpicker-trigger .rzi:before, .rz-colorpicker-trigger .rz-menuitem .rz-menuitem-icon:before, .rz-menuitem .rz-colorpicker-trigger .rz-menuitem-icon:before, .rz-colorpicker-trigger .rz-fileupload-row .rz-button .rzi-close:before, .rz-fileupload-row .rz-button .rz-colorpicker-trigger .rzi-close:before, .rz-colorpicker-trigger .rz-fileupload-row .rz-paginator-element .rzi-close:before, .rz-fileupload-row .rz-paginator-element .rz-colorpicker-trigger .rzi-close:before,
  .rz-colorpicker-trigger .rz-fileupload-row .rz-button .rzi-times:before, .rz-fileupload-row .rz-button .rz-colorpicker-trigger .rzi-times:before,
  .rz-colorpicker-trigger .rz-fileupload-row .rz-paginator-element .rzi-times:before, .rz-fileupload-row .rz-paginator-element .rz-colorpicker-trigger .rzi-times:before,
  .rz-colorpicker-trigger .rz-fileupload-row .rz-button .rz-icon-trash:before, .rz-fileupload-row .rz-button .rz-colorpicker-trigger .rz-icon-trash:before,
  .rz-colorpicker-trigger .rz-fileupload-row .rz-paginator-element .rz-icon-trash:before, .rz-fileupload-row .rz-paginator-element .rz-colorpicker-trigger .rz-icon-trash:before, .rz-colorpicker-trigger .rz-datatable .rzi-chevron-circle-right:before, .rz-datatable .rz-colorpicker-trigger .rzi-chevron-circle-right:before, .rz-colorpicker-trigger .rz-datatable .rzi-chevron-circle-down:before, .rz-datatable .rz-colorpicker-trigger .rzi-chevron-circle-down:before, .rz-colorpicker-trigger .rz-sortable-column .rzi-grid-sort:before, .rz-sortable-column .rz-colorpicker-trigger .rzi-grid-sort:before, .rz-colorpicker-trigger .rz-datatable-header .rzi-plus:before, .rz-datatable-header .rz-colorpicker-trigger .rzi-plus:before, .rz-colorpicker-trigger .rz-datatable-loading-content .rzi-circle-o-notch:before, .rz-datatable-loading-content .rz-colorpicker-trigger .rzi-circle-o-notch:before, .rz-colorpicker-trigger .rz-column-drag:before {
    content: 'arrow_drop_down'; }

.rz-colorpicker-popup {
  display: none;
  position: absolute;
  border: 1px solid #45565d;
  background-color: #5d717b;
  box-shadow: 0 6px 14px 0 rgba(0, 0, 0, 0.06);
  min-width: 200px;
  max-width: 280px;
  padding: 0.625rem;
  border-radius: 4px; }

.rz-colorpicker-value {
  flex: 1;
  border-radius: 4px;
  border: 1px solid #2f3a40;
  min-width: 20px;
  min-height: 20px; }

.rz-saturation-picker {
  height: 150px;
  position: relative;
  touch-action: none; }

.rz-saturation-white {
  background: linear-gradient(to right, #fff, rgba(255, 255, 255, 0)); }

.rz-saturation-black,
.rz-saturation-white {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0; }

.rz-saturation-black {
  background: linear-gradient(to top, #000, rgba(0, 0, 0, 0)); }

.rz-saturation-handle {
  position: absolute;
  width: 8px;
  height: 8px;
  border: 1px solid #fff;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  box-shadow: white 0px 0px 0px 1.5px, rgba(0, 0, 0, 0.3) 0px 0px 1px 1px inset, rgba(0, 0, 0, 0.4) 0px 0px 1px 2px; }

.rz-hue-picker {
  margin-bottom: 4px;
  touch-action: none;
  position: relative;
  background-image: linear-gradient(to right, red 0%, yellow 17%, lime 33%, cyan 50%, blue 67%, magenta 83%, red 100%);
  height: 12px; }

.rz-alpha-picker {
  touch-action: none;
  position: relative;
  height: 16px; }

.rz-hue-handle,
.rz-alpha-handle {
  position: absolute;
  height: 100%;
  width: 6px;
  border: 1px solid #fff;
  transform: translateX(-50%);
  box-shadow: white 0px 0px 0px 1.5px, rgba(0, 0, 0, 0.3) 0px 0px 1px 1px inset, rgba(0, 0, 0, 0.4) 0px 0px 1px 2px; }

.rz-colorpicker-preview-area {
  display: flex; }

.rz-hue-and-alpha {
  flex: 1;
  padding-right: 10px; }

.rz-alpha-picker:before,
.rz-colorpicker-preview:before {
  position: absolute;
  z-index: -1;
  content: '';
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  background-image: linear-gradient(45deg, #808080 25%, transparent 25%), linear-gradient(-45deg, #808080 25%, transparent 25%), linear-gradient(45deg, transparent 75%, #808080 75%), linear-gradient(-45deg, transparent 75%, #808080 75%);
  background-size: 16px 16px;
  background-position: 0 0, 0 8px, 8px -8px, -8px 0px; }

.rz-colorpicker-preview {
  position: relative;
  width: 32px;
  height: 32px;
  box-shadow: rgba(0, 0, 0, 0.15) 0px 0px 0px 1px inset, rgba(0, 0, 0, 0.25) 0px 0px 4px inset; }

.rz-colorpicker-rgba {
  display: flex; }

.rz-color-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1; }
  .rz-color-box:not(:last-child) {
    margin-right: 4px; }
  .rz-color-box .rz-textbox {
    width: 100px;
    padding: 0 3px;
    height: 30px; }
  .rz-color-box .rz-spinner {
    padding: 1px 3px;
    height: 30px; }
    .rz-color-box .rz-spinner .rz-spinner-input {
      padding: 0; }
    .rz-color-box .rz-spinner button {
      display: none; }

.rz-colorpicker-button {
  justify-content: flex-end;
  display: flex; }

.rz-colorpicker-section:not(:last-child) {
  margin-bottom: 10px; }

.rz-colorpicker-colors {
  display: flex;
  flex-wrap: wrap;
  margin-left: -4px;
  margin-right: -4px; }

.rz-colorpicker-item {
  width: 16px;
  height: 16px;
  margin: 4px;
  cursor: pointer;
  box-shadow: rgba(0, 0, 0, 0.15) 0px 0px 0px 1px inset, rgba(0, 0, 0, 0.25) 0px 0px 4px inset; }
