@echo off
setlocal

:: IP-Adresse ermitteln
for /f "tokens=2 delims=[]" %%i in ('ping -4 -n 1 %computername% ^| findstr "["') do set IP=%%i

:: URL öffnen
set URL=https://%IP%:7000

:: Absoluten Pfad zum Icon ermitteln
set ICON_PATH=%~dp0..\wwwroot\Logo.ico

:: Desktop-Pfad mit PowerShell ermitteln
set DESKTOP_PATH=
for /f "usebackq tokens=*" %%i in (`powershell -NoProfile -Command "(New-Object -ComObject WScript.Shell).SpecialFolders.Item('Desktop')"`) do set DESKTOP_PATH=%%i

:: .url-Datei erstellen
(
    echo [InternetShortcut]
    echo URL=%URL%
    echo IconFile=%ICON_PATH%
    echo IconIndex=0
) > "%DESKTOP_PATH%\WingWebApp.url"

start "" "%URL%"

endlocal