<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
        <DefineConstants>BlazorServer</DefineConstants>
        <PublishTrimmed>false</PublishTrimmed>
        <JsonSerializerIsReflectionEnabledByDefault>true</JsonSerializerIsReflectionEnabledByDefault>
        <FileVersion>1.0.0.0</FileVersion>
        <NeutralLanguage>de</NeutralLanguage>
        <IsPackable>true</IsPackable>
        <Version>1.0.0.0</Version>
    </PropertyGroup>
    
    <PropertyGroup Condition=" '$(Configuration)' == 'Debug' AND $([MSBuild]::IsOSPlatform('Windows')) ">
        <PlatformTarget>x64</PlatformTarget>
        <NoWarn>1701;1702;CA1416</NoWarn>
    </PropertyGroup>
    <PropertyGroup Condition=" '$(Configuration)' == 'Release' AND $([MSBuild]::IsOSPlatform('Windows')) ">
        <PlatformTarget>x64</PlatformTarget>
    </PropertyGroup>
    <PropertyGroup Condition=" '$(Configuration)' == 'Release' AND $([MSBuild]::IsOSPlatform('IOS')) ">
        <PlatformTarget>ARM</PlatformTarget>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.Extensions.Hosting.WindowsServices" Version="9.0.8" />
        <PackageReference Include="Microsoft.FluentUI.AspNetCore.Components" Version="4.12.1" />
        <PackageReference Include="Microsoft.FluentUI.AspNetCore.Components.Icons" Version="4.12.1" />
        <PackageReference Include="Costura.Fody" Version="6.0.0">
            <PrivateAssets>all</PrivateAssets>
        </PackageReference>
    </ItemGroup>
    
    <ItemGroup>
      <ProjectReference Include="..\..\..\libs\ServiceDefaults\ServiceDefaults.csproj" />
      <ProjectReference Include="..\..\..\Modules\QuartzScheduler\QuartzScheduler.csproj" />
      <ProjectReference Include="..\..\..\Modules\WingPrinterListLabel\wingPrinterListLabel.ioc\wingPrinterListLabel.ioc.csproj" />
      <ProjectReference Include="..\WinGng.RazorLib\WinGng.RazorLib.csproj">
        <DefineConstants>BlazorServer</DefineConstants>
      </ProjectReference>
    </ItemGroup>

    <ItemGroup>
      <None Update="Cert\cert.key">
        <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      </None>
      <None Update="docker-compose.yml">
        <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      </None>
      <None Update="Tools\InstallService.bat">
        <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      </None>
      <None Update="Tools\StartService.bat">
        <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      </None>
      <None Update="Tools\StopService.bat">
        <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      </None>
      <None Update="Tools\UninstallService.bat">
        <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      </None>
    </ItemGroup>

    <ItemGroup>
      <Resource Include="wwwroot\css\controlling.css">
        <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      </Resource>
      <AdditionalFiles Include="Cert\vas.software.pfx">
        <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      </AdditionalFiles>
    </ItemGroup>
    
</Project>
