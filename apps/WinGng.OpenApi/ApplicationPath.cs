using WingCore.application.Contract.IModels;

namespace WinGng.OpenApi;

public class ApplicationPath(IConfiguration configuration) : IApplicationPath
{
    public string GetPath() => AppContext.BaseDirectory;
    public string GetStammDbPath()
    {
        return configuration["stammDbConfigFilePath"] ?? Path.Combine(GetPath(), "DBConfig");
    }
    
    public bool IAmOpenApiServer() => true;
    public bool IAmBlazorServer() => false;
    public bool IAmMauiAppBlazor() => false;
}