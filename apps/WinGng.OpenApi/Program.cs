using System.Text;
using System.Text.Json.Serialization;
using Asp.Versioning;
using FluentValidation;
using Languages;
using Microsoft.AspNetCore.HttpOverrides;
using Microsoft.AspNetCore.Mvc;
using Microsoft.OpenApi.Models;
using Serilog;
using Serilog.Events;
using ServiceDefaults;
using WingCore.application.Contract;
using WingCore.application.Contract.IModels;
using WingCore.ioc.Endpoints;
using WingCore.ioc.Middlewares;
using wingLager.ioc;
using WinGng.OpenApi;
using LoggerManager = ServiceDefaults.LoggerManager;

var builder = WebApplication.CreateBuilder(args);

SwaggerConfiguration.AddSwaggerConfiguration(builder.Services);

builder.Services.AddSingleton<IApplicationPath, ApplicationPath>();
builder.Services.AddSingleton<ILoggerManager, LoggerManager>();
builder.Services.ConfigureSqlContext();
builder.Services.ConfigureServiceManager();
builder.Services.AddWingLagerServices();
builder.Services.AddSerilog(new LoggerConfiguration()
    .MinimumLevel.Verbose()
    .MinimumLevel.Override("Microsoft", LogEventLevel.Information)
    .Enrich.FromLogContext()
    .WriteTo.File(
        Path.Combine(Path.Combine(AppContext.BaseDirectory, "Logs"), "WinGOpenApi_.log"),
        encoding: Encoding.UTF8,
        flushToDiskInterval: TimeSpan.FromSeconds(1),
        fileSizeLimitBytes: 1024 * 1024 * 100,
        rollingInterval: RollingInterval.Day,
        rollOnFileSizeLimit: true,
        retainedFileCountLimit: 10)
    .CreateLogger());
builder.Services.AddLocalizationServices();
builder.Services.AddValidatorsFromAssembly(typeof(WingCore.ioc.ServiceManager).Assembly);
builder.Host
    .ConfigureLogging(logging =>
    {
        logging.ClearProviders();
        logging.AddConsole();
    })
    .UseWindowsService();
var app = builder.Build();

// Configure the HTTP request pipeline

if (app.Environment.IsProduction())
{
    app.UseHsts();
}
app.UseForwardedHeaders(new ForwardedHeadersOptions
{
    ForwardedHeaders = ForwardedHeaders.All
});
var versionSet = app.NewApiVersionSet()
    .HasApiVersion(new ApiVersion(1))
    //.HasApiVersion(new ApiVersion(2))
    .ReportApiVersions()
    .Build();

var versionGroup = app.MapGroup("/v{version:apiVersion}")
    .WithApiVersionSet(versionSet);

versionGroup.MapMasterDataEndpoints();
versionGroup.MapOutgoingInvoicesEndpoints();
versionGroup.MapIncomingGoodsEndpoints();
versionGroup.MapOrderDataEndpoints();

SwaggerConfiguration.UseSwaggerConfiguration(app);

app.UseMiddleware<ExceptionHandlingMiddleware>();
app.UseMiddleware<ApiAuthMiddleware>();
app.ConfigureLocalization(builder.Configuration);
app.Run();