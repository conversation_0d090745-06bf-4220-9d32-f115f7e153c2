<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Asp.Versioning.Http" Version="8.1.0" />
        <PackageReference Include="Asp.Versioning.Mvc.ApiExplorer" Version="8.1.0" />
        <PackageReference Include="Costura.Fody" Version="6.0.0">
          <PrivateAssets>all</PrivateAssets>
        </PackageReference>
        <PackageReference Include="FluentValidation" Version="12.0.0" />
        <PackageReference Include="FluentValidation.DependencyInjectionExtensions" Version="12.0.0" />
        <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.8" />
        <PackageReference Include="Microsoft.Extensions.Hosting.WindowsServices" Version="9.0.8" />
        <PackageReference Include="Microsoft.Extensions.Localization" Version="9.0.8" />
        <PackageReference Include="Swashbuckle.AspNetCore" Version="9.0.3" />
        <PackageReference Include="Microsoft.Extensions.Hosting" Version="9.0.8" />
    </ItemGroup>
    <ItemGroup>
      <ProjectReference Include="..\..\libs\ServiceDefaults\ServiceDefaults.csproj" />
      <ProjectReference Include="..\..\Modules\WingLager\wingLager.ioc\wingLager.ioc.csproj" />
    </ItemGroup>
    <ItemGroup>
      <None Update="Tools\CreateAndStartBrowserWithLink.bat">
        <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      </None>
      <None Update="Tools\InstallService.bat">
        <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      </None>
      <None Update="Tools\StartService.bat">
        <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      </None>
      <None Update="Tools\StopService.bat">
        <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      </None>
      <None Update="Tools\UninstallService.bat">
        <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      </None>
      <None Update="Cert\cert.key">
        <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      </None>
    </ItemGroup>
    <ItemGroup>
      <AdditionalFiles Include="Cert\vas.software.pfx">
        <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      </AdditionalFiles>
    </ItemGroup>

</Project>
