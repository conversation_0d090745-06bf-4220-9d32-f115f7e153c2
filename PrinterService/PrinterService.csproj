<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>
    
    <ItemGroup Condition="$([MSBuild]::IsOSPlatform('Windows'))">
        <PackageReference Include="Fluid.Core" Version="2.24.0" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" Version="9.0.6" />
        <PackageReference Include="combit.ListLabel30" Version="30.2.0" />
        <PackageReference Include="combit.ListLabel30.Web" Version="30.2.0" />
    </ItemGroup>

    <ItemGroup Condition="$([MSBuild]::IsOSPlatform('Windows'))">
        <ProjectReference Include="..\libs\ObjectDeAndSerialize\ObjectDeAndSerialize.csproj" />
    </ItemGroup>

    <!-- Für Nicht-Windows: Alle C#-<PERSON><PERSON> entfernen -->
    <ItemGroup Condition="!$([MSBuild]::IsOSPlatform('Windows'))">
        <Compile Remove="**/*.cs" />
    </ItemGroup>

</Project>
