using combit.Reporting;
using Fluid;
using PrinterService.Models;

namespace PrinterService.Helper;

public abstract class Send<PERSON><PERSON>Helper
{
    public static void SendEmailWithPdf(EmailDataToCreateEmail emailDataToCreateEmail, IExternalListLabelRepository lExternalListLabelRepository)
    {
        SetEmailDataForSendWithPdf(ref emailDataToCreateEmail, "" ,"", lExternalListLabelRepository);
        SendEmailWithSetData(emailDataToCreateEmail);
     }
    
    public static void SendEmailWithPdfEmbeddedXml(EmailDataToCreateEmail emailDataToCreateEmail,
                                                   string xmlContent,
                                                   string xmlFileName,
                                                   IExternalListLabelRepository lExternalListLabelRepository)
    {
        SetEmailDataForSendWithPdf(ref emailDataToCreateEmail, xmlContent ,xmlFileName, lExternalListLabelRepository);
        SendEmailWithSetData(emailDataToCreateEmail);
    }
    
    public static void SendEmailWithSetData(EmailDataToCreateEmail emailDataToCreateEmail)
    {
        using var mailJob = CreateMailJobAndAddFields(ref emailDataToCreateEmail);
        try
        {
            emailDataToCreateEmail.AttachmentFilePathList.ForEach(a => mailJob.AttachmentList.Add(a));
        
            mailJob.Send();
        }
        catch (Exception e)
        {
            Console.WriteLine(e);
            throw new Exception(mailJob.ErrorText);
        }
        
    }
    
    public static void SetEmailDataForSendWithPdf(ref EmailDataToCreateEmail emailDataToCreateEmail, string xmlContent, string xmlFileName,IExternalListLabelRepository lExternalListLabelRepository)
    {
        ParseFields(ref emailDataToCreateEmail);
        
        if (emailDataToCreateEmail.PrintableObject is null)
            throw new Exception("Printobject is null");
        if (emailDataToCreateEmail.EmailParameter is null)
            throw new Exception("EmailParameter is null");

        string tempFileWithFolder;

        var parsedFileName = emailDataToCreateEmail.ParsedFileName;
        if (!string.IsNullOrWhiteSpace(xmlContent) || !string.IsNullOrWhiteSpace(xmlFileName))
        {
            tempFileWithFolder = ExportAsPdf.ExportPdfToFile(emailDataToCreateEmail.PrintableObject,
                                                            emailDataToCreateEmail.LayoutFileWithPath,
                                                            emailDataToCreateEmail.AppFolder,
                                                            ref parsedFileName,
                                                            xmlContent,
                                                            xmlFileName,
                                                            lExternalListLabelRepository);
        }
        else
        {
            tempFileWithFolder = ExportAsPdf.ExportPdfToFile(emailDataToCreateEmail.PrintableObject,
                                                            emailDataToCreateEmail.LayoutFileWithPath,
                                                            emailDataToCreateEmail.AppFolder,
                                                            ref parsedFileName,
                                                            lExternalListLabelRepository);
        }
        
        emailDataToCreateEmail.ParsedFileName = parsedFileName ?? emailDataToCreateEmail.ParsedFileName ;
        emailDataToCreateEmail.AttachmentFilePathList.Add(tempFileWithFolder);
    }

    private static MailJob CreateMailJobAndAddFields(ref EmailDataToCreateEmail emailDataToCreateEmail)
    {
        var mailJob = new MailJob();
        mailJob.To = emailDataToCreateEmail.EmailParameter.To;
        mailJob.Subject = emailDataToCreateEmail.ParsedSubject;
        mailJob.BodyHtml = emailDataToCreateEmail.ParsedHtmlBody;
        mailJob.Provider = "SMTP";
        mailJob.ShowDialog = emailDataToCreateEmail.EmailParameter.ShowDialog;
        mailJob.BCC = emailDataToCreateEmail.EmailParameter.SmtpSenderAddress;

        mailJob.AdditionalOptions.Add("SMTP.ServerAddress", emailDataToCreateEmail.EmailParameter.SmtpServerAddress.Trim());
        mailJob.AdditionalOptions.Add("SMTP.ServerPort", emailDataToCreateEmail.EmailParameter.SmtpServerPort.ToString().Trim());
        mailJob.AdditionalOptions.Add("SMTP.ServerUser", emailDataToCreateEmail.EmailParameter.SmtpServerUser);
        mailJob.AdditionalOptions.Add("SMTP.ServerPassword", emailDataToCreateEmail.EmailParameter.SmtpServerPassword);
        mailJob.AdditionalOptions.Add("SMTP.SocketTimeout", emailDataToCreateEmail.EmailParameter.SmtpSocketTimeout.ToString());
        mailJob.AdditionalOptions.Add("SMTP.SecureConnection", emailDataToCreateEmail.EmailParameter.SmtpSecureConnection ? "2" : "0");
        mailJob.AdditionalOptions.Add("SMTP.SenderAddress", emailDataToCreateEmail.EmailParameter.SmtpSenderAddress);
        mailJob.AdditionalOptions.Add("SMTP.SenderName", emailDataToCreateEmail.EmailParameter.SmtpSenderName);
        mailJob.AdditionalOptions.Add("SignResult", "1");
        mailJob.AdditionalOptions.Add("SMTP.LogonName", emailDataToCreateEmail.EmailParameter.SmtpSenderAddress);
        mailJob.AdditionalOptions.Add("ShowDialog", emailDataToCreateEmail.EmailParameter.ShowDialog ? "1" : "0");
        mailJob.AdditionalOptions.Add("ForceSendMailInSeparateThread", emailDataToCreateEmail.EmailParameter.ForceSendMailInSeparateThread ? "1" : "0");
        //mailJob.AdditionalOptions.Add("SendResultAs","text/html");

        return mailJob;
    }
    
    public static void ParseFields(ref EmailDataToCreateEmail emailDataToCreateEmail)
    {
        if (emailDataToCreateEmail.PrintableObject is null)
            throw new Exception("Printobject is null");

        var parser = new FluidParser();
        emailDataToCreateEmail.ParsedHtmlBody = string.Empty;
        emailDataToCreateEmail.ParsedSubject = string.Empty;
        emailDataToCreateEmail.ParsedFileName = string.Empty;

        if (parser.TryParse(emailDataToCreateEmail.EmailParameter.BodyTemplate, out var templateBody, out var error))
            emailDataToCreateEmail.ParsedHtmlBody = templateBody.Render(new TemplateContext(emailDataToCreateEmail.PrintableObject));
        else
            throw new Exception($"Parse Body error: {error}");
        
        if (parser.TryParse(emailDataToCreateEmail.EmailParameter.Subject, out var templateSubject, out var errorSubject))
            emailDataToCreateEmail.ParsedSubject = templateSubject.Render(new TemplateContext(emailDataToCreateEmail.PrintableObject));
        else
            throw new Exception($"Parse Subject error: {errorSubject}");

        if (emailDataToCreateEmail.EmailParameter.FileName is not null)
        {
            if (parser.TryParse(emailDataToCreateEmail.EmailParameter.FileName, out var templateFileName, out var errorFileName))
                emailDataToCreateEmail.ParsedFileName = templateFileName.Render(new TemplateContext(emailDataToCreateEmail.PrintableObject)).TrimEnd();
            else
                throw new Exception($"Parse FileName error: {errorFileName}");
        }
    }
}