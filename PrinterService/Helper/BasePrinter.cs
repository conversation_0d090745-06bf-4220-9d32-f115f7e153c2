using combit.Reporting;
using combit.Reporting.DataProviders;

namespace PrinterService.Helper;


public class BasePrinter
{
    protected static ListLabel ConstructListLabel(object printableDto, string printFile,string logPath)
    {
        ListLabel ll;
        #if (DEBUG)
            ll = new ListLabel(LlLanguage.German, true, Path.Combine(logPath,"debug.log"))
            {
                Debug = LlDebug.Enabled | LlDebug.LogToFile,
                WindowsClientWebDesignerMode = false
            };
        #else 
            ll = new ListLabel(LlLanguage.German, true)
            {
                WindowsClientWebDesignerMode = false
            };
        #endif
        

        ll.LicensingInfo = "BCM7Gg";
        ll.AutoDefineVariable += LL_AutoDefineField;
        ll.AutoShowPrintOptions = false;
        ll.AutoShowSelectFile = false;
        ll.AutoProjectFile = printFile;
        ll.AutoProjectType = printFile.EndsWith(".lbl") ? LlProject.Label : LlProject.List;
        ll.DataSource = new ObjectDataProvider(printableDto);
        ll.AutoMasterMode = LlAutoMasterMode.AsVariables;
        ll.AutoDestination = LlPrintMode.Normal;
        ll.AddVarsToFields = true;
        
        AddVariablesFromObject(ll, printableDto);
        
        var dataMember = string.Empty;
        if (printableDto is IPrintObject test)
            dataMember = test.ObjName();

        ll.DataMember = dataMember;

        return ll;
    }
    
    protected static void LL_AutoDefineField(object sender, AutoDefineElementEventArgs e)
    {
        //e.Suppress = true;

        if (sender is not ListLabel listLabel)
            return;
        
        var variableName = e.Name;
        var dotIndex = variableName.IndexOf('.');

        if (dotIndex != -1)
            variableName = variableName[(dotIndex + 1)..];
        
        listLabel.Variables.Add(variableName, e.Value);
        listLabel.Fields.Add(variableName, e.Value);
    }

    private static void AddVariablesFromObject(ListLabel configuredPrinter, object obj)
    {
        if (obj is IEnumerable<object> enumerable)
        {
            foreach (var item in enumerable)
            {
                var properties = item.GetType().GetProperties();
                foreach (var property in properties)
                {
                    var value = property.GetValue(item);
                    var name = property.Name;
                    configuredPrinter.Variables.Add(name, value);
                    configuredPrinter.Fields.Add(name, value);
                }
            }
        }
        else
        {
            var properties = obj.GetType().GetProperties();
            foreach (var property in properties)
            {
                var value = property.GetValue(obj);
                var name = property.Name;
                configuredPrinter.Variables.Add(name, value);
                configuredPrinter.Fields.Add(name, value);
            }
        }
    }
    
  
    protected static string GetPrintFolder(string appFolder)
    {
        var printPath = Path.Combine(appFolder, "Print");
        try
        {
            if (!Directory.Exists(printPath))
                Directory.CreateDirectory(printPath);
        }
        catch
        {
            return "";
        }

        return printPath;
    }
    
    protected static string GetLogFolder(string appFolder)
    {
        var logPath = Path.Combine(GetPrintFolder(appFolder), "Log");
        if (!Directory.Exists(logPath))
            Directory.CreateDirectory(logPath);

        return logPath;
    }
    
    protected static string GetPdfFolder(string appFolder)
    {
        var pdfPath = Path.Combine(GetPrintFolder(appFolder), "PDF");
        if (!File.Exists(pdfPath))
            Directory.CreateDirectory(pdfPath);

        return pdfPath;
    }
    
    public static bool UseRepository(string repositoryIdOrListFile)
    {
        return repositoryIdOrListFile.StartsWith("repository://{");
    }
}
