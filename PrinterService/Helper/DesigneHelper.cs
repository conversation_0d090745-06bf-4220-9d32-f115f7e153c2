using System.Runtime.Versioning;
using combit.Reporting;
using combit.Reporting.Repository;

namespace PrinterService.Helper;
public class DesigneHelper : BasePrinter
{
    public static ListLabel GetListLabel(object printableDto, string printFile,string tempFolder)
    {
        return ConstructListLabel(printableDto, printFile, GetLogFolder(tempFolder));
    }
    
    public static void SetListLabel(object printableDto, string printFile,string tempFolder)
    {
        LastUsedPrintObject = printableDto;
        PrintFile = printFile;
        TempFolder = tempFolder;
    }
    
    public static Object LastUsedPrintObject { get; set; } = new object();
    private static string PrintFile { get; set; } = string.Empty;
    private static string TempFolder { get; set; } = string.Empty;
    
    public static ListLabel GetStaticListLabel()
    {
        var ll = GetListLabel(LastUsedPrintObject, PrintFile, TempFolder);

        return ll;
    }
    
    public static void OpenDesigner(object printableDto, string repoId, IRepository? repository,string tempFolder)
    {
        var dataMember = string.Empty;
        if (printableDto is IPrintObject test)
            dataMember = test.ObjName();
        
        var tempFile = dataMember + DateTime.Now.ToString("HHmmssffff");
        
        if (!tempFile.EndsWith(".pdf"))
            tempFile += ".pdf";
        
        var ll = GetListLabel(printableDto, repoId, tempFolder);
        
        ll.FileRepository = repository;
        ll.ExportOptions.Add(LlExportOption.PdfConformance, "pdfa3a");
        ll.ExportOptions.Add(LlExportOption.ExportTarget, "PDF");
        ll.ExportOptions.Add(LlExportOption.ExportPath, GetPdfFolder(tempFolder));
        ll.ExportOptions.Add(LlExportOption.ExportFile, tempFile);
        ll.ExportOptions.Add(LlExportOption.ExportQuiet, "1");
        ll.ExportOptions.Add(LlExportOption.PdfAuthor, "VAS.Software");

        try
        {
            ll.Design();
        }
        finally
        {
            ll.Dispose();
        }
    }
    
    public static void OpenDesigner(object printableDto, string printFile,string tempFolder)
    {
        var dataMember = string.Empty;
        if (printableDto is IPrintObject test)
            dataMember = test.ObjName();
        
        var tempFile = dataMember + DateTime.Now.ToString("HHmmssffff");
        
        if (!tempFile.EndsWith(".pdf"))
            tempFile += ".pdf";
        
        var ll = GetListLabel(printableDto, printFile, tempFolder);
        
        ll.ExportOptions.Add(LlExportOption.PdfConformance, "pdfa3a");
        ll.ExportOptions.Add(LlExportOption.ExportTarget, "PDF");
        ll.ExportOptions.Add(LlExportOption.ExportPath, GetPdfFolder(tempFolder));
        ll.ExportOptions.Add(LlExportOption.ExportFile, tempFile);
        ll.ExportOptions.Add(LlExportOption.ExportQuiet, "1");
        ll.ExportOptions.Add(LlExportOption.PdfAuthor, "VAS.Software");

        try
        {
            ll.Design();
        }
        finally
        {
            ll.Dispose();
        }
    }

    public static string? GetPrintType()
    {
        if (LastUsedPrintObject is IPrintObject printObject)
            return printObject.PrintType();
        return null;
    }
}