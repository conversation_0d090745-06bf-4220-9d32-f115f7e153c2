using System.Runtime.Versioning;
using combit.Reporting;
using combit.Reporting.Repository;

namespace PrinterService.Helper;

public class PrintFilesHelper : BasePrinter
{
    public static void PrintPdf(object printableDto, string printFile, string printerName, string tempFolder, int numberOfCopies, bool withExportOption, IRepository? repository)
    {
        var ll = ConstructListLabel(printableDto, printFile, GetLogFolder(tempFolder));
        ll.FileRepository = repository;
        
        ll.DefinePrintOptions += (sender, e) => LL_DefinePrintOptions(sender, e, numberOfCopies);

        if (withExportOption)
        {
            var tempFile = nameof(printableDto) + DateTime.Now.ToString("HHmmssffff") + ".pdf";
            ll.ExportOptions.Add(LlExportOption.ExportTarget, "PDF");
            ll.ExportOptions.Add(LlExportOption.ExportPath, GetPdfFolder(tempFolder));
            ll.ExportOptions.Add(LlExportOption.ExportFile,tempFile); 
        }
        try
        {
            ll.Print(printerName);
        }
        finally
        {
            ll.Dispose();
        }
    }

    private static void LL_DefinePrintOptions(object sender, EventArgs e, int numberOfCopies)
    {
        if (sender is ListLabel ll)
            ll.Core.LlPrintSetOption(LlPrintOption.Copies, numberOfCopies);
    }
}