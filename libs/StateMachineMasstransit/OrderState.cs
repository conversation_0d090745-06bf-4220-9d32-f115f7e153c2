using MassTransit;

namespace StateMachineMasstransit;

public class BackgroundState : SagaStateMachineInstance
{
    public Guid CorrelationId { get; set; }
    public DateTime CreationDateTimeUtc { get; set; } = DateTime.UtcNow;
    public string CurrentState { get; set; } = string.Empty;
    public string ObjectName { get; set; } = string.Empty;
    public long Counter { get; set; }
    public string InfoMessage { get; set; } = string.Empty;
    public string Data { get; set; } = string.Empty;
}

public record StarPrinterForProcessOrderHeaderEvent(Guid Id, string Data);
public record ProcessPrinterForProcessOrderHeaderEvent(Guid Id, string Data);
public record StopBackgroundWorkEvent(Guid Id);
public record ErrorBackgroundWorkEvent(Guid Id);

