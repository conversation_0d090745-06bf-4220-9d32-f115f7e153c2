using System.ComponentModel.DataAnnotations;

namespace DatevExport.CSV;

public class DatevExportSachkontobeschriftungenEntryCsv
{
	[Range(1,999999999)] public long Konto { get; set; }
    [MaxLength(40)] public string Kontenbeschriftung { get; set; } = string.Empty;
    //"de-DE"|"en-EN"
    public string SprachId { get; set; } = "de-DE";
    [MaxLength(100)] public string Kontenbeschriftunglang { get; set; } = string.Empty;
}