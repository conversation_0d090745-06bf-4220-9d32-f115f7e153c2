using System.Globalization;
using System.Text;
using CsvHelper;
using CsvHelper.Configuration;
using WingCore.application.Contract;
using WingCore.application.DataTransferObjects.Invoices;

namespace DatevExport.CSV;

public class DatevExportDebitorKreditorStammdatenCsv
{
    private const string TableNames =
        "Konto;Name (Adressattyp Unternehmen);Unternehmensgegenstand;Name (Adressattyp natrl. Person);Vorname (Adressattyp natrl. Person);Name (Adressattyp keine <PERSON>);Adressattyp;Kurzbezeichnung;EU-Land;EU-UStID;Anrede;Titel/Akad. Grad;Adelstitel;Namensvorsatz;Adressart;Straße;Postfach;Postleitzahl;Ort;Land;Versandzusatz;Adresszusatz;Abweichende Anrede;Abw. Zustellbezeichnung 1;Abw. Zustellbezeichnung 2;Kennz. <PERSON>spondenzadresse;Adresse Gült<PERSON> von;Adresse Gltig bis;Telefon;Bemerkung (Telefon);Telefon GL;Bemerkung (Telefon GL);E-Mail;Bemerkung (E-Mail);Internet;Bemerkung (Internet);Fax;Bemerkung (Fax);Sonstige;Bemerkung (Sonstige);Bankleitzahl 1;Bankbezeichnung 1;Bank-Kontonummer 1;Länderkennzeichen 1;IBAN-Nr. 1;Leerfeld;SWIFT-Code 1;Abw. Kontoinhaber 1;Kennz. Hauptbankverb. 1;Bankverb 1 Gültig von;Bankverb 1 Gültig bis;Bankleitzahl 2;Bankbezeichnung 2;Bank-Kontonummer 2;Länderkennzeichen 2;IBAN-Nr. 2;Leerfeld;SWIFT-Code 2;Abw. Kontoinhaber 2;Kennz. Hauptbankverb. 2;Bankverb 2 Gültig von;Bankverb 2 Gültig bis;Bankleitzahl 3;Bankbezeichnung 3;Bank-Kontonummer 3;Länderkennzeichen 3;IBAN-Nr. 3;Leerfeld;SWIFT-Code 3;Abw. Kontoinhaber 3;Kennz. Hauptbankverb. 3;Bankverb 3 Gültig von;Bankverb 3 Gültig bis;Bankleitzahl 4;Bankbezeichnung 4;Bank-Kontonummer 4;Länderkennzeichen 4;IBAN-Nr. 4;Leerfeld;SWIFT-Code 4;Abw. Kontoinhaber 4;Kennz. Hauptbankverb. 4;Bankverb 4 Gültig von;Bankverb 4 Gültig bis;Bankleitzahl 5;Bankbezeichnung 5;Bank-Kontonummer 5;Länderkennzeichen 5;IBAN-Nr. 5;Leerfeld;SWIFT-Code 5;Abw. Kontoinhaber 5;Kennz. Hauptbankverb. 5;Bankverb 5 Gültig von;Bankverb 5 Gültig bis;Leerfeld;Briefanrede;Grußformelformel;Kunden-/Lief.-Nr.;Steuernummer;Sprache;Ansprechpartner;Vertreter;Sachbearbeiter;Diverse-Konto;Ausgabeziel;Währungssteuerung;Kreditlimit (Debitor);Zahlungsbedingung;Fälligkeit in Tagen (Debitor);Skonto in Prozent (Debitor);Kreditoren-Ziel 1 Tg.;Kreditoren-Skonto 1 %;Kreditoren-Ziel 2 Tg.;Kreditoren-Skonto 2 %;Kreditoren-Ziel 3 Brutto Tg.;Kreditoren-Ziel 4 Tg.;Kreditoren-Skonto 4 %;Kreditoren-Ziel 5 Tg.;Kreditoren-Skonto 5 %;Mahnung;Kontoauszug;Mahntext 1;Mahntext 2;Mahntext 3;Kontoauszugstext;Mahnlimit Betrag;Mahnlimit %;Zinsberechnung;Mahnzinssatz 1;Mahnzinssatz 2;Mahnzinssatz 3;Lastschrift;Leerfeld;Mandantenbank;Zahlungsträger;Indiv. Feld 1;Indiv. Feld 2;Indiv. Feld 3;Indiv. Feld 4;Indiv. Feld 5;Indiv. Feld 6;Indiv. Feld 7;Indiv. Feld 8;Indiv. Feld 9;Indiv. Feld 10;Indiv. Feld 11;Indiv. Feld 12;Indiv. Feld 13;Indiv. Feld 14;Indiv. Feld 15;Abweichende Anrede (Rechnungsadresse);Adressart (Rechnungsadresse);Straße (Rechnungsadresse);Postfach (Rechnungsadresse);Postleitzahl (Rechnungsadresse);Ort (Rechnungsadresse);Land (Rechnungsadresse);Versandzusatz (Rechnungsadresse);Adresszusatz (Rechnungsadresse);Abw. Zustellbezeichnung 1 (Rechnungsadresse);Abw. Zustellbezeichnung 2 (Rechnungsadresse);Adresse Gültig von (Rechnungsadresse);Adresse Gültig bis (Rechnungsadresse);Bankleitzahl 6;Bankbezeichnung 6;Bank-Kontonummer 6;Länderkennzeichen 6;IBAN-Nr. 6;Leerfeld;SWIFT-Code 6;Abw. Kontoinhaber 6;Kennz. Hauptbankverb. 6;Bankverb 6 Gültig von;Bankverb 6 Gültig bis;Bankleitzahl 7;Bankbezeichnung 7;Bank-Kontonummer 7;Länderkennzeichen 7;IBAN-Nr. 7;Leerfeld;SWIFT-Code 7;Abw. Kontoinhaber 7;Kennz. Hauptbankverb. 7;Bankverb 7 Gültig von;Bankverb 7 Gültig bis;Bankleitzahl 8;Bankbezeichnung 8;Bank-Kontonummer 8;Länderkennzeichen 8;IBAN-Nr. 8;Leerfeld;SWIFT-Code 8;Abw. Kontoinhaber 8;Kennz. Hauptbankverb. 8;Bankverb 8 Gültig von;Bankverb 8 Gültig bis;Bankleitzahl 9;Bankbezeichnung 9;Bank-Kontonummer 9;Länderkennzeichen 9;IBAN-Nr. 9;Leerfeld;SWIFT-Code 9;Abw. Kontoinhaber 9;Kennz. Hauptbankverb. 9;Bankverb 9 Gültig von;Bankverb 9 Gültig bis;Bankleitzahl 10;Bankbezeichnung 10;Bank-Kontonummer 10;Länderkennzeichen 10;IBAN-Nr. 10;Leerfeld;SWIFT-Code 10;Abw. Kontoinhaber 10;Kennz. Hauptbankverb. 10;Bankverb 10 Gültig von;Bankverb 10 Gültig bis;Nummer Fremdsystem;Insolvent;SEPA-Mandatsreferenz 1;SEPA-Mandatsreferenz 2;SEPA-Mandatsreferenz 3;SEPA-Mandatsreferenz 4;SEPA-Mandatsreferenz 5;SEPA-Mandatsreferenz 6;SEPA-Mandatsreferenz 7;SEPA-Mandatsreferenz 8;SEPA-Mandatsreferenz 9;SEPA-Mandatsreferenz 10;Verknüpftes OPOS-Konto;Mahnsperre bis;Lastschriftsperre bis;Zahlungssperre bis;Gebührenberechnung;MahnGebühr 1;MahnGebühr 2;MahnGebühr 3;Pauschalenberechnung;Verzugspauschale 1;Verzugspauschale 2;Verzugspauschale 3;Alternativer Suchname;Status;Anschrift manuell geändert (Korrespondenzadresse);Anschrift individuell (Korrespondenzadresse);Anschrift manuell geändert (Rechnungsadresse);Anschrift individuell (Rechnungsadresse);Fristberechnung bei Debitor;Mahnfrist 1;Mahnfrist 2;Mahnfrist 3;Letzte Frist";

    
    public static byte[] WriteCsv(DateTime fromDate,
                                DateTime toDate,
                                long beraternummer,
                                long mandantennummer,
                                short sachKontenLaenge,
                                string suffixForKundenkonto,
                                DateTime wirtschaftsjahresbeginn,
                                List<BaseInvoiceOverviewEntryDto> invoiceOverviews,
                                ILoggerManager logger)
    {
        var config = new CsvConfiguration(CultureInfo.CurrentCulture)
        {
            HasHeaderRecord = false,
            NewLine = "\r\n",
            Delimiter = ";",
            Mode = CsvMode.NoEscape
        };

        using var memoryStream = new MemoryStream();
        using var writer = new StreamWriter(memoryStream, Encoding.GetEncoding("ISO-8859-1"));
        using var csv = new CsvWriter(writer, config);

        csv.Context.RegisterClassMap<CsvMapDatevExportHeaderCsv>();
        csv.Context.RegisterClassMap<CsvMapDatevExportDebitorKreditorStammdatenEntryCsv>();
        
        var header = new DatevExportHeaderCsv()
        {
            Sachkontenlaenge = sachKontenLaenge,
            Beraternummer = beraternummer,
            Mandantennummer = mandantennummer,
            Erzeugtam = DateTime.Now,
            Datumvon = null,
            Datumbis = null,
            Wkz = string.Empty,
            Buchungstyp = null,
            Rechnungslegungszweck = null,
            WjBeginn = wirtschaftsjahresbeginn
        };
        header.SetFormatkategorie(HeaderFormatType.DebitorenKreditoren);
        DatevExportHelper.Validate(header, logger, "");

        header.Bezeichnung = string.Empty;
        
        csv.WriteRecords([header]);
        TableNames.Split(";").ToList().ForEach(f => csv.WriteField(f));
        csv.NextRecord();

        var numberToInvoiceHolder = new Dictionary<long, InvoiceSupplierOrHolderDto?>();
        List<DatevExportDebitorKreditorStammdatenEntryCsv> entriesToCsvs = [];
        
        foreach (var invoiceOverview in invoiceOverviews)
        {
            var invoiceHolderOrSupplier = invoiceOverview switch
            {
               OutgoingInvoiceOverviewDto => invoiceOverview.InvoiceHolder,
               IncomingInvoiceOverviewDto => invoiceOverview.InvoiceSupplier,
               GrainSettlementInvoiceOverviewDto => invoiceOverview.InvoiceSupplier,
               RapeSettlementInvoiceOverviewDto => invoiceOverview.InvoiceSupplier,
               _ => null
            };
           
            if(invoiceHolderOrSupplier is null)
                continue;
            
            if(numberToInvoiceHolder.ContainsKey(invoiceHolderOrSupplier.Number ?? 0))
                continue;
             
            var entry = DatevExportDebitorKreditorStammdatenEntryCsv.Create(invoiceHolderOrSupplier, invoiceOverview,
                suffixForKundenkonto);
            entriesToCsvs.Add(entry);
         
            numberToInvoiceHolder.Add(invoiceHolderOrSupplier?.Number ?? 0, invoiceHolderOrSupplier);
        }
        csv.WriteRecordsAsync(entriesToCsvs).GetAwaiter().GetResult();
        csv.Flush();
        return memoryStream.ToArray();
    }
}
    
