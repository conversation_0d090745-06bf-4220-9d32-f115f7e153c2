using System.ComponentModel.DataAnnotations;
using System.Globalization;
using CsvHelper.Configuration;
using CsvHelper.Configuration.Attributes;
using WingCore.application.DataTransferObjects.Invoices;

namespace DatevExport.CSV;
public sealed class CsvMapDatevExportDebitorKreditorStammdatenEntryCsv : ClassMap<DatevExportDebitorKreditorStammdatenEntryCsv>
{
    public CsvMapDatevExportDebitorKreditorStammdatenEntryCsv()
    {
        AutoMap(CultureInfo.CurrentCulture);
        Map(m => m.NameAdressatentypUnternehmen).Convert(args => "\"" + args.Value.NameAdressatentypUnternehmen + "\"");
        Map(m => m.Unternehmensgegenstand).Convert(args => "\"" + args.Value.Unternehmensgegenstand + "\"");
        Map(m => m.NameAdressatentypNatuerlPerson).Convert(args => "\"" + args.Value.NameAdressatentypNatuerlPerson + "\"");
        Map(m => m.VornameAdressatentypNatuerlPerson).Convert(args => "\"" + args.Value.VornameAdressatentypNatuerlPerson + "\"");
        Map(m => m.NameAdressatentypKeineAngabe).Convert(args => "\"" + args.Value.NameAdressatentypKeineAngabe + "\"");
        Map(m => m.Adressatentyp).Convert(args => "\"" + args.Value.Adressatentyp + "\"");
        Map(m => m.Kurzbezeichnung).Convert(args => "\"" + args.Value.Kurzbezeichnung + "\"");
        Map(m => m.EuMitgliedstaat).Convert(args => "\"" + args.Value.EuMitgliedstaat + "\"");
        Map(m => m.EuuStIdNr).Convert(args => "\"" + args.Value.EuuStIdNr + "\"");
        Map(m => m.Anrede).Convert(args => "\"" + args.Value.Anrede + "\"");
        Map(m => m.TitelAkadGrad).Convert(args => "\"" + args.Value.TitelAkadGrad + "\"");
        Map(m => m.Adelstitel).Convert(args => "\"" + args.Value.Adelstitel + "\"");
        Map(m => m.Namensvorsatz).Convert(args => "\"" + args.Value.Namensvorsatz + "\"");
        Map(m => m.Strasse).Convert(args => "\"" + args.Value.Strasse + "\"");
        Map(m => m.Postfach).Convert(args => "\"" + args.Value.Postfach + "\"");
        Map(m => m.Postleitzahl).Convert(args => "\"" + args.Value.Postleitzahl + "\"");
        Map(m => m.Ort).Convert(args => "\"" + args.Value.Ort + "\"");
        Map(m => m.Land).Convert(args => "\"" + args.Value.Land + "\"");
        Map(m => m.Versandzusatz).Convert(args => "\"" + args.Value.Versandzusatz + "\"");
        Map(m => m.Adresszusatz).Convert(args => "\"" + args.Value.Adresszusatz + "\"");
        Map(m => m.AbweichendeAnrede).Convert(args => "\"" + args.Value.AbweichendeAnrede + "\"");
        Map(m => m.AbwZustellbezeichnung1).Convert(args => "\"" + args.Value.AbwZustellbezeichnung1 + "\"");
        Map(m => m.AbwZustellbezeichnung2).Convert(args => "\"" + args.Value.AbwZustellbezeichnung2 + "\"");
        Map(m => m.Telefon).Convert(args => "\"" + args.Value.Telefon + "\"");
        Map(m => m.BemerkungTelefon).Convert(args => "\"" + args.Value.BemerkungTelefon + "\"");
        Map(m => m.TelefonGeschaeftsleitung).Convert(args => "\"" + args.Value.TelefonGeschaeftsleitung + "\"");
        Map(m => m.KennzKorrespondenzadresse).Convert(args => "\"" + args.Value.KennzKorrespondenzadresse + "\"");
        Map(m => m.BemerkungTelefonGl).Convert(args => "\"" + args.Value.BemerkungTelefonGl + "\"");
        Map(m => m.Email).Convert(args => "\"" + args.Value.Email + "\"");
        Map(m => m.BemerkungEmail).Convert(args => "\"" + args.Value.BemerkungEmail + "\"");
        Map(m => m.Internet).Convert(args => "\"" + args.Value.Internet + "\"");
        Map(m => m.BemerkungInternet).Convert(args => "\"" + args.Value.BemerkungInternet + "\"");
        Map(m => m.Fax).Convert(args => "\"" + args.Value.Fax + "\"");
        Map(m => m.BemerkungFax).Convert(args => "\"" + args.Value.BemerkungFax + "\"");
        Map(m => m.Sonstige).Convert(args => "\"" + args.Value.Sonstige + "\"");
        Map(m => m.BemerkungSonstige1).Convert(args => "\"" + args.Value.BemerkungSonstige1 + "\"");
        Map(m => m.Adressart).Convert(args => "\"" + args.Value.Adressart + "\"");
        Map(m => m.Briefanrede).Convert(args => "\"" + args.Value.Briefanrede + "\"");
        Map(m => m.Grussformel).Convert(args => "\"" + args.Value.Grussformel + "\"");
        Map(m => m.Kundennummer).Convert(args => "\"" + args.Value.Kundennummer + "\"");
        Map(m => m.Steuernummer).Convert(args => "\"" + args.Value.Steuernummer + "\"");
        Map(m => m.Ansprechpartner).Convert(args => "\"" + args.Value.Ansprechpartner + "\"");
        Map(m => m.Vertreter).Convert(args => "\"" + args.Value.Vertreter + "\"");
        Map(m => m.Sachbearbeiter).Convert(args => "\"" + args.Value.Sachbearbeiter + "\"");
        Map(m => m.Waehrungssteuerung).Convert(args => "\"" + args.Value.Waehrungssteuerung + "\"");
        Map(m => m.Lastschrift).Convert(args => "\"" + args.Value.Lastschrift + "\"");
        Map(m => m.Zahlungstraeger).Convert(args => "\"" + args.Value.Zahlungstraeger + "\"");
        Map(m => m.IndivFeld1).Convert(args => "\"" + args.Value.IndivFeld1 + "\"");
        Map(m => m.IndivFeld2).Convert(args => "\"" + args.Value.IndivFeld2 + "\"");
        Map(m => m.IndivFeld3).Convert(args => "\"" + args.Value.IndivFeld3 + "\"");
        Map(m => m.IndivFeld4).Convert(args => "\"" + args.Value.IndivFeld4 + "\"");
        Map(m => m.IndivFeld5).Convert(args => "\"" + args.Value.IndivFeld5 + "\"");
        Map(m => m.IndivFeld6).Convert(args => "\"" + args.Value.IndivFeld6 + "\"");
        Map(m => m.IndivFeld7).Convert(args => "\"" + args.Value.IndivFeld7 + "\"");
        Map(m => m.IndivFeld8).Convert(args => "\"" + args.Value.IndivFeld8 + "\"");
        Map(m => m.IndivFeld9).Convert(args => "\"" + args.Value.IndivFeld9 + "\"");
        Map(m => m.IndivFeld10).Convert(args => "\"" + args.Value.IndivFeld10 + "\"");
        Map(m => m.IndivFeld11).Convert(args => "\"" + args.Value.IndivFeld11 + "\"");
        Map(m => m.IndivFeld12).Convert(args => "\"" + args.Value.IndivFeld12 + "\"");
        Map(m => m.IndivFeld13).Convert(args => "\"" + args.Value.IndivFeld13 + "\"");
        Map(m => m.IndivFeld14).Convert(args => "\"" + args.Value.IndivFeld14 + "\"");
        Map(m => m.IndivFeld15).Convert(args => "\"" + args.Value.IndivFeld15 + "\"");
        Map(m => m.AbweichendeAnredeRechnungsadresse).Convert(args => "\"" + args.Value.AbweichendeAnredeRechnungsadresse + "\"");
        Map(m => m.AdressartRechnungsadresse).Convert(args => "\"" + args.Value.AdressartRechnungsadresse + "\"");
        Map(m => m.StrasseRechnungsadresse).Convert(args => "\"" + args.Value.StrasseRechnungsadresse + "\"");
        Map(m => m.PostfachRechnungsadresse).Convert(args => "\"" + args.Value.PostfachRechnungsadresse + "\"");
        Map(m => m.PostleitzahlRechnungsadresse).Convert(args => "\"" + args.Value.PostleitzahlRechnungsadresse + "\"");
        Map(m => m.OrtRechnungsadresse).Convert(args => "\"" + args.Value.OrtRechnungsadresse + "\"");
        Map(m => m.LandRechnungsadresse).Convert(args => "\"" + args.Value.LandRechnungsadresse + "\"");
        Map(m => m.VersandzusatzRechnungsadresse).Convert(args => "\"" + args.Value.VersandzusatzRechnungsadresse + "\"");
        Map(m => m.AdresszusatzRechnungsadresse).Convert(args => "\"" + args.Value.AdresszusatzRechnungsadresse + "\"");
        Map(m => m.AbwZustellbezeichnung1Rechnungsadresse).Convert(args => "\"" + args.Value.AbwZustellbezeichnung1Rechnungsadresse + "\"");
        Map(m => m.AbwZustellbezeichnung2Rechnungsadresse).Convert(args => "\"" + args.Value.AbwZustellbezeichnung2Rechnungsadresse + "\"");
        Map(m => m.AnschriftindividuellRechnungsadresse).Convert(args => "\"" + args.Value.AnschriftindividuellRechnungsadresse + "\"");
        
        Map(m => m.Bankleitzahl1).Convert(args => "\"" + args.Value.Bankleitzahl1 + "\"");
        Map(m => m.Bankleitzahl2).Convert(args => "\"" + args.Value.Bankleitzahl2 + "\"");
        Map(m => m.Bankleitzahl3).Convert(args => "\"" + args.Value.Bankleitzahl3 + "\"");
        Map(m => m.Bankleitzahl4).Convert(args => "\"" + args.Value.Bankleitzahl4 + "\"");
        Map(m => m.Bankleitzahl5).Convert(args => "\"" + args.Value.Bankleitzahl5 + "\"");
        Map(m => m.Bankleitzahl6).Convert(args => "\"" + args.Value.Bankleitzahl6 + "\"");
        Map(m => m.Bankleitzahl7).Convert(args => "\"" + args.Value.Bankleitzahl7 + "\"");
        Map(m => m.Bankleitzahl8).Convert(args => "\"" + args.Value.Bankleitzahl8 + "\"");
        Map(m => m.Bankleitzahl9).Convert(args => "\"" + args.Value.Bankleitzahl9 + "\"");
        Map(m => m.Bankleitzahl10).Convert(args => "\"" + args.Value.Bankleitzahl10 + "\"");
        
        Map(m => m.Bankbezeichung1).Convert(args => "\"" + args.Value.Bankbezeichung1 + "\"");
        Map(m => m.Bankbezeichung2).Convert(args => "\"" + args.Value.Bankbezeichung2 + "\"");
        Map(m => m.Bankbezeichung3).Convert(args => "\"" + args.Value.Bankbezeichung3 + "\"");
        Map(m => m.Bankbezeichung4).Convert(args => "\"" + args.Value.Bankbezeichung4 + "\"");
        Map(m => m.Bankbezeichung5).Convert(args => "\"" + args.Value.Bankbezeichung5 + "\"");
        Map(m => m.Bankbezeichung6).Convert(args => "\"" + args.Value.Bankbezeichung6 + "\"");
        Map(m => m.Bankbezeichung7).Convert(args => "\"" + args.Value.Bankbezeichung7 + "\"");
        Map(m => m.Bankbezeichung8).Convert(args => "\"" + args.Value.Bankbezeichung8 + "\"");
        Map(m => m.Bankbezeichung9).Convert(args => "\"" + args.Value.Bankbezeichung9 + "\"");
        Map(m => m.Bankbezeichung10).Convert(args => "\"" + args.Value.Bankbezeichung10 + "\"");
        
        Map(m => m.BankkontoNummer1).Convert(args => "\"" + args.Value.BankkontoNummer1 + "\"");
        Map(m => m.BankkontoNummer2).Convert(args => "\"" + args.Value.BankkontoNummer2 + "\"");
        Map(m => m.BankkontoNummer3).Convert(args => "\"" + args.Value.BankkontoNummer3 + "\"");
        Map(m => m.BankkontoNummer4).Convert(args => "\"" + args.Value.BankkontoNummer4 + "\"");
        Map(m => m.BankkontoNummer5).Convert(args => "\"" + args.Value.BankkontoNummer5 + "\"");
        Map(m => m.BankkontoNummer6).Convert(args => "\"" + args.Value.BankkontoNummer6 + "\"");
        Map(m => m.BankkontoNummer7).Convert(args => "\"" + args.Value.BankkontoNummer7 + "\"");
        Map(m => m.BankkontoNummer8).Convert(args => "\"" + args.Value.BankkontoNummer8 + "\"");
        Map(m => m.BankkontoNummer9).Convert(args => "\"" + args.Value.BankkontoNummer9 + "\"");
        Map(m => m.BankkontoNummer10).Convert(args => "\"" + args.Value.BankkontoNummer10 + "\"");
        
        Map(m => m.Laenderkennzeichen1).Convert(args => "\"" + args.Value.Laenderkennzeichen1 + "\"");
        Map(m => m.Laenderkennzeichen2).Convert(args => "\"" + args.Value.Laenderkennzeichen2 + "\"");
        Map(m => m.Laenderkennzeichen3).Convert(args => "\"" + args.Value.Laenderkennzeichen3 + "\"");
        Map(m => m.Laenderkennzeichen4).Convert(args => "\"" + args.Value.Laenderkennzeichen4 + "\"");
        Map(m => m.Laenderkennzeichen5).Convert(args => "\"" + args.Value.Laenderkennzeichen5 + "\"");
        Map(m => m.Laenderkennzeichen6).Convert(args => "\"" + args.Value.Laenderkennzeichen6 + "\"");
        Map(m => m.Laenderkennzeichen7).Convert(args => "\"" + args.Value.Laenderkennzeichen7 + "\"");
        Map(m => m.Laenderkennzeichen8).Convert(args => "\"" + args.Value.Laenderkennzeichen8 + "\"");
        Map(m => m.Laenderkennzeichen9).Convert(args => "\"" + args.Value.Laenderkennzeichen9 + "\"");
        Map(m => m.Laenderkennzeichen10).Convert(args => "\"" + args.Value.Laenderkennzeichen10 + "\"");
        
        Map(m => m.IbanNummer1).Convert(args => "\"" + args.Value.IbanNummer1 + "\"");
        Map(m => m.IbanNummer2).Convert(args => "\"" + args.Value.IbanNummer2 + "\"");
        Map(m => m.IbanNummer3).Convert(args => "\"" + args.Value.IbanNummer3 + "\"");
        Map(m => m.IbanNummer4).Convert(args => "\"" + args.Value.IbanNummer4 + "\"");
        Map(m => m.IbanNummer5).Convert(args => "\"" + args.Value.IbanNummer5 + "\"");
        Map(m => m.IbanNummer6).Convert(args => "\"" + args.Value.IbanNummer6 + "\"");
        Map(m => m.IbanNummer7).Convert(args => "\"" + args.Value.IbanNummer7 + "\"");
        Map(m => m.IbanNummer8).Convert(args => "\"" + args.Value.IbanNummer8 + "\"");
        Map(m => m.IbanNummer9).Convert(args => "\"" + args.Value.IbanNummer9 + "\"");
        Map(m => m.IbanNummer10).Convert(args => "\"" + args.Value.IbanNummer10 + "\"");
        
        Map(m => m.Leerfeld).Convert(args => "\"" + args.Value.Leerfeld + "\"");
        Map(m => m.Leerfeld2).Convert(args => "\"" + args.Value.Leerfeld2 + "\"");
        Map(m => m.Leerfeld3).Convert(args => "\"" + args.Value.Leerfeld3 + "\"");
        Map(m => m.Leerfeld4).Convert(args => "\"" + args.Value.Leerfeld4 + "\"");
        Map(m => m.Leerfeld5).Convert(args => "\"" + args.Value.Leerfeld5 + "\"");
        Map(m => m.Leerfeld6).Convert(args => "\"" + args.Value.Leerfeld6 + "\"");
        Map(m => m.Leerfeld7).Convert(args => "\"" + args.Value.Leerfeld7 + "\"");
        Map(m => m.Leerfeld8).Convert(args => "\"" + args.Value.Leerfeld8 + "\"");
        Map(m => m.Leerfeld9).Convert(args => "\"" + args.Value.Leerfeld9 + "\"");
        Map(m => m.Leerfeld10).Convert(args => "\"" + args.Value.Leerfeld10 + "\"");
        Map(m => m.Leerfeld11).Convert(args => "\"" + args.Value.Leerfeld11 + "\"");
        Map(m => m.Leerfeld12).Convert(args => "\"" + args.Value.Leerfeld12 + "\"");
        
        Map(m => m.SwiftCode1).Convert(args => "\"" + args.Value.SwiftCode1 + "\"");
        Map(m => m.SwiftCode2).Convert(args => "\"" + args.Value.SwiftCode2 + "\"");
        Map(m => m.SwiftCode3).Convert(args => "\"" + args.Value.SwiftCode3 + "\"");
        Map(m => m.SwiftCode4).Convert(args => "\"" + args.Value.SwiftCode4 + "\"");
        Map(m => m.SwiftCode5).Convert(args => "\"" + args.Value.SwiftCode5 + "\"");
        Map(m => m.SwiftCode6).Convert(args => "\"" + args.Value.SwiftCode6 + "\"");
        Map(m => m.SwiftCode7).Convert(args => "\"" + args.Value.SwiftCode7 + "\"");
        Map(m => m.SwiftCode8).Convert(args => "\"" + args.Value.SwiftCode8 + "\"");
        Map(m => m.SwiftCode9).Convert(args => "\"" + args.Value.SwiftCode9 + "\"");
        Map(m => m.SwiftCode10).Convert(args => "\"" + args.Value.SwiftCode10 + "\"");
        
        Map(m => m.AbwKontoinhaber1).Convert(args => "\"" + args.Value.AbwKontoinhaber1 + "\"");
        Map(m => m.AbwKontoinhaber2).Convert(args => "\"" + args.Value.AbwKontoinhaber2 + "\"");
        Map(m => m.AbwKontoinhaber3).Convert(args => "\"" + args.Value.AbwKontoinhaber3 + "\"");
        Map(m => m.AbwKontoinhaber4).Convert(args => "\"" + args.Value.AbwKontoinhaber4 + "\"");
        Map(m => m.AbwKontoinhaber5).Convert(args => "\"" + args.Value.AbwKontoinhaber5 + "\"");
        Map(m => m.AbwKontoinhaber6).Convert(args => "\"" + args.Value.AbwKontoinhaber6 + "\"");
        Map(m => m.AbwKontoinhaber7).Convert(args => "\"" + args.Value.AbwKontoinhaber7 + "\"");
        Map(m => m.AbwKontoinhaber8).Convert(args => "\"" + args.Value.AbwKontoinhaber8 + "\"");
        Map(m => m.AbwKontoinhaber9).Convert(args => "\"" + args.Value.AbwKontoinhaber9 + "\"");
        Map(m => m.AbwKontoinhaber10).Convert(args => "\"" + args.Value.AbwKontoinhaber10 + "\"");
        
        Map(m => m.KennzHauptbankverb1).Convert(args => "\"" + args.Value.KennzHauptbankverb1 + "\"");
        Map(m => m.KennzHauptbankverb2).Convert(args => "\"" + args.Value.KennzHauptbankverb2 + "\"");
        Map(m => m.KennzHauptbankverb3).Convert(args => "\"" + args.Value.KennzHauptbankverb3 + "\"");
        Map(m => m.KennzHauptbankverb4).Convert(args => "\"" + args.Value.KennzHauptbankverb4 + "\"");
        Map(m => m.KennzHauptbankverb5).Convert(args => "\"" + args.Value.KennzHauptbankverb5 + "\"");
        Map(m => m.KennzHauptbankverb6).Convert(args => "\"" + args.Value.KennzHauptbankverb6 + "\"");
        Map(m => m.KennzHauptbankverb7).Convert(args => "\"" + args.Value.KennzHauptbankverb7 + "\"");
        Map(m => m.KennzHauptbankverb8).Convert(args => "\"" + args.Value.KennzHauptbankverb8 + "\"");
        Map(m => m.KennzHauptbankverb9).Convert(args => "\"" + args.Value.KennzHauptbankverb9 + "\"");
        Map(m => m.KennzHauptbankverb10).Convert(args => "\"" + args.Value.KennzHauptbankverb10 + "\"");
        
        Map(m => m.NummerFremdsystem).Convert(args => "\"" + args.Value.NummerFremdsystem + "\"");
        Map(m => m.SepaMandatsreferenz1).Convert(args => "\"" + args.Value.SepaMandatsreferenz1 + "\"");
        Map(m => m.SepaMandatsreferenz2).Convert(args => "\"" + args.Value.SepaMandatsreferenz2 + "\"");
        Map(m => m.SepaMandatsreferenz3).Convert(args => "\"" + args.Value.SepaMandatsreferenz3 + "\"");
        Map(m => m.SepaMandatsreferenz4).Convert(args => "\"" + args.Value.SepaMandatsreferenz4 + "\"");
        Map(m => m.SepaMandatsreferenz5).Convert(args => "\"" + args.Value.SepaMandatsreferenz5 + "\"");
        Map(m => m.SepaMandatsreferenz6).Convert(args => "\"" + args.Value.SepaMandatsreferenz6 + "\"");
        Map(m => m.SepaMandatsreferenz7).Convert(args => "\"" + args.Value.SepaMandatsreferenz7 + "\"");
        Map(m => m.SepaMandatsreferenz8).Convert(args => "\"" + args.Value.SepaMandatsreferenz8 + "\"");
        Map(m => m.SepaMandatsreferenz9).Convert(args => "\"" + args.Value.SepaMandatsreferenz9 + "\"");
        Map(m => m.SepaMandatsreferenz10).Convert(args => "\"" + args.Value.SepaMandatsreferenz10 + "\"");
        Map(m => m.AlternativerSuchname).Convert(args => "\"" + args.Value.AlternativerSuchname + "\"");
        Map(m => m.AnschriftindividuellKorrespondenzadresse).Convert(args => "\"" + args.Value.AnschriftindividuellKorrespondenzadresse + "\"");
        Map(m => m.AnschriftmanuellgeaendertRechnungsadresse).Convert(args => "\"" + args.Value.AnschriftmanuellgeaendertRechnungsadresse + "\"");
    }
}

public class DatevExportDebitorKreditorStammdatenEntryCsv
{
    public static DatevExportDebitorKreditorStammdatenEntryCsv Create(InvoiceSupplierOrHolderDto invoiceSupplierOrHolderDto,
                                                                      BaseInvoiceOverviewEntryDto invoiceOverviewEntryDto,
                                                                      string suffixForKundenkonto)
    {

        var euCountry = !string.IsNullOrEmpty(invoiceSupplierOrHolderDto.UstIdNr) &&
                        invoiceSupplierOrHolderDto.Country.Trim() == "Deutschland"
            ? "DE"
            : "";
        
        var ustIdnr=  string.IsNullOrWhiteSpace(invoiceSupplierOrHolderDto.UstIdNr) ? string.Empty : invoiceSupplierOrHolderDto.UstIdNr.EscapeAndTrim();
        if (ustIdnr.Length > 13)
        {
            euCountry = ustIdnr[..2];
            var lastIndex = ustIdnr.Length - 2;
            if (lastIndex > 13)
                lastIndex = 13;
            
            ustIdnr = ustIdnr.Substring(2, lastIndex);
        }

        var fullName = string.Join(' ',
                                        invoiceSupplierOrHolderDto.Name,
                                        invoiceSupplierOrHolderDto.Name2,
                                        invoiceSupplierOrHolderDto.Name3);
        
        var kontoAsString = invoiceSupplierOrHolderDto.Number.ToString() + suffixForKundenkonto;
        return new DatevExportDebitorKreditorStammdatenEntryCsv()
        {
            Konto = Convert.ToInt64(kontoAsString),
            FaelligkeitInTagenDebitor = Convert.ToInt32(invoiceSupplierOrHolderDto.DueDays),
            NameAdressatentypUnternehmen = fullName.EscapeAndTrim(50),
            Unternehmensgegenstand = "",//invoiceSupplierOrHolderDto.Anrede.EscapeAndTrim(50),
            Adressatentyp = 2,
            Kurzbezeichnung = fullName.EscapeAndTrim(15),
            EuMitgliedstaat = euCountry,
            Email = invoiceSupplierOrHolderDto.Email.EscapeAndTrim(60),
            Telefon = invoiceSupplierOrHolderDto.Phone.EscapeAndTrim(60),
            EuuStIdNr = ustIdnr,
            Anrede = "Firma",
            Strasse = invoiceSupplierOrHolderDto.Street.EscapeAndTrim(36),
            Postleitzahl = invoiceSupplierOrHolderDto.Plz.EscapeAndTrim(10),
            Ort = invoiceSupplierOrHolderDto.Ort.EscapeAndTrim(30),
            Land =  invoiceSupplierOrHolderDto.Country == "Deutschland" ? "DE" : "",
            Bankleitzahl1 = invoiceOverviewEntryDto.BankInfo?.Blz.EscapeAndTrim(8) ?? string.Empty,
            Bankbezeichung1 = invoiceOverviewEntryDto.BankInfo?.BankName.EscapeAndTrim(30) ?? string.Empty,
            BankkontoNummer1 = invoiceOverviewEntryDto.BankInfo?.KontoNumber.EscapeAndTrim(10) ?? string.Empty
        };
    }

    [Range(1,*********)] public long Konto { get; set; }
    [MaxLength(50)] public string NameAdressatentypUnternehmen { get; set; } = string.Empty;
    [MaxLength(50)] public string Unternehmensgegenstand  { get; set; } = string.Empty;
    [MaxLength(30)] public string NameAdressatentypNatuerlPerson  { get; set; } = string.Empty;
    [MaxLength(30)] public string VornameAdressatentypNatuerlPerson  { get; set; } = string.Empty;
    [MaxLength(30)] public string NameAdressatentypKeineAngabe  { get; set; } = string.Empty;
    //0 = keine Angabe ,1 = natürliche Person  ,2 = Unternehmen ,keine Angabe / Default-Wert = Unternehmen
    [AllowedValues(0, 1, 2)] public int Adressatentyp  { get; set; } = 2;
    [MaxLength(15)] public string Kurzbezeichnung  { get; set; } = string.Empty;
    [MaxLength(2)] public string EuMitgliedstaat  { get; set; } = string.Empty;
    [MaxLength(13)] public string EuuStIdNr  { get; set; } = string.Empty;
    [MaxLength(30)] public string Anrede  { get; set; } = string.Empty;
    //Nur bei Adressatentyp „natürliche Person“ relevant.
    [MaxLength(25)] public string TitelAkadGrad  { get; set; } = string.Empty;
    //Nur bei Adressatentyp „natürliche Person“ relevant.
    [MaxLength(15)] public string Adelstitel  { get; set; } = string.Empty;
    //Nur bei Adressatentyp „natürliche Person“ relevant.
    [MaxLength(14)] public string Namensvorsatz  { get; set; } = string.Empty;
    //STR = Straße, PF = Postfach,GK = Großkunde
    [AllowedValues("STR", "PF", "GK")] public string Adressart  { get; set; } = "STR";
    [MaxLength(36)] public string Strasse  { get; set; } = string.Empty;
    [MaxLength(10)] public string Postfach  { get; set; } = string.Empty;
    [MaxLength(10)] public string Postleitzahl  { get; set; } = string.Empty;
    [MaxLength(30)] public string Ort  { get; set; } = string.Empty;
    [MaxLength(2)] public string Land  { get; set; } = string.Empty;
    [MaxLength(50)] public string Versandzusatz  { get; set; } = string.Empty;
    [MaxLength(36)] public string Adresszusatz  { get; set; } = string.Empty;
    [MaxLength(30)] public string AbweichendeAnrede  { get; set; } = string.Empty;
    [MaxLength(50)] public string AbwZustellbezeichnung1  { get; set; } = string.Empty;
    [MaxLength(36)] public string AbwZustellbezeichnung2  { get; set; } = string.Empty;
    //1 = Kennzeichnung Korrespondenzadresse
    [MaxLength(1)] public int KennzKorrespondenzadresse  { get; set; } = 1;
    
    //Format: TTMMJJJJ
    [Format("ddMMyyyy")] public DateTime? AdresseGueltigVon  { get; set; }
    //Format: TTMMJJJJ
    [Format("ddMMyyyy")] public DateTime? AdresseGueltigBis  { get; set; }
    [MaxLength(60)] public string Telefon  { get; set; } = string.Empty;
    [MaxLength(40)] public string BemerkungTelefon  { get; set; } = string.Empty;
    [MaxLength(60)] public string TelefonGeschaeftsleitung  { get; set; } = string.Empty;
    [MaxLength(40)] public string BemerkungTelefonGl  { get; set; } = string.Empty;
    [MaxLength(60)] public string Email  { get; set; } = string.Empty;
    [MaxLength(40)] public string BemerkungEmail  { get; set; } = string.Empty;
    [MaxLength(60)] public string Internet  { get; set; } = string.Empty;
    [MaxLength(40)] public string BemerkungInternet  { get; set; } = string.Empty;
    [MaxLength(60)] public string Fax  { get; set; } = string.Empty;
    [MaxLength(40)] public string BemerkungFax  { get; set; } = string.Empty;
    [MaxLength(60)] public string Sonstige  { get; set; } = string.Empty;
    [MaxLength(40)] public string BemerkungSonstige1  { get; set; } = string.Empty;
    [MaxLength(8)] public string Bankleitzahl1  { get; set; } = string.Empty;
    [MaxLength(30)] public string Bankbezeichung1  { get; set; } = string.Empty;
    [MaxLength(10)] public string BankkontoNummer1  { get; set; } = string.Empty;
    [MaxLength(2)] public string Laenderkennzeichen1  { get; set; } = string.Empty;
    [MaxLength(34)] public string IbanNummer1  { get; set; } = string.Empty;
    public string Leerfeld  { get; set; } = string.Empty;
    [MaxLength(11)] public string SwiftCode1  { get; set; } = string.Empty;
    [MaxLength(70)] public string AbwKontoinhaber1  { get; set; } = string.Empty;
    //Kennzeichnung als Haupt-Bankverbindung 1 = Ja 0 = Nein
    [AllowedValues(0,1)] public int KennzHauptbankverb1  { get; set; } = 1;
    //Format: TTMMJJJJ
    [Format("ddMMyyyy")] public DateTime? Bankverb1GueltigVon  { get; set; }
    //Format: TTMMJJJJ
    [Format("ddMMyyyy")] public DateTime? Bankverb1GueltigBis  { get; set; }
    [MaxLength(8)] public string Bankleitzahl2  { get; set; } = string.Empty;
    [MaxLength(30)] public string Bankbezeichung2  { get; set; } = string.Empty;
    [MaxLength(10)] public string BankkontoNummer2  { get; set; } = string.Empty;
    [MaxLength(2)] public string Laenderkennzeichen2  { get; set; } = string.Empty;
    [MaxLength(34)] public string IbanNummer2  { get; set; } = string.Empty;
    public string Leerfeld2  { get; set; } = string.Empty;
    [MaxLength(11)] public string SwiftCode2  { get; set; } = string.Empty;
    [MaxLength(70)] public string AbwKontoinhaber2 { get; set; } = string.Empty;
    //Kennzeichnung als Haupt-Bankverbindung 1 = Ja 0 = Nein
    [AllowedValues(0,1)] public int KennzHauptbankverb2 { get; set; } = 0;
    //Format: TTMMJJJJ
    [Format("ddMMyyyy")] public DateTime? Bankverb2GueltigVon  { get; set; }
    //Format: TTMMJJJJ
    [Format("ddMMyyyy")] public DateTime? Bankverb2GueltigBis  { get; set; }
    
    [MaxLength(8)] public string Bankleitzahl3  { get; set; } = string.Empty;
    [MaxLength(30)] public string Bankbezeichung3  { get; set; } = string.Empty;
    [MaxLength(10)] public string BankkontoNummer3  { get; set; } = string.Empty;
    [MaxLength(2)] public string Laenderkennzeichen3  { get; set; } = string.Empty;
    [MaxLength(34)] public string IbanNummer3  { get; set; } = string.Empty;
    public string Leerfeld3  { get; set; } = string.Empty;
    [MaxLength(11)] public string SwiftCode3  { get; set; } = string.Empty;
    [MaxLength(70)] public string AbwKontoinhaber3 { get; set; } = string.Empty;
    //Kennzeichnung als Haupt-Bankverbindung 1 = Ja 0 = Nein
    [AllowedValues(0,1)] public int KennzHauptbankverb3 { get; set; } = 0;
    //Format: TTMMJJJJ
    [Format("ddMMyyyy")] public DateTime? Bankverb3GueltigVon  { get; set; }
    //Format: TTMMJJJJ
    [Format("ddMMyyyy")] public DateTime? Bankverb3GueltigBis  { get; set; }
    
    [MaxLength(8)] public string Bankleitzahl4  { get; set; } = string.Empty;
    [MaxLength(30)] public string Bankbezeichung4  { get; set; } = string.Empty;
    [MaxLength(10)] public string BankkontoNummer4  { get; set; } = string.Empty;
    [MaxLength(2)] public string Laenderkennzeichen4  { get; set; } = string.Empty;
    [MaxLength(34)] public string IbanNummer4  { get; set; } = string.Empty;
    public string Leerfeld4  { get; set; } = string.Empty;
    [MaxLength(11)] public string SwiftCode4  { get; set; } = string.Empty;
    [MaxLength(70)] public string AbwKontoinhaber4 { get; set; } = string.Empty;
    //Kennzeichnung als Haupt-Bankverbindung 1 = Ja 0 = Nein
    [AllowedValues(0,1)] public int KennzHauptbankverb4 { get; set; } = 0;
    //Format: TTMMJJJJ
    [Format("ddMMyyyy")] public DateTime? Bankverb4GueltigVon  { get; set; }
    //Format: TTMMJJJJ
    [Format("ddMMyyyy")] public DateTime? Bankverb4GueltigBis  { get; set; }
    
    [MaxLength(8)] public string Bankleitzahl5  { get; set; } = string.Empty;
    [MaxLength(30)] public string Bankbezeichung5  { get; set; } = string.Empty;
    [MaxLength(10)] public string BankkontoNummer5  { get; set; } = string.Empty;
    [MaxLength(2)] public string Laenderkennzeichen5  { get; set; } = string.Empty;
    [MaxLength(34)] public string IbanNummer5  { get; set; } = string.Empty;
    public string Leerfeld5  { get; set; } = string.Empty;
    [MaxLength(11)] public string SwiftCode5  { get; set; } = string.Empty;
    [MaxLength(70)] public string AbwKontoinhaber5 { get; set; } = string.Empty;
    //Kennzeichnung als Haupt-Bankverbindung 1 = Ja 0 = Nein
    [AllowedValues(0,1)] public int KennzHauptbankverb5 { get; set; } = 0;
    //Format: TTMMJJJJ
    [Format("ddMMyyyy")] public DateTime? Bankverb5GueltigVon  { get; set; }
    //Format: TTMMJJJJ
    [Format("ddMMyyyy")] public DateTime? Bankverb5GueltigBis  { get; set; }
    
    public string Leerfeld6  { get; set; } = string.Empty;
    [MaxLength(100)] public string Briefanrede  { get; set; } = string.Empty;
    [MaxLength(50)] public string Grussformel  { get; set; } = string.Empty;
    [MaxLength(15)] public string Kundennummer  { get; set; } = string.Empty;
    [MaxLength(20)] public string Steuernummer  { get; set; } = string.Empty;
    //1 = deutsch,4 = französisch,5 = englisch,10 = spanisch,19 = italienisch
    [AllowedValues(1,4,5,10,19)] public int Sprache  { get; set; } = 1;
    [MaxLength(40)] public string Ansprechpartner  { get; set; } = string.Empty;
    [MaxLength(40)] public string Vertreter  { get; set; } = string.Empty;
    [MaxLength(40)] public string Sachbearbeiter  { get; set; } = string.Empty;
    //0 = Nein, 1 = Ja
    [AllowedValues(0,1)] public int? DiverseKonto  { get; set; }
    //1 = Druck, 2 = Telefax, 3 = E-Mail
    [AllowedValues(1,2,3)] public int? Ausgabeziel  { get; set; }
    //0 = Zahlungen in Eingabewährung, 2 = Ausgabe in EUR
    [AllowedValues(0, 2)] public int? Waehrungssteuerung { get; set; } = null;
    [MaxLength(13)] public int KreditlimitDebitor  { get; set; }
    [MaxLength(3)] public int Zahlungsbedingung  { get; set; }
    [MaxLength(3)] public int FaelligkeitInTagenDebitor  { get; set; }
    public decimal SkontoInProzentDebitor  { get; set; }
    public int KreditorenZiel1Tage  { get; set; }
    public decimal KreditorenSkonto1Prozent  { get; set; }
    public int KreditorenZiel2Tage  { get; set; }
    public decimal KreditorenSkonto2Prozent  { get; set; }
    public int KreditorenZiel3BruttoTage  { get; set; }
    public int KreditorenZiel4Tage  { get; set; }
    public decimal KreditorenSkonto4Prozent  { get; set; }
    public int KreditorenZiel5Tage  { get; set; }
    public decimal KreditorenSkonto5Prozent  { get; set; }
    public string Mahnung  { get; set; } = string.Empty;
    public string Kontoauszug  { get; set; } = string.Empty;
    public string Mahntext1  { get; set; } = string.Empty;
    public string Mahntext2  { get; set; } = string.Empty;
    public string Mahntext3  { get; set; } = string.Empty;
    public string Kontoauszugstest  { get; set; } = string.Empty;
    public string MahnlimitBetrag  { get; set; } = string.Empty;
    public string MahnlimitProzent  { get; set; } = string.Empty;
    public string Zinsberechnung  { get; set; } = string.Empty;
    public string Mahnzinssatz1  { get; set; } = string.Empty;
    public string Mahnzinssatz2  { get; set; } = string.Empty;
    public string Mahnzinssatz3  { get; set; } = string.Empty;
    public string Lastschrift  { get; set; } = string.Empty;
    public string Leerfeld12  { get; set; } = string.Empty;
    public int Mandantenbank  { get; set; }
    public string Zahlungstraeger  { get; set; } = string.Empty;
    public string IndivFeld1  { get; set; } = string.Empty;
    public string IndivFeld2  { get; set; } = string.Empty;
    public string IndivFeld3  { get; set; } = string.Empty;
    public string IndivFeld4  { get; set; } = string.Empty;
    public string IndivFeld5  { get; set; } = string.Empty;
    public string IndivFeld6  { get; set; } = string.Empty;
    public string IndivFeld7  { get; set; } = string.Empty;
    public string IndivFeld8  { get; set; } = string.Empty;
    public string IndivFeld9  { get; set; } = string.Empty;
    public string IndivFeld10  { get; set; } = string.Empty;
    public string IndivFeld11  { get; set; } = string.Empty;
    public string IndivFeld12  { get; set; } = string.Empty;
    public string IndivFeld13  { get; set; } = string.Empty;
    public string IndivFeld14  { get; set; } = string.Empty;
    public string IndivFeld15  { get; set; } = string.Empty;
    public string AbweichendeAnredeRechnungsadresse  { get; set; } = string.Empty;
    public string AdressartRechnungsadresse  { get; set; } = string.Empty;
    public string StrasseRechnungsadresse  { get; set; } = string.Empty;
    public string PostfachRechnungsadresse  { get; set; } = string.Empty;
    public string PostleitzahlRechnungsadresse  { get; set; } = string.Empty;
    public string OrtRechnungsadresse  { get; set; } = string.Empty;
    public string LandRechnungsadresse  { get; set; } = string.Empty;
    public string VersandzusatzRechnungsadresse  { get; set; } = string.Empty;
    public string AdresszusatzRechnungsadresse  { get; set; } = string.Empty;
    public string AbwZustellbezeichnung1Rechnungsadresse  { get; set; } = string.Empty;
    public string AbwZustellbezeichnung2Rechnungsadresse  { get; set; } = string.Empty;
    public string AdresseGueltigVonRechnungsadresse  { get; set; } = string.Empty;
    public string AdresseGueltigBisRechnungsadresse  { get; set; } = string.Empty;
    public string Bankleitzahl6  { get; set; } = string.Empty;
    public string Bankbezeichung6  { get; set; } = string.Empty;
    public string BankkontoNummer6  { get; set; } = string.Empty;
    public string Laenderkennzeichen6  { get; set; } = string.Empty;
    public string IbanNummer6  { get; set; } = string.Empty;
    public string Leerfeld7  { get; set; } = string.Empty;
    public string SwiftCode6  { get; set; } = string.Empty;
    public string AbwKontoinhaber6  { get; set; } = string.Empty;
    public string KennzHauptbankverb6  { get; set; } = string.Empty;
    public string Bankverb6GueltigVon  { get; set; } = string.Empty;
    public string Bankverb6GueltigBis  { get; set; } = string.Empty;
    public string Bankleitzahl7  { get; set; } = string.Empty;
    public string Bankbezeichung7  { get; set; } = string.Empty;
    public string BankkontoNummer7  { get; set; } = string.Empty;
    public string Laenderkennzeichen7  { get; set; } = string.Empty;
    public string IbanNummer7  { get; set; } = string.Empty;
    public string Leerfeld8  { get; set; } = string.Empty;
    public string SwiftCode7  { get; set; } = string.Empty;
    public string AbwKontoinhaber7  { get; set; } = string.Empty;
    public string KennzHauptbankverb7  { get; set; } = string.Empty;
    public string Bankverb7GueltigVon  { get; set; } = string.Empty;
    public string Bankverb7GueltigBis  { get; set; } = string.Empty;
    public string Bankleitzahl8  { get; set; } = string.Empty;
    public string Bankbezeichung8  { get; set; } = string.Empty;
    public string BankkontoNummer8  { get; set; } = string.Empty;
    public string Laenderkennzeichen8  { get; set; } = string.Empty;
    public string IbanNummer8  { get; set; } = string.Empty;
    public string Leerfeld9  { get; set; } = string.Empty;
    public string SwiftCode8  { get; set; } = string.Empty;
    public string AbwKontoinhaber8  { get; set; } = string.Empty;
    public string KennzHauptbankverb8  { get; set; } = string.Empty;
    public string Bankverb8GueltigVon  { get; set; } = string.Empty;
    public string Bankverb8GueltigBis  { get; set; } = string.Empty;
    public string Bankleitzahl9  { get; set; } = string.Empty;
    public string Bankbezeichung9  { get; set; } = string.Empty;
    public string BankkontoNummer9  { get; set; } = string.Empty;
    public string Laenderkennzeichen9  { get; set; } = string.Empty;
    public string IbanNummer9  { get; set; } = string.Empty;
    public string Leerfeld10  { get; set; } = string.Empty;
    public string SwiftCode9  { get; set; } = string.Empty;
    public string AbwKontoinhaber9  { get; set; } = string.Empty;
    public string KennzHauptbankverb9  { get; set; } = string.Empty;
    public string Bankverb9GueltigVon  { get; set; } = string.Empty;
    public string Bankverb9GueltigBis  { get; set; } = string.Empty;
    public string Bankleitzahl10  { get; set; } = string.Empty;
    public string Bankbezeichung10  { get; set; } = string.Empty;
    public string BankkontoNummer10  { get; set; } = string.Empty;
    public string Laenderkennzeichen10  { get; set; } = string.Empty;
    public string IbanNummer10  { get; set; } = string.Empty;
    public string Leerfeld11  { get; set; } = string.Empty;
    public string SwiftCode10  { get; set; } = string.Empty;
    public string AbwKontoinhaber10  { get; set; } = string.Empty;
    public string KennzHauptbankverb10  { get; set; } = string.Empty;
    public string Bankverb10GueltigVon  { get; set; } = string.Empty;
    public string Bankverb10GueltigBis  { get; set; } = string.Empty;
    public string NummerFremdsystem  { get; set; } = string.Empty;
    public string Insolvent  { get; set; } = string.Empty;
    public string SepaMandatsreferenz1  { get; set; } = string.Empty;
    public string SepaMandatsreferenz2  { get; set; } = string.Empty;
    public string SepaMandatsreferenz3  { get; set; } = string.Empty;
    public string SepaMandatsreferenz4  { get; set; } = string.Empty;
    public string SepaMandatsreferenz5  { get; set; } = string.Empty;
    public string SepaMandatsreferenz6  { get; set; } = string.Empty;
    public string SepaMandatsreferenz7  { get; set; } = string.Empty;
    public string SepaMandatsreferenz8  { get; set; } = string.Empty;
    public string SepaMandatsreferenz9  { get; set; } = string.Empty;
    public string SepaMandatsreferenz10  { get; set; } = string.Empty;
    public string VerknüpftesOposKonto  { get; set; } = string.Empty;
    public string Mahnsperrebis  { get; set; } = string.Empty;
    public string Lastschriftsperrebis  { get; set; } = string.Empty;
    public string Zahlungssperrebis  { get; set; } = string.Empty;
    public string Gebührenberechnung  { get; set; } = string.Empty;
    public string Mahngebuehr1  { get; set; } = string.Empty;
    public string Mahngebuehr2  { get; set; } = string.Empty;
    public string Mahngebuehr3  { get; set; } = string.Empty;
    public string Pauschalenberechnung  { get; set; } = string.Empty;
    public string Verzugspauschale1  { get; set; } = string.Empty;
    public string Verzugspauschale2  { get; set; } = string.Empty;
    public string Verzugspauschale3  { get; set; } = string.Empty;
    public string AlternativerSuchname  { get; set; } = string.Empty;
    public string Status  { get; set; } = string.Empty;
    public string AnschriftmanuellgeaendertKorrespondenzadresse  { get; set; } = string.Empty;
    public string AnschriftindividuellKorrespondenzadresse  { get; set; } = string.Empty;
    public string AnschriftmanuellgeaendertRechnungsadresse  { get; set; } = string.Empty;
    public string AnschriftindividuellRechnungsadresse  { get; set; } = string.Empty;
    public string FristberechnungbeiDebitor  { get; set; } = string.Empty;
    public string Mahnfrist1  { get; set; } = string.Empty;
    public string Mahnfrist2  { get; set; } = string.Empty;
    public string Mahnfrist3  { get; set; } = string.Empty;
    public string LetzteFrist  { get; set; } = string.Empty;
}