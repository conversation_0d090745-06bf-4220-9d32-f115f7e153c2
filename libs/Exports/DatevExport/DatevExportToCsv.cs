using System.Diagnostics;
using System.IO.Compression;
using DatevExport.CSV;
using Languages.Resources;
using Microsoft.Extensions.Localization;
using WingCore.application.Contract;
using WingCore.application.Contract.IModels.Helper;
using WingCore.application.Contract.Services;
using WingCore.application.DataTransferObjects.Invoices;
using WingCore.domain.Common;
using WingCore.domain.Common.Configurations;

namespace DatevExport;

public class DatevExportToCsv(
                            IOutgoingInvoiceHeaderService outgoingInvoiceHeaderService,
                            IIncomingInvoiceHeaderService incomingInvoiceHeaderHeaderService,
                            IGrainSettlementInvoiceHeaderService grainSettlementInvoiceHeaderService,
                            IRapeSettlementInvoiceHeaderService rapeSettlementInvoiceHeaderService,
                            ILoggerManager logger,
                            IStringLocalizer<Resource> localizer)
{
    public long NumberOfExportInvoices { get; set; }
    public string FileName { get; set; } = string.Empty;
    
    public async Task<MemoryStream> ExportInvoicesAsZip(ConfigurationDatev configuration)
    {
        List<BaseInvoiceOverviewEntryDto> invoiceOverviews = [];
        List<long> outgoingInvoiceId = [];
        List<long> incomingInvoiceId = [];
        List<long> grainInvoiceId = [];
        List<long> rapeInvoiceId = [];
        var memoryStream = new MemoryStream();
        using var archive = new ZipArchive(memoryStream, ZipArchiveMode.Create, true);

        await Task.Delay(2000);
        
        
        if (configuration.SachKontenLaenge is < 4 or > 9)
        {
            throw new Exception(localizer["GeneralLedgerAccountLengthError"]);
        }
        
        if (configuration.Wirtschaftsjahresbeginn != new DateTime(configuration.Wirtschaftsjahresbeginn.Year,configuration.Wirtschaftsjahresbeginn.Month,1))
        {
            throw new Exception(localizer["StartOfFinancialYearFirstDayOfMonthError"]);
        }
                
        InvoiceSearchParam searchParam = new()
        {
            FromDate = configuration.FromDate ?? configuration.BeginingExportDate,
            ToDate =  configuration.ToDate,
            FromInvoiceNumber = configuration.FromInvoiceNumber,
            ToInvoiceNumber = configuration.ToInvoiceNumber,
            WithAllData = true,
            Fibu2 = configuration.WithExportedInvoices ? null : false
        };
                
        if (configuration.WithOutgoingInvoice)
        {
            var outgoingInvoiceOverviews = (await outgoingInvoiceHeaderService.GetAllInvoiceHeaderDataOverview(false, searchParam)).ToList();

            if (outgoingInvoiceOverviews.Count != 0)
            {
                var csvByte = await ExportBuchungsStapelCsv(configuration, outgoingInvoiceOverviews);
                await AddEntryToZip(archive, csvByte, "EXTF_Buchungsstapel_AusgangsRechnungen.csv");
            }
            
            invoiceOverviews = [..invoiceOverviews, ..outgoingInvoiceOverviews];
            outgoingInvoiceId = outgoingInvoiceOverviews.Select(x => x.Id).ToList();
        }

        if (configuration.WithIncomingInvoice)
        {
            var incomingInvoiceOverviews = (await incomingInvoiceHeaderHeaderService
                                                                        .GetAllInvoiceHeaderDataOverview(false, searchParam))
                                                                        .ToList();

            if (incomingInvoiceOverviews.Count != 0)
            {
                var csvByte = await ExportBuchungsStapelCsv(configuration, incomingInvoiceOverviews);
                await AddEntryToZip(archive, csvByte, "EXTF_Buchungsstapel_EingangsRechnungen.csv");
            }

            invoiceOverviews = [..invoiceOverviews, ..incomingInvoiceOverviews];
            incomingInvoiceId = incomingInvoiceOverviews.Select(x => x.Id).ToList();
        }

        if (configuration.WithGrainSettlementInvoice)
        {
            IEnumerable<BaseInvoiceOverviewEntryDto> grainAndRapeSettlementInvoiceOverviews = 
                await grainSettlementInvoiceHeaderService.GetAllInvoiceHeaderDataOverview(false, searchParam);


            grainAndRapeSettlementInvoiceOverviews =
            [
                ..grainAndRapeSettlementInvoiceOverviews,
                ..await rapeSettlementInvoiceHeaderService.GetAllInvoiceHeaderDataOverview(false, searchParam)
            ];

            var grainAndRapeSettlementInvoiceOverviewsList = grainAndRapeSettlementInvoiceOverviews.ToList();
            if (grainAndRapeSettlementInvoiceOverviewsList.Count != 0)
            {
                var csvByte = await ExportBuchungsStapelCsv(configuration, grainAndRapeSettlementInvoiceOverviewsList);
                await AddEntryToZip(archive, csvByte, "EXTF_Buchungsstapel_Getreideabrechnung.csv");
            }
            
            invoiceOverviews = [..invoiceOverviews, ..grainAndRapeSettlementInvoiceOverviewsList];
            grainInvoiceId = grainAndRapeSettlementInvoiceOverviewsList
                                    .Where(i=>i.Type.IsGrainSettlementInvoice)
                                    .Select(x => x.Id).ToList();
            rapeInvoiceId = grainAndRapeSettlementInvoiceOverviewsList
                                    .Where(i=>i.Type.IsRapeSettlementInvoice)
                                    .Select(x => x.Id).ToList();
        }


        NumberOfExportInvoices = invoiceOverviews.Count;
        var csvByteExportDebAndKreditor = await ExportDebitorUndKreditorStammDatenCsv(configuration, invoiceOverviews);
        await AddEntryToZip(archive, csvByteExportDebAndKreditor, "EXTF_DebKred_Stamm.csv");


        FileName = $"DatevExport_{DateTime.Now:yyyyMMdd-HHmmss}.zip";
        SetAllOutgoingInvoiceFibu2(outgoingInvoiceId);
        SetAllIncomingInvoiceFibu2(incomingInvoiceId);
        SetAllGrainSettlementInvoiceFibu2(grainInvoiceId);
        SetAllRapeSettlementInvoiceFibu2(rapeInvoiceId);
        
        return memoryStream;
    }

    private void SetAllOutgoingInvoiceFibu2(List<long> invoiceIds)
    { 
        outgoingInvoiceHeaderService.SetAllInvoiceDatevExportSuccess(invoiceIds);
    }
    private void SetAllIncomingInvoiceFibu2(List<long> invoiceIds)
    { 
        incomingInvoiceHeaderHeaderService.SetAllInvoiceDatevExportSuccess(invoiceIds);
    }
    private void SetAllGrainSettlementInvoiceFibu2(List<long> invoiceIds)
    { 
        grainSettlementInvoiceHeaderService.SetAllInvoiceDatevExportSuccess(invoiceIds);
    }
    private void SetAllRapeSettlementInvoiceFibu2(List<long> invoiceIds)
    { 
        rapeSettlementInvoiceHeaderService.SetAllInvoiceDatevExportSuccess(invoiceIds);
    }

    private Task AddEntryToZip(ZipArchive archive, byte[] contentForFileEntry, string fileName)
    {
        var fileEntry = archive.CreateEntry(fileName);
        using var entryStream = fileEntry.Open();
        entryStream.Write(contentForFileEntry, 0, contentForFileEntry.Length);

        return Task.CompletedTask;
    }


    private Task<byte[]> ExportBuchungsStapelCsv(ConfigurationDatev configuration,
                                                 IEnumerable<BaseInvoiceOverviewEntryDto> invoiceOverviews)
    {
        var invoiceOverviewsAsList = invoiceOverviews.ToList();
        if (invoiceOverviewsAsList.Count == 0)
        {
            return Task.FromResult<byte[]>([]);
        }

        var fromDate = invoiceOverviewsAsList.Min(invoice => invoice.InvoiceDate);
        var toDate = invoiceOverviewsAsList.Max(invoice => invoice.InvoiceDate);
        
        InvoicePostingCompressKeyOption invoicePostingCompressKeyOption = new(true, true, false, false);

        logger.LogInfo($"Start exporting Buchungsstapel to CSV");
        var time = Stopwatch.StartNew();
        var streamBytes = DatevExportBuchungsStapelCsv.WriteCsv(fromDate,
                                                                        toDate,
                                                                        configuration,
                                                                        invoiceOverviewsAsList,
                                                                        invoicePostingCompressKeyOption,
                                                                        logger);
        time.Stop();
        var ml = time.ElapsedMilliseconds;
        logger.LogInfo($"End exporting Buchungsstapel to CSV");
        return Task.FromResult(streamBytes);
    }

    private Task<byte[]> ExportDebitorUndKreditorStammDatenCsv(ConfigurationDatev configuration,
                                                               List<BaseInvoiceOverviewEntryDto> invoiceOverviews)
    {
        if (invoiceOverviews.Count == 0)
        {
            return Task.FromResult<byte[]>([]);
        }
        
        var fromDate = invoiceOverviews.Min(invoice => invoice.InvoiceDate);
        var toDate = invoiceOverviews.Max(invoice => invoice.InvoiceDate);
        
        logger.LogInfo($"Start exporting DebKred_Stamm to CSV");
        var streamBytes = DatevExportDebitorKreditorStammdatenCsv.WriteCsv(fromDate,
                                                                                 toDate,
                                                                                 configuration.BeraterNummer,
                                                                                 configuration.MandantenNummer,
                                                                                 configuration.SachKontenLaenge,
                                                                                 configuration.SuffixForKundenkonto,
                                                                                 configuration.Wirtschaftsjahresbeginn,
                                                                                 invoiceOverviews,
                                                                                 logger);

        logger.LogInfo($"End exporting DebKred_Stamm to CSV");
        return Task.FromResult(streamBytes);
    }
}