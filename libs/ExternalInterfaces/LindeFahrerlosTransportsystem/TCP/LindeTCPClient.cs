using System.Net;
using System.Net.Sockets;
using System.Text;
using System.Xml;
using LindeFahrerlosTransportsystem.Requests.AlivenessCheck;
using LindeFahrerlosTransportsystem.Requests.BaseRequests;
using LindeFahrerlosTransportsystem.Requests.RobotStatus;

namespace LindeFahrerlosTransportsystem.TCP;



public class LindeTcpClient : ILindeTcpClient
{
    private readonly Socket _client;
    //private readonly Socket _clientListener;
    private readonly CancellationTokenSource _cancellationTokenSource = new();
    private readonly Task _receivingTask;
    private List<string> _responseList = [];
    private ILoggerForLindeFahrerlosTransportSystemClient? _logger;

    public LindeTcpClient(string serverIp,
                          int serverPort,
                          string localIp,
                          int localPort,
                          ILoggerForLindeFahrerlosTransportSystemClient logger)
    {
        _logger = logger;
        
        var endpoint = new IPEndPoint(IPAddress.Parse(serverIp), serverPort);
        var localEndPoint = new IPEndPoint(IPAddress.Parse(localIp), localPort);

        /*// Socket erstellen
        _clientListener = new Socket(AddressFamily.InterNetwork, SocketType.Stream, ProtocolType.Tcp);
        // Lokalen Endpunkt binden
        _clientListener.SetSocketOption(SocketOptionLevel.Socket, SocketOptionName.ReuseAddress, true);
        _clientListener.Bind(localEndPoint);
        // Socket in den Listenmodus versetzen
        _clientListener.Listen(10);*/
        
        _logger.Information("Server gestartet auf {LocalIp}:{LocalPort}", localIp, localPort);

        // Socket erstellen
        _client = new Socket(AddressFamily.InterNetwork, SocketType.Stream, ProtocolType.Tcp);
        // Lokalen Endpunkt binden
        _client.SetSocketOption(SocketOptionLevel.Socket, SocketOptionName.ReuseAddress, true);
        _client.Bind(localEndPoint);
        _client.ConnectAsync(endpoint).Wait();

        _logger.Information("Verbunden mit {ServerIp}:{ServerPort}", serverIp, serverPort);

        //_receivingTask = Task.Run(AcceptMessagesAsync);
        _receivingTask = Task.Run(ReceiveMessagesAsync);
    }
    

    public bool IsConnected => _client.Connected;
    
    public async Task<RobotStatusResponse?> SendRequestWithRobotStatusResponse<TRequest>(BaseRequest<TRequest> request)
    {
        var doc = await SendRequestAndGetResponseAsXmlDocument(request);
        if(doc is null)
            return null;
        
        return new RobotStatusResponse()
        {
            DataFields = RobotStatusResponseDatafields.CreateFromXmlDocument(doc),
            HeaderFields = HeaderFields.CreateFromXmlDocument(doc)
        };
    }

    public static ILindeTcpClient Create(string serverIp, int serverPort, string localIp, int localPort,
        ILoggerForLindeFahrerlosTransportSystemClient logger)
    {
        return new LindeTcpClient(serverIp, serverPort, localIp, localPort, logger);
    }

    public async Task<AlivenessCheckResponse?> SendRequestWithAlivenessCheckResponse<TRequest>(
        BaseRequest<TRequest> request)
    {
        var doc = await SendRequestAndGetResponseAsXmlDocument(request);
        if(doc is null)
            return null;

        return new AlivenessCheckResponse()
        {
            DataFields = AlivenessCheckDatafield.CreateFromXmlDocument(doc),
            HeaderFields = HeaderFields.CreateFromXmlDocument(doc)
        };
    }
    public async Task<BaseResponse?> SendRequest<TRequest>(BaseRequest<TRequest> request)
    {
        var doc = await SendRequestAndGetResponseAsXmlDocument(request);
        if(doc is null)
            doc = await SendRequestAndGetResponseAsXmlDocument(request);
        if(doc is null)
            return null;
        var responseObj = new BaseResponse()
        {
            DataFields = BaseResponseDatafield.CreateFromXmlDocument(doc),
            HeaderFields = HeaderFields.CreateFromXmlDocument(doc)
        };

        return responseObj;
    }
    
    
    public ICollection<XmlDocument> GetAllUpdateNotificationsAsXmlDocument()
    {
        List<XmlDocument> result = [];
        
        foreach (var responseCleaned in GetAllUpdateNotificationsAsString())
        {
            if(string.IsNullOrWhiteSpace(responseCleaned))
                continue;

            var doc = new XmlDocument();
            doc.LoadXml(responseCleaned);
            result.Add(doc);
        }

        return result;
    }

    public ICollection<string> GetAllUpdateNotificationsAsString()
    {
        List<string> result = [];

        foreach (var response in _responseList.ToList())
        {
            if (string.IsNullOrWhiteSpace(response))
            {
                _responseList.Remove(response);
                continue;
            }
            var responseCleaned = CleanMessage(response);
            var messageType = ExtractMessageType(responseCleaned);

            if((int)MessageTypes.LoadTransferNotification != messageType)
                continue;

            result.Add(responseCleaned);
            //_responseList.Remove(response);
        }

        return result;
    }

    public ICollection<string> GetAllNotificationsAsString()
    {
        List<string> result = [];
        
        foreach (var response in _responseList.ToList())
        {
            if (string.IsNullOrWhiteSpace(response))
            {
                _responseList.Remove(response);
                continue;
            }
            
            var responseCleaned = CleanMessage(response);
            
            result.Add(responseCleaned);
            //_responseList.Remove(response);
        }

        return result;
    }
    
    private async Task<XmlDocument?> SendRequestAndGetResponseAsXmlDocument<TRequest>(BaseRequest<TRequest> requestObj)
    {
        var requestJsonString = requestObj.ToRequestBodyToXml();
        if (string.IsNullOrWhiteSpace(requestJsonString))
            throw new Exception("SendRequest: Request is empty");
        // Nachricht senden
        var messageBytes = Encoding.ASCII.GetBytes(requestJsonString);
        await _client.SendAsync(messageBytes, SocketFlags.None);
        
        _logger?.Information("Nachricht senden: {RequestJsonString}", requestJsonString);
        var missionIdFromRequest = requestObj.MissionID;
        var sessionIdFromRequest = requestObj.SessionID;
        // Antwort empfangen
        var tryCounter = 0;
        while (tryCounter++ < 10)
        {
            await Task.Delay(900);
            foreach (var response in _responseList.ToList())
            {
                if (string.IsNullOrWhiteSpace(response))
                {
                    _responseList.Remove(response);
                    continue;
                }
                var responseCleaned = CleanMessage(response);
                var messageType = ExtractMessageType(responseCleaned);
                var missionIdFromResponse = ExtractMissionId(responseCleaned);
                var sessionIdFromResponse = ExtractSessionId(responseCleaned);


                if (sessionIdFromRequest != sessionIdFromResponse)
                    continue;
                
                if (missionIdFromRequest != missionIdFromResponse)
                    continue;
                
                _logger?.Information("Antwort vom Server: {responseCleaned}", responseCleaned);
                _logger?.Information("MessageType: {messageType}", messageType);
                var doc = new XmlDocument();
                doc.LoadXml(responseCleaned);
                _responseList.Remove(response);
                return doc;
            }
        }
 
        _logger?.Information("Keine Antwort vom Server erhalten.");
        return null;
    }
    
    /*private async Task AcceptMessagesAsync()
    {
        while (!_cancellationTokenSource.Token.IsCancellationRequested)
        {
            try
            {
                var clientSocket = await _clientListener.AcceptAsync();
                _logger?.Information("Verbindung akzeptiert!");

                // Daten empfangen
                var buffer = new byte[1024];
                var bytesRead = await clientSocket.ReceiveAsync(buffer, SocketFlags.None);
                var receivedMessage = Encoding.ASCII.GetString(buffer, 0, bytesRead);
                _logger?.Information("Empfangene Nachricht: {receivedMessage}",receivedMessage);
            }
            catch (SocketException ex)
            {
                _logger?.Information("Socket-Fehler: {ex.Message}",ex.Message);
                break;
            }
        }
    }*/
    
    private async Task ReceiveMessagesAsync()
    {
        while (!_cancellationTokenSource.Token.IsCancellationRequested)
        {
            try
            {
                var buffer = new byte[1024];
                var stringBuilder = new StringBuilder();
                int bytesRead;
                do
                {
                    try
                    {
                        using var cancellationTokenSource = new CancellationTokenSource(300); // Timeout von 500 ms
                        bytesRead = await _client.ReceiveAsync(buffer, SocketFlags.None, cancellationTokenSource.Token);
                        if (bytesRead <= 0)
                            continue;

                        var responsePart = Encoding.ASCII.GetString(buffer, 0, bytesRead);
                        stringBuilder.Append(responsePart);
                    }
                    catch (OperationCanceledException)
                    {
                        break;
                    }
                } while (bytesRead > 0);
                
                var fullResponse = stringBuilder.ToString();
                if (string.IsNullOrWhiteSpace(fullResponse))
                    continue;
                
                _responseList.Add(fullResponse);
                _logger?.Information("Vollständige empfangene Nachricht: {FullResponse}", fullResponse);
            }
            catch (SocketException ex)
            {
                Console.WriteLine($"Socket-Fehler: {ex.Message}");
                _logger?.Error("Socket-Fehler: {ExMessage}", ex.Message);
                break;
            }
        }
    }
    
    public void Dispose()
    {
        _cancellationTokenSource.Cancel();
        Task.WaitAll(_receivingTask);
        _client.Shutdown(SocketShutdown.Both);
        _client.Dispose();
    }
    
    public void CloseConnections()
    {
        try
        {
            _cancellationTokenSource.Cancel();
        
            // Timeout für WaitAll hinzufügen, um Deadlocks zu vermeiden
            if (!Task.WaitAll(new[] { _receivingTask }, TimeSpan.FromSeconds(5)))
            {
                _logger?.Warning("ReceivingTask konnte nicht innerhalb von 5 Sekunden beendet werden");
            }
        
            if (_client.Connected)
            {
                _client.Shutdown(SocketShutdown.Both);
            }
        }
        catch (ObjectDisposedException)
        {
            // Socket bereits disposed - das ist ok
            _logger?.Information("Socket bereits disposed");
        }
        catch (SocketException ex)
        {
            _logger?.Warning("SocketException beim Schließen: {Message}", ex.Message);
        }
        catch (Exception ex)
        {
            _logger?.Error("Unerwarteter Fehler beim Schließen der Verbindung: {Message}", ex.Message);
        }
        finally
        {
            try
            {
                _client.Close();
            }
            catch (Exception ex)
            {
                _logger?.Error("Fehler beim Close() des Sockets: {Message}", ex.Message);
            }
        }

    }
    
    private static string CleanMessage(string input)
    {
        if (string.IsNullOrWhiteSpace(input))
            throw new ArgumentException("Input string cannot be null or empty", nameof(input));
        
        return input.TrimStart().TrimEnd().Replace("//","").Replace("##","");
    }
    private static int ExtractMessageType(string xml)
    {
        try
        {
            using var stringReader = new StringReader(xml);
            using var xmlReader = XmlReader.Create(stringReader);
            xmlReader.MoveToContent();
            xmlReader.ReadToDescendant("MessageType");
            return xmlReader.ReadElementContentAsInt();
        }
        catch
        {
            return 0;
        }
    }
    private static int? ExtractMissionId(string xml)
    {
        try
        {
            using var stringReader = new StringReader(xml);
            using var xmlReader = XmlReader.Create(stringReader);
            xmlReader.MoveToContent();
            xmlReader.ReadToDescendant("MissionID");
            return xmlReader.ReadElementContentAsInt();
        }
        catch
        {
            return null;
        }
    }
    private static int? ExtractSessionId(string xml)
    {
        try
        {
            using var stringReader = new StringReader(xml);
            using var xmlReader = XmlReader.Create(stringReader);
            xmlReader.MoveToContent();
            xmlReader.ReadToDescendant("SessionID");
            return xmlReader.ReadElementContentAsInt();
        }
        catch
        {
            return null;
        }
    }
}