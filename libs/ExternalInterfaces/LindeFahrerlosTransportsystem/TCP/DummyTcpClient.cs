using System.Xml;
using LindeFahrerlosTransportsystem.Requests.AlivenessCheck;
using LindeFahrerlosTransportsystem.Requests.BaseRequests;
using LindeFahrerlosTransportsystem.Requests.RobotStatus;

namespace LindeFahrerlosTransportsystem.TCP;

public class DummyTcpClient : ILindeTcpClient
{
    //private readonly Socket _clientListener;
    private readonly CancellationTokenSource _cancellationTokenSource = new();
    private List<string> _responseList = [];
    private ILoggerForLindeFahrerlosTransportSystemClient? _logger;
    public DummyTcpClient(ILoggerForLindeFahrerlosTransportSystemClient logger)
    {
        _logger = logger;
        _logger.Information("Dummy Server gestartet");
    }

    public bool IsConnected => true;
    
    public async Task<RobotStatusResponse?> SendRequestWithRobotStatusResponse<TRequest>(BaseRequest<TRequest> request)
    {
        return await Task.FromResult(new RobotStatusResponse()
        {
            DataFields = new RobotStatusResponseDatafields
            {
                TaskStatus = null,
                SafetyStatus = null,
                TrafficStatus = null,
                Comstatus = null,
                Location = null,
                ZoneNameIDLength = null,
                ZoneNameID = null,
                PositionID = null,
                StepTask = null,
                BatteryLevel = null,
                ErrorMessageLength = null,
                ErrorMessage = null
            },
            HeaderFields = GetHeaderFields(request)
        });
    }

    public static ILindeTcpClient Create(string serverIp, int serverPort, string localIp, int localPort,
        ILoggerForLindeFahrerlosTransportSystemClient logger)
    {
        return new DummyTcpClient(logger);  
    }

    public async Task<AlivenessCheckResponse?> SendRequestWithAlivenessCheckResponse<TRequest>(
        BaseRequest<TRequest> request)
    {
        return await Task.FromResult(new AlivenessCheckResponse
        {
            DataFields = new AlivenessCheckDatafield
            {
                Count = 1
            },
            HeaderFields = GetHeaderFields(request)
        });
    }
    public async Task<BaseResponse?> SendRequest<TRequest>(BaseRequest<TRequest> request)
    {
        var responseObj = await Task.FromResult(new BaseResponse()
        {
            DataFields = new BaseResponseDatafield
            {
                Result = 1
            },
            HeaderFields = GetHeaderFields(request)
        });

        return responseObj;
    }

    public ICollection<XmlDocument> GetAllUpdateNotificationsAsXmlDocument()
    {
        throw new NotImplementedException();
    }

    private HeaderFields GetHeaderFields<TRequest>(BaseRequest<TRequest> request)
    {
        return new HeaderFields
        {
            SessionID = request.SessionID,
            MessageType = 000,
            MessageID = request.MessageID,
            MissionID = request.MissionID,
            Timestamp = DateTime.Now.ToString("yyyyMMddHHmmss"),
        };
    }

    public ICollection<string> GetAllNotificationsAsString()
    {
        throw new NotImplementedException();
    }

    public void CloseConnections()
    {
    }

    public void Dispose()
    {
    }


    public ICollection<string> GetAllUpdateNotificationsAsString()
    {
        return
        [
            "<informations><dataFields><ItemID>157</ItemID><ResultCode>5</ResultCode><ActualLocationID>WEP</ActualLocationID><PickLocationID>WEP</PickLocationID><RobotID>1</RobotID></dataFields><headerFields><MessageID>2233</MessageID><Timestamp>20250708101530</Timestamp><MessageType>204</MessageType><MissionID>143007635</MissionID><SessionID>1</SessionID></headerFields></informations>",
            "<informations><dataFields><ItemID>157</ItemID><ResultCode>5</ResultCode><ActualLocationID>WEP</ActualLocationID><PickLocationID>WEP</PickLocationID><RobotID>1</RobotID></dataFields><headerFields><MessageID>2233</MessageID><Timestamp>20250708101530</Timestamp><MessageType>204</MessageType><MissionID>143007717</MissionID><SessionID>1</SessionID></headerFields></informations>"
        ];
    }



}