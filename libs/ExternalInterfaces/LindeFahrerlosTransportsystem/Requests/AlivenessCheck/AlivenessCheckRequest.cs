using LindeFahrerlosTransportsystem.Requests.BaseRequests;

namespace LindeFahrerlosTransportsystem.Requests.AlivenessCheck;

public record AlivenessCheckRequest : BaseRequest<AlivenessCheckRequestBody>
{
    protected override int MessageType => (int)MessageTypes.AlivenessCheck;
    public int Counter { get; set; } = 1;
    public override Information<AlivenessCheckRequestBody> ToRequestBody()
    {
        StartRequest = DateTime.Now;
        
        return new Information<AlivenessCheckRequestBody> { 
            HeaderFields = Header()
            ,DataFields = new AlivenessCheckRequestBody()
            {
                Counter = Counter
            },
            TaskName = "AlivenessCheckRequest"
        };
    }
}