using System.Xml;
using System.Xml.Serialization;
using LindeFahrerlosTransportsystem.Requests.BaseRequests;

namespace LindeFahrerlosTransportsystem.Requests.AlivenessCheck;

[XmlRoot(ElementName = "informations")]
public record AlivenessCheckResponse : Information<AlivenessCheckDatafield>;

public record AlivenessCheckDatafield
{
    public int Count { get; set; }
    
    public static AlivenessCheckDatafield CreateFromXmlDocument(XmlDocument doc)
    {
        return new AlivenessCheckDatafield
        {
            Count = Convert.ToInt32(doc.SelectSingleNode("//dataFields/Counter")?.InnerText ?? "0")
        };
    }
}