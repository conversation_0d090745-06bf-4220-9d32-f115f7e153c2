namespace LindeFahrerlosTransportsystem.Requests.NewLoadTransfer;

public class NewLoadTransferResponse
{
    private const int Success = 0x00; // Erfolg – Erfolg – Aufgabenanfrage wurde durch den RM angenommen und kann einem verfügbaren Roboter zugewiesen werden
    private const int SuccessInQueue = 0x01; // Erfolg – Aufgabenanfrage wurde durch den RM angenommen und kann für eine spätere Zuweisung in die Warteschlange gestellt werden
    private const int InvalidTimestamp = 0x17; // ungültiger Zeitstempel in der Anfrage
    private const int InvalidSessionId = 0x18; // ungültige SessionID in der Anfrage
    private const int InvalidMissionId = 0x19; // ungültige MissionID in der Anfrage
    private const int InvalidFromLocation = 0x20; // ungültiger Startort in der Anfrage
    private const int InvalidToLocation = 0x21; // ungültiger Zielort in der Anfrage
    private const int InvalidLoadId = 0x22; // ungültige LoadID in der Anfrage
    private const int NoConnectionBetweenFromAndTo = 0x23; // ungültiger Weg in der Anfrage
    private const int NoAvailAgv = 0x24; //Not allowed to queue
    private const int ParsingError = 0x254; // Fehler beim Parsen der eingehenden Nachricht
    private const int OtherError = 0xFF; // sonstige Situation
    
    public static bool IsSuccess(int code)
    {
        return code == Success || code == SuccessInQueue;
    }
    
    public static string GetCodeMessage(int code)
    {
        return code switch
        {
            Success => "Aufgabenanfrage wurde durch den RM angenommen und kann einem verfügbaren Roboter zugewiesen werden",
            SuccessInQueue => "Aufgabenanfrage wurde durch den RM angenommen und kann für eine spätere Zuweisung in die Warteschlange gestellt werden",
            InvalidFromLocation => "ungültiger Startort in der Anfrage",
            InvalidToLocation => "ungültiger Zielort in der Anfrage",
            InvalidLoadId => "ungültige LoadID in der Anfrage",
            NoConnectionBetweenFromAndTo => "ungültiger Weg in der Anfrage",
            InvalidTimestamp => "ungültiger Zeitstempel in der Anfrage",
            InvalidSessionId => "ungültige SessionID in der Anfrage",
            InvalidMissionId => "ungültige MissionID in der Anfrage",
            NoAvailAgv => "Not allowed to queue",
            ParsingError => "Fehler beim Parsen der eingehenden Nachricht",
            OtherError => "sonstige Situation",
            _ => "Unbekannter Fehlercode"
        };
    }
}