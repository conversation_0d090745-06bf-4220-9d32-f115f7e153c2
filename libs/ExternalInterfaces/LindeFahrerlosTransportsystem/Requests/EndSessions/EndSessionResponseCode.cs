namespace LindeFahrerlosTransportsystem.Requests.EndSessions;

public static class EndSessionResponseCode
{
    private const int Success = 0; // wenn der RM die Anfrage zum Beenden einer Session annimmt
    private const int Error = 16; //  wenn der RM die Anfrage ablehnt

    public static bool IsSuccess(int code)
    {
        return code == Success;
    }
    
    public static string GetCodeMessage(int code)
    {
        return code switch
        {
            Success => "Session wird beendet",
            Error => "Anfrage wurde abgelehnt",
            _ => "Unbekannter Fehlercode"
        };
    }
}