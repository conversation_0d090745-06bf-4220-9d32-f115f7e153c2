using LindeFahrerlosTransportsystem.Requests.BaseRequests;

namespace LindeFahrerlosTransportsystem.Requests.EndSessions;

public record EndSessionRequest : BaseRequest<EndSessionRequestBody>
{
    protected override int MessageType => (int)MessageTypes.EndSession;

    public override Information<EndSessionRequestBody> ToRequestBody()
    {
        StartRequest = DateTime.Now;

        return new Information<EndSessionRequestBody>
        { 
            HeaderFields = Header(),
            DataFields = null,
            TaskName = "EndSessionRequest"
        };
    }
}