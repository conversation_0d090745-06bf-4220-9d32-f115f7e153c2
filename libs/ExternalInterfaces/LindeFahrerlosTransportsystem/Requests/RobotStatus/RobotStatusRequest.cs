using LindeFahrerlosTransportsystem.Requests.BaseRequests;

namespace LindeFahrerlosTransportsystem.Requests.RobotStatus;

public record RobotStatusRequest(string RobotID) : BaseRequest<RobotStatusRequestBody>
{
    protected override int MessageType => (int)MessageTypes.RobotStatusRequest;
    
    public override Information<RobotStatusRequestBody> ToRequestBody()
    {
        StartRequest = DateTime.Now;
        return new Information<RobotStatusRequestBody>()
        {
            HeaderFields = Header(),
            DataFields = new RobotStatusRequestBody(RobotID.Length,RobotID),
            TaskName = "RobotStatusRequest"
        };

    }
}