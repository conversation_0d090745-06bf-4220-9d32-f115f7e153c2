namespace LindeFahrerlosTransportsystem.Requests.StartSessions;

public static class StartSessionResponseCode
{
    private const int Success = 0x00; // Erfolg – Session konnte durch den RM gestartet werden
    private const int UnsupportedProtocolVersion = 0x16; // <PERSON>hler – Client hat nicht unterstützte Protokollversion angefordert
    private const int InvalidTimestamp = 0x17; // Fehler – ungültiger Zeitstempel in der Anfrage
    private const int InvalidSessionId = 0x18; // <PERSON><PERSON> – ungültige SessionID in der Anfrage
    private const int ParsingError = 0x254; // <PERSON><PERSON> – <PERSON><PERSON> beim Parsen der eingehenden Nachricht
    private const int OtherError = 0xFF; // Fehler – sonstige Situation
    
    public static bool IsSuccess(int code)
    {
        return code == Success;
    }
    
    public static string GetCodeMessage(int code)
    {
        return code switch
        {
            Success => "Session konnte durch den RM gestartet werden",
            InvalidTimestamp => "Ungültiger Zeitstempel in der Anfrage",
            InvalidSessionId => "Ungültige SessionID in der Anfrage",
            ParsingError => "Fehler beim Parsen der eingehenden Nachricht",
            UnsupportedProtocolVersion => "Client hat nicht unterstützte Protokollversion angefordert",
            OtherError => "Sonstige Situation",
            _ => "Unbekannter Fehlercode"
        };
    }
}