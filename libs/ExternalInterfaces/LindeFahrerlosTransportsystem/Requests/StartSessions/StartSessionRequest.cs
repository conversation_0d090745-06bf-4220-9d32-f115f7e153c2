using System.Xml.Serialization;
using LindeFahrerlosTransportsystem.Requests.BaseRequests;
using Microsoft.VisualBasic;

namespace LindeFahrerlosTransportsystem.Requests.StartSessions; 

public record StartSessionRequest : BaseRequest<StartSessionRequestBody>
{
    protected override int MessageType => (int)MessageTypes.StartSession;
    
    public override Information<StartSessionRequestBody> ToRequestBody()
    {
        StartRequest = DateTime.Now;
        
        return new Information<StartSessionRequestBody> { 
             HeaderFields = Header()
            ,DataFields = new StartSessionRequestBody()
                {
                    Timeout = TimeOutInMs,
                    ProtocolVersion = ProtocolVersion
                },
            TaskName = "StartSessionRequest"
        };
    }
    
    public int GetSessionId()
    {
        return SessionID;
    }
}
