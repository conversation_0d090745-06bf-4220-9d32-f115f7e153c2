namespace LindeFahrerlosTransportsystem.Requests.UpdateLoadTransfer;

public record UpdateLoadTransferResponseCode
{
    private const int Success = 0x00; // Anfrage zur Aktualisierung der Aufgabe durch den RM angenommen
    private const int InvalidPick = 0x20; // Aufgabenanfrage enthält eine ungültige Entnahmeposition
    private const int InvalidDrop = 0x21; // Aufgabenanfrage enthält eine ungültige Ablegeposition
    private const int InvalidItemId = 0x22; // Aufgabenanfrage enthält eine ungültige Item-ID
    private const int InvalidItinerary = 0x23; // kein Weg zur Verbindung der angefragten Entnahme- und Ablegepositionen verfügbar
    private const int FailCantReachPick = 0x25; // Entnahmeposition nicht erreichbar
    private const int FailCantReachDrop = 0x26; // Ablegeposition nicht erreichbar
    private const int FailModLocAfterPick = 0x27; //Die Location kann nicht geändert werden, wenn die Aufgabe bereits einem FTS zugewiesen wurde
    private const int FailModItemAfterPick = 0x28; // Die ItemId kann nicht geändert werden, wenn die Aufgabe bereits einem FTS zugewiesen wurde
    private const int InvalidTimestamp = 0x17; // ungültiger Zeitstempel in der Anfrage
    private const int InvalidSessionId = 0x18; // ungültige SessionID in der Anfrage
    private const int InvalidMissionId = 0x19; // ungültige MissionID in der Anfrage
    private const int ParsingError = 0x254; // Fehler beim Parsen der eingehenden Nachricht
    private const int OtherError = 0xFF; // sonstige Situation
    
    
    public static bool IsSuccess(int code)
    {
        return code == Success;
    }

    public static string GetCodeMessage(int code)
    {
        return code switch
        {
            Success => "Anfrage zur Aktualisierung der Aufgabe durch den RM angenommen",
            InvalidPick => "Aufgabenanfrage enthält eine ungültige Entnahmeposition",
            InvalidDrop => "Aufgabenanfrage enthält eine ungültige Ablegeposition",
            InvalidItemId => "Aufgabenanfrage enthält eine ungültige Item-ID",
            InvalidItinerary => "kein Weg zur Verbindung der angefragten Entnahme- und Ablegepositionen verfügbar",
            FailCantReachPick => "Entnahmeposition nicht erreichbar",
            FailCantReachDrop => "Ablegeposition nicht erreichbar",
            FailModLocAfterPick => "Die Location kann nicht geändert werden, wenn die Aufgabe bereits einem FTS zugewiesen wurde",
            FailModItemAfterPick => "Die Entnahmeposition kann nicht geändert werden, wenn die Aufgabe bereits einem FTS zugewiesen wurde",
            InvalidTimestamp => "ungültiger Zeitstempel in der Anfrage",
            InvalidSessionId => "ungültige SessionID in der Anfrage",
            InvalidMissionId => "ungültige MissionID in der Anfrage",
            ParsingError => "Fehler beim Parsen der eingehenden Nachricht",
            OtherError => "sonstige Situation",
            _ => "Unbekannter Fehlercode"
        };
    }
}