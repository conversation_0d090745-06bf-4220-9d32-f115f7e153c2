using System.Xml.Serialization;

namespace LindeFahrerlosTransportsystem.Requests.BaseRequests;
[XmlRoot(ElementName = "informations")]
public record  Information<T>
{
    [XmlElement(ElementName = "headerfields")]
    public HeaderFields HeaderFields { get; set; } = null!;
    [XmlElement(ElementName = "datafields")]
    public virtual T? DataFields { get; set; }
    [XmlElement(ElementName = "taskName")]
    public string? TaskName { get; set; }
}
