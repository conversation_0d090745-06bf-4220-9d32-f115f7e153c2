namespace LindeFahrerlosTransportsystem.Requests.CancleLoadTransfer;

public static class CancelLoadTransferResponseCode
{
    private const int Success = 0x00; //Erfolg – Anfrage für eine Stornierung wurde durch den RM akzeptiert
    private const int InvalidTimestamp = 0x17; //Fehler – ungültiger Zeitstempel in der Anfrage
    private const int InvalidSessionId = 0x18; //Fehler – ungültige SessionID in der Anfrage
    private const int InvalidMissId = 0x19;     //Fehler - ungültige MissionID in der Anfrage
    private const int FailAlreadyPick = 0x29;   //Fehler - die Ladung wurde bereits aufgehoben
    private const int ParsingError = 0x254; //Fehler – Fehler beim Parsen der eingehenden Nachricht
    private const int OtherError = 0xFF; //Fehler – sonstige Situation
    
    public static bool IsSuccess(int code)
    {
        return code == Success;
    }
    
    public static string GetCodeMessage(int code)
    {
        return code switch
        {
            Success => "Anfrage für eine Stornierung wurde durch den RM akzeptiert",
            InvalidTimestamp => "Ungültiger Zeitstempel in der Anfrage",
            InvalidSessionId => "Ungültige SessionID in der Anfrage",
            InvalidMissId => "Ungültige MissionID in der Anfrage",
            FailAlreadyPick => "Die Ladung wurde bereits aufgehoben",
            ParsingError => "Fehler beim Parsen der eingehenden Nachricht",
            OtherError => "Sonstige Situation",
            _ => "Unbekannter Fehlercode"
        };
    }
}