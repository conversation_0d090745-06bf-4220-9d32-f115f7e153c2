namespace LindeFahrerlosTransportsystem.Requests.LoadTransferNotification;

public class LoadTransferNotificationResultCode
{
    public const int SuccessDropAuto = 0x00; //Erfolg - Last wurde automatisch an den Zielort transportiert
    public const int SuccessDropManu = 0x01; //Erfolg - Last wurde an den Zielort transportiert mit manueller Validierung
    public const int SuccessOnAlternativEndposition = 0x02; //Erfolg - die Last wurde an eine alternative Endposition transportiert
    public const int SuccessUnknownEndpositionWithManualValidation = 0x03; //Erfolg - die Last wurde an eine unbekannte Endposition transportiert, mit manueller Validierung
    public const int SuccessPickAuto = 0x04; //Erfolg - die Last wurde automatisch aufgehoben
    public const int SuccessPickManu = 0x05; //Erfolg - die Last wurde aufgehoben, mit manueller Validierung
    public const int SuccessPickAnom = 0x06; //Erfolg - die Last wurde anonym aufgehoben
    public const int SuccessPickUnknownLoc = 0x07; //Erfolg - die Last wurde von einer unbekannten Position aufgehoben
    public const int ErrorPick = 0x08; //Erfolg - Fehler beim Aufheben der Last
    public const int ErrorDrop = 0x09; //Erfolg - Fehler beim Abladen der Last
    public const int InvalidTimestamp = 0x17; //Fehler - ungültiger Zeitstempel in der Anfrage
    public const int InvalidSessionId = 0x18; //Fehler - ungültige SessionID in der Anfrage
    public const int InvalidMissId = 0x19; //Fehler - ungültige MissionID in der Anfrage
    public const int CancelNoLoad = 0x30; //Last nicht an der Abholposition verfügbar
    public const int CancelPickErr = 0x31; //Last nicht ladbar
    public const int CancelledByWms = 0x32; //Entnahme abgebrochen durch WMS
    public const int CancelledByRm = 0x33; //Entnahme abgebrochen durch RM
    public const int CancelledByRobotHmi = 0x34; //Entnahme abgebrochen durch Robot HMI
    public const int Unknown = 0xFF; //Unbekannter Fehler

    public static string GetCodeMessage(int code)
    {
        return code switch
        {
            SuccessDropAuto => "Last wird automatisch an den Zielort transportiert",
            SuccessDropManu => "Last wurde an den Zielort transportiert mit manueller Validierung",
            SuccessOnAlternativEndposition => "Erfolg - die Last wurde an eine alternative Endposition transportiert",
            SuccessUnknownEndpositionWithManualValidation => "Last wurde an eine unbekannte Endposition transportiert, mit manueller Validierung",
            SuccessPickAuto => "Die Last wurde automatisch aufgehoben",
            SuccessPickManu => "Die Last wurde aufgehoben, mit manueller Validierung",
            SuccessPickAnom => "Die Last wurde anonym aufgehoben",
            SuccessPickUnknownLoc => "Die Last wurde von einer unbekannten Position aufgehoben",
            ErrorPick => "Fehler beim Aufheben der Last",
            ErrorDrop => "Fehler beim Abladen der Last",
            InvalidTimestamp => "Fehler - ungültiger Zeitstempel in der Anfrage",
            InvalidSessionId => "Fehler - ungültige SessionID in der Anfrage",
            InvalidMissId => "Fehler - ungültige MissionID in der Anfrage",
            CancelNoLoad => "Last nicht an der Abholposition verfügbar",
            CancelPickErr => "Last nicht ladbar",
            CancelledByWms => "Entnahme abgebrochen durch WMS",
            CancelledByRm => "Entnahme abgebrochen durch RM",
            CancelledByRobotHmi => "Entnahme abgebrochen durch Robot HMI",
            Unknown => "Unbekannter Fehler",
            _ => "Unbekannter Fehlercode"
        };
    }
}