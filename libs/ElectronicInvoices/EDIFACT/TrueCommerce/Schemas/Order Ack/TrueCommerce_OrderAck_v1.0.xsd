<?xml version="1.0" encoding="UTF-8"?>
<!-- edited with XMLSpy v2015 rel. 3 sp1 (x64) (http://www.altova.com) by <PERSON> (<PERSON><PERSON><PERSON><PERSON>) -->
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:tc="http://www.truecommerce.com/docs/orderAck" xmlns:cm="http://www.truecommerce.com/docs/common/components" targetNamespace="http://www.truecommerce.com/docs/orderAck" elementFormDefault="unqualified" attributeFormDefault="unqualified">
	<xs:import namespace="http://www.truecommerce.com/docs/common/components" schemaLocation="truecommerce_components_v1.0.xsd"/>
	<xs:element name="TrueCommerceOrderAck">
		<xs:annotation>
			<xs:documentation>TrueCommerce Order Acknowledgement spcification</xs:documentation>
		</xs:annotation>
		<xs:complexType>
			<xs:sequence>
				<xs:element name="MsgHeader" type="cm:MessageHeader"/>
				<xs:element name="Document" type="tc:OrderAckDoc" maxOccurs="unbounded"/>
				<xs:element name="MsgTrailer" type="cm:MessageTrailer"/>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<xs:complexType name="OrderAckDoc">
		<xs:annotation>
			<xs:documentation>Order Acknowledgement document</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="DocHeader" type="cm:DocumentHeader"/>
			<xs:element name="AckHeader" type="tc:OrderAckHeader">
				<xs:annotation>
					<xs:documentation>Order Acknowledgement header.  One per order</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="AckLine" type="tc:OrderAckLine" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>Acknowledgement line.  Only changes to the original order lines need be given here.  If the order is being acknowledged without change then no lines will be provided.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DocTrailer" type="cm:DocumentTrailer"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="OrderAckHeader">
		<xs:annotation>
			<xs:documentation>Acknowledgement header</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="AckCode">
				<xs:annotation>
					<xs:documentation>Acknowledgement Type: ACCEPTED, ACCEPTED_WITH_CHANGES,    REJECTED, ACCEPTED_WITH_PREVIOUS_CHANGES, ACCEPTED_BY_LINE</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:enumeration value="ACCEPTED"/>
						<xs:enumeration value="ACCEPTED_WITH_CHANGES"/>
						<xs:enumeration value="REJECTED"/>
						<xs:enumeration value="ACCEPTED_WITH_PREVIOUS_CHANGES"/>
						<xs:enumeration value="ACCEPTED_BY_LINE"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="Ack_Date" type="cm:DateTime" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Date and time of acknowledgement</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="CustOrder" type="xs:string">
				<xs:annotation>
					<xs:documentation>Purchase order number of the customer (aka buyer)</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="CustOrderDate" type="cm:DateTime" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Date order placed by customer</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="SuppOrder" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Purchase order number of the supplier (aka vendor)</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="SuppOrderDate" type="cm:DateTime" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Date order received by supplier</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="SalesChannel" type="xs:string" minOccurs="0"/>
			<xs:element name="Salesman" type="xs:string" minOccurs="0"/>
			<xs:element name="Perishable" type="xs:boolean" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Is the order for perishable items?  Defaults to false</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="BookingRef" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Booking Reference, (aka Transport Delivery Number)</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Expeditor" type="cm:Contact" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Buyer name</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="CommonRef" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Common Reference number (aka Customer Reference)</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="OrigCustOrder" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Order number reference of the originating customer</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ThirdPartyOrderRef" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Order number reference supplied by a third-party.  This is applicable in situations where a 3rd Party is selling goods on behalf of the TrueCommerce Customer.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Contract" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Contract number to which this order relates.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Promotion" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Promotional discount code</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="SpecNo" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Specification number to which this order relates.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Delivery" type="tc:DeliveryInfo" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Delivery information</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Locations" type="cm:Locations" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Location Addresses.  Note: Customer and Supplier don't exist here because they are stored within the Document Header section.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Notes" type="cm:Notes" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>General notes pertaining to the order</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Revision" type="cm:RevisionDetails" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Revision Details</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="SettlementTerms" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>Settlement terms agreed between the buyer and seller, expressed in terms of payment due date/days and percentage.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="TermsOfPayment" type="xs:string" minOccurs="0">
							<xs:annotation>
								<xs:documentation>Text for Terms of Payment.</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="SettlementDate" type="xs:int" minOccurs="0">
							<xs:annotation>
								<xs:documentation>Date that payment should be made available to the payee to obtain the specified discount.</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="SettlementDays" type="xs:long" minOccurs="0">
							<xs:annotation>
								<xs:documentation>Days until payment is due is defined as the number of calendar days after a reference date, commonly the date of invoice, that the payment is to be made availble to the payee.</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="Percentage" minOccurs="0">
							<xs:annotation>
								<xs:documentation>Percentage discount applicable</xs:documentation>
							</xs:annotation>
							<xs:simpleType>
								<xs:restriction base="xs:decimal">
									<xs:minInclusive value="-999.99"/>
									<xs:maxInclusive value="999.99"/>
									<xs:totalDigits value="5"/>
									<xs:fractionDigits value="2"/>
								</xs:restriction>
							</xs:simpleType>
						</xs:element>
						<xs:element name="Code" type="xs:string" minOccurs="0">
							<xs:annotation>
								<xs:documentation>Code for Settlement Terms</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="TotalOrderWeight" type="cm:Weight" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Total goods weight of the order</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="TotalOrderVolume" type="cm:Volume" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Total goods volume of the order</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="TotalOrderUnits" type="xs:int" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Total number of units ordered across all lines</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="TotalOrderVal" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Total order value, excl. VAT</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:decimal">
						<xs:minInclusive value="-999999999999.9999999"/>
						<xs:maxInclusive value="999999999999.9999999"/>
						<xs:totalDigits value="19"/>
						<xs:fractionDigits value="7"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="PricingDate" type="cm:DateTime" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Date the order was priced</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Additional" type="cm:AdditionalInfo" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Additional reference information for the generic document.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="OrderAckLine">
		<xs:annotation>
			<xs:documentation>Acknowledgement line</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="LineNo" type="xs:int" minOccurs="0">
				<xs:annotation>
					<xs:documentation>order line number</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="LineCode" default="CHANGED" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Dictates what this acknowledgement represents  with reference to the correponding order line. A supplier can create a new line, change an existing line, or cancel a line.  Valid values: (NEW, CHANGED, CANCELLATION,ACCEPTED).  Defaults to CHANGED</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:enumeration value="NEW"/>
						<xs:enumeration value="CHANGED"/>
						<xs:enumeration value="CANCELLATION"/>
						<xs:enumeration value="ACCEPTED"/>
						<xs:enumeration value="ACK_CHG"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="OriginalRequirements" type="tc:OrderLineDetails" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Documents the original requirements as given by the customer on the order</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="SupplierOverrides" type="tc:OrdAckLineDetails" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Documents the amendments the supplier wishes to make to the order line.  This information is not required if the line is being cancelled.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="OutstandingBalance" type="tc:OutstandingBalance" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Current outstanding balance according to the suppliers records at a given date.  Only applicable if the supplier has already started shipping against the line</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Narrative" type="cm:Narrative" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Narrative pertaining to the acknowledgement line</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Additional" type="cm:AdditionalInfo" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Additional reference information for the generic document.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="DeliveryInfo">
		<xs:annotation>
			<xs:documentation>Delivery details</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="DelMethod" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Delivery Method: BRANCH_COLLECTION, DELIVER_TO_BRANCH, DELIVER_TO_DEPOT, DIRECT_DELIVERY,
CUSTOMER_COLLECTION</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:pattern value="BRANCH_COLLECTION"/>
						<xs:pattern value="DELIVER_TO_BRANCH"/>
						<xs:pattern value="DELIVER_TO_DEPOT"/>
						<xs:pattern value="DIRECT_DELIVERY"/>
						<xs:pattern value="CUSTOMER_COLLECTION"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="PayMethod" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Payment method:        CUSTOMER_PAYS,  SUPPLIER_PAYS,  3RD_PARTY_PAYS,  NO_CHARGE</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:pattern value="CUSTOMER_PAYS"/>
						<xs:pattern value="SUPPLIER_PAYS"/>
						<xs:pattern value="3RD_PARTY_PAYS"/>
						<xs:pattern value="NO_CHARGE"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="ReqDesp" type="cm:DateTime" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Required despatch date/time from the ship-from location</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="EarliestDesp" type="cm:DateTime" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Earliest despatch (shipment) date/time from the supplier location</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="LatestDesp" type="cm:DateTime" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Latest despatch (shipment) date/time from the supplier location</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ReqDel" type="cm:DateTime" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Required delivery date/time at the deliver-to location</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="EarliestDel" type="cm:DateTime" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Earliest delivery date/time at the deliver-to location</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="LatestDel" type="cm:DateTime" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Latest delivery date/time at the deliver-to location</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ReqPickUp" type="cm:DateTime" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Required pickup (collection) date/time.  Used when the delivery method is CUSTOMER_COLLECTION</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DelPoint" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Delivery point</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DelDock" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Delivery dock</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DelGate" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Delivery gate</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DelPriority" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Delivery Priority.  A textual description of the priority</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="AETC" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Authorised Excess Transportation Cost (AETC) code. </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DelUnits" type="xs:long" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Number of delivery units (cases, cartons, packages) transported</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="TareWeight" type="cm:Weight" minOccurs="0">
				<xs:annotation>
					<xs:documentation>This is the weight of the unladen vehicle, i.e. the weight you will use to calculate the vehicle's load.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="GoodsWeight" type="cm:Weight" minOccurs="0">
				<xs:annotation>
					<xs:documentation>This is the weight of the load</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="TotalWeight" type="cm:Weight" minOccurs="0">
				<xs:annotation>
					<xs:documentation>This is the weight of the vehicle fully laden, and is an indication of the total weight of everything on the bridge.
Total = tare + goods</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="JourneyRef" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Reference number for the journey</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="JourneyDesc" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Textual description of the journey route</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DespatchFrom" type="cm:Address" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Full address of the despatch (ship-from) location</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DeliverTo" type="cm:Address" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Full address of the deliver-to location.  This will usually match the ordering branch address, except for direct deliveries, or depot deliveries</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Carrier" type="cm:Address" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Location details of any carrier used.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="SecondaryShipLoc" type="cm:Address" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Secondary or trans-shipment location</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Instructions" type="cm:Narrative" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Delivery Instruction notes</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DelCombined" type="xs:boolean" default="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Allow order to be delivered combined with other orders. Defaults to true</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Additional" type="cm:AdditionalInfo" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Additional reference information for the generic document.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="OrderLineDetails">
		<xs:annotation>
			<xs:documentation>Order line details (quantities, item numbers, etc).  A clone of OrderLine without the order no, line no, and line status</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="CommonRef" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Common Reference number (aka Customer Reference)</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Item" type="cm:ItemDetails" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Item details</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Perishable" type="xs:boolean" default="false" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Is the order line for perishable items?  Defaults to false</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="UnitOfOrder" type="cm:UnitOfOrderQty" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Describes what units are being ordered within the qty_ordered field. The qty ordered may be 100, but this is 100 x 24 cans.  The 24 cans is described within this field.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="OrderQty" type="cm:Quantity" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Describes what quantities are being ordered, relative to the ordering units.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="PricingMeasure" type="cm:PricingMeasure" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Describes the mechanism for pricing the invoice line</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="CostPrice" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Cost price, before deducting line discounts, etc (excluding VAT) per  pricing measure</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:decimal">
						<xs:minInclusive value="-999999999999.9999999"/>
						<xs:maxInclusive value="999999999999.9999999"/>
						<xs:totalDigits value="19"/>
						<xs:fractionDigits value="7"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="LineAmount" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Net cost of detail line, including any line discounts, etc (excluding VAT) of the order line</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:decimal">
						<xs:minInclusive value="-999999999999.9999999"/>
						<xs:maxInclusive value="999999999999.9999999"/>
						<xs:totalDigits value="19"/>
						<xs:fractionDigits value="7"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="SellPrice" type="cm:Price" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Selling price (before the application of VAT) of a traded unit or measure.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Special" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Special price flag EXPORT, FREE, PROMOTIONAL</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:pattern value="EXPORT"/>
						<xs:pattern value="FREE"/>
						<xs:pattern value="PROMOTIONAL"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="Notes" type="cm:Notes" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>General order line notes</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ProdVar" type="tc:OrderLineProdVar" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>Product Variances</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Delivery" type="tc:DeliveryLineInfo" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>Delivery information When, and how much to deliver</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DiscountsAndCharges" type="tc:DiscountOrCharge" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>Discount and charge details</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="OrderType" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Holds the order type information, as supplied by the customer ERP system </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Contract" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Contract number to which this order line relates.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ContractLine" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Contract Line number to which this order line relates.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="SpecNo" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Specification number to which this order line relates.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="OrderBranch" type="cm:Address" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Full address of the ordering branch.  </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DespatchFrom" type="cm:Address" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Full address of the despatch (ship-from) location</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DeliverTo" type="cm:Address" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Full address of the deliver-to location.  This will usually match the ordering branch address, except for direct deliveries, or depot deliveries. </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Tax" type="cm:Tax" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Provides tax information for the line</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="GoodsWeight" type="cm:Weight" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Goods weight of the line</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="UserDefined" type="cm:UserDefined" minOccurs="0">
				<xs:annotation>
					<xs:documentation>User defined fields</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Additional" type="cm:AdditionalInfo" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Additional reference information for the generic document.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="DiscountOrCharge">
		<xs:annotation>
			<xs:documentation>Complex order line adjustments</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="DiscountOrCharge" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Discount or Charge indicator. D=Discount, C=Charge</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Type" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>A=Special, B=Basic, Q=Quantity, V=Value</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="AccumulationRules" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Accumulation Rules. G = Gross, N = Nett</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Percentage" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Actual Adjustment Percentage</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:decimal">
						<xs:minInclusive value="-999.99"/>
						<xs:maxInclusive value="999.99"/>
						<xs:totalDigits value="5"/>
						<xs:fractionDigits value="2"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="Amount" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Actual Adjustment Amount</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:decimal">
						<xs:minInclusive value="-999999999999.9999999"/>
						<xs:maxInclusive value="999999999999.9999999"/>
						<xs:totalDigits value="19"/>
						<xs:fractionDigits value="7"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="OrderLineProdVar">
		<xs:annotation>
			<xs:documentation>Order Line Product Variant</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="Seq" type="xs:long" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Sequence number</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Type">
				<xs:annotation>
					<xs:documentation>Variant type code (registered with ANA)</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:maxLength value="3"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="Detail">
				<xs:annotation>
					<xs:documentation>Variable data:  Ie. size, colour</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:maxLength value="40"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="OrderQty" type="cm:Quantity" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Describes what quantities are being ordered, relative to the ordering units.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Additional" type="cm:AdditionalInfo" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Additional reference information for the generic document.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="DeliveryLineInfo">
		<xs:annotation>
			<xs:documentation>Delivery Line Information (quantities, etc)</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="ReqDesp" type="cm:DateTime" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Required despatch date/time from the ship-from location</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ReqDel" type="cm:DateTime" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Required delivery date/time at the deliver-to location</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ReqPickUp" type="cm:DateTime" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Required pickup (collection) date/time.  Used when the delivery method is CUSTOMER_COLLECTION</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ReqQty" type="cm:Quantity" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Required quantity to be delivered.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Notes" type="cm:Notes" minOccurs="0"/>
			<xs:element name="DelPoint" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Delivery point</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DelGate" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Delivery gate</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DelDock" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Delivery dock</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DelPriority" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Delivery Priority.  A textual description of the priority</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="GoodsWeight" type="cm:Weight" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Goods weight of the delivery line</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="LoadStructure" type="cm:LoadStructure" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Loading structure of the delivery line.  Number of cases per layer, and number of layers per pallet</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Additional" type="cm:AdditionalInfo" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Additional reference information for the generic document.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="OrdAckLineDetails">
		<xs:annotation>
			<xs:documentation>Order line detail overrides (Actuals used on order acknowledgement)</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="Item" type="cm:ItemDetails" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Substitute Item details</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Perishable" type="xs:boolean" default="false" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Is the order line for perishable items?  Defaults to false</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="UnitOfOrder" type="cm:UnitOfOrderQty" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Describes what units are being ordered. The qty ordered may be 100, but this is 100 x 24 cans.  The 24 cans is described within this field.  NOTE: This is only necessary for NEW lines being added by the supplier.  </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="AckQty" type="cm:Quantity" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Quantity acknowledged to be delivered by the supplier</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="PricingMeasure" type="cm:PricingMeasure" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Describes the mechanism for pricing the invoice line</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="CostPrice" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Cost price, before deducting line discounts, etc (excluding VAT) per  pricing measure</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:decimal">
						<xs:minInclusive value="-999999999999.9999999"/>
						<xs:maxInclusive value="999999999999.9999999"/>
						<xs:totalDigits value="19"/>
						<xs:fractionDigits value="7"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="LineAmount" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Net cost of detail line, including any line discounts, etc (excluding VAT) of the order line</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:decimal">
						<xs:minInclusive value="-999999999999.9999999"/>
						<xs:maxInclusive value="999999999999.9999999"/>
						<xs:totalDigits value="19"/>
						<xs:fractionDigits value="7"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="SellPrice" type="cm:Price" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Selling price (before the application of VAT) of a traded unit or measure.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Special" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Special price flag EXPORT, FREE, PROMOTIONAL</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:pattern value="EXPORT"/>
						<xs:pattern value="FREE"/>
						<xs:pattern value="PROMOTIONAL"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="Notes" type="cm:Notes" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>General order line notes</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ProdVar" type="tc:OrderLineProdVar" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>Product Variances</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Delivery" type="tc:DeliveryLineAck" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>Delivery information When, and how much is to be delivered</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DiscountsAndCharges" type="tc:DiscountOrCharge" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>Discount and charge details</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DespatchFrom" type="cm:Address" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Full address of the despatch (ship-from) location</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DeliverTo" type="cm:Address" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Full address of the deliver-to location.  This will usually match the ordering branch address, except for direct deliveries, or depot deliveries. </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Tax" type="cm:Tax" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Detail tax information for the line, as provided by the supplier</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="GoodsWeight" type="cm:Weight" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Goods weight of the line</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Additional" type="cm:AdditionalInfo" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Additional reference information for the generic document.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="DeliveryLineAck">
		<xs:annotation>
			<xs:documentation>Delivery line acknowledgement information (overrrides)</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="ExpDespDate" type="cm:DateTime" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Expected despatch date/time from the ship-from location</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ExpDelDate" type="cm:DateTime" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Expected delivery date/time at the deliver-to location</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ExpPickUp" type="cm:DateTime" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Expected pickup (collection) date/time.  Used when the delivery method is CUSTOMER_COLLECTION</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="QtyToDespatch" type="cm:Quantity" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Quantity to be despatch</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Notes" type="cm:Notes" minOccurs="0"/>
			<xs:element name="DelPoint" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Delivery point</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DelGate" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Delivery gate</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DelDock" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Delivery dock</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DelPriority" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Delivery Priority.  A textual description of the priority</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="GoodsWeight" type="cm:Weight" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Goods weight of the delivery line</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="LoadStructure" type="cm:LoadStructure" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Loading structure of the delivery line.  Number of cases per layer, and number of layers per pallet</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DelNoteNumber" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Delivery note number the delivery will be assigned to (if known at this stage)</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Additional" type="cm:AdditionalInfo" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Additional reference information for the generic document.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="OutstandingBalance">
		<xs:annotation>
			<xs:documentation>Current outstanding balance per supplier's records at a given date </xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="OutstandingBalance" type="cm:Quantity">
				<xs:annotation>
					<xs:documentation>The current outstanding balance according to the suppliers records</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="BalanceDate" type="cm:DateTime" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Date of balance provided by supplier</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Additional" type="cm:AdditionalInfo" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Additional reference information for the generic document.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="DespatchAdvice">
		<xs:annotation>
			<xs:documentation>Advice of goods despatched</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="Sequence1" type="xs:long">
				<xs:annotation>
					<xs:documentation>Corresponds to the acknowledgement line sequence</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Sequence2" type="xs:long">
				<xs:annotation>
					<xs:documentation>Starts at 1 and is increremented by 1 for each segment of this type in the message</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DeliveryQuantity" type="cm:UnitOfOrderQty"/>
			<xs:element name="DelNoteNumber" type="xs:string">
				<xs:annotation>
					<xs:documentation>Delivery Note Number</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DespatchDate" type="cm:DateTime"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="ExpectedDelivery">
		<xs:annotation>
			<xs:documentation>Expected delivery details</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="Sequence1" type="xs:long">
				<xs:annotation>
					<xs:documentation>Corresponds to the acknowledgement line sequence</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Sequence2" type="xs:long">
				<xs:annotation>
					<xs:documentation>Starts at 1 and is increremented by 1 for each segment of this type in the message</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ReqDelDate" type="cm:DateTime">
				<xs:annotation>
					<xs:documentation>Requested delivery date and time</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="RequiredQty" type="cm:UnitOfOrderQty"/>
			<xs:element name="ExpectedDelDate" type="cm:DateTime"/>
			<xs:element name="ExpectedQty" type="cm:UnitOfOrderQty"/>
		</xs:sequence>
	</xs:complexType>
</xs:schema>
