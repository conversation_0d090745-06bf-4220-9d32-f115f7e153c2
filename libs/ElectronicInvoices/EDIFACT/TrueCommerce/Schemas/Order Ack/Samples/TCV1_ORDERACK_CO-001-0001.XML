<?xml version="1.0" encoding="UTF-8"?>
<tc:TrueCommerceOrderAck xmlns:tc="http://www.truecommerce.com/docs/orderAck" xmlns:cm="http://www.truecommerce.com/docs/common/components" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.truecommerce.com/docs/orderAck truecommerce_orderack_v1.0.xsd">
	<MsgHeader>
		<MsgType>ORDER_ACK</MsgType>
		<VersionID>v1.0</VersionID>
		<TransmittedDate>
			<Date>2018-04-10</Date>
			<Time>15:55:49</Time>
		</TransmittedDate>
		<FileDate>
			<Date>2018-04-10</Date>
			<Time>15:55:49</Time>
		</FileDate>
		<Interchange>
			<Standard>TRUECOMMERCE</Standard>
			<SenderRef>2485</SenderRef>
		</Interchange>
	</MsgHeader>
	<Document>
		<DocHeader>
			<DocType>ORDER_ACK</DocType>
			<DocFunction>ORIGINAL</DocFunction>
			<CustAddr>
				<Code>00010</Code>
				<EAN13>5000020000115</EAN13>
				<Name>ABC Foods Limited</Name>
				<Address1>1 High Street</Address1>
				<Address5>London</Address5>
				<Country>GB</Country>
				<VAT>
					<Num>123-456-789-123</Num>
				</VAT>
				<Contact1>
					<Name>Carl Denver</Name>
					<Phone>010101 123 456</Phone>
					<Fax>010101 123 457</Fax>
				</Contact1>
				<Contact2>
					<Name>Elisabeth Francis</Name>
					<Phone>010101 123 458</Phone>
				</Contact2>
			</CustAddr>
			<SuppAddr>
				<Code>TC30816</Code>
				<EAN13>5024657121929</EAN13>
				<Name>DemoCo</Name>
				<Address1>DemoCo House</Address1>
				<Address2>100 High Street</Address2>
				<Address5>Worcester</Address5>
				<PostCode>WR5 1AA</PostCode>
				<Country>GB</Country>
				<VAT>
					<Num>*********</Num>
					<Alpha>GB*********</Alpha>
				</VAT>
				<Contact1>
					<Name>Ellie Jones</Name>
					<Phone>01905 321987</Phone>
					<Email><EMAIL></Email>
					<Fax>01905 321988</Fax>
				</Contact1>
				<Contact2>
					<Name>Elizabeth Kilbey</Name>
					<Phone>01905 321986</Phone>
					<Email><EMAIL></Email>
					<Fax>01905 321985</Fax>
				</Contact2>
			</SuppAddr>
			<DocDate>
				<Date>2018-04-10</Date>
				<Time>15:55:39</Time>
			</DocDate>
			<TranInfo/>
			<RoutingCode>A80410AAAB</RoutingCode>
		</DocHeader>
		<AckHeader>
			<AckCode>ACCEPTED</AckCode>
			<Ack_Date>
				<Date>2018-04-10</Date>
				<Time>15:55:39</Time>
			</Ack_Date>
			<CustOrder>CO-001-0001</CustOrder>
			<CustOrderDate>
				<Date>2018-04-10</Date>
				<Time>15:46:00</Time>
			</CustOrderDate>
			<SuppOrder>SO0010001</SuppOrder>
			<Perishable>false</Perishable>
			<BookingRef>BR-001-0001</BookingRef>
			<OrigCustOrder>OCR-001-0001</OrigCustOrder>
			<Delivery>
				<DelMethod>DELIVER_TO_DEPOT</DelMethod>
				<ReqDesp>
					<Date>2018-04-10</Date>
					<Time>21:59:00</Time>
				</ReqDesp>
				<EarliestDesp>
					<Date>2018-04-10</Date>
					<Time>22:59:00</Time>
				</EarliestDesp>
				<LatestDesp>
					<Date>2018-04-10</Date>
					<Time>22:59:00</Time>
				</LatestDesp>
				<ReqDel>
					<Date>2018-04-10</Date>
					<Time>22:59:00</Time>
				</ReqDel>
				<EarliestDel>
					<Date>2018-04-10</Date>
					<Time>22:59:00</Time>
				</EarliestDel>
				<LatestDel>
					<Date>2018-04-10</Date>
					<Time>22:59:00</Time>
				</LatestDel>
				<DespatchFrom>
					<Code>000</Code>
					<EAN13>5000010000019</EAN13>
					<Name>DemoCo</Name>
					<Address1>DemoCo House</Address1>
					<Address2>100 High Street</Address2>
					<Address5>Worcester</Address5>
					<PostCode>WR5 1AA</PostCode>
					<Country>GB</Country>
					<VAT>
						<Num>*********</Num>
						<Alpha>GB*********</Alpha>
					</VAT>
					<Contact1>
						<Name>Ellie Jones</Name>
						<Phone>01905 321987</Phone>
						<Email><EMAIL></Email>
						<Fax>01905 321988</Fax>
					</Contact1>
					<Contact2>
						<Name>Elizabeth Kilbey</Name>
						<Phone>01905 321986</Phone>
						<Email><EMAIL></Email>
						<Fax>01905 321985</Fax>
					</Contact2>
				</DespatchFrom>
				<DeliverTo>
					<Code>000</Code>
					<EAN13>5024657121941</EAN13>
					<Name>ABC Foods Limited</Name>
					<Address1>1 High Street</Address1>
					<Address5>London</Address5>
					<Country>GB</Country>
					<VAT>
						<Num>123-456-789-123</Num>
						<Alpha>123-456-789-123</Alpha>
					</VAT>
					<Contact1>
						<Name>Carl Denver</Name>
						<Phone>010101 123 456</Phone>
						<Fax>010101 123 457</Fax>
					</Contact1>
					<Contact2>
						<Name>Elisabeth Francis</Name>
						<Phone>010101 123 458</Phone>
					</Contact2>
				</DeliverTo>
				<Instructions>
					<Line1>Please deliver to gate G1</Line1>
				</Instructions>
				<DelCombined>false</DelCombined>
			</Delivery>
			<Locations>
				<OrderBranch>
					<Code>000</Code>
					<EAN13>5024657121941</EAN13>
					<Name>ABC Foods Limited</Name>
					<Address1>1 High Street</Address1>
					<Address5>London</Address5>
					<Country>GB</Country>
					<VAT>
						<Num>123-456-789-123</Num>
						<Alpha>123-456-789-123</Alpha>
					</VAT>
					<Contact1>
						<Name>Carl Denver</Name>
						<Phone>010101 123 456</Phone>
						<Fax>010101 123 457</Fax>
					</Contact1>
					<Contact2>
						<Name>Elisabeth Francis</Name>
						<Phone>010101 123 458</Phone>
					</Contact2>
				</OrderBranch>
				<InvoiceTo>
					<Code>000</Code>
					<Name>ABC Foods Limited (Retail Division)</Name>
					<Address1>1 High Street</Address1>
					<Address5>London</Address5>
					<Country>GB</Country>
					<VAT>
						<Num>123-456-789-123</Num>
						<Alpha>123-456-789-123</Alpha>
					</VAT>
					<Contact1>
						<Name>Carl Denver</Name>
						<Phone>010101 123 456</Phone>
						<Fax>010101 123 457</Fax>
					</Contact1>
					<Contact2>
						<Name>Elisabeth Francis</Name>
						<Phone>010101 123 458</Phone>
					</Contact2>
				</InvoiceTo>
				<InvoiceFrom>
					<Code>000</Code>
					<EAN13>5000010000019</EAN13>
					<Name>DemoCo (Research) Ltd</Name>
					<Address1>DemoCo House</Address1>
					<Address2>100 High Street</Address2>
					<Address5>Worcester</Address5>
					<PostCode>WR5 1AA</PostCode>
					<Country>GB</Country>
					<VAT>
						<Num>*********</Num>
						<Alpha>GB*********</Alpha>
					</VAT>
					<Contact1>
						<Name>Ellie Jones</Name>
						<Phone>01905 321987</Phone>
						<Email><EMAIL></Email>
						<Fax>01905 321988</Fax>
					</Contact1>
					<Contact2>
						<Name>Elizabeth Kilbey</Name>
						<Phone>01905 321986</Phone>
						<Email><EMAIL></Email>
						<Fax>01905 321985</Fax>
					</Contact2>
				</InvoiceFrom>
			</Locations>
			<Notes>
				<Seq>1</Seq>
				<Narrative>
					<Line1>This is in relation to contract 989-0987</Line1>
				</Narrative>
			</Notes>
			<Revision/>
			<TotalOrderUnits>60</TotalOrderUnits>
			<TotalOrderVal>840.0000000</TotalOrderVal>
			<PricingDate>
				<Date>2018-04-09</Date>
				<Time>23:00:00</Time>
			</PricingDate>
		</AckHeader>
		<AckLine>
			<LineNo>1</LineNo>
			<LineCode>ACCEPTED</LineCode>
			<OriginalRequirements>
				<Item>
					<CustItem>
						<OwnBrandEAN>1213192703201</OwnBrandEAN>
						<Code>LMS</Code>
						<SKU>SKULMS</SKU>
					</CustItem>
					<SuppItem>
						<Code>LMS</Code>
						<EAN13>1213192703202</EAN13>
						<UPC12>121319270320</UPC12>
						<DUN14>12131927032011</DUN14>
						<ISBN>sLMS</ISBN>
						<ManufacturersCode>MCLMS</ManufacturersCode>
						<Desc1>(s) Large Mixed Salad</Desc1>
					</SuppItem>
					<RetailEAN>1213192703201</RetailEAN>
					<Desc1>Large Mixed Salad</Desc1>
				</Item>
				<Perishable>false</Perishable>
				<UnitOfOrder>
					<Unit>1</Unit>
					<OrderMeasure>0</OrderMeasure>
				</UnitOfOrder>
				<OrderQty>
					<Unit>10.000</Unit>
					<UOM>CASE</UOM>
				</OrderQty>
				<PricingMeasure>
					<MeasureQty>1</MeasureQty>
				</PricingMeasure>
				<CostPrice>18.0000000</CostPrice>
				<LineAmount>180.0000000</LineAmount>
				<SellPrice>
					<MeasureQty>1</MeasureQty>
				</SellPrice>
				<Notes>
					<Seq>1</Seq>
					<Narrative>
						<Line1>No substitutes for this item.</Line1>
					</Narrative>
				</Notes>
				<Delivery>
					<ReqDesp>
						<Date>2018-04-10</Date>
						<Time>22:59:00</Time>
					</ReqDesp>
					<ReqDel>
						<Date>2018-04-10</Date>
						<Time>22:59:00</Time>
					</ReqDel>
					<ReqQty>
						<Unit>10.000</Unit>
						<UOM>CASE</UOM>
					</ReqQty>
				</Delivery>
				<Tax>
					<TaxCode>Z</TaxCode>
					<TaxPcnt>0.00</TaxPcnt>
					<DutyCode>STD</DutyCode>
				</Tax>
			</OriginalRequirements>
			<SupplierOverrides>
				<Item>
					<CustItem>
						<OwnBrandEAN>1213192703201</OwnBrandEAN>
						<Code>LMS</Code>
						<SKU>SKULMS</SKU>
					</CustItem>
					<SuppItem>
						<Code>LMS</Code>
						<EAN13>1213192703202</EAN13>
						<UPC12>121319270320</UPC12>
						<DUN14>12131927032011</DUN14>
						<ISBN>sLMS</ISBN>
						<ManufacturersCode>MCLMS</ManufacturersCode>
						<Desc1>(s) Large Mixed Salad</Desc1>
					</SuppItem>
					<RetailEAN>1213192703201</RetailEAN>
					<Desc1>Large Mixed Salad</Desc1>
				</Item>
				<Perishable>false</Perishable>
				<UnitOfOrder>
					<Unit>1</Unit>
					<OrderMeasure>0</OrderMeasure>
				</UnitOfOrder>
				<AckQty>
					<Unit>10.000</Unit>
					<UOM>CASE</UOM>
				</AckQty>
				<PricingMeasure>
					<MeasureQty>1</MeasureQty>
				</PricingMeasure>
				<CostPrice>18.0000000</CostPrice>
				<LineAmount>180.0000000</LineAmount>
				<SellPrice>
					<MeasureQty>1</MeasureQty>
				</SellPrice>
				<Notes>
					<Seq>1</Seq>
					<Narrative>
						<Line1>No substitutes for this item.</Line1>
					</Narrative>
				</Notes>
				<Delivery>
					<ExpDespDate>
						<Date>2018-04-10</Date>
						<Time>22:59:00</Time>
					</ExpDespDate>
					<ExpDelDate>
						<Date>2018-04-10</Date>
						<Time>22:59:00</Time>
					</ExpDelDate>
					<QtyToDespatch>
						<Unit>10.000</Unit>
						<UOM>CASE</UOM>
					</QtyToDespatch>
				</Delivery>
				<Tax>
					<TaxCode>Z</TaxCode>
					<TaxPcnt>0.00</TaxPcnt>
					<DutyCode>STD</DutyCode>
				</Tax>
			</SupplierOverrides>
			<OutstandingBalance>
				<OutstandingBalance>
					<Unit>0.000</Unit>
				</OutstandingBalance>
			</OutstandingBalance>
			<Narrative/>
		</AckLine>
		<AckLine>
			<LineNo>2</LineNo>
			<LineCode>ACCEPTED</LineCode>
			<OriginalRequirements>
				<Item>
					<CustItem>
						<OwnBrandEAN>1913192708201</OwnBrandEAN>
						<Code>SMS</Code>
						<SKU>SKUSMS</SKU>
					</CustItem>
					<SuppItem>
						<Code>sSMS</Code>
						<EAN13>1913192708203</EAN13>
						<UPC12>191319270820</UPC12>
						<DUN14>19131927082011</DUN14>
						<ISBN>sSMS</ISBN>
						<ManufacturersCode>MCSMS</ManufacturersCode>
						<Desc1>Small Mixed Salad</Desc1>
					</SuppItem>
					<RetailEAN>1913192708202</RetailEAN>
					<Desc1>Small Mixed Salad</Desc1>
				</Item>
				<Perishable>false</Perishable>
				<UnitOfOrder>
					<Unit>1</Unit>
					<OrderMeasure>0</OrderMeasure>
				</UnitOfOrder>
				<OrderQty>
					<Unit>20.000</Unit>
					<UOM>CASE</UOM>
				</OrderQty>
				<PricingMeasure>
					<MeasureQty>1</MeasureQty>
				</PricingMeasure>
				<CostPrice>12.0000000</CostPrice>
				<LineAmount>240.0000000</LineAmount>
				<SellPrice>
					<MeasureQty>1</MeasureQty>
				</SellPrice>
				<Delivery>
					<ReqDesp>
						<Date>2018-04-10</Date>
						<Time>22:59:00</Time>
					</ReqDesp>
					<ReqDel>
						<Date>2018-04-10</Date>
						<Time>22:59:00</Time>
					</ReqDel>
					<ReqQty>
						<Unit>20.000</Unit>
						<UOM>CASE</UOM>
					</ReqQty>
				</Delivery>
				<Tax>
					<TaxCode>Z</TaxCode>
					<TaxPcnt>0.00</TaxPcnt>
					<DutyCode>STD</DutyCode>
				</Tax>
			</OriginalRequirements>
			<SupplierOverrides>
				<Item>
					<CustItem>
						<OwnBrandEAN>1913192708201</OwnBrandEAN>
						<Code>SMS</Code>
						<SKU>SKUSMS</SKU>
					</CustItem>
					<SuppItem>
						<Code>sSMS</Code>
						<EAN13>1913192708203</EAN13>
						<UPC12>191319270820</UPC12>
						<DUN14>19131927082011</DUN14>
						<ISBN>sSMS</ISBN>
						<ManufacturersCode>MCSMS</ManufacturersCode>
						<Desc1>Small Mixed Salad</Desc1>
					</SuppItem>
					<RetailEAN>1913192708202</RetailEAN>
					<Desc1>Small Mixed Salad</Desc1>
				</Item>
				<Perishable>false</Perishable>
				<UnitOfOrder>
					<Unit>1</Unit>
					<OrderMeasure>0</OrderMeasure>
				</UnitOfOrder>
				<AckQty>
					<Unit>20.000</Unit>
					<UOM>CASE</UOM>
				</AckQty>
				<PricingMeasure>
					<MeasureQty>1</MeasureQty>
				</PricingMeasure>
				<CostPrice>12.0000000</CostPrice>
				<LineAmount>240.0000000</LineAmount>
				<SellPrice>
					<MeasureQty>1</MeasureQty>
				</SellPrice>
				<Delivery>
					<ExpDespDate>
						<Date>2018-04-10</Date>
						<Time>22:59:00</Time>
					</ExpDespDate>
					<ExpDelDate>
						<Date>2018-04-10</Date>
						<Time>22:59:00</Time>
					</ExpDelDate>
					<QtyToDespatch>
						<Unit>20.000</Unit>
						<UOM>CASE</UOM>
					</QtyToDespatch>
				</Delivery>
				<Tax>
					<TaxCode>Z</TaxCode>
					<TaxPcnt>0.00</TaxPcnt>
					<DutyCode>STD</DutyCode>
				</Tax>
			</SupplierOverrides>
			<OutstandingBalance>
				<OutstandingBalance>
					<Unit>0.000</Unit>
				</OutstandingBalance>
			</OutstandingBalance>
			<Narrative/>
		</AckLine>
		<AckLine>
			<LineNo>3</LineNo>
			<LineCode>ACCEPTED</LineCode>
			<OriginalRequirements>
				<Item>
					<CustItem>
						<OwnBrandEAN>131319270118</OwnBrandEAN>
						<Code>MMS</Code>
						<SKU>SKU-001-0002</SKU>
					</CustItem>
					<SuppItem>
						<Code>MMS</Code>
						<EAN13>1313192701204</EAN13>
						<UPC12>131319270121</UPC12>
						<DUN14>1313192701181</DUN14>
						<ISBN>sMMS</ISBN>
						<ManufacturersCode>SMR-001-0002</ManufacturersCode>
						<Desc1>(s) Medium Mixed Salad</Desc1>
					</SuppItem>
					<RetailEAN>131319270119</RetailEAN>
					<Desc1>Medium Mixed Salad</Desc1>
				</Item>
				<Perishable>false</Perishable>
				<UnitOfOrder>
					<Unit>1</Unit>
					<OrderMeasure>0</OrderMeasure>
				</UnitOfOrder>
				<OrderQty>
					<Unit>30.000</Unit>
					<UOM>CASE</UOM>
				</OrderQty>
				<PricingMeasure>
					<MeasureQty>1</MeasureQty>
				</PricingMeasure>
				<CostPrice>14.0000000</CostPrice>
				<LineAmount>420.0000000</LineAmount>
				<SellPrice>
					<MeasureQty>1</MeasureQty>
				</SellPrice>
				<Delivery>
					<ReqDesp>
						<Date>2018-04-10</Date>
						<Time>22:59:00</Time>
					</ReqDesp>
					<ReqDel>
						<Date>2018-04-10</Date>
						<Time>22:59:00</Time>
					</ReqDel>
					<ReqQty>
						<Unit>30.000</Unit>
						<UOM>CASE</UOM>
					</ReqQty>
				</Delivery>
				<Tax>
					<TaxCode>Z</TaxCode>
					<TaxPcnt>0.00</TaxPcnt>
					<DutyCode>STD</DutyCode>
				</Tax>
			</OriginalRequirements>
			<SupplierOverrides>
				<Item>
					<CustItem>
						<OwnBrandEAN>131319270118</OwnBrandEAN>
						<Code>MMS</Code>
						<SKU>SKU-001-0002</SKU>
					</CustItem>
					<SuppItem>
						<Code>MMS</Code>
						<EAN13>1313192701204</EAN13>
						<UPC12>131319270121</UPC12>
						<DUN14>1313192701181</DUN14>
						<ISBN>sMMS</ISBN>
						<ManufacturersCode>SMR-001-0002</ManufacturersCode>
						<Desc1>(s) Medium Mixed Salad</Desc1>
					</SuppItem>
					<RetailEAN>131319270119</RetailEAN>
					<Desc1>Medium Mixed Salad</Desc1>
				</Item>
				<Perishable>false</Perishable>
				<UnitOfOrder>
					<Unit>1</Unit>
					<OrderMeasure>0</OrderMeasure>
				</UnitOfOrder>
				<AckQty>
					<Unit>30.000</Unit>
					<UOM>CASE</UOM>
				</AckQty>
				<PricingMeasure>
					<MeasureQty>1</MeasureQty>
				</PricingMeasure>
				<CostPrice>14.0000000</CostPrice>
				<LineAmount>420.0000000</LineAmount>
				<SellPrice>
					<MeasureQty>1</MeasureQty>
				</SellPrice>
				<Delivery>
					<ExpDespDate>
						<Date>2018-04-10</Date>
						<Time>22:59:00</Time>
					</ExpDespDate>
					<ExpDelDate>
						<Date>2018-04-10</Date>
						<Time>22:59:00</Time>
					</ExpDelDate>
					<QtyToDespatch>
						<Unit>30.000</Unit>
						<UOM>CASE</UOM>
					</QtyToDespatch>
				</Delivery>
				<Tax>
					<TaxCode>Z</TaxCode>
					<TaxPcnt>0.00</TaxPcnt>
					<DutyCode>STD</DutyCode>
				</Tax>
			</SupplierOverrides>
			<OutstandingBalance>
				<OutstandingBalance>
					<Unit>0.000</Unit>
				</OutstandingBalance>
			</OutstandingBalance>
			<Narrative/>
		</AckLine>
		<DocTrailer>
			<TotalLines>3</TotalLines>
		</DocTrailer>
	</Document>
	<MsgTrailer>
		<TotalDocs>1</TotalDocs>
	</MsgTrailer>
</tc:TrueCommerceOrderAck>
