<?xml version="1.0" encoding="UTF-8"?>
<!-- edited with XMLSpy v2014 sp1 (x64) (http://www.altova.com) by <PERSON> (Wesupply Limited) -->
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:tc="http://www.truecommerce.com/docs/order" xmlns:cm="http://www.truecommerce.com/docs/common/components" targetNamespace="http://www.truecommerce.com/docs/order" elementFormDefault="unqualified" attributeFormDefault="unqualified">
	<xs:import namespace="http://www.truecommerce.com/docs/common/components" schemaLocation="truecommerce_components_v1.0.xsd"/>
	<xs:element name="TrueCommerceOrder">
		<xs:annotation>
			<xs:documentation>TrueCommerce Order specification</xs:documentation>
		</xs:annotation>
		<xs:complexType>
			<xs:sequence>
				<xs:element name="MsgHeader" type="cm:MessageHeader"/>
				<xs:element name="Document" type="tc:OrderDoc" maxOccurs="unbounded"/>
				<xs:element name="MsgTrailer" type="cm:MessageTrailer"/>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<xs:complexType name="OrderDoc">
		<xs:annotation>
			<xs:documentation>Order Document</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="DocHeader" type="cm:DocumentHeader">
				<xs:annotation>
					<xs:documentation>Provides general (non doc-type specific) information about the document</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="OrderHeader" type="tc:OrderHeader" id="HEADER">
				<xs:annotation>
					<xs:documentation>Order Header Information</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="OrderLine" type="tc:OrderLine" id="LINE" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>Order lines</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DocTrailer" type="cm:DocumentTrailer">
				<xs:annotation>
					<xs:documentation>Provides general (non doc-type specific) summary about the document</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="OrderHeader">
		<xs:annotation>
			<xs:documentation>Ordering Details</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="CustOrder" type="xs:string" id="DOC_NUMBER" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Purchase order number of the customer (aka buyer)</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="CustOrderDate" type="cm:DateTime" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Date order placed by customer</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="SuppOrder" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Purchase order reference of the supplier (aka vendor)</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="SuppOrderDate" type="cm:DateTime" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Date order received by supplier</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="OrderClass" type="xs:string" id="DOC_CLASS" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Order Classification. Used to destinguish replenishment orders from other types.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="OrderBrand" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Brand name associated with the order.  This is purely for reference purposes only, and may be used for reference during KPI analysis.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="OrderCode" default="NEW">
				<xs:annotation>
					<xs:documentation>Dictates what the order is: (NEW, NET_CHANGE, CANCELLATION, PLANNED)</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:pattern value="NEW"/>
						<xs:pattern value="NET_CHANGE"/>
						<xs:pattern value="CANCELLATION"/>
						<xs:pattern value="PLANNED"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="SalesChannel" type="xs:string" minOccurs="0"/>
			<xs:element name="Salesman" type="xs:string" minOccurs="0"/>
			<xs:element name="Perishable" type="xs:boolean" default="false" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Is the order for perishable items?  Defaults to false</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Expeditor" type="cm:Contact" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Buyer name</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="BookingRef" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Booking Reference, (aka Transport Delivery Number)</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="CommonRef" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Common Reference number (aka Customer Reference)</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="OrigCustOrder" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Order number reference of the originating customer</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ThirdPartyOrderRef" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Order number reference supplied by a third-party.  This is applicable in situations where a 3rd Party is selling goods on behalf of the TrueCommerce Customer.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Contract" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Contract number to which this order relates.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Promotion" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Promotional discount code</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="SpecNo" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Specification number to which this order relates.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Delivery" type="tc:DeliveryInfo" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Delivery information</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Locations" type="cm:Locations" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Location Addresses.  Note: Customer and Supplier don't exist here because they are stored within the Document Header section.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Notes" type="cm:Notes" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>General notes pertaining to the order</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Revision" type="cm:RevisionDetails" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Revision Details</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="SettlementTerms" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>Settlement terms agreed between the buyer and seller, expressed in terms of payment due date/days and percentage.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="TermsOfPayment" type="xs:string" minOccurs="0">
							<xs:annotation>
								<xs:documentation>Text for Terms of Payment.</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="SettlementDate" type="xs:date" minOccurs="0">
							<xs:annotation>
								<xs:documentation>Date that payment should be made available to the payee to obtain the specified discount.

XML date.
If no date has been supplied, then current date is assumed.
The date is specified in the following form "YYYY-MM-DD" where:

YYYY indicates the year
MM indicates the month
DD indicates the day</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="SettlementDays" type="xs:long" minOccurs="0">
							<xs:annotation>
								<xs:documentation>Days until payment is due is defined as the number of calendar days after a reference date, commonly the date of invoice, that the payment is to be made availble to the payee.</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="Percentage" minOccurs="0">
							<xs:annotation>
								<xs:documentation>Percentage discount applicable</xs:documentation>
							</xs:annotation>
							<xs:simpleType>
								<xs:restriction base="xs:decimal">
									<xs:minInclusive value="-999.99"/>
									<xs:maxInclusive value="999.99"/>
									<xs:totalDigits value="5"/>
									<xs:fractionDigits value="2"/>
								</xs:restriction>
							</xs:simpleType>
						</xs:element>
						<xs:element name="Code" type="xs:string" minOccurs="0">
							<xs:annotation>
								<xs:documentation>Code for Settlement Terms</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="TotalOrderWeight" type="cm:Weight" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Total goods weight of the order</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="TotalOrderVolume" type="cm:Volume" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Total goods volume of the order</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="TotalOrderUnits" type="xs:int" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Total number of units ordered across all lines</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="TotalOrderVal" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Total order value, excl. tax</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:decimal">
						<xs:totalDigits value="19"/>
						<xs:fractionDigits value="7"/>
						<xs:minInclusive value="-999999999999.9999999"/>
						<xs:maxInclusive value="999999999999.9999999"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="PricingDate" type="cm:DateTime" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Date the order was priced</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Currency" type="cm:Currency" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Invoicing currency details</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="AckReq" type="xs:boolean" default="false" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Determines whether the supplier needs to send an Order Acknowledgement / Order Response.  Defaults to false.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="LoadPlan" type="xs:boolean" default="false" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Load Planning required? Used by TrueCommerce. Defaults to false</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="UserDefined" type="cm:UserDefined" minOccurs="0">
				<xs:annotation>
					<xs:documentation>User defined fields</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Authorised" type="xs:boolean" default="false" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Has the Order been authorised? Defaults to false.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="AuthorisedBy" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Order is Authorised by.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="AuthorisationDate" type="cm:DateTime" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Date Order was Authorised</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Additional" type="cm:AdditionalInfo" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Additional reference information.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="DeliveryInfo">
		<xs:annotation>
			<xs:documentation>Delivery details</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="DelMethod" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Delivery Method: BRANCH_COLLECTION, DELIVER_TO_BRANCH, DELIVER_TO_DEPOT, DIRECT_DELIVERY,
CUSTOMER_COLLECTION</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:pattern value="BRANCH_COLLECTION"/>
						<xs:pattern value="DELIVER_TO_BRANCH"/>
						<xs:pattern value="DELIVER_TO_DEPOT"/>
						<xs:pattern value="DIRECT_DELIVERY"/>
						<xs:pattern value="CUSTOMER_COLLECTION"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="PayMethod" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Payment method:        CUSTOMER_PAYS,  SUPPLIER_PAYS,  3RD_PARTY_PAYS,  NO_CHARGE</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:pattern value="CUSTOMER_PAYS"/>
						<xs:pattern value="SUPPLIER_PAYS"/>
						<xs:pattern value="3RD_PARTY_PAYS"/>
						<xs:pattern value="NO_CHARGE"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="ReqDesp" type="cm:DateTime" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Required despatch date/time from the ship-from location</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="EarliestDesp" type="cm:DateTime" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Earliest despatch (shipment) date/time from the supplier location</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="LatestDesp" type="cm:DateTime" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Latest despatch (shipment) date/time from the supplier location</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ReqDel" type="cm:DateTime" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Required delivery date/time at the deliver-to location</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="EarliestDel" type="cm:DateTime" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Earliest delivery date/time at the deliver-to location</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="LatestDel" type="cm:DateTime" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Latest delivery date/time at the deliver-to location</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="LatestDelLeadTime" type="cm:LeadTime" minOccurs="0">
				<xs:annotation>
					<xs:documentation>lead time in days/hours to specify when the order must be delivered by.  This information can be used to let TrueCommerce calculate a latest delivery date for the order.  It will also be used to determine order line delivery dates if no line delivery information is supplied.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ReqPickUp" type="cm:DateTime" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Required pickup (collection) date/time.  Used when the delivery method is CUSTOMER_COLLECTION</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DelPoint" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Delivery point</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DelGate" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Delivery gate</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DelDock" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Delivery dock</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DelPriority" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Delivery Priority.  A textual description of the priority</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="AETC" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Authorised Excess Transportation Cost (AETC) code. </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="JourneyRef" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Reference number for the journey</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="JourneyDesc" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Textual description of the journey route</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DelUnits" type="xs:int" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Number of delivery units (cases, cartons, packages) transported</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="NumberOfPallets" type="xs:int" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Expected number of Pallets</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="SupplyGroup" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Group code associated with the fufilment of the order.  This may be used to reference a haulier.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="TareWeight" type="cm:Weight" minOccurs="0">
				<xs:annotation>
					<xs:documentation>This is the weight of the unladen vehicle, i.e. the weight you will use to calculate the vehicle's load.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="GoodsWeight" type="cm:Weight" minOccurs="0">
				<xs:annotation>
					<xs:documentation>This is the weight of the load</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="TotalWeight" type="cm:Weight" minOccurs="0">
				<xs:annotation>
					<xs:documentation>This is the weight of the vehicle fully laden, and is an indication of the total weight of everything on the bridge.
Total = tare + goods</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DespatchFrom" type="cm:Address" id="SHFR" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Full address of the despatch (ship-from) location</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DeliverTo" type="cm:Address" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Full address of the deliver-to location.  This will usually match the ordering branch address, except for direct deliveries, or depot deliveries</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Carrier" type="cm:Address" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Location details of any carrier used.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="SecondaryShipLoc" type="cm:Address" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Secondary or trans-shipment location</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Instructions" type="cm:Narrative" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Delivery Instruction notes</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DelCombined" type="xs:boolean" default="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Allow order to be delivered combined with other orders. Defaults to true</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="MultipleDrops" type="xs:boolean" default="false" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Determines whether the supplier can deliver in multiple drops, and is therefore allowed to generate multiple shipments for the same purchase order. Defaults to false. This option will be overridden for online shipping if the lines have multiple delivery schedule lines</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Additional" type="cm:AdditionalInfo" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Additional reference information.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="OrderLine">
		<xs:annotation>
			<xs:documentation>Order line information (quantities, etc)</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="LineNo" minOccurs="0">
				<xs:annotation>
					<xs:documentation>order line number</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:int"/>
				</xs:simpleType>
			</xs:element>
			<xs:element name="LineCode" default="NEW" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Dictates whether the order line is: (NEW, CHANGE, CANCELLATION, REDUCTION)</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:pattern value="NEW"/>
						<xs:pattern value="CHANGE"/>
						<xs:pattern value="CANCELLATION"/>
						<xs:pattern value="REDUCTION"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="CommonRef" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Common Reference number (aka Customer Reference)</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Item" type="cm:ItemDetails" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Item details</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Perishable" type="xs:boolean" default="false" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Is the order line for perishable items?  Defaults to false</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="UnitOfOrder" type="cm:UnitOfOrderQty" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Describes what units are being ordered within the qty_ordered field. The qty ordered may be 100, but this is 100 x 24 cans.  The 24 cans is described within this field.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="OrderQty" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Describes what quantities are being ordered, relative to the ordering units.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:complexContent>
						<xs:extension base="cm:Quantity">
							<xs:attribute name="Structure"/>
						</xs:extension>
					</xs:complexContent>
				</xs:complexType>
			</xs:element>
			<xs:element name="PricingMeasure" type="cm:PricingMeasure" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Describes the mechanism for pricing the invoice line</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="CostPrice" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Cost price, before deducting line discounts, etc (excluding VAT) per  pricing measure</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:decimal">
						<xs:minInclusive value="-999999999999.9999999"/>
						<xs:maxInclusive value="999999999999.9999999"/>
						<xs:totalDigits value="19"/>
						<xs:fractionDigits value="7"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="LineAmount" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Net cost of detail line, including any line discounts, etc (excluding VAT) of the order line</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:decimal">
						<xs:minInclusive value="-999999999999.9999999"/>
						<xs:maxInclusive value="999999999999.9999999"/>
						<xs:totalDigits value="19"/>
						<xs:fractionDigits value="7"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="SellPrice" type="cm:Price" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Selling price (before the application of VAT) of a traded unit or measure.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Special" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Special price flag EXPORT, FREE, PROMOTIONAL</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:pattern value="EXPORT"/>
						<xs:pattern value="FREE"/>
						<xs:pattern value="PROMOTIONAL"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="Notes" type="cm:Notes" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>General order line notes</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ProdVar" type="tc:OrderLineProdVar" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>Product Variances</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Delivery" type="tc:DeliveryLineInfo" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>Delivery information When, and how much to deliver</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DiscountsAndCharges" type="tc:DiscountOrCharge" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>Discounts and charges details</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="OrderType" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Holds the order type information, as supplied by the customer ERP system </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Contract" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Contract number to which this order line relates.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ContractLine" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Contract Line number to which this order line relates.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="SpecNo" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Specification number to which this order line relates.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="SupplyGroup" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Group code associated with the fufilment of the order line.  This may be used to reference a haulier.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="OrderBranch" type="cm:Address" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Full address of the ordering branch.  </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DespatchFrom" type="cm:Address" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Full address of the despatch (ship-from) location</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DeliverTo" type="cm:Address" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Full address of the deliver-to location.  This will usually match the ordering branch address, except for direct deliveries, or depot deliveries. </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Tax" type="cm:Tax" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Provides tax information for the line</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="GoodsWeight" type="cm:Weight" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Goods weight of the line</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="UserDefined" type="cm:UserDefined" minOccurs="0">
				<xs:annotation>
					<xs:documentation>User defined fields</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Additional" type="cm:AdditionalInfo" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Additional reference information.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="DiscountOrCharge">
		<xs:annotation>
			<xs:documentation>Complex order line adjustments</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="DiscountOrCharge" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Discount or Charge indicator. D=Discount, C=Charge</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Type" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>A=Special, B=Basic, Q=Quantity, V=Value</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="AccumulationRules" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Accumulation Rules. G = Gross, N = Nett</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Percentage" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Actual Adjustment Percentage</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:decimal">
						<xs:totalDigits value="5"/>
						<xs:fractionDigits value="2"/>
						<xs:minInclusive value="-999.99"/>
						<xs:maxInclusive value="999.99"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="Amount" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Actual Adjustment Amount</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:decimal">
						<xs:totalDigits value="19"/>
						<xs:fractionDigits value="3"/>
						<xs:minInclusive value="-9999999999999999.999"/>
						<xs:maxInclusive value="9999999999999999.999"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="OrderLineProdVar">
		<xs:annotation>
			<xs:documentation>Order Line Product Variant</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="Seq" type="xs:int" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Sequence number</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Type">
				<xs:annotation>
					<xs:documentation>Variant type code (registered with ANA)</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:maxLength value="3"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="Detail">
				<xs:annotation>
					<xs:documentation>Variable data:  Ie. size, colour</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:maxLength value="40"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="OrderQty" type="cm:Quantity" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Describes what quantities are being ordered, relative to the ordering units.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Additional" type="cm:AdditionalInfo" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Additional reference information.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="DeliveryLineInfo">
		<xs:annotation>
			<xs:documentation>Delivery Line Information (quantities, etc)</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="ReqDesp" type="cm:DateTime" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Required despatch date/time from the ship-from location</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ReqDel" type="cm:DateTime" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Required delivery date/time at the deliver-to location</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ReqPickUp" type="cm:DateTime" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Required pickup (collection) date/time.  Used when the delivery method is CUSTOMER_COLLECTION</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ReqQty" type="cm:Quantity" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Quantity required</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Notes" type="cm:Notes" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="DelPoint" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Delivery point</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DelGate" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Delivery gate</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DelDock" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Delivery dock</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DelPriority" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Delivery Priority.  A textual description of the priority</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DelRef" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Delivery Reference</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="GoodsWeight" type="cm:Weight" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Goods weight of the delivery line</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="NumberOfPallets" type="xs:int" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Expected number of pallets</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="LoadStructure" type="cm:LoadStructure" minOccurs="0"/>
			<xs:element name="Additional" type="cm:AdditionalInfo" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Additional reference information.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
</xs:schema>
