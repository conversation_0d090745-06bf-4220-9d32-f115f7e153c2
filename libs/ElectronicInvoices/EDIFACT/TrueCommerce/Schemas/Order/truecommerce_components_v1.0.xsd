<?xml version="1.0" encoding="UTF-8"?>
<!-- edited with XMLSpy v2015 rel. 3 sp1 (x64) (http://www.altova.com) by <PERSON> (<PERSON><PERSON><PERSON><PERSON>) -->
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns="http://www.truecommerce.com/docs/common/components" targetNamespace="http://www.truecommerce.com/docs/common/components" elementFormDefault="unqualified" attributeFormDefault="unqualified">
	<xs:complexType name="AdditionalInfo">
		<xs:annotation>
			<xs:documentation>Additional information</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="DataItem" maxOccurs="unbounded">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="Ref" type="xs:string">
							<xs:annotation>
								<xs:documentation>Segment reference.  
The OneTime loader will validate this reference exists within the OneTime Reference Repository for this particular segment, and will reject the document if the reference is not known.</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:sequence>
							<xs:annotation>
								<xs:documentation>Field value.  Can be declared using a simple string, or via one or more complex structures.</xs:documentation>
							</xs:annotation>
							<xs:element name="Address" type="Address" minOccurs="0">
								<xs:annotation>
									<xs:documentation>Data value as an address</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="Contact" type="Contact" minOccurs="0">
								<xs:annotation>
									<xs:documentation>Data value as a contact</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="Date" type="DateTime" minOccurs="0">
								<xs:annotation>
									<xs:documentation>Data value as a date</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="Item" type="ItemDetails" minOccurs="0">
								<xs:annotation>
									<xs:documentation>Data value as an item</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="Notes" type="Notes" minOccurs="0">
								<xs:annotation>
									<xs:documentation>Date value in notes format</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="Price" type="Price" minOccurs="0">
								<xs:annotation>
									<xs:documentation>Data value as a price</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="Qty" type="Quantity" minOccurs="0">
								<xs:annotation>
									<xs:documentation>Data value as qty/uom pair</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="Tax" type="Tax" minOccurs="0">
								<xs:annotation>
									<xs:documentation>Data value as a tax</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="UserDefined" type="UserDefined" minOccurs="0">
								<xs:annotation>
									<xs:documentation>Data value as a user-defined structure</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="Volume" type="Volume" minOccurs="0">
								<xs:annotation>
									<xs:documentation>Data value as volumne/measure pair</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="Weight" type="Weight" minOccurs="0">
								<xs:annotation>
									<xs:documentation>Data value as weight/measure pair</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="Value" type="xs:string" minOccurs="0">
								<xs:annotation>
									<xs:documentation>Simple data value.  Numerics will need to be formatted as stated for the reference within the OneTime reference repository</xs:documentation>
								</xs:annotation>
							</xs:element>
						</xs:sequence>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="Address">
		<xs:annotation>
			<xs:documentation>Single address structure</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:sequence minOccurs="0">
				<xs:annotation>
					<xs:documentation>At least some form of code identification is required.  All are optional here, but clients can specify their exact requirements</xs:documentation>
				</xs:annotation>
				<xs:element name="Code" type="xs:string" minOccurs="0">
					<xs:annotation>
						<xs:documentation>Internal code for location</xs:documentation>
					</xs:annotation>
				</xs:element>
				<xs:element name="AltCode" type="xs:string" minOccurs="0">
					<xs:annotation>
						<xs:documentation>Id used by the alternative trading partner for this location</xs:documentation>
					</xs:annotation>
				</xs:element>
				<xs:element name="EAN13" type="xs:string" minOccurs="0">
					<xs:annotation>
						<xs:documentation>13 digit EAN code</xs:documentation>
					</xs:annotation>
				</xs:element>
				<xs:element name="DUNS9" type="xs:string" minOccurs="0">
					<xs:annotation>
						<xs:documentation>9 digit DUNS code</xs:documentation>
					</xs:annotation>
				</xs:element>
				<xs:element name="Name" type="xs:string" minOccurs="0">
					<xs:annotation>
						<xs:documentation>Name</xs:documentation>
					</xs:annotation>
				</xs:element>
				<xs:element name="Address1" type="xs:string" minOccurs="0">
					<xs:annotation>
						<xs:documentation>Address line 1</xs:documentation>
					</xs:annotation>
				</xs:element>
				<xs:element name="Address2" type="xs:string" minOccurs="0">
					<xs:annotation>
						<xs:documentation>Address line 2</xs:documentation>
					</xs:annotation>
				</xs:element>
				<xs:element name="Address3" type="xs:string" minOccurs="0">
					<xs:annotation>
						<xs:documentation>Address line 3</xs:documentation>
					</xs:annotation>
				</xs:element>
				<xs:element name="Address4" type="xs:string" minOccurs="0">
					<xs:annotation>
						<xs:documentation>Address line 4</xs:documentation>
					</xs:annotation>
				</xs:element>
				<xs:element name="Address5" type="xs:string" minOccurs="0">
					<xs:annotation>
						<xs:documentation>Address line 5</xs:documentation>
					</xs:annotation>
				</xs:element>
				<xs:element name="PostCode" type="xs:string" minOccurs="0">
					<xs:annotation>
						<xs:documentation>Post code / ZIP Code</xs:documentation>
					</xs:annotation>
				</xs:element>
				<xs:element name="Country" type="xs:string" minOccurs="0">
					<xs:annotation>
						<xs:documentation>Country code</xs:documentation>
					</xs:annotation>
				</xs:element>
				<xs:element name="VAT" type="VAT" minOccurs="0">
					<xs:annotation>
						<xs:documentation>VAT registration number</xs:documentation>
					</xs:annotation>
				</xs:element>
				<xs:element name="Contact1" type="Contact" minOccurs="0">
					<xs:annotation>
						<xs:documentation>Prinicipal Contact</xs:documentation>
					</xs:annotation>
				</xs:element>
				<xs:element name="Contact2" type="Contact" minOccurs="0">
					<xs:annotation>
						<xs:documentation>Secondary Contact</xs:documentation>
					</xs:annotation>
				</xs:element>
			</xs:sequence>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="CodeValue">
		<xs:annotation>
			<xs:documentation>Code and Value pair, used for sending partner relationship specific details</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="Table" type="xs:string">
				<xs:annotation>
					<xs:documentation>Code list - agreed between sender and receiver</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Value" type="xs:string">
				<xs:annotation>
					<xs:documentation>Code value</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="Contact">
		<xs:annotation>
			<xs:documentation>Contact details for an individual within a trading partner entity</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="Name" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Contact Name</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Phone" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Landline phone number</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Mobile" type="xs:string" minOccurs="0"/>
			<xs:element name="Email" type="xs:string" minOccurs="0"/>
			<xs:element name="Fax" type="xs:string" minOccurs="0"/>
			<xs:element name="JobTitle" type="xs:string" minOccurs="0"/>
			<xs:element name="FriendlyName" type="xs:string" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="Currency">
		<xs:annotation>
			<xs:documentation>Currency structure</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="InvCurr" type="xs:string">
				<xs:annotation>
					<xs:documentation>Invoice currency</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="PayCurr" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Alternative payment currency</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ExchRate" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Exchange rate between base and alternate currency codes</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:decimal">
						<xs:totalDigits value="30"/>
						<xs:fractionDigits value="9"/>
						<xs:minInclusive value="0"/>
						<xs:maxInclusive value="999999999999999999999.999999999"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="ExchRateDate" type="DateTime" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Date/Time ExchRate value taken.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ExchangeID" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>ID code of currency Exchange centre. E.G.  IMF=International Monetary Fund.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="DateTime">
		<xs:annotation>
			<xs:documentation>Date time structure</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:sequence minOccurs="0">
				<xs:element name="Date" type="xs:date" minOccurs="0">
					<xs:annotation>
						<xs:documentation>XML date.
If no date has been supplied, then current date is assumed.
The date is specified in the following form "YYYY-MM-DD" where:

YYYY indicates the year
MM indicates the month
DD indicates the day</xs:documentation>
					</xs:annotation>
				</xs:element>
				<xs:element name="Time" type="xs:time" minOccurs="0">
					<xs:annotation>
						<xs:documentation>XML Time. 
The time is specified in the following form "hh:mm:ss" where:

hh indicates the hour
mm indicates the minute
ss indicates the second
Note: All components are required!

You can also specify an offset from the UTC time by adding a positive or negative time behind the time - like this: 09:30:10-06:00

If no time has been supplied (or no timezone), then customer timezone is assumed</xs:documentation>
					</xs:annotation>
				</xs:element>
			</xs:sequence>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="DocumentHeader">
		<xs:annotation>
			<xs:documentation>Document Header - common to all document types</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="DocType">
				<xs:annotation>
					<xs:documentation>Describes the message type. For example 'ORDER', or 'INVOICE'. Will match msg_type from the message header if all docs are same type</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:enumeration value="ORDER"/>
						<xs:enumeration value="ORDER_ACK"/>
						<xs:enumeration value="ASN"/>
						<xs:enumeration value="INVOICE"/>
						<xs:enumeration value="DEBIT"/>
						<xs:enumeration value="DEBIT_AMENDMENT"/>
						<xs:enumeration value="CREDIT"/>
						<xs:enumeration value="REMITTANCE"/>
						<xs:enumeration value="STOCK_SNAPSHOT"/>
						<xs:enumeration value="RECEIPT"/>
						<xs:enumeration value="BOOKING"/>
						<xs:enumeration value="PRODUCT_PLANNING"/>
						<xs:enumeration value="PRICELIST"/>
						<xs:enumeration value="FUNCTIONAL_ACK"/>
						<xs:enumeration value="TEXT"/>
						<xs:enumeration value="GENERAL COMMS"/>
						<xs:enumeration value="PICKLIST"/>
						<xs:enumeration value="CONSIGNMENT"/>
						<xs:enumeration value="RETANN"/>
						<xs:enumeration value="LOCRPT"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="DocFunction">
				<xs:annotation>
					<xs:documentation>Describes the purpose of the document transmission. ORIGINAL, CANCELLATION, REPLACEMENT</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:enumeration value="ORIGINAL"/>
						<xs:enumeration value="CANCELLATION"/>
						<xs:enumeration value="REPLACEMENT"/>
						<xs:enumeration value="REPRINT"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="CustAddr" type="Address" id="CUST">
				<xs:annotation>
					<xs:documentation>Customer details</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="SuppAddr" type="Address" id="SUPP">
				<xs:annotation>
					<xs:documentation>Supplier details</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DocDate" type="DateTime">
				<xs:annotation>
					<xs:documentation>Document creation date (provided by the sender)</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Notes" type="Notes" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>General notes attached to the document</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="TranInfo" type="TransactionType" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Transaction Information</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="RoutingCode" type="xs:string" id="TR" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Document routing code. This  is used to enable documents to be routed to specific mail-slots, based on this value.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="TransDocNo" type="xs:string" id="DOC_NUMBER" minOccurs="0">
				<xs:annotation>
					<xs:documentation>For transient message use only:  Document number</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Additional" type="AdditionalInfo" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Additional reference information for the generic document.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="DocumentTrailer">
		<xs:annotation>
			<xs:documentation>Generic Document trailer</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="TotalLines" type="xs:long">
				<xs:annotation>
					<xs:documentation>Total number of lines</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="ItemDetails">
		<xs:annotation>
			<xs:documentation>Item structure</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="CustItem" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Customer item details</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="OwnBrandEAN" type="xs:string" minOccurs="0">
							<xs:annotation>
								<xs:documentation>Can be used for 'own label' items. Can be an EAN code (13A), or an internal code</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="Code" type="xs:string" minOccurs="0">
							<xs:annotation>
								<xs:documentation>Customer's internal reference (non EAN) identifying the traded unit</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="SKU" type="xs:string" minOccurs="0">
							<xs:annotation>
								<xs:documentation>SKU Code.  This is a reference field that will usually match the customer item code (CustItem/Code).  It will only be different if the customer has two different codes in operation.</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="SuppItem" type="SupplierItem" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Supplier item details</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="RetailEAN" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>EAN Number for the consumer item. EAN number allocated to the retail (POS) unit. </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Desc1" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Item Description line 1</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Desc2" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Item description line 2</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="EngLvl" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Engineering level</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="LeadTime">
		<xs:annotation>
			<xs:documentation>Lead time structure, containing days and hours</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="Days" type="xs:long" minOccurs="0"/>
			<xs:element name="Hours" type="xs:long" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="LoadStructure">
		<xs:annotation>
			<xs:documentation>Load Structure</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="CasesPerLayer" type="xs:long">
				<xs:annotation>
					<xs:documentation>Number of cases per layer</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="LayersPerPallet" type="xs:long">
				<xs:annotation>
					<xs:documentation>Number of layers per pallet</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="Locations">
		<xs:annotation>
			<xs:documentation>Location Addresses structure. 
Note: Customer and Supplier don't exist here because they are stored within the Document Header section.
</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="OrderBranch" type="Address" id="SHTO" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Full address of the ordering branch.  </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="InvoiceTo" type="Address" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Full address of the invoice-to location</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="InvoiceFrom" type="Address" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Full address of the invoice_from location</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="MessageHeader">
		<xs:annotation>
			<xs:documentation>Message Header</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="MsgType">
				<xs:annotation>
					<xs:documentation>Describes the message type: See enumeration</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:enumeration value="ORDER"/>
						<xs:enumeration value="ORDER_ACK"/>
						<xs:enumeration value="ASN"/>
						<xs:enumeration value="INVOICE"/>
						<xs:enumeration value="DEBIT"/>
						<xs:enumeration value="DEBIT_AMD"/>
						<xs:enumeration value="CREDIT"/>
						<xs:enumeration value="REMITTANCE"/>
						<xs:enumeration value="STOCK_SNAPSHOT"/>
						<xs:enumeration value="RECEIPT"/>
						<xs:enumeration value="PRICELIST"/>
						<xs:enumeration value="FORECAST"/>
						<xs:enumeration value="BOOKING"/>
						<xs:enumeration value="PRODUCT_PLANNING"/>
						<xs:enumeration value="SALES_BASED_REP"/>
						<xs:enumeration value="FUNCTIONAL_ACK"/>
						<xs:enumeration value="TEXT"/>
						<xs:enumeration value="GENERAL COMMS"/>
						<xs:enumeration value="PICKLIST"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="VersionID" type="xs:string">
				<xs:annotation>
					<xs:documentation>Unique identifier to denote the version of the XML being passed. E.G.: "v1.0"</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="TransmittedDate" type="DateTime">
				<xs:annotation>
					<xs:documentation>Transaction date/time
TCOMS: Trdt
EDIFACT: CreationDate (UNB04)
X12: CreationDate/Time (ISA09/10)</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="FileDate" type="DateTime" minOccurs="0">
				<xs:annotation>
					<xs:documentation>File created date/time
TCOMS: FileCreationDate
EDIFACT:
X12:</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Interchange">
				<xs:annotation>
					<xs:documentation>Interchange information</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="Standard">
							<xs:annotation>
								<xs:documentation>See enumeration</xs:documentation>
							</xs:annotation>
							<xs:simpleType>
								<xs:restriction base="xs:string">
									<xs:enumeration value="EDIFACT"/>
									<xs:enumeration value="TRADACOMS"/>
									<xs:enumeration value="TRUECOMMERCE"/>
									<xs:enumeration value="X12"/>
								</xs:restriction>
							</xs:simpleType>
						</xs:element>
						<xs:sequence>
							<xs:annotation>
								<xs:documentation>Authority and security information</xs:documentation>
							</xs:annotation>
							<xs:element name="StandardsId" minOccurs="0">
								<xs:annotation>
									<xs:documentation>Standards ID
TCOMS: StdsId (STDS.1)
EDIFACT: SyntaxId/ Controlling agency (UNB01)
X12: Standards Id (ISA11)</xs:documentation>
								</xs:annotation>
								<xs:simpleType>
									<xs:restriction base="xs:string"/>
								</xs:simpleType>
							</xs:element>
							<xs:element name="StandardsVer" minOccurs="0">
								<xs:annotation>
									<xs:documentation>Standards version
TCOMS: StdsVer (STDS.2)
EDIFACT: Syntax version (UNB02)
X12: StandsVer (ISA12)</xs:documentation>
								</xs:annotation>
								<xs:simpleType>
									<xs:restriction base="xs:long"/>
								</xs:simpleType>
							</xs:element>
							<xs:element name="AuthorizationQual" minOccurs="0">
								<xs:annotation>
									<xs:documentation>Authorization Information Qualifier.
TCOMS: N/A
EDIFACT: N/A
X12: AuthQual (ISA01)</xs:documentation>
								</xs:annotation>
								<xs:simpleType>
									<xs:restriction base="xs:string"/>
								</xs:simpleType>
							</xs:element>
							<xs:element name="AuthorizationId" minOccurs="0">
								<xs:annotation>
									<xs:documentation>Authorization Information
TCOMS: N/A
EDIFACT: N/A
X12: AuthId (ISA02)</xs:documentation>
								</xs:annotation>
								<xs:simpleType>
									<xs:restriction base="xs:string"/>
								</xs:simpleType>
							</xs:element>
							<xs:element name="SecurityQual" minOccurs="0">
								<xs:annotation>
									<xs:documentation>Security Information Identifier.
TCOMS: N/A
EDIFACT: RcvPwdQual (UNB06)
X12: SecQual (ISA03)</xs:documentation>
								</xs:annotation>
								<xs:simpleType>
									<xs:restriction base="xs:string"/>
								</xs:simpleType>
							</xs:element>
							<xs:element name="SecurityId" minOccurs="0">
								<xs:annotation>
									<xs:documentation>Security Information 
TCOMS: N/A
EDIFACT: RcvPwd (UNB06)
X12: SecId (ISA04)</xs:documentation>
								</xs:annotation>
								<xs:simpleType>
									<xs:restriction base="xs:string"/>
								</xs:simpleType>
							</xs:element>
						</xs:sequence>
						<xs:sequence>
							<xs:annotation>
								<xs:documentation>Sender details</xs:documentation>
							</xs:annotation>
							<xs:element name="SenderQual" minOccurs="0">
								<xs:annotation>
									<xs:documentation>Sender Id Qualifier. 
TCOMS: N/A
EDIFACT: SenderQual (UNB02)
X12: SenderQual (ISA05)</xs:documentation>
								</xs:annotation>
								<xs:simpleType>
									<xs:restriction base="xs:string"/>
								</xs:simpleType>
							</xs:element>
							<xs:element name="SenderId" minOccurs="0">
								<xs:annotation>
									<xs:documentation>Sender Id 
TCOMS: FromCode (FROM.CODE)
EDIFACT: SenderId (UNB02)
X12: SenderId (ISA06)</xs:documentation>
								</xs:annotation>
								<xs:simpleType>
									<xs:restriction base="xs:string"/>
								</xs:simpleType>
							</xs:element>
							<xs:element name="SenderName" minOccurs="0">
								<xs:annotation>
									<xs:documentation>TCOMS: FromName (FROM.NAME)
EDIFACT: N/A
X12: N/A </xs:documentation>
								</xs:annotation>
								<xs:simpleType>
									<xs:restriction base="xs:string"/>
								</xs:simpleType>
							</xs:element>
							<xs:element name="SenderRef" minOccurs="0">
								<xs:annotation>
									<xs:documentation>TCOMS: Senders Transmission reference
EDIFACT: SenderRoute (UNB02)
X12: N/A</xs:documentation>
								</xs:annotation>
								<xs:simpleType>
									<xs:restriction base="xs:string"/>
								</xs:simpleType>
							</xs:element>
						</xs:sequence>
						<xs:sequence>
							<xs:annotation>
								<xs:documentation>Receiver details</xs:documentation>
							</xs:annotation>
							<xs:element name="ReceiverQual" minOccurs="0">
								<xs:annotation>
									<xs:documentation>Receiver Id Qualifier
TCOMS: N/A
EDIFACT: RcvQual (UNB03)
X12: RecvQual (ISA07)</xs:documentation>
								</xs:annotation>
								<xs:simpleType>
									<xs:restriction base="xs:string"/>
								</xs:simpleType>
							</xs:element>
							<xs:element name="ReceiverId" minOccurs="0">
								<xs:annotation>
									<xs:documentation>Receiver Id 
TCOMS: UntoCode (UNTO.CODE)
EDIFACT: RcvId (UNB03)
X12: RecvId (ISA08)</xs:documentation>
								</xs:annotation>
								<xs:simpleType>
									<xs:restriction base="xs:string"/>
								</xs:simpleType>
							</xs:element>
							<xs:element name="ReceiverName" minOccurs="0">
								<xs:annotation>
									<xs:documentation>TCOMS: UntoName (UNTO.NAME)
EDIFACT: N/A
X12: N/A</xs:documentation>
								</xs:annotation>
								<xs:simpleType>
									<xs:restriction base="xs:string"/>
								</xs:simpleType>
							</xs:element>
							<xs:element name="ReceiverRef" minOccurs="0">
								<xs:annotation>
									<xs:documentation>TCOMS: RecvRef
EDIFACT: ReceiverRoute (UNB03)
X12: N/A</xs:documentation>
								</xs:annotation>
								<xs:simpleType>
									<xs:restriction base="xs:string"/>
								</xs:simpleType>
							</xs:element>
						</xs:sequence>
						<xs:sequence>
							<xs:annotation>
								<xs:documentation>Interchange version information</xs:documentation>
							</xs:annotation>
							<xs:element name="ControlRef" minOccurs="0">
								<xs:annotation>
									<xs:documentation>Control reference:
TCOMS: N/A
EDIFACT: ControlRef (UNB05)
X12: Controlref (ISA13)</xs:documentation>
								</xs:annotation>
								<xs:simpleType>
									<xs:restriction base="xs:string"/>
								</xs:simpleType>
							</xs:element>
							<xs:element name="AppRef" minOccurs="0">
								<xs:annotation>
									<xs:documentation>Message identification if the interchange contains only one type of message. 
TCOMS: AppRef
EDIFACT: AppRef (UNB07)
X12: N/A</xs:documentation>
								</xs:annotation>
								<xs:simpleType>
									<xs:restriction base="xs:string"/>
								</xs:simpleType>
							</xs:element>
							<xs:element name="FileGenNo" minOccurs="0">
								<xs:annotation>
									<xs:documentation>File Generation Number:
TCOMS: FileGenNo
EDIFACT: N/A
X12: N/A</xs:documentation>
								</xs:annotation>
								<xs:simpleType>
									<xs:restriction base="xs:string"/>
								</xs:simpleType>
							</xs:element>
							<xs:element name="FileVerNo" minOccurs="0">
								<xs:annotation>
									<xs:documentation>File Version Number:
TCOMS: File version no
EDIFACT: N/A
X12: N/A</xs:documentation>
								</xs:annotation>
								<xs:simpleType>
									<xs:restriction base="xs:string"/>
								</xs:simpleType>
							</xs:element>
						</xs:sequence>
						<xs:sequence>
							<xs:annotation>
								<xs:documentation>Message processing flags</xs:documentation>
							</xs:annotation>
							<xs:element name="Ackn" minOccurs="0">
								<xs:annotation>
									<xs:documentation>Acknowledge Requested. ‘0’ for No, '1' for Yes
TCOMS: N/A
EDIFACT: Ackn (UNB09)
X12: Ackn (ISA14)</xs:documentation>
								</xs:annotation>
								<xs:simpleType>
									<xs:restriction base="xs:string"/>
								</xs:simpleType>
							</xs:element>
							<xs:element name="Test" minOccurs="0">
								<xs:annotation>
									<xs:documentation>Test Indicator.
TCOMS: N/A
EDIFACT: Test (UNB11)
X12: Test (ISA15)</xs:documentation>
								</xs:annotation>
								<xs:simpleType>
									<xs:restriction base="xs:string"/>
								</xs:simpleType>
							</xs:element>
							<xs:element name="PtyCode" minOccurs="0">
								<xs:annotation>
									<xs:documentation>Priority Code
TCOMS: PtyCode
EDIFACT: PtyCode (UNB08)
X12: N/A</xs:documentation>
								</xs:annotation>
								<xs:simpleType>
									<xs:restriction base="xs:string"/>
								</xs:simpleType>
							</xs:element>
							<xs:element name="Comms" minOccurs="0">
								<xs:annotation>
									<xs:documentation>Communications agreement:
TCOMS: N/A
EDIFACT: Comms (UNB10)
X12: N/A</xs:documentation>
								</xs:annotation>
								<xs:simpleType>
									<xs:restriction base="xs:string"/>
								</xs:simpleType>
							</xs:element>
						</xs:sequence>
						<xs:sequence>
							<xs:annotation>
								<xs:documentation>Formatting </xs:documentation>
							</xs:annotation>
							<xs:element name="SubElem" minOccurs="0">
								<xs:annotation>
									<xs:documentation>Subelement Separator. Usually '>' (ISA16)
TCOMS: N/A
EDIFACT: N/A
X12: SubElem (ISA16)</xs:documentation>
								</xs:annotation>
								<xs:simpleType>
									<xs:restriction base="xs:string"/>
								</xs:simpleType>
							</xs:element>
							<xs:element name="ReelID" minOccurs="0">
								<xs:annotation>
									<xs:documentation>Reel ID
TCOMS: RellID
EDIFACT: N/A
X12: N/A</xs:documentation>
								</xs:annotation>
								<xs:simpleType>
									<xs:restriction base="xs:string"/>
								</xs:simpleType>
							</xs:element>
						</xs:sequence>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="Additional" type="AdditionalInfo" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Additional information applicable to the whole message.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="MessageTrailer">
		<xs:annotation>
			<xs:documentation>Message Summary</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="TotalDocs" type="xs:long">
				<xs:annotation>
					<xs:documentation>Total number of documents within the message</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="Narrative">
		<xs:annotation>
			<xs:documentation>Narrative text</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="RefNo" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>A reference number that provides a unique reference to the narrative below. This could be a delivery instruction reference number, for example.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Line1" minOccurs="0">
				<xs:annotation>
					<xs:documentation>40 character narrative line</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:maxLength value="40"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="Line2" minOccurs="0">
				<xs:annotation>
					<xs:documentation>40 character narrative line</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:maxLength value="40"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="Line3" minOccurs="0">
				<xs:annotation>
					<xs:documentation>40 character narrative line</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:maxLength value="40"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="Line4" minOccurs="0">
				<xs:annotation>
					<xs:documentation>40 character narrative line</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:maxLength value="40"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="Line5" minOccurs="0">
				<xs:annotation>
					<xs:documentation>40 character narrative line</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:maxLength value="40"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="Notes">
		<xs:annotation>
			<xs:documentation>Notes</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="Seq" type="xs:long" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Counter for the notes</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Code" type="CodeValue" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Code (table/value) agreed between the two trading partners.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Narrative" type="Narrative" minOccurs="0">
				<xs:annotation>
					<xs:documentation>General narrative</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:sequence minOccurs="0">
				<xs:annotation>
					<xs:documentation>Registered Text.  This section is used by trading partners to pass additional information between them that is specific to their trading relationship.</xs:documentation>
				</xs:annotation>
				<xs:element name="Reg1Code" type="xs:string" minOccurs="0">
					<xs:annotation>
						<xs:documentation>Code agreed between parties</xs:documentation>
					</xs:annotation>
				</xs:element>
				<xs:element name="Reg1Text" type="xs:string" minOccurs="0">
					<xs:annotation>
						<xs:documentation>Text associated with the code</xs:documentation>
					</xs:annotation>
				</xs:element>
				<xs:element name="Reg2Code" type="xs:string" minOccurs="0">
					<xs:annotation>
						<xs:documentation>Code agreed between parties</xs:documentation>
					</xs:annotation>
				</xs:element>
				<xs:element name="Reg2Text" type="xs:string" minOccurs="0">
					<xs:annotation>
						<xs:documentation>Text associated with the code</xs:documentation>
					</xs:annotation>
				</xs:element>
				<xs:element name="Reg3Code" type="xs:string" minOccurs="0">
					<xs:annotation>
						<xs:documentation>Code agreed between parties</xs:documentation>
					</xs:annotation>
				</xs:element>
				<xs:element name="Reg3Text" type="xs:string" minOccurs="0">
					<xs:annotation>
						<xs:documentation>Text associated with the code</xs:documentation>
					</xs:annotation>
				</xs:element>
				<xs:element name="Reg4Code" type="xs:string" minOccurs="0">
					<xs:annotation>
						<xs:documentation>Code agreed between parties</xs:documentation>
					</xs:annotation>
				</xs:element>
				<xs:element name="Reg4Text" type="xs:string" minOccurs="0">
					<xs:annotation>
						<xs:documentation>Text associated with the code</xs:documentation>
					</xs:annotation>
				</xs:element>
				<xs:element name="Reg5Code" type="xs:string" minOccurs="0">
					<xs:annotation>
						<xs:documentation>Code agreed between parties</xs:documentation>
					</xs:annotation>
				</xs:element>
				<xs:element name="Reg5Text" type="xs:string" minOccurs="0">
					<xs:annotation>
						<xs:documentation>Text associated with the code</xs:documentation>
					</xs:annotation>
				</xs:element>
			</xs:sequence>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="Price">
		<xs:annotation>
			<xs:documentation>Price structure</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="Price" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Price per traded unit, or optional measure indicator (below)</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:decimal">
						<xs:minInclusive value="-999999999999.9999999"/>
						<xs:maxInclusive value="999999999999.9999999"/>
						<xs:totalDigits value="19"/>
						<xs:fractionDigits value="7"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="Measure" type="xs:string" default="EA" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Pricing unit of measure.   Can be blank, 1 or EA to indicate price per unit.  Can be a number to indicate price per number of units.  Can be a measure name, ie TONNE, KG to indicate price per weight/volume, size.  Defaults to EA to signify per unit.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="MeasureQty" type="xs:long" default="1" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Used in conjunction with the measure code to determine the number of measure units being priced.  For example, a measure of 'KG' and a measureQty of 30 means the price is per 30Kg.  Defaults to 1.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="PricingMeasure">
		<xs:annotation>
			<xs:documentation>Pricing measure structure - declares how a price is derived.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="Measure" type="xs:string" default="EA" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Pricing unit of measure.   Can be blank, 1 or EA to indicate price per unit.  Can be a number to indicate price per number of units.  Can be a measure name, ie TONNE, KG to indicate price per weight/volume, size.  Defaults to EA to signify per unit.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="MeasureQty" type="xs:long" default="1" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Used in conjunction with the measure code to determine the number of measure units being priced.  For example, a measure of 'KG' and a measureQty of 30 means the price is per 30Kg.  Defaults to 1.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="Quantity">
		<xs:annotation>
			<xs:documentation>Quantity structure</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:annotation>
				<xs:documentation>The units and measure pair enable a quantity of traded units OR a weight/volume measure to be described</xs:documentation>
			</xs:annotation>
			<xs:element name="Unit">
				<xs:annotation>
					<xs:documentation>No of units.  If UOM is blank or 'EA' then this indicates traded units, otherwise it represents units of volume, weight, etc.</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:decimal">
						<xs:minInclusive value="-9999999999999999.999"/>
						<xs:maxInclusive value="9999999999999999.999"/>
						<xs:totalDigits value="19"/>
						<xs:fractionDigits value="3"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="UOM" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Unit of measure associated with the quantity. Ie EA, KG</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="RevisionDetails">
		<xs:annotation>
			<xs:documentation>Revision Number, date and time. </xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="Num" type="xs:long" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Revision Number</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Date" type="DateTime" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Revision Date</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="SupplierItem">
		<xs:annotation>
			<xs:documentation>Item codes structure</xs:documentation>
		</xs:annotation>
		<xs:sequence minOccurs="0">
			<xs:element name="Code" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Supplier's internal reference (non-EAN) identifying the traded unit</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="EAN13" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>13 digit EAN code</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="EAN8" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>8 digit EAN code</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="UPC12" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>12 digit Universal Product Code</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DUN14" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>14 digit DUN code</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ISBN" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>ISBN (for book trade only)</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ManufacturersCode" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Original manufacturers code</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="TUC14" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Traded Unit Code. 14 digits. (generally a zero appended to the front of a 13 digit item no.)</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Desc1" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Supplier's item Description line 1</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Desc2" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Supplier's item description line 2</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="Tax">
		<xs:annotation>
			<xs:documentation>Tax structure</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:annotation>
				<xs:documentation>Details the tax code, the percentage tax rate, and the amount of tax payable at this rate</xs:documentation>
			</xs:annotation>
			<xs:element name="TaxCode" type="xs:string">
				<xs:annotation>
					<xs:documentation>Tax code</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="TaxPcnt">
				<xs:annotation>
					<xs:documentation>Tax percentage</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:decimal">
						<xs:minInclusive value="-999.99"/>
						<xs:maxInclusive value="999.99"/>
						<xs:totalDigits value="5"/>
						<xs:fractionDigits value="2"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="DutyCode">
				<xs:annotation>
					<xs:documentation>Determines wheter tax is applied. Choices are: STD, EXPORT, ZERO</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:pattern value="STD"/>
						<xs:pattern value="EXPORT"/>
						<xs:pattern value="ZERO"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="Taxable" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Taxable amount. This is the value that is subject to tax.</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:decimal">
						<xs:minInclusive value="-9999999999999999.999"/>
						<xs:maxInclusive value="9999999999999999.999"/>
						<xs:totalDigits value="19"/>
						<xs:fractionDigits value="3"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="Payable" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Tax amount payable in the invoicing currency</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:decimal">
						<xs:minInclusive value="-9999999999999999.999"/>
						<xs:maxInclusive value="9999999999999999.999"/>
						<xs:totalDigits value="19"/>
						<xs:fractionDigits value="3"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="TaxControl">
		<xs:annotation>
			<xs:documentation>Tax Control structure</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="Summary" type="TaxControlSummary">
				<xs:annotation>
					<xs:documentation>Summary details.  Period dates, control number, etc</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Document" type="TaxControlDocs" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>Tax control documents.  One occurrence is required per document type.  So, if a message is generated containing 10 invoices and 1 credit then this section will require 2 documents:  1 for the invoices, and 1 for the credit.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="TaxControlDocs">
		<xs:annotation>
			<xs:documentation>Tax Control Document Details</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="DocType">
				<xs:annotation>
					<xs:documentation>Describes the message type. INVOICE, CREDIT, DEBIT</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:enumeration value="ORDER"/>
						<xs:enumeration value="ORDER_ACK"/>
						<xs:enumeration value="ASN"/>
						<xs:enumeration value="INVOICE"/>
						<xs:enumeration value="DEBIT"/>
						<xs:enumeration value="CREDIT"/>
						<xs:enumeration value="REMITTANCE"/>
						<xs:enumeration value="STOCK_SNAPSHOT"/>
						<xs:enumeration value="RECEIPT"/>
						<xs:enumeration value="BOOKING"/>
						<xs:enumeration value="PRODUCT_PLANNING"/>
						<xs:enumeration value="PRICELIST"/>
						<xs:enumeration value="FUNCTIONAL_ACK"/>
						<xs:enumeration value="TEXT"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="DocCount" type="xs:long">
				<xs:annotation>
					<xs:documentation>Number of documents included in the summary</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="TaxableAmount" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Extended amount for the specified invoices lines, after inclusion of settlement discounts. Before VAT applied.                             =  ExtAmount -DiscountAmount.  This is the sum of the invoice ExtAmountInclSDisc</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:decimal">
						<xs:minInclusive value="-999999999999.9999999"/>
						<xs:maxInclusive value="999999999999.9999999"/>
						<xs:totalDigits value="19"/>
						<xs:fractionDigits value="7"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="VATAmount" minOccurs="0">
				<xs:annotation>
					<xs:documentation>VAT amount payable for specified invoice lines.  This is the sum of the invoice VATAmount</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:decimal">
						<xs:minInclusive value="-999999999999.9999999"/>
						<xs:maxInclusive value="999999999999.9999999"/>
						<xs:totalDigits value="19"/>
						<xs:fractionDigits value="7"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="TotalInclVAT" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Payable amount, after settlement discount, including VAT. For the specified invoice lines.  This is the sum of the invoice ExtAmountInclSDscAndVAT</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:decimal">
						<xs:minInclusive value="-999999999999.9999999"/>
						<xs:maxInclusive value="999999999999.9999999"/>
						<xs:totalDigits value="19"/>
						<xs:fractionDigits value="7"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="Additional" type="AdditionalInfo" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Additional reference information.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="VATDetail" type="TaxControlVAT" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>VAT Breakdown for the document.  One occurrence per document VAT code.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="TaxControlSummary">
		<xs:annotation>
			<xs:documentation>Tax Control Summary</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="Generated" type="DateTime" minOccurs="0"/>
			<xs:element name="AccountingPeriodStart" type="DateTime" minOccurs="0"/>
			<xs:element name="AccountingPeriodEnd" type="DateTime" minOccurs="0"/>
			<xs:element name="TaxPeriodStart" type="DateTime" minOccurs="0"/>
			<xs:element name="TaxPeriodEnd" type="DateTime" minOccurs="0"/>
			<xs:element name="InvoicingPeriodStart" type="DateTime" minOccurs="0"/>
			<xs:element name="InvoicingPeriodEnd" type="DateTime" minOccurs="0"/>
			<xs:element name="CtrlNo" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Tax control number</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="PrevCtrlNo" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Previous tax control number</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Customer" type="Address" minOccurs="0"/>
			<xs:element name="CustomerAgent" type="Address" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Customer's agent / representative.  Only used when the TaxControl is being used for period summary purposes, and the message is to be issued to a third party.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Supplier" type="Address" minOccurs="0"/>
			<xs:element name="SupplierAgent" type="Address" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Supplier's agent / representative.  Only used when the TaxControl is being used for period summary purposes, and the message is to be issued to a third party.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="TaxOffice" type="Address" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Tax collector's office.  Only used when the TaxControl is being used for period summary purposes, and the message is to be issued to a third party.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="MsgRef" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Original Message Reference to the original invoice/credit/debit interchange reference  </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="MsgRefDate" type="DateTime" minOccurs="0"/>
			<xs:element name="Additional" type="AdditionalInfo" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Additional reference information.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="TaxControlVAT">
		<xs:annotation>
			<xs:documentation>Tax Control VAT details (per doc)</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="VATCode" type="xs:string">
				<xs:annotation>
					<xs:documentation>VAT Code</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="VATPercentage">
				<xs:annotation>
					<xs:documentation>VAT Percentage</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:decimal">
						<xs:minInclusive value="-999.999"/>
						<xs:maxInclusive value="999.999"/>
						<xs:totalDigits value="6"/>
						<xs:fractionDigits value="3"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="Currency" type="Currency">
				<xs:annotation>
					<xs:documentation>Currency details</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DutyCode" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Determines wheter tax is applied. Choices are: STD, EXPORT, ZERO</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:pattern value="STD"/>
						<xs:pattern value="EXPORT"/>
						<xs:pattern value="ZERO"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="TaxableAmount" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Extended amount for the specified invoices lines, after inclusion of settlement discounts. Before VAT applied.                             =  ExtAmount -DiscountAmount.  This is the sum of the invoice ExtAmountInclSDisc</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:decimal">
						<xs:minInclusive value="-999999999999.9999999"/>
						<xs:maxInclusive value="999999999999.9999999"/>
						<xs:totalDigits value="19"/>
						<xs:fractionDigits value="7"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="VATAmount" minOccurs="0">
				<xs:annotation>
					<xs:documentation>VAT amount payable for specified invoice lines.  This is the sum of the invoice VATAmount</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:decimal">
						<xs:minInclusive value="-999999999999.9999999"/>
						<xs:maxInclusive value="999999999999.9999999"/>
						<xs:totalDigits value="19"/>
						<xs:fractionDigits value="7"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="TotalInclVAT" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Payable amount, after settlement discount, including VAT. For the specified invoice lines.  This is the sum of the invoice ExtAmountInclSDscAndVAT</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:decimal">
						<xs:minInclusive value="-999999999999.9999999"/>
						<xs:maxInclusive value="999999999999.9999999"/>
						<xs:totalDigits value="19"/>
						<xs:fractionDigits value="7"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="Additional" type="AdditionalInfo" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Additional reference information.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="TransactionType">
		<xs:annotation>
			<xs:documentation>Transaction type structure, allowing single values only</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="MsgType" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Message type supplied within the original message. ie.  ORDHDR, or 850</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:maxLength value="10"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="MsgVer" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Message version supplied within the original message. Ie.  '9' for TCOM Simple Order Version 9.</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:long">
						<xs:totalDigits value="4"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="TranCode" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Transaction code supplied within the original message. Ie.  '0470' for mixed order.</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:maxLength value="20"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="TranType" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Transaction type supplied within the original message.</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:maxLength value="20"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="UnitOfOrderQty">
		<xs:annotation>
			<xs:documentation>Unit Of Ordering Quantity (with order measure)</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="Unit" type="xs:long" minOccurs="0">
				<xs:annotation>
					<xs:documentation>No of consumer units. This is effectively the pack or box quantity </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="OrderMeasure" type="xs:long" minOccurs="0">
				<xs:annotation>
					<xs:documentation>This is the size of each consumer unit in terms of weight, size or volume.  A customer may order a pack containing 12 330ml cans.  In this case unit will be 12, ordermeasure will be 330, and uom will be ML  </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="UOM" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Unit of measure associated with the ordering measure.  Ie EA, KG</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="UserDefined">
		<xs:annotation>
			<xs:documentation>User defined fields</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="Alpha1" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>User-defined alpha 1</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Alpha2" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>User-defined alpha 2</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Alpha3" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>User-defined alpha 3</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Alpha4" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>User-defined alpha 4</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Alpha5" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>User-defined alpha 5</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Num1" minOccurs="0">
				<xs:annotation>
					<xs:documentation>User-defined number 1</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:decimal">
						<xs:totalDigits value="19"/>
						<xs:fractionDigits value="3"/>
						<xs:minInclusive value="-9999999999999999.999"/>
						<xs:maxInclusive value="9999999999999999.999"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="Num2" minOccurs="0">
				<xs:annotation>
					<xs:documentation>User-defined number 2</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:decimal">
						<xs:minInclusive value="-9999999999999999.999"/>
						<xs:maxInclusive value="9999999999999999.999"/>
						<xs:totalDigits value="19"/>
						<xs:fractionDigits value="3"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="Num3" minOccurs="0">
				<xs:annotation>
					<xs:documentation>User-defined number 3</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:decimal">
						<xs:minInclusive value="-9999999999999999.999"/>
						<xs:maxInclusive value="9999999999999999.999"/>
						<xs:totalDigits value="19"/>
						<xs:fractionDigits value="3"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="Num4" minOccurs="0">
				<xs:annotation>
					<xs:documentation>User-defined number 4</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:decimal">
						<xs:minInclusive value="-9999999999999999.999"/>
						<xs:maxInclusive value="9999999999999999.999"/>
						<xs:totalDigits value="19"/>
						<xs:fractionDigits value="3"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="Num5" minOccurs="0">
				<xs:annotation>
					<xs:documentation>User-defined number 5</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:decimal">
						<xs:minInclusive value="-9999999999999999.999"/>
						<xs:maxInclusive value="9999999999999999.999"/>
						<xs:totalDigits value="19"/>
						<xs:fractionDigits value="3"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="VAT">
		<xs:annotation>
			<xs:documentation>VAT Registration number (numeric and alpha format)</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="Num" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Trader's VAT number allocated by HM Customs and Excise.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Alpha" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Government department or non-UK VAT number</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="ViewProfile">
		<xs:annotation>
			<xs:documentation>View Profile options</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="Key" type="xs:string">
				<xs:annotation>
					<xs:documentation>Profile option key</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Value" type="xs:string">
				<xs:annotation>
					<xs:documentation>Profile option value</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="Volume">
		<xs:annotation>
			<xs:documentation>Volume structure</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="Volume">
				<xs:annotation>
					<xs:documentation>Volume </xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:decimal">
						<xs:minInclusive value="0"/>
						<xs:maxInclusive value="9999999999999999.999"/>
						<xs:totalDigits value="19"/>
						<xs:fractionDigits value="3"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="Measure" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Volume unit of measure</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="Weight">
		<xs:annotation>
			<xs:documentation>Weight structure</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="Weight">
				<xs:annotation>
					<xs:documentation>Weight</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:decimal">
						<xs:totalDigits value="19"/>
						<xs:fractionDigits value="3"/>
						<xs:minInclusive value="0"/>
						<xs:maxInclusive value="9999999999999999.999"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="Measure" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Weight unit of measure</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
</xs:schema>
