<?xml version="1.0" encoding="UTF-8"?>
<tc:TrueCommerceOrder xmlns:tc="http://www.truecommerce.com/docs/order" xmlns:cm="http://www.truecommerce.com/docs/common/components" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.truecommerce.com/docs/order truecommerce_order_v1.0.xsd">
	<MsgHeader>
		<MsgType>ORDER</MsgType>
		<VersionID>v1.0</VersionID>
		<TransmittedDate>
			<Date>2018-04-10</Date> 
			<Time>15:55:03</Time>
		</TransmittedDate>
		<FileDate>
			<Date>2018-04-10</Date>
			<Time>15:55:03</Time>
		</FileDate>
		<Interchange>
			<Standard>TRUECOMMERCE</Standard>
			<SenderRef>2483</SenderRef> Auftragskopf/Loginfos
		</Interchange>
	</MsgHeader>
	<Document>
		<DocHeader>
			<DocType>ORDER</DocType>
			<DocFunction>ORIGINAL</DocFunction>
			<CustAddr>
				<Code>00010</Code> Kundennummer
				<EAN13>5000020000115</EAN13> Kunden/KDLEH - verküpfung zu kundendaten
				<Name>ABC Foods Limited</Name>
				<Address1>1 High Street</Address1>
				<Address5>London</Address5>
				<Country>GB</Country>
				<VAT>
					<Num>***********-123</Num>
				</VAT>
				<Contact1>
					<Name>Carl Denver</Name>
					<Phone>********** 456</Phone>
					<Fax>********** 457</Fax>
				</Contact1>
				<Contact2>
					<Name>Elisabeth Francis</Name>
					<Phone>********** 458</Phone>
				</Contact2>
			</CustAddr>
			<SuppAddr> -> Das ist Mandant
				<Code>TC30816</Code>
				<EAN13>5024657121929</EAN13> -> prüfen ob das stimmt
				<Name>DemoCo</Name>
				<Address1>DemoCo House</Address1>
				<Address2>100 High Street</Address2>
				<Address5>Worcester</Address5>
				<PostCode>WR5 1AA</PostCode>
				<Country>GB</Country>
				<VAT>
					<Num>*********</Num>
					<Alpha>GB*********</Alpha>
				</VAT>
				<Contact1>
					<Name>Ellie Jones</Name>
					<Phone>01905 321987</Phone>
					<Email><EMAIL></Email>
					<Fax>01905 321988</Fax>
				</Contact1>
				<Contact2>
					<Name>Elizabeth Kilbey</Name>
					<Phone>01905 321986</Phone>
					<Email><EMAIL></Email>
					<Fax>01905 321985</Fax>
				</Contact2>
			</SuppAddr>
			<DocDate>
				<Date>2018-04-10</Date>Auftragskopf/Auftragsdatum
				<Time>15:46:37</Time>
			</DocDate>
			<TranInfo/>
			<RoutingCode>T80410AAGB</RoutingCode>
		</DocHeader>
		<OrderHeader>
			<CustOrder>CO-001-0001</CustOrder> Auftragskopf/Bestellnummer
			<CustOrderDate> Auftragskopf/Auftragsdatum ????
				<Date>2018-04-10</Date>
				<Time>15:46:00</Time>
			</CustOrderDate>
			<SuppOrder>SO0010001</SuppOrder>
			<OrderClass>A</OrderClass>
			<OrderBrand>BR001</OrderBrand>
			<OrderCode>NEW</OrderCode> Auftragskop neu oder storno 
			<Perishable>false</Perishable>
			<BookingRef>BR-001-0001</BookingRef>
			<OrigCustOrder>OCR-001-0001</OrigCustOrder> erst nicht wichtig -> Vllt Kontragt
			<Delivery>
				<DelMethod>DELIVER_TO_DEPOT</DelMethod> eigentlisch immer DELIVER_TO_DEPOT aber für uns nicht wichhtig
				<ReqDesp>
					<Date>2018-04-10</Date>
					<Time>21:59:00</Time>
				</ReqDesp>
				<EarliestDesp>
					<Date>2018-04-10</Date>
					<Time>22:59:00</Time>
				</EarliestDesp>
				<LatestDesp>
					<Date>2018-04-10</Date>
					<Time>22:59:00</Time>
				</LatestDesp>
				<ReqDel>
					<Date>2018-04-10</Date>
					<Time>22:59:00</Time>
				</ReqDel>
				<EarliestDel>Auftragskopf/LVonDatum
					<Date>2018-04-10</Date>
					<Time>22:59:00</Time>
				</EarliestDel>
				<LatestDel>Auftragskopf/LBIsDatum Auftragskopf/ELDatum
					<Date>2018-04-10</Date>
					<Time>22:59:00</Time>
				</LatestDel>
				<LatestDelLeadTime/>
				<DespatchFrom> Abholaddress /Mandant
					<Code>000</Code>
					<EAN13>5000010000019</EAN13>
					<Name>DemoCo</Name>
					<Address1>DemoCo House</Address1>
					<Address2>100 High Street</Address2>
					<Address5>Worcester</Address5>
					<PostCode>WR5 1AA</PostCode>
					<Country>GB</Country>
					<VAT>
						<Num>*********</Num>
						<Alpha>GB*********</Alpha>
					</VAT>
					<Contact1>
						<Name>Ellie Jones</Name>
						<Phone>01905 321987</Phone>
						<Email><EMAIL></Email>
						<Fax>01905 321988</Fax>
					</Contact1>
					<Contact2>
						<Name>Elizabeth Kilbey</Name>
						<Phone>01905 321986</Phone>
						<Email><EMAIL></Email>
						<Fax>01905 321985</Fax>
					</Contact2>
				</DespatchFrom>
				<DeliverTo>/Warenempfänge 
					<Code>000</Code>
					<EAN13>5024657121941</EAN13>
					<Name>ABC Foods Limited</Name>
					<Address1>1 High Street</Address1>
					<Address5>London</Address5>
					<Country>GB</Country>
					<VAT>
						<Num>***********-123</Num>
						<Alpha>***********-123</Alpha>
					</VAT>
					<Contact1>
						<Name>Carl Denver</Name>
						<Phone>********** 456</Phone>
						<Fax>********** 457</Fax>
					</Contact1>
					<Contact2>
						<Name>Elisabeth Francis</Name>
						<Phone>********** 458</Phone>
					</Contact2>
				</DeliverTo>
				<Instructions>
					<Line1>Please deliver to gate G1</Line1>
				</Instructions>
				<DelCombined>false</DelCombined>
				<MultipleDrops>false</MultipleDrops>
			</Delivery>
			<Locations>
				<OrderBranch> noch nicht notwendig/ besteller -> Intressant bei Fr. Kunden (LEH)
					<Code>000</Code>
					<EAN13>5024657121941</EAN13>
					<Name>ABC Foods Limited</Name>
					<Address1>1 High Street</Address1>
					<Address5>London</Address5>
					<Country>GB</Country>
					<VAT>
						<Num>***********-123</Num>
						<Alpha>***********-123</Alpha>
					</VAT>
					<Contact1>
						<Name>Carl Denver</Name>
						<Phone>********** 456</Phone>
						<Fax>********** 457</Fax>
					</Contact1>
					<Contact2>
						<Name>Elisabeth Francis</Name>
						<Phone>********** 458</Phone>
					</Contact2>
				</OrderBranch>
				<InvoiceTo> //REchnungsempfänger
					<Code>000</Code>
					<Name>ABC Foods Limited (Retail Division)</Name>
					<Address1>1 High Street</Address1>
					<Address5>London</Address5>
					<Country>GB</Country>
					<VAT>
						<Num>***********-123</Num>
						<Alpha>***********-123</Alpha>
					</VAT>
					<Contact1>
						<Name>Carl Denver</Name>
						<Phone>********** 456</Phone>
						<Fax>********** 457</Fax>
					</Contact1>
					<Contact2>
						<Name>Elisabeth Francis</Name>
						<Phone>********** 458</Phone>
					</Contact2>
				</InvoiceTo>
				<InvoiceFrom>// Mandan
					<Code>000</Code>
					<EAN13>5000010000019</EAN13>
					<Name>DemoCo (Research) Ltd</Name>
					<Address1>DemoCo House</Address1>
					<Address2>100 High Street</Address2>
					<Address5>Worcester</Address5>
					<PostCode>WR5 1AA</PostCode>
					<Country>GB</Country>
					<VAT>
						<Num>*********</Num>
						<Alpha>GB*********</Alpha>
					</VAT>
					<Contact1>
						<Name>Ellie Jones</Name>
						<Phone>01905 321987</Phone>
						<Email><EMAIL></Email>
						<Fax>01905 321988</Fax>
					</Contact1>
					<Contact2>
						<Name>Elizabeth Kilbey</Name>
						<Phone>01905 321986</Phone>
						<Email><EMAIL></Email>
						<Fax>01905 321985</Fax>
					</Contact2>
				</InvoiceFrom>
			</Locations>
			<Notes>
				<Seq>1</Seq>
				<Narrative>
					<Line1>This is in relation to contract 989-0987</Line1>
				</Narrative>
			</Notes>
			<Revision/>
			<TotalOrderUnits>60</TotalOrderUnits>
			<TotalOrderVal>840.000</TotalOrderVal>
			<PricingDate>
				<Date>2018-04-10</Date>
				<Time>00:00:00</Time>
			</PricingDate>
			<Currency>
				<InvCurr>GBP</InvCurr>
				<PayCurr>GBP</PayCurr>
			</Currency>
			<AckReq>false</AckReq>
			<LoadPlan>false</LoadPlan>
			<Authorised>false</Authorised>
		</OrderHeader>
		<OrderLine>
			<LineNo>1</LineNo>
			<LineCode>NEW</LineCode>
			<Item>
				<CustItem>
					<OwnBrandEAN>1213192703201</OwnBrandEAN> 
					<Code>LMS</Code>
					<SKU>SKULMS</SKU>
				</CustItem>
				<SuppItem>
					<Code>LMS</Code>
					<EAN13>1213192703202</EAN13>/Artikel/ArtEAN
					<UPC12>121319270320</UPC12>
					<DUN14>12131927032011</DUN14>
					<ISBN>sLMS</ISBN>
					<ManufacturersCode>MCLMS</ManufacturersCode>
					<Desc1>(s) Large Mixed Salad</Desc1>
				</SuppItem>
				<RetailEAN>1213192703201</RetailEAN>
				<Desc1>Large Mixed Salad</Desc1>
			</Item>
			<Perishable>false</Perishable>
			<UnitOfOrder>
				<Unit>1</Unit>
				<OrderMeasure>0</OrderMeasure>
			</UnitOfOrder>
			<OrderQty>
				<Unit>10.000</Unit>
				<UOM>CASE</UOM>
			</OrderQty>
			<PricingMeasure>
				<MeasureQty>1</MeasureQty>
			</PricingMeasure>
			<CostPrice>18.000</CostPrice> einzelpreis
			<LineAmount>180.000</LineAmount> gesamt 
			<SellPrice>
				<MeasureQty>1</MeasureQty>
			</SellPrice>
			<Notes>
				<Seq>1</Seq>
				<Narrative>
					<Line1>No substitutes for this item.</Line1> // soll einen TE pos werden
				</Narrative>
			</Notes>
			<Delivery>
				<ReqDesp>
					<Date>2018-04-10</Date>
					<Time>22:59:00</Time>
				</ReqDesp>
				<ReqDel>
					<Date>2018-04-10</Date>
					<Time>22:59:00</Time>
				</ReqDel>
				<ReqQty>
					<Unit>10.000</Unit>
					<UOM>CASE</UOM>
				</ReqQty>
			</Delivery>
			<OrderBranch>
				<Code>000</Code>
				<EAN13>5024657121941</EAN13>
				<Name>ABC Foods Limited</Name>
				<Address1>1 High Street</Address1>
				<Address5>London</Address5>
				<Country>GB</Country>
				<VAT>
					<Num>***********-123</Num>
					<Alpha>***********-123</Alpha>
				</VAT>
				<Contact1>
					<Name>Carl Denver</Name>
					<Phone>********** 456</Phone>
					<Fax>********** 457</Fax>
				</Contact1>
				<Contact2>
					<Name>Elisabeth Francis</Name>
					<Phone>********** 458</Phone>
				</Contact2>
			</OrderBranch>
			<DespatchFrom>
				<Code>000</Code>
				<EAN13>5000010000019</EAN13>
				<Name>DemoCo</Name>
				<Address1>DemoCo House</Address1>
				<Address2>100 High Street</Address2>
				<Address5>Worcester</Address5>
				<PostCode>WR5 1AA</PostCode>
				<Country>GB</Country>
				<VAT>
					<Num>*********</Num>
					<Alpha>GB*********</Alpha>
				</VAT>
				<Contact1>
					<Name>Ellie Jones</Name>
					<Phone>01905 321987</Phone>
					<Email><EMAIL></Email>
					<Fax>01905 321988</Fax>
				</Contact1>
				<Contact2>
					<Name>Elizabeth Kilbey</Name>
					<Phone>01905 321986</Phone>
					<Email><EMAIL></Email>
					<Fax>01905 321985</Fax>
				</Contact2>
			</DespatchFrom>
			<DeliverTo>
				<Code>000</Code>
				<EAN13>5024657121941</EAN13>
				<Name>ABC Foods Limited</Name>
				<Address1>1 High Street</Address1>
				<Address5>London</Address5>
				<Country>GB</Country>
				<VAT>
					<Num>***********-123</Num>
					<Alpha>***********-123</Alpha>
				</VAT>
				<Contact1>
					<Name>Carl Denver</Name>
					<Phone>********** 456</Phone>
					<Fax>********** 457</Fax>
				</Contact1>
				<Contact2>
					<Name>Elisabeth Francis</Name>
					<Phone>********** 458</Phone>
				</Contact2>
			</DeliverTo>
			<Tax> / nicht notwendig soll aus Artikel kommen
				<TaxCode>Z</TaxCode>
				<TaxPcnt>0.00</TaxPcnt>
				<DutyCode>STD</DutyCode>
			</Tax>
		</OrderLine>
		<OrderLine>
			<LineNo>2</LineNo>
			<LineCode>NEW</LineCode>
			<Item>
				<CustItem>
					<OwnBrandEAN>1913192708201</OwnBrandEAN>
					<Code>SMS</Code>
					<SKU>SKUSMS</SKU>
				</CustItem>
				<SuppItem>
					<Code>sSMS</Code>
					<EAN13>1913192708203</EAN13>
					<UPC12>191319270820</UPC12>
					<DUN14>19131927082011</DUN14>
					<ISBN>sSMS</ISBN>
					<ManufacturersCode>MCSMS</ManufacturersCode>
					<Desc1>Small Mixed Salad</Desc1>
				</SuppItem>
				<RetailEAN>1913192708202</RetailEAN>
				<Desc1>Small Mixed Salad</Desc1>
			</Item>
			<Perishable>false</Perishable>
			<UnitOfOrder>
				<Unit>1</Unit>
				<OrderMeasure>0</OrderMeasure>
			</UnitOfOrder>
			<OrderQty>
				<Unit>20.000</Unit>
				<UOM>CASE</UOM>
			</OrderQty>
			<PricingMeasure>
				<MeasureQty>1</MeasureQty>
			</PricingMeasure>
			<CostPrice>12.000</CostPrice>
			<LineAmount>240.000</LineAmount>
			<SellPrice>
				<MeasureQty>1</MeasureQty>
			</SellPrice>
			<Delivery>
				<ReqDesp>
					<Date>2018-04-10</Date>
					<Time>22:59:00</Time>
				</ReqDesp>
				<ReqDel>
					<Date>2018-04-10</Date>
					<Time>22:59:00</Time>
				</ReqDel>
				<ReqQty>
					<Unit>20.000</Unit>
					<UOM>CASE</UOM>
				</ReqQty>
			</Delivery>
			<OrderBranch>
				<Code>000</Code>
				<EAN13>5024657121941</EAN13>
				<Name>ABC Foods Limited</Name>
				<Address1>1 High Street</Address1>
				<Address5>London</Address5>
				<Country>GB</Country>
				<VAT>
					<Num>***********-123</Num>
					<Alpha>***********-123</Alpha>
				</VAT>
				<Contact1>
					<Name>Carl Denver</Name>
					<Phone>********** 456</Phone>
					<Fax>********** 457</Fax>
				</Contact1>
				<Contact2>
					<Name>Elisabeth Francis</Name>
					<Phone>********** 458</Phone>
				</Contact2>
			</OrderBranch>
			<DespatchFrom>
				<Code>000</Code>
				<EAN13>5000010000019</EAN13>
				<Name>DemoCo</Name>
				<Address1>DemoCo House</Address1>
				<Address2>100 High Street</Address2>
				<Address5>Worcester</Address5>
				<PostCode>WR5 1AA</PostCode>
				<Country>GB</Country>
				<VAT>
					<Num>*********</Num>
					<Alpha>GB*********</Alpha>
				</VAT>
				<Contact1>
					<Name>Ellie Jones</Name>
					<Phone>01905 321987</Phone>
					<Email><EMAIL></Email>
					<Fax>01905 321988</Fax>
				</Contact1>
				<Contact2>
					<Name>Elizabeth Kilbey</Name>
					<Phone>01905 321986</Phone>
					<Email><EMAIL></Email>
					<Fax>01905 321985</Fax>
				</Contact2>
			</DespatchFrom>
			<DeliverTo>
				<Code>000</Code>
				<EAN13>5024657121941</EAN13>
				<Name>ABC Foods Limited</Name>
				<Address1>1 High Street</Address1>
				<Address5>London</Address5>
				<Country>GB</Country>
				<VAT>
					<Num>***********-123</Num>
					<Alpha>***********-123</Alpha>
				</VAT>
				<Contact1>
					<Name>Carl Denver</Name>
					<Phone>********** 456</Phone>
					<Fax>********** 457</Fax>
				</Contact1>
				<Contact2>
					<Name>Elisabeth Francis</Name>
					<Phone>********** 458</Phone>
				</Contact2>
			</DeliverTo>
			<Tax>
				<TaxCode>Z</TaxCode>
				<TaxPcnt>0.00</TaxPcnt>
				<DutyCode>STD</DutyCode>
			</Tax>
		</OrderLine>
		<OrderLine>
			<LineNo>3</LineNo>
			<LineCode>NEW</LineCode>
			<Item>
				<CustItem>
					<OwnBrandEAN>131319270118</OwnBrandEAN>
					<Code>MMS</Code>
					<SKU>SKU-001-0002</SKU>
				</CustItem>
				<SuppItem>
					<Code>MMS</Code>
					<EAN13>1313192701204</EAN13>
					<UPC12>131319270121</UPC12>
					<DUN14>1313192701181</DUN14>
					<ISBN>sMMS</ISBN>
					<ManufacturersCode>SMR-001-0002</ManufacturersCode>
					<Desc1>(s) Medium Mixed Salad</Desc1>
				</SuppItem>
				<RetailEAN>131319270119</RetailEAN>
				<Desc1>Medium Mixed Salad</Desc1>
			</Item>
			<Perishable>false</Perishable>
			<UnitOfOrder>
				<Unit>1</Unit>
				<OrderMeasure>0</OrderMeasure>
			</UnitOfOrder>
			<OrderQty>
				<Unit>30.000</Unit>
				<UOM>CASE</UOM>
			</OrderQty>
			<PricingMeasure>
				<MeasureQty>1</MeasureQty>
			</PricingMeasure>
			<CostPrice>14.000</CostPrice>
			<LineAmount>420.000</LineAmount>
			<SellPrice>
				<MeasureQty>1</MeasureQty>
			</SellPrice>
			<Delivery>
				<ReqDesp>
					<Date>2018-04-10</Date>
					<Time>22:59:00</Time>
				</ReqDesp>
				<ReqDel>
					<Date>2018-04-10</Date>
					<Time>22:59:00</Time>
				</ReqDel>
				<ReqQty>
					<Unit>30.000</Unit>
					<UOM>CASE</UOM>
				</ReqQty>
			</Delivery>
			<OrderBranch>
				<Code>000</Code>
				<EAN13>5024657121941</EAN13>
				<Name>ABC Foods Limited</Name>
				<Address1>1 High Street</Address1>
				<Address5>London</Address5>
				<Country>GB</Country>
				<VAT>
					<Num>***********-123</Num>
					<Alpha>***********-123</Alpha>
				</VAT>
				<Contact1>
					<Name>Carl Denver</Name>
					<Phone>********** 456</Phone>
					<Fax>********** 457</Fax>
				</Contact1>
				<Contact2>
					<Name>Elisabeth Francis</Name>
					<Phone>********** 458</Phone>
				</Contact2>
			</OrderBranch>
			<DespatchFrom>
				<Code>000</Code>
				<EAN13>5000010000019</EAN13>
				<Name>DemoCo</Name>
				<Address1>DemoCo House</Address1>
				<Address2>100 High Street</Address2>
				<Address5>Worcester</Address5>
				<PostCode>WR5 1AA</PostCode>
				<Country>GB</Country>
				<VAT>
					<Num>*********</Num>
					<Alpha>GB*********</Alpha>
				</VAT>
				<Contact1>
					<Name>Ellie Jones</Name>
					<Phone>01905 321987</Phone>
					<Email><EMAIL></Email>
					<Fax>01905 321988</Fax>
				</Contact1>
				<Contact2>
					<Name>Elizabeth Kilbey</Name>
					<Phone>01905 321986</Phone>
					<Email><EMAIL></Email>
					<Fax>01905 321985</Fax>
				</Contact2>
			</DespatchFrom>
			<DeliverTo>
				<Code>000</Code>
				<EAN13>5024657121941</EAN13>
				<Name>ABC Foods Limited</Name>
				<Address1>1 High Street</Address1>
				<Address5>London</Address5>
				<Country>GB</Country>
				<VAT>
					<Num>***********-123</Num>
					<Alpha>***********-123</Alpha>
				</VAT>
				<Contact1>
					<Name>Carl Denver</Name>
					<Phone>********** 456</Phone>
					<Fax>********** 457</Fax>
				</Contact1>
				<Contact2>
					<Name>Elisabeth Francis</Name>
					<Phone>********** 458</Phone>
				</Contact2>
			</DeliverTo>
			<Tax>
				<TaxCode>Z</TaxCode>
				<TaxPcnt>0.00</TaxPcnt>
				<DutyCode>STD</DutyCode>
			</Tax>
		</OrderLine>
		<DocTrailer>
			<TotalLines>3</TotalLines>
		</DocTrailer>
	</Document>
	<MsgTrailer>
		<TotalDocs>1</TotalDocs>
	</MsgTrailer>
</tc:TrueCommerceOrder>
