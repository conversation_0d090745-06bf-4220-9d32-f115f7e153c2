<?xml version="1.0" encoding="UTF-8"?>
<tc:TrueCommerceInvoice xmlns:tc="http://www.truecommerce.com/docs/invoice"
    xmlns:cm="http://www.truecommerce.com/docs/common/components"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.truecommerce.com/docs/invoice truecommerce_invoice_v1.0.xsd">
    <MsgHeader>
        <MsgType>INVOICE</MsgType>
        <VersionID>v1.0</VersionID>
        <TransmittedDate>
            <Date>2018-04-27</Date>
            <Time>12:59:51</Time>
        </TransmittedDate>
        <FileDate>
            <Date>2018-04-27</Date>
            <Time>12:59:51</Time>
        </FileDate>
        <Interchange>
            <Standard>TRUECOMMERCE</Standard>
            <SenderRef>2859</SenderRef>
        </Interchange>
    </MsgHeader>
    <Document>
        <DocHeader>
            <DocType>INVOICE</DocType>
            <DocFunction>ORIGINAL</DocFunction>
            <CustAddr>
                <Code>00010</Code>
                <EAN13>5000020000115</EAN13>
                <Name>ABC Foods Limited</Name>
                <Address1>1 High Street</Address1>
                <Address5>London</Address5>
                <Country>GB</Country>
                <VAT>
                    <Num>***********-123</Num>
                </VAT>
                <Contact1>
                    <Name>Carl Denver</Name>
                    <Phone>********** 456</Phone>
                    <Fax>********** 457</Fax>
                </Contact1>
                <Contact2>
                    <Name>Elisabeth Francis</Name>
                    <Phone>********** 458</Phone>
                </Contact2>
            </CustAddr>
            <SuppAddr>
                <Code>TC30816</Code>
                <EAN13>5024657121929</EAN13>
                <Name>DemoCo</Name>
                <Address1>DemoCo House</Address1>
                <Address2>100 High Street</Address2>
                <Address5>Worcester</Address5>
                <PostCode>WR5 1AA</PostCode>
                <Country>GB</Country>
                <VAT>
                    <Num>001000203</Num>
                    <Alpha>GB001000203</Alpha>
                </VAT>
                <Contact1>
                    <Name>Ellie Jones</Name>
                    <Phone>01905 321987</Phone>
                    <Email><EMAIL></Email>
                    <Fax>01905 321988</Fax>
                </Contact1>
                <Contact2>
                    <Name>Elizabeth Kilbey</Name>
                    <Phone>01905 321986</Phone>
                    <Email><EMAIL></Email>
                    <Fax>01905 321985</Fax>
                </Contact2>
            </SuppAddr>
            <DocDate>
                <Date>2018-04-27</Date>
                <Time>12:59:08</Time>
            </DocDate>
            <TranInfo></TranInfo>
            <RoutingCode>T80427AALR</RoutingCode>
        </DocHeader>
        <InvoiceHeader>
            <InvoiceNo>IN-001-0004</InvoiceNo>
            <InvoiceDate>2018-04-27</InvoiceDate>
            <TaxPointDate>2018-04-10</TaxPointDate>
            <InvoicePeriodEndDate>2018-04-10</InvoicePeriodEndDate>
            <DeliveryPeriodEndDate>2018-04-10</DeliveryPeriodEndDate>
            <InvoiceClass>0</InvoiceClass>
            <CreditNoteAdditionalInfo></CreditNoteAdditionalInfo>
            <InvoiceTo>
                <Code>000</Code>
                <Name>ABC Foods Limited (Retail Division)</Name>
                <Address1>1 High Street</Address1>
                <Address5>London</Address5>
                <Country>GB</Country>
                <VAT>
                    <Num>***********-123</Num>
                    <Alpha>***********-123</Alpha>
                </VAT>
                <Contact1>
                    <Name>Carl Denver</Name>
                    <Phone>********** 456</Phone>
                    <Fax>********** 457</Fax>
                </Contact1>
                <Contact2>
                    <Name>Elisabeth Francis</Name>
                    <Phone>********** 458</Phone>
                </Contact2>
            </InvoiceTo>
            <InvoiceFrom>
                <Code>000</Code>
                <EAN13>5000010000019</EAN13>
                <Name>DemoCo (Research) Ltd</Name>
                <Address1>DemoCo House</Address1>
                <Address2>100 High Street</Address2>
                <Address5>Worcester</Address5>
                <PostCode>WR5 1AA</PostCode>
                <Country>GB</Country>
                <VAT>
                    <Num>001000203</Num>
                    <Alpha>GB001000203</Alpha>
                </VAT>
                <Contact1>
                    <Name>Ellie Jones</Name>
                    <Phone>01905 321987</Phone>
                    <Email><EMAIL></Email>
                    <Fax>01905 321988</Fax>
                </Contact1>
                <Contact2>
                    <Name>Elizabeth Kilbey</Name>
                    <Phone>01905 321986</Phone>
                    <Email><EMAIL></Email>
                    <Fax>01905 321985</Fax>
                </Contact2>
            </InvoiceFrom>
            <SettlementTerms></SettlementTerms>
            <Currency>
                <InvCurr>GBP</InvCurr>
            </Currency>
            <Notes>
                <Seq>1</Seq>
                <Narrative>
                    <RefNo>PDF_FILE</RefNo>
                    <Line1>U_INV_PDF_467.PDF</Line1>
                </Narrative>
            </Notes>
        </InvoiceHeader>
        <InvoiceLine>
            <LineNo>1</LineNo>
            <CustOrder>CO-001-0004</CustOrder>
            <OrderLineNo>1</OrderLineNo>
            <CustOrderDate>
                <Date>2018-04-10</Date>
                <Time>15:46:00</Time>
            </CustOrderDate>
            <OrderBranch>
                <Code>000</Code>
                <EAN13>5024657121941</EAN13>
                <Name>ABC Foods Limited</Name>
                <Address1>1 High Street</Address1>
                <Address5>London</Address5>
                <Country>GB</Country>
                <VAT>
                    <Num>***********-123</Num>
                    <Alpha>***********-123</Alpha>
                </VAT>
                <Contact1>
                    <Name>Carl Denver</Name>
                    <Phone>********** 456</Phone>
                    <Fax>********** 457</Fax>
                </Contact1>
                <Contact2>
                    <Name>Elisabeth Francis</Name>
                    <Phone>********** 458</Phone>
                </Contact2>
            </OrderBranch>
            <SuppOrder>SO0010004</SuppOrder>
            <DateOrderReceived>
                <Date>2018-04-10</Date>
                <Time>15:46:37</Time>
            </DateOrderReceived>
            <Item>
                <CustItem>
                    <OwnBrandEAN>1213192703201</OwnBrandEAN>
                    <Code>LMS</Code>
                    <SKU>SKULMS</SKU>
                </CustItem>
                <SuppItem>
                    <Code>LMS</Code>
                    <EAN13>1213192703202</EAN13>
                    <UPC12>121319270320</UPC12>
                    <DUN14>12131927032011</DUN14>
                    <ISBN>sLMS</ISBN>
                    <ManufacturersCode>MCLMS</ManufacturersCode>
                    <Desc1>(s) Large Mixed Salad</Desc1>
                </SuppItem>
                <RetailEAN>1213192703201</RetailEAN>
                <Desc1>Large Mixed Salad</Desc1>
            </Item>
            <UnitOfOrder>
                <Unit>1</Unit>
                <OrderMeasure>0</OrderMeasure>
                <UOM>CASE</UOM>
            </UnitOfOrder>
            <OrderQty>
                <Unit>1.000</Unit>
                <UOM>CASE</UOM>
            </OrderQty>
            <InvoiceQty>
                <Unit>1.000</Unit>
                <UOM>CASE</UOM>
            </InvoiceQty>
            <InvoiceLineCosts>
                <PricingMeasure>
                    <Measure>CASE</Measure>
                    <MeasureQty>1</MeasureQty>
                </PricingMeasure>
                <CostPrice>18.0000</CostPrice>
                <NetCostInclDisc>18.0000</NetCostInclDisc>
                <PricingMeasureQty>0.000</PricingMeasureQty>
                <LineAmount>18.00</LineAmount>
                <TAXRate>
                    <TAXCode>Z</TAXCode>
                    <TAXPercentage>0.000</TAXPercentage>
                </TAXRate>
                <TAXAmount>0.00</TAXAmount>
                <DiscountAmount></DiscountAmount>
                <LineTotal>18.00</LineTotal>
                <Subsidy>0.000</Subsidy>
            </InvoiceLineCosts>
            <SellOnPrices></SellOnPrices>
            <DeliveryInfo>
                <DelNoteDate>
                    <Date>2018-04-11</Date>
                    <Time>22:59:00</Time>
                </DelNoteDate>
                <ActualDesp>
                    <Date>2018-04-10</Date>
                    <Time>22:59:00</Time>
                </ActualDesp>
                <ActualDel>
                    <Date>2018-04-11</Date>
                    <Time>22:59:00</Time>
                </ActualDel>
                <DelUnits>1</DelUnits>
                <DespatchFrom>
                    <Code>000</Code>
                    <EAN13>000</EAN13>
                    <Name>DemoCo</Name>
                    <Address1>DemoCo House</Address1>
                    <Address2>100 High Street</Address2>
                    <Address5>Worcester</Address5>
                    <PostCode>WR5 1AA</PostCode>
                    <Country>GB</Country>
                    <VAT>
                        <Num>001000203</Num>
                        <Alpha>GB001000203</Alpha>
                    </VAT>
                    <Contact1>
                        <Name>Ellie Jones</Name>
                        <Phone>01905 321987</Phone>
                        <Email><EMAIL></Email>
                        <Fax>01905 321988</Fax>
                    </Contact1>
                    <Contact2>
                        <Name>Elizabeth Kilbey</Name>
                        <Phone>01905 321986</Phone>
                        <Email><EMAIL></Email>
                        <Fax>01905 321985</Fax>
                    </Contact2>
                </DespatchFrom>
                <DeliverTo>
                    <Code>000</Code>
                    <EAN13>5024657121941</EAN13>
                    <Name>ABC Foods Limited</Name>
                    <Address1>1 High Street</Address1>
                    <Address5>London</Address5>
                    <Country>GB</Country>
                    <VAT>
                        <Num>***********-123</Num>
                        <Alpha>***********-123</Alpha>
                    </VAT>
                    <Contact1>
                        <Name>Carl Denver</Name>
                        <Phone>********** 456</Phone>
                        <Fax>********** 457</Fax>
                    </Contact1>
                    <Contact2>
                        <Name>Elisabeth Francis</Name>
                        <Phone>********** 458</Phone>
                    </Contact2>
                </DeliverTo>
            </DeliveryInfo>
            <CreditLineInfo></CreditLineInfo>
        </InvoiceLine>
        <InvoiceLine>
            <LineNo>2</LineNo>
            <CustOrder>CO-001-0004</CustOrder>
            <OrderLineNo>1</OrderLineNo>
            <CustOrderDate>
                <Date>2018-04-10</Date>
                <Time>15:46:00</Time>
            </CustOrderDate>
            <OrderBranch>
                <Code>000</Code>
                <EAN13>5024657121941</EAN13>
                <Name>ABC Foods Limited</Name>
                <Address1>1 High Street</Address1>
                <Address5>London</Address5>
                <Country>GB</Country>
                <VAT>
                    <Num>***********-123</Num>
                    <Alpha>***********-123</Alpha>
                </VAT>
                <Contact1>
                    <Name>Carl Denver</Name>
                    <Phone>********** 456</Phone>
                    <Fax>********** 457</Fax>
                </Contact1>
                <Contact2>
                    <Name>Elisabeth Francis</Name>
                    <Phone>********** 458</Phone>
                </Contact2>
            </OrderBranch>
            <SuppOrder>SO0010004</SuppOrder>
            <DateOrderReceived>
                <Date>2018-04-10</Date>
                <Time>15:46:37</Time>
            </DateOrderReceived>
            <Item>
                <CustItem>
                    <OwnBrandEAN>1213192703201</OwnBrandEAN>
                    <Code>LMS</Code>
                    <SKU>SKULMS</SKU>
                </CustItem>
                <SuppItem>
                    <Code>LMS</Code>
                    <EAN13>1213192703202</EAN13>
                    <UPC12>121319270320</UPC12>
                    <DUN14>12131927032011</DUN14>
                    <ISBN>sLMS</ISBN>
                    <ManufacturersCode>MCLMS</ManufacturersCode>
                    <Desc1>(s) Large Mixed Salad</Desc1>
                </SuppItem>
                <RetailEAN>1213192703201</RetailEAN>
                <Desc1>Large Mixed Salad</Desc1>
            </Item>
            <UnitOfOrder>
                <Unit>1</Unit>
                <OrderMeasure>0</OrderMeasure>
                <UOM>CASE</UOM>
            </UnitOfOrder>
            <OrderQty>
                <Unit>1.000</Unit>
                <UOM>CASE</UOM>
            </OrderQty>
            <InvoiceQty>
                <Unit>1.000</Unit>
                <UOM>CASE</UOM>
            </InvoiceQty>
            <InvoiceLineCosts>
                <PricingMeasure>
                    <Measure>CASE</Measure>
                    <MeasureQty>1</MeasureQty>
                </PricingMeasure>
                <CostPrice>18.0000</CostPrice>
                <NetCostInclDisc>18.0000</NetCostInclDisc>
                <PricingMeasureQty>0.000</PricingMeasureQty>
                <LineAmount>18.00</LineAmount>
                <TAXRate>
                    <TAXCode>Z</TAXCode>
                    <TAXPercentage>0.000</TAXPercentage>
                </TAXRate>
                <TAXAmount>0.00</TAXAmount>
                <DiscountAmount></DiscountAmount>
                <LineTotal>18.00</LineTotal>
                <Subsidy>0.000</Subsidy>
            </InvoiceLineCosts>
            <SellOnPrices></SellOnPrices>
            <DeliveryInfo>
                <DelNoteDate>
                    <Date>2018-04-12</Date>
                    <Time>22:59:00</Time>
                </DelNoteDate>
                <ActualDesp>
                    <Date>2018-04-11</Date>
                    <Time>22:59:00</Time>
                </ActualDesp>
                <ActualDel>
                    <Date>2018-04-12</Date>
                    <Time>22:59:00</Time>
                </ActualDel>
                <DelUnits>1</DelUnits>
                <DespatchFrom>
                    <Code>000</Code>
                    <EAN13>000</EAN13>
                    <Name>DemoCo</Name>
                    <Address1>DemoCo House</Address1>
                    <Address2>100 High Street</Address2>
                    <Address5>Worcester</Address5>
                    <PostCode>WR5 1AA</PostCode>
                    <Country>GB</Country>
                    <VAT>
                        <Num>001000203</Num>
                        <Alpha>GB001000203</Alpha>
                    </VAT>
                    <Contact1>
                        <Name>Ellie Jones</Name>
                        <Phone>01905 321987</Phone>
                        <Email><EMAIL></Email>
                        <Fax>01905 321988</Fax>
                    </Contact1>
                    <Contact2>
                        <Name>Elizabeth Kilbey</Name>
                        <Phone>01905 321986</Phone>
                        <Email><EMAIL></Email>
                        <Fax>01905 321985</Fax>
                    </Contact2>
                </DespatchFrom>
                <DeliverTo>
                    <Code>000</Code>
                    <EAN13>5024657121941</EAN13>
                    <Name>ABC Foods Limited</Name>
                    <Address1>1 High Street</Address1>
                    <Address5>London</Address5>
                    <Country>GB</Country>
                    <VAT>
                        <Num>***********-123</Num>
                        <Alpha>***********-123</Alpha>
                    </VAT>
                    <Contact1>
                        <Name>Carl Denver</Name>
                        <Phone>********** 456</Phone>
                        <Fax>********** 457</Fax>
                    </Contact1>
                    <Contact2>
                        <Name>Elisabeth Francis</Name>
                        <Phone>********** 458</Phone>
                    </Contact2>
                </DeliverTo>
            </DeliveryInfo>
            <CreditLineInfo></CreditLineInfo>
        </InvoiceLine>
        <InvoiceLine>
            <LineNo>3</LineNo>
            <CustOrder>CO-001-0004</CustOrder>
            <OrderLineNo>1</OrderLineNo>
            <CustOrderDate>
                <Date>2018-04-10</Date>
                <Time>15:46:00</Time>
            </CustOrderDate>
            <OrderBranch>
                <Code>000</Code>
                <EAN13>5024657121941</EAN13>
                <Name>ABC Foods Limited</Name>
                <Address1>1 High Street</Address1>
                <Address5>London</Address5>
                <Country>GB</Country>
                <VAT>
                    <Num>***********-123</Num>
                    <Alpha>***********-123</Alpha>
                </VAT>
                <Contact1>
                    <Name>Carl Denver</Name>
                    <Phone>********** 456</Phone>
                    <Fax>********** 457</Fax>
                </Contact1>
                <Contact2>
                    <Name>Elisabeth Francis</Name>
                    <Phone>********** 458</Phone>
                </Contact2>
            </OrderBranch>
            <SuppOrder>SO0010004</SuppOrder>
            <DateOrderReceived>
                <Date>2018-04-10</Date>
                <Time>15:46:37</Time>
            </DateOrderReceived>
            <Item>
                <CustItem>
                    <OwnBrandEAN>1213192703201</OwnBrandEAN>
                    <Code>LMS</Code>
                    <SKU>SKULMS</SKU>
                </CustItem>
                <SuppItem>
                    <Code>LMS</Code>
                    <EAN13>1213192703202</EAN13>
                    <UPC12>121319270320</UPC12>
                    <DUN14>12131927032011</DUN14>
                    <ISBN>sLMS</ISBN>
                    <ManufacturersCode>MCLMS</ManufacturersCode>
                    <Desc1>(s) Large Mixed Salad</Desc1>
                </SuppItem>
                <RetailEAN>1213192703201</RetailEAN>
                <Desc1>Large Mixed Salad</Desc1>
            </Item>
            <UnitOfOrder>
                <Unit>1</Unit>
                <OrderMeasure>0</OrderMeasure>
                <UOM>CASE</UOM>
            </UnitOfOrder>
            <OrderQty>
                <Unit>1.000</Unit>
                <UOM>CASE</UOM>
            </OrderQty>
            <InvoiceQty>
                <Unit>1.000</Unit>
                <UOM>CASE</UOM>
            </InvoiceQty>
            <InvoiceLineCosts>
                <PricingMeasure>
                    <Measure>CASE</Measure>
                    <MeasureQty>1</MeasureQty>
                </PricingMeasure>
                <CostPrice>18.0000</CostPrice>
                <NetCostInclDisc>18.0000</NetCostInclDisc>
                <PricingMeasureQty>0.000</PricingMeasureQty>
                <LineAmount>18.00</LineAmount>
                <TAXRate>
                    <TAXCode>Z</TAXCode>
                    <TAXPercentage>0.000</TAXPercentage>
                </TAXRate>
                <TAXAmount>0.00</TAXAmount>
                <DiscountAmount></DiscountAmount>
                <LineTotal>18.00</LineTotal>
                <Subsidy>0.000</Subsidy>
            </InvoiceLineCosts>
            <SellOnPrices></SellOnPrices>
            <DeliveryInfo>
                <DelNoteDate>
                    <Date>2018-04-13</Date>
                    <Time>22:59:00</Time>
                </DelNoteDate>
                <ActualDesp>
                    <Date>2018-04-12</Date>
                    <Time>22:59:00</Time>
                </ActualDesp>
                <ActualDel>
                    <Date>2018-04-13</Date>
                    <Time>22:59:00</Time>
                </ActualDel>
                <DelUnits>1</DelUnits>
                <DespatchFrom>
                    <Code>000</Code>
                    <EAN13>000</EAN13>
                    <Name>DemoCo</Name>
                    <Address1>DemoCo House</Address1>
                    <Address2>100 High Street</Address2>
                    <Address5>Worcester</Address5>
                    <PostCode>WR5 1AA</PostCode>
                    <Country>GB</Country>
                    <VAT>
                        <Num>001000203</Num>
                        <Alpha>GB001000203</Alpha>
                    </VAT>
                    <Contact1>
                        <Name>Ellie Jones</Name>
                        <Phone>01905 321987</Phone>
                        <Email><EMAIL></Email>
                        <Fax>01905 321988</Fax>
                    </Contact1>
                    <Contact2>
                        <Name>Elizabeth Kilbey</Name>
                        <Phone>01905 321986</Phone>
                        <Email><EMAIL></Email>
                        <Fax>01905 321985</Fax>
                    </Contact2>
                </DespatchFrom>
                <DeliverTo>
                    <Code>000</Code>
                    <EAN13>5024657121941</EAN13>
                    <Name>ABC Foods Limited</Name>
                    <Address1>1 High Street</Address1>
                    <Address5>London</Address5>
                    <Country>GB</Country>
                    <VAT>
                        <Num>***********-123</Num>
                        <Alpha>***********-123</Alpha>
                    </VAT>
                    <Contact1>
                        <Name>Carl Denver</Name>
                        <Phone>********** 456</Phone>
                        <Fax>********** 457</Fax>
                    </Contact1>
                    <Contact2>
                        <Name>Elisabeth Francis</Name>
                        <Phone>********** 458</Phone>
                    </Contact2>
                </DeliverTo>
            </DeliveryInfo>
            <CreditLineInfo></CreditLineInfo>
        </InvoiceLine>
        <InvoiceLine>
            <LineNo>4</LineNo>
            <CustOrder>CO-001-0004</CustOrder>
            <OrderLineNo>1</OrderLineNo>
            <CustOrderDate>
                <Date>2018-04-10</Date>
                <Time>15:46:00</Time>
            </CustOrderDate>
            <OrderBranch>
                <Code>000</Code>
                <EAN13>5024657121941</EAN13>
                <Name>ABC Foods Limited</Name>
                <Address1>1 High Street</Address1>
                <Address5>London</Address5>
                <Country>GB</Country>
                <VAT>
                    <Num>***********-123</Num>
                    <Alpha>***********-123</Alpha>
                </VAT>
                <Contact1>
                    <Name>Carl Denver</Name>
                    <Phone>********** 456</Phone>
                    <Fax>********** 457</Fax>
                </Contact1>
                <Contact2>
                    <Name>Elisabeth Francis</Name>
                    <Phone>********** 458</Phone>
                </Contact2>
            </OrderBranch>
            <SuppOrder>SO0010004</SuppOrder>
            <DateOrderReceived>
                <Date>2018-04-10</Date>
                <Time>15:46:37</Time>
            </DateOrderReceived>
            <Item>
                <CustItem>
                    <OwnBrandEAN>1213192703201</OwnBrandEAN>
                    <Code>LMS</Code>
                    <SKU>SKULMS</SKU>
                </CustItem>
                <SuppItem>
                    <Code>LMS</Code>
                    <EAN13>1213192703202</EAN13>
                    <UPC12>121319270320</UPC12>
                    <DUN14>12131927032011</DUN14>
                    <ISBN>sLMS</ISBN>
                    <ManufacturersCode>MCLMS</ManufacturersCode>
                    <Desc1>(s) Large Mixed Salad</Desc1>
                </SuppItem>
                <RetailEAN>1213192703201</RetailEAN>
                <Desc1>Large Mixed Salad</Desc1>
            </Item>
            <UnitOfOrder>
                <Unit>1</Unit>
                <OrderMeasure>0</OrderMeasure>
                <UOM>CASE</UOM>
            </UnitOfOrder>
            <OrderQty>
                <Unit>1.000</Unit>
                <UOM>CASE</UOM>
            </OrderQty>
            <InvoiceQty>
                <Unit>1.000</Unit>
                <UOM>CASE</UOM>
            </InvoiceQty>
            <InvoiceLineCosts>
                <PricingMeasure>
                    <Measure>CASE</Measure>
                    <MeasureQty>1</MeasureQty>
                </PricingMeasure>
                <CostPrice>18.0000</CostPrice>
                <NetCostInclDisc>18.0000</NetCostInclDisc>
                <PricingMeasureQty>0.000</PricingMeasureQty>
                <LineAmount>18.00</LineAmount>
                <TAXRate>
                    <TAXCode>Z</TAXCode>
                    <TAXPercentage>0.000</TAXPercentage>
                </TAXRate>
                <TAXAmount>0.00</TAXAmount>
                <DiscountAmount></DiscountAmount>
                <LineTotal>18.00</LineTotal>
                <Subsidy>0.000</Subsidy>
            </InvoiceLineCosts>
            <SellOnPrices></SellOnPrices>
            <DeliveryInfo>
                <DelNoteDate>
                    <Date>2018-04-14</Date>
                    <Time>22:59:00</Time>
                </DelNoteDate>
                <ActualDesp>
                    <Date>2018-04-13</Date>
                    <Time>22:59:00</Time>
                </ActualDesp>
                <ActualDel>
                    <Date>2018-04-14</Date>
                    <Time>22:59:00</Time>
                </ActualDel>
                <DelUnits>1</DelUnits>
                <DespatchFrom>
                    <Code>000</Code>
                    <EAN13>000</EAN13>
                    <Name>DemoCo</Name>
                    <Address1>DemoCo House</Address1>
                    <Address2>100 High Street</Address2>
                    <Address5>Worcester</Address5>
                    <PostCode>WR5 1AA</PostCode>
                    <Country>GB</Country>
                    <VAT>
                        <Num>001000203</Num>
                        <Alpha>GB001000203</Alpha>
                    </VAT>
                    <Contact1>
                        <Name>Ellie Jones</Name>
                        <Phone>01905 321987</Phone>
                        <Email><EMAIL></Email>
                        <Fax>01905 321988</Fax>
                    </Contact1>
                    <Contact2>
                        <Name>Elizabeth Kilbey</Name>
                        <Phone>01905 321986</Phone>
                        <Email><EMAIL></Email>
                        <Fax>01905 321985</Fax>
                    </Contact2>
                </DespatchFrom>
                <DeliverTo>
                    <Code>000</Code>
                    <EAN13>5024657121941</EAN13>
                    <Name>ABC Foods Limited</Name>
                    <Address1>1 High Street</Address1>
                    <Address5>London</Address5>
                    <Country>GB</Country>
                    <VAT>
                        <Num>***********-123</Num>
                        <Alpha>***********-123</Alpha>
                    </VAT>
                    <Contact1>
                        <Name>Carl Denver</Name>
                        <Phone>********** 456</Phone>
                        <Fax>********** 457</Fax>
                    </Contact1>
                    <Contact2>
                        <Name>Elisabeth Francis</Name>
                        <Phone>********** 458</Phone>
                    </Contact2>
                </DeliverTo>
            </DeliveryInfo>
            <CreditLineInfo></CreditLineInfo>
        </InvoiceLine>
        <InvoiceLine>
            <LineNo>5</LineNo>
            <CustOrder>CO-001-0004</CustOrder>
            <OrderLineNo>1</OrderLineNo>
            <CustOrderDate>
                <Date>2018-04-10</Date>
                <Time>15:46:00</Time>
            </CustOrderDate>
            <OrderBranch>
                <Code>000</Code>
                <EAN13>5024657121941</EAN13>
                <Name>ABC Foods Limited</Name>
                <Address1>1 High Street</Address1>
                <Address5>London</Address5>
                <Country>GB</Country>
                <VAT>
                    <Num>***********-123</Num>
                    <Alpha>***********-123</Alpha>
                </VAT>
                <Contact1>
                    <Name>Carl Denver</Name>
                    <Phone>********** 456</Phone>
                    <Fax>********** 457</Fax>
                </Contact1>
                <Contact2>
                    <Name>Elisabeth Francis</Name>
                    <Phone>********** 458</Phone>
                </Contact2>
            </OrderBranch>
            <SuppOrder>SO0010004</SuppOrder>
            <DateOrderReceived>
                <Date>2018-04-10</Date>
                <Time>15:46:37</Time>
            </DateOrderReceived>
            <Item>
                <CustItem>
                    <OwnBrandEAN>1213192703201</OwnBrandEAN>
                    <Code>LMS</Code>
                    <SKU>SKULMS</SKU>
                </CustItem>
                <SuppItem>
                    <Code>LMS</Code>
                    <EAN13>1213192703202</EAN13>
                    <UPC12>121319270320</UPC12>
                    <DUN14>12131927032011</DUN14>
                    <ISBN>sLMS</ISBN>
                    <ManufacturersCode>MCLMS</ManufacturersCode>
                    <Desc1>(s) Large Mixed Salad</Desc1>
                </SuppItem>
                <RetailEAN>1213192703201</RetailEAN>
                <Desc1>Large Mixed Salad</Desc1>
            </Item>
            <UnitOfOrder>
                <Unit>1</Unit>
                <OrderMeasure>0</OrderMeasure>
                <UOM>CASE</UOM>
            </UnitOfOrder>
            <OrderQty>
                <Unit>1.000</Unit>
                <UOM>CASE</UOM>
            </OrderQty>
            <InvoiceQty>
                <Unit>1.000</Unit>
                <UOM>CASE</UOM>
            </InvoiceQty>
            <InvoiceLineCosts>
                <PricingMeasure>
                    <Measure>CASE</Measure>
                    <MeasureQty>1</MeasureQty>
                </PricingMeasure>
                <CostPrice>18.0000</CostPrice>
                <NetCostInclDisc>18.0000</NetCostInclDisc>
                <PricingMeasureQty>0.000</PricingMeasureQty>
                <LineAmount>18.00</LineAmount>
                <TAXRate>
                    <TAXCode>Z</TAXCode>
                    <TAXPercentage>0.000</TAXPercentage>
                </TAXRate>
                <TAXAmount>0.00</TAXAmount>
                <DiscountAmount></DiscountAmount>
                <LineTotal>18.00</LineTotal>
                <Subsidy>0.000</Subsidy>
            </InvoiceLineCosts>
            <SellOnPrices></SellOnPrices>
            <DeliveryInfo>
                <DelNoteDate>
                    <Date>2018-04-15</Date>
                    <Time>22:59:00</Time>
                </DelNoteDate>
                <ActualDesp>
                    <Date>2018-04-14</Date>
                    <Time>22:59:00</Time>
                </ActualDesp>
                <ActualDel>
                    <Date>2018-04-15</Date>
                    <Time>22:59:00</Time>
                </ActualDel>
                <DelUnits>1</DelUnits>
                <DespatchFrom>
                    <Code>000</Code>
                    <EAN13>000</EAN13>
                    <Name>DemoCo</Name>
                    <Address1>DemoCo House</Address1>
                    <Address2>100 High Street</Address2>
                    <Address5>Worcester</Address5>
                    <PostCode>WR5 1AA</PostCode>
                    <Country>GB</Country>
                    <VAT>
                        <Num>001000203</Num>
                        <Alpha>GB001000203</Alpha>
                    </VAT>
                    <Contact1>
                        <Name>Ellie Jones</Name>
                        <Phone>01905 321987</Phone>
                        <Email><EMAIL></Email>
                        <Fax>01905 321988</Fax>
                    </Contact1>
                    <Contact2>
                        <Name>Elizabeth Kilbey</Name>
                        <Phone>01905 321986</Phone>
                        <Email><EMAIL></Email>
                        <Fax>01905 321985</Fax>
                    </Contact2>
                </DespatchFrom>
                <DeliverTo>
                    <Code>000</Code>
                    <EAN13>5024657121941</EAN13>
                    <Name>ABC Foods Limited</Name>
                    <Address1>1 High Street</Address1>
                    <Address5>London</Address5>
                    <Country>GB</Country>
                    <VAT>
                        <Num>***********-123</Num>
                        <Alpha>***********-123</Alpha>
                    </VAT>
                    <Contact1>
                        <Name>Carl Denver</Name>
                        <Phone>********** 456</Phone>
                        <Fax>********** 457</Fax>
                    </Contact1>
                    <Contact2>
                        <Name>Elisabeth Francis</Name>
                        <Phone>********** 458</Phone>
                    </Contact2>
                </DeliverTo>
            </DeliveryInfo>
            <CreditLineInfo></CreditLineInfo>
        </InvoiceLine>
        <InvoiceLine>
            <LineNo>6</LineNo>
            <CustOrder>CO-001-0004</CustOrder>
            <OrderLineNo>1</OrderLineNo>
            <CustOrderDate>
                <Date>2018-04-10</Date>
                <Time>15:46:00</Time>
            </CustOrderDate>
            <OrderBranch>
                <Code>000</Code>
                <EAN13>5024657121941</EAN13>
                <Name>ABC Foods Limited</Name>
                <Address1>1 High Street</Address1>
                <Address5>London</Address5>
                <Country>GB</Country>
                <VAT>
                    <Num>***********-123</Num>
                    <Alpha>***********-123</Alpha>
                </VAT>
                <Contact1>
                    <Name>Carl Denver</Name>
                    <Phone>********** 456</Phone>
                    <Fax>********** 457</Fax>
                </Contact1>
                <Contact2>
                    <Name>Elisabeth Francis</Name>
                    <Phone>********** 458</Phone>
                </Contact2>
            </OrderBranch>
            <SuppOrder>SO0010004</SuppOrder>
            <DateOrderReceived>
                <Date>2018-04-10</Date>
                <Time>15:46:37</Time>
            </DateOrderReceived>
            <Item>
                <CustItem>
                    <OwnBrandEAN>1213192703201</OwnBrandEAN>
                    <Code>LMS</Code>
                    <SKU>SKULMS</SKU>
                </CustItem>
                <SuppItem>
                    <Code>LMS</Code>
                    <EAN13>1213192703202</EAN13>
                    <UPC12>121319270320</UPC12>
                    <DUN14>12131927032011</DUN14>
                    <ISBN>sLMS</ISBN>
                    <ManufacturersCode>MCLMS</ManufacturersCode>
                    <Desc1>(s) Large Mixed Salad</Desc1>
                </SuppItem>
                <RetailEAN>1213192703201</RetailEAN>
                <Desc1>Large Mixed Salad</Desc1>
            </Item>
            <UnitOfOrder>
                <Unit>1</Unit>
                <OrderMeasure>0</OrderMeasure>
                <UOM>CASE</UOM>
            </UnitOfOrder>
            <OrderQty>
                <Unit>1.000</Unit>
                <UOM>CASE</UOM>
            </OrderQty>
            <InvoiceQty>
                <Unit>1.000</Unit>
                <UOM>CASE</UOM>
            </InvoiceQty>
            <InvoiceLineCosts>
                <PricingMeasure>
                    <Measure>CASE</Measure>
                    <MeasureQty>1</MeasureQty>
                </PricingMeasure>
                <CostPrice>18.0000</CostPrice>
                <NetCostInclDisc>18.0000</NetCostInclDisc>
                <PricingMeasureQty>0.000</PricingMeasureQty>
                <LineAmount>18.00</LineAmount>
                <TAXRate>
                    <TAXCode>Z</TAXCode>
                    <TAXPercentage>0.000</TAXPercentage>
                </TAXRate>
                <TAXAmount>0.00</TAXAmount>
                <DiscountAmount></DiscountAmount>
                <LineTotal>18.00</LineTotal>
                <Subsidy>0.000</Subsidy>
            </InvoiceLineCosts>
            <SellOnPrices></SellOnPrices>
            <DeliveryInfo>
                <DelNoteDate>
                    <Date>2018-04-16</Date>
                    <Time>22:59:00</Time>
                </DelNoteDate>
                <ActualDesp>
                    <Date>2018-04-15</Date>
                    <Time>22:59:00</Time>
                </ActualDesp>
                <ActualDel>
                    <Date>2018-04-16</Date>
                    <Time>22:59:00</Time>
                </ActualDel>
                <DelUnits>1</DelUnits>
                <DespatchFrom>
                    <Code>000</Code>
                    <EAN13>000</EAN13>
                    <Name>DemoCo</Name>
                    <Address1>DemoCo House</Address1>
                    <Address2>100 High Street</Address2>
                    <Address5>Worcester</Address5>
                    <PostCode>WR5 1AA</PostCode>
                    <Country>GB</Country>
                    <VAT>
                        <Num>001000203</Num>
                        <Alpha>GB001000203</Alpha>
                    </VAT>
                    <Contact1>
                        <Name>Ellie Jones</Name>
                        <Phone>01905 321987</Phone>
                        <Email><EMAIL></Email>
                        <Fax>01905 321988</Fax>
                    </Contact1>
                    <Contact2>
                        <Name>Elizabeth Kilbey</Name>
                        <Phone>01905 321986</Phone>
                        <Email><EMAIL></Email>
                        <Fax>01905 321985</Fax>
                    </Contact2>
                </DespatchFrom>
                <DeliverTo>
                    <Code>000</Code>
                    <EAN13>5024657121941</EAN13>
                    <Name>ABC Foods Limited</Name>
                    <Address1>1 High Street</Address1>
                    <Address5>London</Address5>
                    <Country>GB</Country>
                    <VAT>
                        <Num>***********-123</Num>
                        <Alpha>***********-123</Alpha>
                    </VAT>
                    <Contact1>
                        <Name>Carl Denver</Name>
                        <Phone>********** 456</Phone>
                        <Fax>********** 457</Fax>
                    </Contact1>
                    <Contact2>
                        <Name>Elisabeth Francis</Name>
                        <Phone>********** 458</Phone>
                    </Contact2>
                </DeliverTo>
            </DeliveryInfo>
            <CreditLineInfo></CreditLineInfo>
        </InvoiceLine>
        <InvoiceLine>
            <LineNo>7</LineNo>
            <CustOrder>CO-001-0004</CustOrder>
            <OrderLineNo>1</OrderLineNo>
            <CustOrderDate>
                <Date>2018-04-10</Date>
                <Time>15:46:00</Time>
            </CustOrderDate>
            <OrderBranch>
                <Code>000</Code>
                <EAN13>5024657121941</EAN13>
                <Name>ABC Foods Limited</Name>
                <Address1>1 High Street</Address1>
                <Address5>London</Address5>
                <Country>GB</Country>
                <VAT>
                    <Num>***********-123</Num>
                    <Alpha>***********-123</Alpha>
                </VAT>
                <Contact1>
                    <Name>Carl Denver</Name>
                    <Phone>********** 456</Phone>
                    <Fax>********** 457</Fax>
                </Contact1>
                <Contact2>
                    <Name>Elisabeth Francis</Name>
                    <Phone>********** 458</Phone>
                </Contact2>
            </OrderBranch>
            <SuppOrder>SO0010004</SuppOrder>
            <DateOrderReceived>
                <Date>2018-04-10</Date>
                <Time>15:46:37</Time>
            </DateOrderReceived>
            <Item>
                <CustItem>
                    <OwnBrandEAN>1213192703201</OwnBrandEAN>
                    <Code>LMS</Code>
                    <SKU>SKULMS</SKU>
                </CustItem>
                <SuppItem>
                    <Code>LMS</Code>
                    <EAN13>1213192703202</EAN13>
                    <UPC12>121319270320</UPC12>
                    <DUN14>12131927032011</DUN14>
                    <ISBN>sLMS</ISBN>
                    <ManufacturersCode>MCLMS</ManufacturersCode>
                    <Desc1>(s) Large Mixed Salad</Desc1>
                </SuppItem>
                <RetailEAN>1213192703201</RetailEAN>
                <Desc1>Large Mixed Salad</Desc1>
            </Item>
            <UnitOfOrder>
                <Unit>1</Unit>
                <OrderMeasure>0</OrderMeasure>
                <UOM>CASE</UOM>
            </UnitOfOrder>
            <OrderQty>
                <Unit>1.000</Unit>
                <UOM>CASE</UOM>
            </OrderQty>
            <InvoiceQty>
                <Unit>1.000</Unit>
                <UOM>CASE</UOM>
            </InvoiceQty>
            <InvoiceLineCosts>
                <PricingMeasure>
                    <Measure>CASE</Measure>
                    <MeasureQty>1</MeasureQty>
                </PricingMeasure>
                <CostPrice>18.0000</CostPrice>
                <NetCostInclDisc>18.0000</NetCostInclDisc>
                <PricingMeasureQty>0.000</PricingMeasureQty>
                <LineAmount>18.00</LineAmount>
                <TAXRate>
                    <TAXCode>Z</TAXCode>
                    <TAXPercentage>0.000</TAXPercentage>
                </TAXRate>
                <TAXAmount>0.00</TAXAmount>
                <DiscountAmount></DiscountAmount>
                <LineTotal>18.00</LineTotal>
                <Subsidy>0.000</Subsidy>
            </InvoiceLineCosts>
            <SellOnPrices></SellOnPrices>
            <DeliveryInfo>
                <DelNoteDate>
                    <Date>2018-04-17</Date>
                    <Time>22:59:00</Time>
                </DelNoteDate>
                <ActualDesp>
                    <Date>2018-04-16</Date>
                    <Time>22:59:00</Time>
                </ActualDesp>
                <ActualDel>
                    <Date>2018-04-17</Date>
                    <Time>22:59:00</Time>
                </ActualDel>
                <DelUnits>1</DelUnits>
                <DespatchFrom>
                    <Code>000</Code>
                    <EAN13>000</EAN13>
                    <Name>DemoCo</Name>
                    <Address1>DemoCo House</Address1>
                    <Address2>100 High Street</Address2>
                    <Address5>Worcester</Address5>
                    <PostCode>WR5 1AA</PostCode>
                    <Country>GB</Country>
                    <VAT>
                        <Num>001000203</Num>
                        <Alpha>GB001000203</Alpha>
                    </VAT>
                    <Contact1>
                        <Name>Ellie Jones</Name>
                        <Phone>01905 321987</Phone>
                        <Email><EMAIL></Email>
                        <Fax>01905 321988</Fax>
                    </Contact1>
                    <Contact2>
                        <Name>Elizabeth Kilbey</Name>
                        <Phone>01905 321986</Phone>
                        <Email><EMAIL></Email>
                        <Fax>01905 321985</Fax>
                    </Contact2>
                </DespatchFrom>
                <DeliverTo>
                    <Code>000</Code>
                    <EAN13>5024657121941</EAN13>
                    <Name>ABC Foods Limited</Name>
                    <Address1>1 High Street</Address1>
                    <Address5>London</Address5>
                    <Country>GB</Country>
                    <VAT>
                        <Num>***********-123</Num>
                        <Alpha>***********-123</Alpha>
                    </VAT>
                    <Contact1>
                        <Name>Carl Denver</Name>
                        <Phone>********** 456</Phone>
                        <Fax>********** 457</Fax>
                    </Contact1>
                    <Contact2>
                        <Name>Elisabeth Francis</Name>
                        <Phone>********** 458</Phone>
                    </Contact2>
                </DeliverTo>
            </DeliveryInfo>
            <CreditLineInfo></CreditLineInfo>
        </InvoiceLine>
        <InvoiceLine>
            <LineNo>8</LineNo>
            <CustOrder>CO-001-0004</CustOrder>
            <OrderLineNo>1</OrderLineNo>
            <CustOrderDate>
                <Date>2018-04-10</Date>
                <Time>15:46:00</Time>
            </CustOrderDate>
            <OrderBranch>
                <Code>000</Code>
                <EAN13>5024657121941</EAN13>
                <Name>ABC Foods Limited</Name>
                <Address1>1 High Street</Address1>
                <Address5>London</Address5>
                <Country>GB</Country>
                <VAT>
                    <Num>***********-123</Num>
                    <Alpha>***********-123</Alpha>
                </VAT>
                <Contact1>
                    <Name>Carl Denver</Name>
                    <Phone>********** 456</Phone>
                    <Fax>********** 457</Fax>
                </Contact1>
                <Contact2>
                    <Name>Elisabeth Francis</Name>
                    <Phone>********** 458</Phone>
                </Contact2>
            </OrderBranch>
            <SuppOrder>SO0010004</SuppOrder>
            <DateOrderReceived>
                <Date>2018-04-10</Date>
                <Time>15:46:37</Time>
            </DateOrderReceived>
            <Item>
                <CustItem>
                    <OwnBrandEAN>1213192703201</OwnBrandEAN>
                    <Code>LMS</Code>
                    <SKU>SKULMS</SKU>
                </CustItem>
                <SuppItem>
                    <Code>LMS</Code>
                    <EAN13>1213192703202</EAN13>
                    <UPC12>121319270320</UPC12>
                    <DUN14>12131927032011</DUN14>
                    <ISBN>sLMS</ISBN>
                    <ManufacturersCode>MCLMS</ManufacturersCode>
                    <Desc1>(s) Large Mixed Salad</Desc1>
                </SuppItem>
                <RetailEAN>1213192703201</RetailEAN>
                <Desc1>Large Mixed Salad</Desc1>
            </Item>
            <UnitOfOrder>
                <Unit>1</Unit>
                <OrderMeasure>0</OrderMeasure>
                <UOM>CASE</UOM>
            </UnitOfOrder>
            <OrderQty>
                <Unit>1.000</Unit>
                <UOM>CASE</UOM>
            </OrderQty>
            <InvoiceQty>
                <Unit>1.000</Unit>
                <UOM>CASE</UOM>
            </InvoiceQty>
            <InvoiceLineCosts>
                <PricingMeasure>
                    <Measure>CASE</Measure>
                    <MeasureQty>1</MeasureQty>
                </PricingMeasure>
                <CostPrice>18.0000</CostPrice>
                <NetCostInclDisc>18.0000</NetCostInclDisc>
                <PricingMeasureQty>0.000</PricingMeasureQty>
                <LineAmount>18.00</LineAmount>
                <TAXRate>
                    <TAXCode>Z</TAXCode>
                    <TAXPercentage>0.000</TAXPercentage>
                </TAXRate>
                <TAXAmount>0.00</TAXAmount>
                <DiscountAmount></DiscountAmount>
                <LineTotal>18.00</LineTotal>
                <Subsidy>0.000</Subsidy>
            </InvoiceLineCosts>
            <SellOnPrices></SellOnPrices>
            <DeliveryInfo>
                <DelNoteDate>
                    <Date>2018-04-18</Date>
                    <Time>22:59:00</Time>
                </DelNoteDate>
                <ActualDesp>
                    <Date>2018-04-17</Date>
                    <Time>22:59:00</Time>
                </ActualDesp>
                <ActualDel>
                    <Date>2018-04-18</Date>
                    <Time>22:59:00</Time>
                </ActualDel>
                <DelUnits>1</DelUnits>
                <DespatchFrom>
                    <Code>000</Code>
                    <EAN13>000</EAN13>
                    <Name>DemoCo</Name>
                    <Address1>DemoCo House</Address1>
                    <Address2>100 High Street</Address2>
                    <Address5>Worcester</Address5>
                    <PostCode>WR5 1AA</PostCode>
                    <Country>GB</Country>
                    <VAT>
                        <Num>001000203</Num>
                        <Alpha>GB001000203</Alpha>
                    </VAT>
                    <Contact1>
                        <Name>Ellie Jones</Name>
                        <Phone>01905 321987</Phone>
                        <Email><EMAIL></Email>
                        <Fax>01905 321988</Fax>
                    </Contact1>
                    <Contact2>
                        <Name>Elizabeth Kilbey</Name>
                        <Phone>01905 321986</Phone>
                        <Email><EMAIL></Email>
                        <Fax>01905 321985</Fax>
                    </Contact2>
                </DespatchFrom>
                <DeliverTo>
                    <Code>000</Code>
                    <EAN13>5024657121941</EAN13>
                    <Name>ABC Foods Limited</Name>
                    <Address1>1 High Street</Address1>
                    <Address5>London</Address5>
                    <Country>GB</Country>
                    <VAT>
                        <Num>***********-123</Num>
                        <Alpha>***********-123</Alpha>
                    </VAT>
                    <Contact1>
                        <Name>Carl Denver</Name>
                        <Phone>********** 456</Phone>
                        <Fax>********** 457</Fax>
                    </Contact1>
                    <Contact2>
                        <Name>Elisabeth Francis</Name>
                        <Phone>********** 458</Phone>
                    </Contact2>
                </DeliverTo>
            </DeliveryInfo>
            <CreditLineInfo></CreditLineInfo>
        </InvoiceLine>
        <InvoiceLine>
            <LineNo>9</LineNo>
            <CustOrder>CO-001-0004</CustOrder>
            <OrderLineNo>1</OrderLineNo>
            <CustOrderDate>
                <Date>2018-04-10</Date>
                <Time>15:46:00</Time>
            </CustOrderDate>
            <OrderBranch>
                <Code>000</Code>
                <EAN13>5024657121941</EAN13>
                <Name>ABC Foods Limited</Name>
                <Address1>1 High Street</Address1>
                <Address5>London</Address5>
                <Country>GB</Country>
                <VAT>
                    <Num>***********-123</Num>
                    <Alpha>***********-123</Alpha>
                </VAT>
                <Contact1>
                    <Name>Carl Denver</Name>
                    <Phone>********** 456</Phone>
                    <Fax>********** 457</Fax>
                </Contact1>
                <Contact2>
                    <Name>Elisabeth Francis</Name>
                    <Phone>********** 458</Phone>
                </Contact2>
            </OrderBranch>
            <SuppOrder>SO0010004</SuppOrder>
            <DateOrderReceived>
                <Date>2018-04-10</Date>
                <Time>15:46:37</Time>
            </DateOrderReceived>
            <Item>
                <CustItem>
                    <OwnBrandEAN>1213192703201</OwnBrandEAN>
                    <Code>LMS</Code>
                    <SKU>SKULMS</SKU>
                </CustItem>
                <SuppItem>
                    <Code>LMS</Code>
                    <EAN13>1213192703202</EAN13>
                    <UPC12>121319270320</UPC12>
                    <DUN14>12131927032011</DUN14>
                    <ISBN>sLMS</ISBN>
                    <ManufacturersCode>MCLMS</ManufacturersCode>
                    <Desc1>(s) Large Mixed Salad</Desc1>
                </SuppItem>
                <RetailEAN>1213192703201</RetailEAN>
                <Desc1>Large Mixed Salad</Desc1>
            </Item>
            <UnitOfOrder>
                <Unit>1</Unit>
                <OrderMeasure>0</OrderMeasure>
                <UOM>CASE</UOM>
            </UnitOfOrder>
            <OrderQty>
                <Unit>1.000</Unit>
                <UOM>CASE</UOM>
            </OrderQty>
            <InvoiceQty>
                <Unit>1.000</Unit>
                <UOM>CASE</UOM>
            </InvoiceQty>
            <InvoiceLineCosts>
                <PricingMeasure>
                    <Measure>CASE</Measure>
                    <MeasureQty>1</MeasureQty>
                </PricingMeasure>
                <CostPrice>18.0000</CostPrice>
                <NetCostInclDisc>18.0000</NetCostInclDisc>
                <PricingMeasureQty>0.000</PricingMeasureQty>
                <LineAmount>18.00</LineAmount>
                <TAXRate>
                    <TAXCode>Z</TAXCode>
                    <TAXPercentage>0.000</TAXPercentage>
                </TAXRate>
                <TAXAmount>0.00</TAXAmount>
                <DiscountAmount></DiscountAmount>
                <LineTotal>18.00</LineTotal>
                <Subsidy>0.000</Subsidy>
            </InvoiceLineCosts>
            <SellOnPrices></SellOnPrices>
            <DeliveryInfo>
                <DelNoteDate>
                    <Date>2018-04-19</Date>
                    <Time>22:59:00</Time>
                </DelNoteDate>
                <ActualDesp>
                    <Date>2018-04-18</Date>
                    <Time>22:59:00</Time>
                </ActualDesp>
                <ActualDel>
                    <Date>2018-04-19</Date>
                    <Time>22:59:00</Time>
                </ActualDel>
                <DelUnits>1</DelUnits>
                <DespatchFrom>
                    <Code>000</Code>
                    <EAN13>000</EAN13>
                    <Name>DemoCo</Name>
                    <Address1>DemoCo House</Address1>
                    <Address2>100 High Street</Address2>
                    <Address5>Worcester</Address5>
                    <PostCode>WR5 1AA</PostCode>
                    <Country>GB</Country>
                    <VAT>
                        <Num>001000203</Num>
                        <Alpha>GB001000203</Alpha>
                    </VAT>
                    <Contact1>
                        <Name>Ellie Jones</Name>
                        <Phone>01905 321987</Phone>
                        <Email><EMAIL></Email>
                        <Fax>01905 321988</Fax>
                    </Contact1>
                    <Contact2>
                        <Name>Elizabeth Kilbey</Name>
                        <Phone>01905 321986</Phone>
                        <Email><EMAIL></Email>
                        <Fax>01905 321985</Fax>
                    </Contact2>
                </DespatchFrom>
                <DeliverTo>
                    <Code>000</Code>
                    <EAN13>5024657121941</EAN13>
                    <Name>ABC Foods Limited</Name>
                    <Address1>1 High Street</Address1>
                    <Address5>London</Address5>
                    <Country>GB</Country>
                    <VAT>
                        <Num>***********-123</Num>
                        <Alpha>***********-123</Alpha>
                    </VAT>
                    <Contact1>
                        <Name>Carl Denver</Name>
                        <Phone>********** 456</Phone>
                        <Fax>********** 457</Fax>
                    </Contact1>
                    <Contact2>
                        <Name>Elisabeth Francis</Name>
                        <Phone>********** 458</Phone>
                    </Contact2>
                </DeliverTo>
            </DeliveryInfo>
            <CreditLineInfo></CreditLineInfo>
        </InvoiceLine>
        <InvoiceLine>
            <LineNo>10</LineNo>
            <CustOrder>CO-001-0004</CustOrder>
            <OrderLineNo>1</OrderLineNo>
            <CustOrderDate>
                <Date>2018-04-10</Date>
                <Time>15:46:00</Time>
            </CustOrderDate>
            <OrderBranch>
                <Code>000</Code>
                <EAN13>5024657121941</EAN13>
                <Name>ABC Foods Limited</Name>
                <Address1>1 High Street</Address1>
                <Address5>London</Address5>
                <Country>GB</Country>
                <VAT>
                    <Num>***********-123</Num>
                    <Alpha>***********-123</Alpha>
                </VAT>
                <Contact1>
                    <Name>Carl Denver</Name>
                    <Phone>********** 456</Phone>
                    <Fax>********** 457</Fax>
                </Contact1>
                <Contact2>
                    <Name>Elisabeth Francis</Name>
                    <Phone>********** 458</Phone>
                </Contact2>
            </OrderBranch>
            <SuppOrder>SO0010004</SuppOrder>
            <DateOrderReceived>
                <Date>2018-04-10</Date>
                <Time>15:46:37</Time>
            </DateOrderReceived>
            <Item>
                <CustItem>
                    <OwnBrandEAN>1213192703201</OwnBrandEAN>
                    <Code>LMS</Code>
                    <SKU>SKULMS</SKU>
                </CustItem>
                <SuppItem>
                    <Code>LMS</Code>
                    <EAN13>1213192703202</EAN13>
                    <UPC12>121319270320</UPC12>
                    <DUN14>12131927032011</DUN14>
                    <ISBN>sLMS</ISBN>
                    <ManufacturersCode>MCLMS</ManufacturersCode>
                    <Desc1>(s) Large Mixed Salad</Desc1>
                </SuppItem>
                <RetailEAN>1213192703201</RetailEAN>
                <Desc1>Large Mixed Salad</Desc1>
            </Item>
            <UnitOfOrder>
                <Unit>1</Unit>
                <OrderMeasure>0</OrderMeasure>
                <UOM>CASE</UOM>
            </UnitOfOrder>
            <OrderQty>
                <Unit>1.000</Unit>
                <UOM>CASE</UOM>
            </OrderQty>
            <InvoiceQty>
                <Unit>1.000</Unit>
                <UOM>CASE</UOM>
            </InvoiceQty>
            <InvoiceLineCosts>
                <PricingMeasure>
                    <Measure>CASE</Measure>
                    <MeasureQty>1</MeasureQty>
                </PricingMeasure>
                <CostPrice>18.0000</CostPrice>
                <NetCostInclDisc>18.0000</NetCostInclDisc>
                <PricingMeasureQty>0.000</PricingMeasureQty>
                <LineAmount>18.00</LineAmount>
                <TAXRate>
                    <TAXCode>Z</TAXCode>
                    <TAXPercentage>0.000</TAXPercentage>
                </TAXRate>
                <TAXAmount>0.00</TAXAmount>
                <DiscountAmount></DiscountAmount>
                <LineTotal>18.00</LineTotal>
                <Subsidy>0.000</Subsidy>
            </InvoiceLineCosts>
            <SellOnPrices></SellOnPrices>
            <DeliveryInfo>
                <DelNoteDate>
                    <Date>2018-04-20</Date>
                    <Time>22:59:00</Time>
                </DelNoteDate>
                <ActualDesp>
                    <Date>2018-04-19</Date>
                    <Time>22:59:00</Time>
                </ActualDesp>
                <ActualDel>
                    <Date>2018-04-20</Date>
                    <Time>22:59:00</Time>
                </ActualDel>
                <DelUnits>1</DelUnits>
                <DespatchFrom>
                    <Code>000</Code>
                    <EAN13>000</EAN13>
                    <Name>DemoCo</Name>
                    <Address1>DemoCo House</Address1>
                    <Address2>100 High Street</Address2>
                    <Address5>Worcester</Address5>
                    <PostCode>WR5 1AA</PostCode>
                    <Country>GB</Country>
                    <VAT>
                        <Num>001000203</Num>
                        <Alpha>GB001000203</Alpha>
                    </VAT>
                    <Contact1>
                        <Name>Ellie Jones</Name>
                        <Phone>01905 321987</Phone>
                        <Email><EMAIL></Email>
                        <Fax>01905 321988</Fax>
                    </Contact1>
                    <Contact2>
                        <Name>Elizabeth Kilbey</Name>
                        <Phone>01905 321986</Phone>
                        <Email><EMAIL></Email>
                        <Fax>01905 321985</Fax>
                    </Contact2>
                </DespatchFrom>
                <DeliverTo>
                    <Code>000</Code>
                    <EAN13>5024657121941</EAN13>
                    <Name>ABC Foods Limited</Name>
                    <Address1>1 High Street</Address1>
                    <Address5>London</Address5>
                    <Country>GB</Country>
                    <VAT>
                        <Num>***********-123</Num>
                        <Alpha>***********-123</Alpha>
                    </VAT>
                    <Contact1>
                        <Name>Carl Denver</Name>
                        <Phone>********** 456</Phone>
                        <Fax>********** 457</Fax>
                    </Contact1>
                    <Contact2>
                        <Name>Elisabeth Francis</Name>
                        <Phone>********** 458</Phone>
                    </Contact2>
                </DeliverTo>
            </DeliveryInfo>
            <CreditLineInfo></CreditLineInfo>
        </InvoiceLine>
        <InvoiceLine>
            <LineNo>11</LineNo>
            <CustOrder>CO-001-0004</CustOrder>
            <OrderLineNo>2</OrderLineNo>
            <CustOrderDate>
                <Date>2018-04-10</Date>
                <Time>15:46:00</Time>
            </CustOrderDate>
            <OrderBranch>
                <Code>000</Code>
                <EAN13>5024657121941</EAN13>
                <Name>ABC Foods Limited</Name>
                <Address1>1 High Street</Address1>
                <Address5>London</Address5>
                <Country>GB</Country>
                <VAT>
                    <Num>***********-123</Num>
                    <Alpha>***********-123</Alpha>
                </VAT>
                <Contact1>
                    <Name>Carl Denver</Name>
                    <Phone>********** 456</Phone>
                    <Fax>********** 457</Fax>
                </Contact1>
                <Contact2>
                    <Name>Elisabeth Francis</Name>
                    <Phone>********** 458</Phone>
                </Contact2>
            </OrderBranch>
            <SuppOrder>SO0010004</SuppOrder>
            <DateOrderReceived>
                <Date>2018-04-10</Date>
                <Time>15:46:37</Time>
            </DateOrderReceived>
            <Item>
                <CustItem>
                    <OwnBrandEAN>1913192708201</OwnBrandEAN>
                    <Code>SMS</Code>
                    <SKU>SKUSMS</SKU>
                </CustItem>
                <SuppItem>
                    <Code>sSMS</Code>
                    <EAN13>1913192708203</EAN13>
                    <UPC12>191319270820</UPC12>
                    <DUN14>19131927082011</DUN14>
                    <ISBN>sSMS</ISBN>
                    <ManufacturersCode>MCSMS</ManufacturersCode>
                    <Desc1>Small Mixed Salad</Desc1>
                </SuppItem>
                <RetailEAN>1913192708202</RetailEAN>
                <Desc1>Small Mixed Salad</Desc1>
            </Item>
            <UnitOfOrder>
                <Unit>1</Unit>
                <OrderMeasure>0</OrderMeasure>
                <UOM>CASE</UOM>
            </UnitOfOrder>
            <OrderQty>
                <Unit>20.000</Unit>
                <UOM>CASE</UOM>
            </OrderQty>
            <InvoiceQty>
                <Unit>20.000</Unit>
                <UOM>CASE</UOM>
            </InvoiceQty>
            <InvoiceLineCosts>
                <PricingMeasure>
                    <Measure>CASE</Measure>
                    <MeasureQty>1</MeasureQty>
                </PricingMeasure>
                <CostPrice>12.0000</CostPrice>
                <NetCostInclDisc>12.0000</NetCostInclDisc>
                <PricingMeasureQty>0.000</PricingMeasureQty>
                <LineAmount>240.00</LineAmount>
                <TAXRate>
                    <TAXCode>Z</TAXCode>
                    <TAXPercentage>0.000</TAXPercentage>
                </TAXRate>
                <TAXAmount>0.00</TAXAmount>
                <DiscountAmount></DiscountAmount>
                <LineTotal>240.00</LineTotal>
                <Subsidy>0.000</Subsidy>
            </InvoiceLineCosts>
            <SellOnPrices></SellOnPrices>
            <DeliveryInfo>
                <DelNoteDate>
                    <Date>2018-04-10</Date>
                    <Time>22:59:00</Time>
                </DelNoteDate>
                <ActualDesp>
                    <Date>2018-04-10</Date>
                    <Time>22:59:00</Time>
                </ActualDesp>
                <ActualDel>
                    <Date>2018-04-10</Date>
                    <Time>22:59:00</Time>
                </ActualDel>
                <DelUnits>20</DelUnits>
                <DespatchFrom>
                    <Code>000</Code>
                    <EAN13>000</EAN13>
                    <Name>DemoCo</Name>
                    <Address1>DemoCo House</Address1>
                    <Address2>100 High Street</Address2>
                    <Address5>Worcester</Address5>
                    <PostCode>WR5 1AA</PostCode>
                    <Country>GB</Country>
                    <VAT>
                        <Num>001000203</Num>
                        <Alpha>GB001000203</Alpha>
                    </VAT>
                    <Contact1>
                        <Name>Ellie Jones</Name>
                        <Phone>01905 321987</Phone>
                        <Email><EMAIL></Email>
                        <Fax>01905 321988</Fax>
                    </Contact1>
                    <Contact2>
                        <Name>Elizabeth Kilbey</Name>
                        <Phone>01905 321986</Phone>
                        <Email><EMAIL></Email>
                        <Fax>01905 321985</Fax>
                    </Contact2>
                </DespatchFrom>
                <DeliverTo>
                    <Code>000</Code>
                    <EAN13>5024657121941</EAN13>
                    <Name>ABC Foods Limited</Name>
                    <Address1>1 High Street</Address1>
                    <Address5>London</Address5>
                    <Country>GB</Country>
                    <VAT>
                        <Num>***********-123</Num>
                        <Alpha>***********-123</Alpha>
                    </VAT>
                    <Contact1>
                        <Name>Carl Denver</Name>
                        <Phone>********** 456</Phone>
                        <Fax>********** 457</Fax>
                    </Contact1>
                    <Contact2>
                        <Name>Elisabeth Francis</Name>
                        <Phone>********** 458</Phone>
                    </Contact2>
                </DeliverTo>
            </DeliveryInfo>
            <CreditLineInfo></CreditLineInfo>
        </InvoiceLine>
        <InvoiceLine>
            <LineNo>12</LineNo>
            <CustOrder>CO-001-0004</CustOrder>
            <OrderLineNo>3</OrderLineNo>
            <CustOrderDate>
                <Date>2018-04-10</Date>
                <Time>15:46:00</Time>
            </CustOrderDate>
            <OrderBranch>
                <Code>000</Code>
                <EAN13>5024657121941</EAN13>
                <Name>ABC Foods Limited</Name>
                <Address1>1 High Street</Address1>
                <Address5>London</Address5>
                <Country>GB</Country>
                <VAT>
                    <Num>***********-123</Num>
                    <Alpha>***********-123</Alpha>
                </VAT>
                <Contact1>
                    <Name>Carl Denver</Name>
                    <Phone>********** 456</Phone>
                    <Fax>********** 457</Fax>
                </Contact1>
                <Contact2>
                    <Name>Elisabeth Francis</Name>
                    <Phone>********** 458</Phone>
                </Contact2>
            </OrderBranch>
            <SuppOrder>SO0010004</SuppOrder>
            <DateOrderReceived>
                <Date>2018-04-10</Date>
                <Time>15:46:37</Time>
            </DateOrderReceived>
            <Item>
                <CustItem>
                    <OwnBrandEAN>131319270118</OwnBrandEAN>
                    <Code>MMS</Code>
                    <SKU>SKU-001-0002</SKU>
                </CustItem>
                <SuppItem>
                    <Code>MMS</Code>
                    <EAN13>1313192701204</EAN13>
                    <UPC12>131319270121</UPC12>
                    <DUN14>1313192701181</DUN14>
                    <ISBN>sMMS</ISBN>
                    <ManufacturersCode>SMR-001-0002</ManufacturersCode>
                    <Desc1>(s) Medium Mixed Salad</Desc1>
                </SuppItem>
                <RetailEAN>131319270119</RetailEAN>
                <Desc1>Medium Mixed Salad</Desc1>
            </Item>
            <UnitOfOrder>
                <Unit>1</Unit>
                <OrderMeasure>0</OrderMeasure>
                <UOM>CASE</UOM>
            </UnitOfOrder>
            <OrderQty>
                <Unit>30.000</Unit>
                <UOM>CASE</UOM>
            </OrderQty>
            <InvoiceQty>
                <Unit>30.000</Unit>
                <UOM>CASE</UOM>
            </InvoiceQty>
            <InvoiceLineCosts>
                <PricingMeasure>
                    <Measure>CASE</Measure>
                    <MeasureQty>1</MeasureQty>
                </PricingMeasure>
                <CostPrice>14.0000</CostPrice>
                <NetCostInclDisc>14.0000</NetCostInclDisc>
                <PricingMeasureQty>0.000</PricingMeasureQty>
                <LineAmount>420.00</LineAmount>
                <TAXRate>
                    <TAXCode>Z</TAXCode>
                    <TAXPercentage>0.000</TAXPercentage>
                </TAXRate>
                <TAXAmount>0.00</TAXAmount>
                <DiscountAmount></DiscountAmount>
                <LineTotal>420.00</LineTotal>
                <Subsidy>0.000</Subsidy>
            </InvoiceLineCosts>
            <SellOnPrices></SellOnPrices>
            <DeliveryInfo>
                <DelNoteDate>
                    <Date>2018-04-10</Date>
                    <Time>22:59:00</Time>
                </DelNoteDate>
                <ActualDesp>
                    <Date>2018-04-10</Date>
                    <Time>22:59:00</Time>
                </ActualDesp>
                <ActualDel>
                    <Date>2018-04-10</Date>
                    <Time>22:59:00</Time>
                </ActualDel>
                <DelUnits>30</DelUnits>
                <DespatchFrom>
                    <Code>000</Code>
                    <EAN13>000</EAN13>
                    <Name>DemoCo</Name>
                    <Address1>DemoCo House</Address1>
                    <Address2>100 High Street</Address2>
                    <Address5>Worcester</Address5>
                    <PostCode>WR5 1AA</PostCode>
                    <Country>GB</Country>
                    <VAT>
                        <Num>001000203</Num>
                        <Alpha>GB001000203</Alpha>
                    </VAT>
                    <Contact1>
                        <Name>Ellie Jones</Name>
                        <Phone>01905 321987</Phone>
                        <Email><EMAIL></Email>
                        <Fax>01905 321988</Fax>
                    </Contact1>
                    <Contact2>
                        <Name>Elizabeth Kilbey</Name>
                        <Phone>01905 321986</Phone>
                        <Email><EMAIL></Email>
                        <Fax>01905 321985</Fax>
                    </Contact2>
                </DespatchFrom>
                <DeliverTo>
                    <Code>000</Code>
                    <EAN13>5024657121941</EAN13>
                    <Name>ABC Foods Limited</Name>
                    <Address1>1 High Street</Address1>
                    <Address5>London</Address5>
                    <Country>GB</Country>
                    <VAT>
                        <Num>***********-123</Num>
                        <Alpha>***********-123</Alpha>
                    </VAT>
                    <Contact1>
                        <Name>Carl Denver</Name>
                        <Phone>********** 456</Phone>
                        <Fax>********** 457</Fax>
                    </Contact1>
                    <Contact2>
                        <Name>Elisabeth Francis</Name>
                        <Phone>********** 458</Phone>
                    </Contact2>
                </DeliverTo>
            </DeliveryInfo>
            <CreditLineInfo></CreditLineInfo>
        </InvoiceLine>
        <InvoiceTrailer>
            <TAXSummary>
                <NoOfLines>12</NoOfLines>
                <TAXRate>
                    <TAXCode>Z</TAXCode>
                    <TAXPercentage>0.000</TAXPercentage>
                </TAXRate>
                <TAXDetails>
                    <LineAmount>840.00</LineAmount>
                    <DiscForInvoiceQty>0.00</DiscForInvoiceQty>
                    <DiscForInvoiceValue>0.00</DiscForInvoiceValue>
                    <Surcharge>0.00</Surcharge>
                    <Subsidy>0.00</Subsidy>
                    <ExtAmount>840.00</ExtAmount>
                    <SDiscountAmount>0.00</SDiscountAmount>
                    <ExtAmountInclSDisc>840.00</ExtAmountInclSDisc>
                    <TAXAmount>0.00</TAXAmount>
                    <ExtAmountInclTAX>840.00</ExtAmountInclTAX>
                    <ExtAmountInclSDiscAndTAX>840.00</ExtAmountInclSDiscAndTAX>
                    <HashTotalQty>0.000</HashTotalQty>
                    <HashTotalMeasure>0.000</HashTotalMeasure>
                </TAXDetails>
            </TAXSummary>
            <InvoiceTotal>
                <LineAmount>840.00</LineAmount>
                <DiscForInvoiceQty>0.00</DiscForInvoiceQty>
                <DiscForInvoiceValue>0.00</DiscForInvoiceValue>
                <Surcharge>40.00</Surcharge>
                <Subsidy>0.00</Subsidy>
                <ExtAmount>840.00</ExtAmount>
                <SDiscountAmount>0.00</SDiscountAmount>
                <ExtAmountInclSDisc>840.00</ExtAmountInclSDisc>
                <TAXAmount>0.00</TAXAmount>
                <ExtAmountInclTAX>880.00</ExtAmountInclTAX>
                <ExtAmountInclSDiscAndTAX>880.00</ExtAmountInclSDiscAndTAX>
                <HashTotalQty>0.000</HashTotalQty>
                <HashTotalMeasure>0.000</HashTotalMeasure>
            </InvoiceTotal>
        </InvoiceTrailer>
        <DocTrailer>
            <TotalLines>12</TotalLines>
        </DocTrailer>
    </Document>
    <TaxControl>
        <Summary>
            <Generated>
                <Date>2018-04-27</Date>
                <Time>12:59:51</Time>
            </Generated>
        </Summary>
        <Document>
            <DocType>INVOICE</DocType>
            <DocCount>1</DocCount>
            <TaxableAmount>840.00</TaxableAmount>
            <TotalInclVAT>880.00</TotalInclVAT>
            <VATDetail>
                <VATCode>Z</VATCode>
                <VATPercentage>0.000</VATPercentage>
                <Currency>
                    <InvCurr>GBP</InvCurr>
                </Currency>
                <TaxableAmount>840.00</TaxableAmount>
                <VATAmount>0.00</VATAmount>
                <TotalInclVAT>840.00</TotalInclVAT>
            </VATDetail>
        </Document>
    </TaxControl>
    <MsgTrailer>
        <TotalDocs>1</TotalDocs>
    </MsgTrailer>
</tc:TrueCommerceInvoice>