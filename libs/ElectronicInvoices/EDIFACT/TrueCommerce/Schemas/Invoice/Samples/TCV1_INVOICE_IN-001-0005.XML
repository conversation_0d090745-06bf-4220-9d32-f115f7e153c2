<?xml version="1.0" encoding="UTF-8"?>
<tc:TrueCommerceInvoice xmlns:tc="http://www.truecommerce.com/docs/invoice"
    xmlns:cm="http://www.truecommerce.com/docs/common/components"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.truecommerce.com/docs/invoice truecommerce_invoice_v1.0.xsd">
    <MsgHeader>
        <MsgType>INVOICE</MsgType>
        <VersionID>v1.0</VersionID>
        <TransmittedDate>
            <Date>2018-04-27</Date>
            <Time>14:43:30</Time>
        </TransmittedDate>
        <FileDate>
            <Date>2018-04-27</Date>
            <Time>14:43:30</Time>
        </FileDate>
        <Interchange>
            <Standard>TRUECOMMERCE</Standard>
            <SenderRef>2883</SenderRef>
        </Interchange>
    </MsgHeader>
    <Document>
        <DocHeader>
            <DocType>INVOICE</DocType>
            <DocFunction>ORIGINAL</DocFunction>
            <CustAddr>
                <Code>00010</Code>
                <EAN13>5000020000115</EAN13>
                <Name>ABC Foods Limited</Name>
                <Address1>1 High Street</Address1>
                <Address5>London</Address5>
                <Country>GB</Country>
                <VAT>
                    <Num>***********-123</Num>
                </VAT>
                <Contact1>
                    <Name>Carl Denver</Name>
                    <Phone>********** 456</Phone>
                    <Fax>********** 457</Fax>
                </Contact1>
                <Contact2>
                    <Name>Elisabeth Francis</Name>
                    <Phone>********** 458</Phone>
                </Contact2>
            </CustAddr>
            <SuppAddr>
                <Code>TC30816</Code>
                <EAN13>5024657121929</EAN13>
                <Name>DemoCo</Name>
                <Address1>DemoCo House</Address1>
                <Address2>100 High Street</Address2>
                <Address5>Worcester</Address5>
                <PostCode>WR5 1AA</PostCode>
                <Country>GB</Country>
                <VAT>
                    <Num>001000203</Num>
                    <Alpha>GB001000203</Alpha>
                </VAT>
                <Contact1>
                    <Name>Ellie Jones</Name>
                    <Phone>01905 321987</Phone>
                    <Email><EMAIL></Email>
                    <Fax>01905 321988</Fax>
                </Contact1>
                <Contact2>
                    <Name>Elizabeth Kilbey</Name>
                    <Phone>01905 321986</Phone>
                    <Email><EMAIL></Email>
                    <Fax>01905 321985</Fax>
                </Contact2>
            </SuppAddr>
            <DocDate>
                <Date>2018-04-27</Date>
                <Time>14:42:58</Time>
            </DocDate>
            <TranInfo></TranInfo>
            <RoutingCode>T80427AAQB</RoutingCode>
        </DocHeader>
        <InvoiceHeader>
            <InvoiceNo>IN-005-0001</InvoiceNo>
            <InvoiceDate>2018-04-27</InvoiceDate>
            <TaxPointDate>2018-04-27</TaxPointDate>
            <InvoicePeriodEndDate>2018-04-27</InvoicePeriodEndDate>
            <DeliveryPeriodEndDate>2018-04-27</DeliveryPeriodEndDate>
            <InvoiceClass>0</InvoiceClass>
            <CreditNoteAdditionalInfo></CreditNoteAdditionalInfo>
            <InvoiceTo>
                <Code>000</Code>
                <Name>ABC Foods Limited (Retail Division)</Name>
                <Address1>1 High Street</Address1>
                <Address5>London</Address5>
                <Country>GB</Country>
                <VAT>
                    <Num>***********-123</Num>
                    <Alpha>***********-123</Alpha>
                </VAT>
                <Contact1>
                    <Name>Carl Denver</Name>
                    <Phone>********** 456</Phone>
                    <Fax>********** 457</Fax>
                </Contact1>
                <Contact2>
                    <Name>Elisabeth Francis</Name>
                    <Phone>********** 458</Phone>
                </Contact2>
            </InvoiceTo>
            <InvoiceFrom>
                <Code>000</Code>
                <EAN13>5000010000019</EAN13>
                <Name>DemoCo (Research) Ltd</Name>
                <Address1>DemoCo House</Address1>
                <Address2>100 High Street</Address2>
                <Address5>Worcester</Address5>
                <PostCode>WR5 1AA</PostCode>
                <Country>GB</Country>
                <VAT>
                    <Num>001000203</Num>
                    <Alpha>GB001000203</Alpha>
                </VAT>
                <Contact1>
                    <Name>Ellie Jones</Name>
                    <Phone>01905 321987</Phone>
                    <Email><EMAIL></Email>
                    <Fax>01905 321988</Fax>
                </Contact1>
                <Contact2>
                    <Name>Elizabeth Kilbey</Name>
                    <Phone>01905 321986</Phone>
                    <Email><EMAIL></Email>
                    <Fax>01905 321985</Fax>
                </Contact2>
            </InvoiceFrom>
            <SettlementTerms></SettlementTerms>
            <Currency>
                <InvCurr>GBP</InvCurr>
            </Currency>
            <Notes>
                <Seq>1</Seq>
                <Narrative>
                    <RefNo>PDF_FILE</RefNo>
                    <Line1>U_INV_PDF_471.PDF</Line1>
                </Narrative>
            </Notes>
        </InvoiceHeader>
        <InvoiceLine>
            <LineNo>1</LineNo>
            <CustOrder>CO-001-0005</CustOrder>
            <OrderLineNo>1</OrderLineNo>
            <CustOrderDate>
                <Date>2018-04-27</Date>
                <Time>14:30:00</Time>
            </CustOrderDate>
            <OrderBranch>
                <Code>000</Code>
                <EAN13>5024657121941</EAN13>
                <Name>ABC Foods Limited</Name>
                <Address1>1 High Street</Address1>
                <Address5>London</Address5>
                <Country>GB</Country>
                <VAT>
                    <Num>***********-123</Num>
                    <Alpha>***********-123</Alpha>
                </VAT>
                <Contact1>
                    <Name>Carl Denver</Name>
                    <Phone>********** 456</Phone>
                    <Fax>********** 457</Fax>
                </Contact1>
                <Contact2>
                    <Name>Elisabeth Francis</Name>
                    <Phone>********** 458</Phone>
                </Contact2>
            </OrderBranch>
            <SuppOrder>SO0010005</SuppOrder>
            <DateOrderReceived>
                <Date>2018-04-27</Date>
                <Time>14:30:48</Time>
            </DateOrderReceived>
            <Item>
                <CustItem>
                    <OwnBrandEAN>5113192703501</OwnBrandEAN>
                    <Code>WSCB</Code>
                    <SKU>skuWSCB</SKU>
                </CustItem>
                <SuppItem>
                    <Code>sWSCB</Code>
                    <EAN13>5113192703504</EAN13>
                    <UPC12>511319270505</UPC12>
                    <DUN14>5113192703506</DUN14>
                    <ISBN>sWSCB</ISBN>
                    <ManufacturersCode>mWSCB</ManufacturersCode>
                    <Desc1>(s) Wike Chocolate Biscuits 100g</Desc1>
                </SuppItem>
                <RetailEAN>5113192703502</RetailEAN>
                <Desc1>Wike Chocolate Biscuits 100g</Desc1>
            </Item>
            <UnitOfOrder>
                <Unit>1</Unit>
                <OrderMeasure>0</OrderMeasure>
                <UOM>EA</UOM>
            </UnitOfOrder>
            <OrderQty>
                <Unit>10.000</Unit>
                <UOM>EA</UOM>
            </OrderQty>
            <InvoiceQty>
                <Unit>10.000</Unit>
                <UOM>EA</UOM>
            </InvoiceQty>
            <InvoiceLineCosts>
                <PricingMeasure>
                    <Measure>EA</Measure>
                    <MeasureQty>1</MeasureQty>
                </PricingMeasure>
                <CostPrice>0.5100</CostPrice>
                <NetCostInclDisc>0.5100</NetCostInclDisc>
                <PricingMeasureQty>0.000</PricingMeasureQty>
                <LineAmount>5.10</LineAmount>
                <TAXRate>
                    <TAXCode>S</TAXCode>
                    <TAXPercentage>20.000</TAXPercentage>
                </TAXRate>
                <TAXAmount>1.02</TAXAmount>
                <DiscountAmount></DiscountAmount>
                <LineTotal>6.12</LineTotal>
                <Subsidy>0.000</Subsidy>
            </InvoiceLineCosts>
            <SellOnPrices></SellOnPrices>
            <DeliveryInfo>
                <DelNoteDate>
                    <Date>2018-04-27</Date>
                    <Time>22:59:00</Time>
                </DelNoteDate>
                <ActualDesp>
                    <Date>2018-04-27</Date>
                    <Time>22:59:00</Time>
                </ActualDesp>
                <ActualDel>
                    <Date>2018-04-27</Date>
                    <Time>22:59:00</Time>
                </ActualDel>
                <DelUnits>10</DelUnits>
                <DespatchFrom>
                    <Code>000</Code>
                    <EAN13>000</EAN13>
                    <Name>DemoCo</Name>
                    <Address1>DemoCo House</Address1>
                    <Address2>100 High Street</Address2>
                    <Address5>Worcester</Address5>
                    <PostCode>WR5 1AA</PostCode>
                    <Country>GB</Country>
                    <VAT>
                        <Num>001000203</Num>
                        <Alpha>GB001000203</Alpha>
                    </VAT>
                    <Contact1>
                        <Name>Ellie Jones</Name>
                        <Phone>01905 321987</Phone>
                        <Email><EMAIL></Email>
                        <Fax>01905 321988</Fax>
                    </Contact1>
                    <Contact2>
                        <Name>Elizabeth Kilbey</Name>
                        <Phone>01905 321986</Phone>
                        <Email><EMAIL></Email>
                        <Fax>01905 321985</Fax>
                    </Contact2>
                </DespatchFrom>
                <DeliverTo>
                    <Code>000</Code>
                    <EAN13>5024657121941</EAN13>
                    <Name>ABC Foods Limited</Name>
                    <Address1>1 High Street</Address1>
                    <Address5>London</Address5>
                    <Country>GB</Country>
                    <VAT>
                        <Num>***********-123</Num>
                        <Alpha>***********-123</Alpha>
                    </VAT>
                    <Contact1>
                        <Name>Carl Denver</Name>
                        <Phone>********** 456</Phone>
                        <Fax>********** 457</Fax>
                    </Contact1>
                    <Contact2>
                        <Name>Elisabeth Francis</Name>
                        <Phone>********** 458</Phone>
                    </Contact2>
                </DeliverTo>
            </DeliveryInfo>
            <CreditLineInfo></CreditLineInfo>
        </InvoiceLine>
        <InvoiceLine>
            <LineNo>2</LineNo>
            <CustOrder>CO-001-0005</CustOrder>
            <OrderLineNo>2</OrderLineNo>
            <CustOrderDate>
                <Date>2018-04-27</Date>
                <Time>14:30:00</Time>
            </CustOrderDate>
            <OrderBranch>
                <Code>000</Code>
                <EAN13>5024657121941</EAN13>
                <Name>ABC Foods Limited</Name>
                <Address1>1 High Street</Address1>
                <Address5>London</Address5>
                <Country>GB</Country>
                <VAT>
                    <Num>***********-123</Num>
                    <Alpha>***********-123</Alpha>
                </VAT>
                <Contact1>
                    <Name>Carl Denver</Name>
                    <Phone>********** 456</Phone>
                    <Fax>********** 457</Fax>
                </Contact1>
                <Contact2>
                    <Name>Elisabeth Francis</Name>
                    <Phone>********** 458</Phone>
                </Contact2>
            </OrderBranch>
            <SuppOrder>SO0010005</SuppOrder>
            <DateOrderReceived>
                <Date>2018-04-27</Date>
                <Time>14:30:48</Time>
            </DateOrderReceived>
            <Item>
                <CustItem>
                    <OwnBrandEAN>5113192703601</OwnBrandEAN>
                    <Code>WMCB</Code>
                    <SKU>skuWMCB</SKU>
                </CustItem>
                <SuppItem>
                    <Code>sWMSCB</Code>
                    <EAN13>5113192703603</EAN13>
                    <UPC12>511319270605</UPC12>
                    <DUN14>51131927035066</DUN14>
                    <ISBN>sWMCB</ISBN>
                    <ManufacturersCode>mWMCB</ManufacturersCode>
                    <Desc1>(s) Wike Chocolate Biscuits 200g</Desc1>
                </SuppItem>
                <RetailEAN>5113192703602</RetailEAN>
                <Desc1>Wike Chocolate Biscuits 200g</Desc1>
            </Item>
            <UnitOfOrder>
                <Unit>1</Unit>
                <OrderMeasure>0</OrderMeasure>
                <UOM>EA</UOM>
            </UnitOfOrder>
            <OrderQty>
                <Unit>20.000</Unit>
                <UOM>EA</UOM>
            </OrderQty>
            <InvoiceQty>
                <Unit>20.000</Unit>
                <UOM>EA</UOM>
            </InvoiceQty>
            <InvoiceLineCosts>
                <PricingMeasure>
                    <Measure>EA</Measure>
                    <MeasureQty>1</MeasureQty>
                </PricingMeasure>
                <CostPrice>0.7700</CostPrice>
                <NetCostInclDisc>0.7700</NetCostInclDisc>
                <PricingMeasureQty>0.000</PricingMeasureQty>
                <LineAmount>15.40</LineAmount>
                <TAXRate>
                    <TAXCode>S</TAXCode>
                    <TAXPercentage>20.000</TAXPercentage>
                </TAXRate>
                <TAXAmount>3.08</TAXAmount>
                <DiscountAmount></DiscountAmount>
                <LineTotal>18.48</LineTotal>
                <Subsidy>0.000</Subsidy>
            </InvoiceLineCosts>
            <SellOnPrices></SellOnPrices>
            <DeliveryInfo>
                <DelNoteDate>
                    <Date>2018-04-27</Date>
                    <Time>22:59:00</Time>
                </DelNoteDate>
                <ActualDesp>
                    <Date>2018-04-27</Date>
                    <Time>22:59:00</Time>
                </ActualDesp>
                <ActualDel>
                    <Date>2018-04-27</Date>
                    <Time>22:59:00</Time>
                </ActualDel>
                <DelUnits>20</DelUnits>
                <DespatchFrom>
                    <Code>000</Code>
                    <EAN13>000</EAN13>
                    <Name>DemoCo</Name>
                    <Address1>DemoCo House</Address1>
                    <Address2>100 High Street</Address2>
                    <Address5>Worcester</Address5>
                    <PostCode>WR5 1AA</PostCode>
                    <Country>GB</Country>
                    <VAT>
                        <Num>001000203</Num>
                        <Alpha>GB001000203</Alpha>
                    </VAT>
                    <Contact1>
                        <Name>Ellie Jones</Name>
                        <Phone>01905 321987</Phone>
                        <Email><EMAIL></Email>
                        <Fax>01905 321988</Fax>
                    </Contact1>
                    <Contact2>
                        <Name>Elizabeth Kilbey</Name>
                        <Phone>01905 321986</Phone>
                        <Email><EMAIL></Email>
                        <Fax>01905 321985</Fax>
                    </Contact2>
                </DespatchFrom>
                <DeliverTo>
                    <Code>000</Code>
                    <EAN13>5024657121941</EAN13>
                    <Name>ABC Foods Limited</Name>
                    <Address1>1 High Street</Address1>
                    <Address5>London</Address5>
                    <Country>GB</Country>
                    <VAT>
                        <Num>***********-123</Num>
                        <Alpha>***********-123</Alpha>
                    </VAT>
                    <Contact1>
                        <Name>Carl Denver</Name>
                        <Phone>********** 456</Phone>
                        <Fax>********** 457</Fax>
                    </Contact1>
                    <Contact2>
                        <Name>Elisabeth Francis</Name>
                        <Phone>********** 458</Phone>
                    </Contact2>
                </DeliverTo>
            </DeliveryInfo>
            <CreditLineInfo></CreditLineInfo>
        </InvoiceLine>
        <InvoiceLine>
            <LineNo>3</LineNo>
            <CustOrder>CO-001-0005</CustOrder>
            <OrderLineNo>3</OrderLineNo>
            <CustOrderDate>
                <Date>2018-04-27</Date>
                <Time>14:30:00</Time>
            </CustOrderDate>
            <OrderBranch>
                <Code>000</Code>
                <EAN13>5024657121941</EAN13>
                <Name>ABC Foods Limited</Name>
                <Address1>1 High Street</Address1>
                <Address5>London</Address5>
                <Country>GB</Country>
                <VAT>
                    <Num>***********-123</Num>
                    <Alpha>***********-123</Alpha>
                </VAT>
                <Contact1>
                    <Name>Carl Denver</Name>
                    <Phone>********** 456</Phone>
                    <Fax>********** 457</Fax>
                </Contact1>
                <Contact2>
                    <Name>Elisabeth Francis</Name>
                    <Phone>********** 458</Phone>
                </Contact2>
            </OrderBranch>
            <SuppOrder>SO0010005</SuppOrder>
            <DateOrderReceived>
                <Date>2018-04-27</Date>
                <Time>14:30:48</Time>
            </DateOrderReceived>
            <Item>
                <CustItem>
                    <OwnBrandEAN>5113192703701</OwnBrandEAN>
                    <Code>WLCB</Code>
                    <SKU>skuWLCB</SKU>
                </CustItem>
                <SuppItem>
                    <Code>sWLCB</Code>
                    <EAN13>5113192703702</EAN13>
                    <UPC12>511319270705</UPC12>
                    <DUN14>51131927037066</DUN14>
                    <ISBN>sWLCB</ISBN>
                    <ManufacturersCode>mWLCB</ManufacturersCode>
                    <Desc1>(s) Wike Chocolate Biscuits 300g</Desc1>
                </SuppItem>
                <RetailEAN>5113192703702</RetailEAN>
                <Desc1>Wike Chocolate Biscuits 300g</Desc1>
            </Item>
            <UnitOfOrder>
                <Unit>1</Unit>
                <OrderMeasure>0</OrderMeasure>
                <UOM>EA</UOM>
            </UnitOfOrder>
            <OrderQty>
                <Unit>30.000</Unit>
                <UOM>EA</UOM>
            </OrderQty>
            <InvoiceQty>
                <Unit>30.000</Unit>
                <UOM>EA</UOM>
            </InvoiceQty>
            <InvoiceLineCosts>
                <PricingMeasure>
                    <Measure>EA</Measure>
                    <MeasureQty>1</MeasureQty>
                </PricingMeasure>
                <CostPrice>1.0300</CostPrice>
                <NetCostInclDisc>1.0300</NetCostInclDisc>
                <PricingMeasureQty>0.000</PricingMeasureQty>
                <LineAmount>30.90</LineAmount>
                <TAXRate>
                    <TAXCode>S</TAXCode>
                    <TAXPercentage>20.000</TAXPercentage>
                </TAXRate>
                <TAXAmount>6.18</TAXAmount>
                <DiscountAmount></DiscountAmount>
                <LineTotal>37.08</LineTotal>
                <Subsidy>0.000</Subsidy>
            </InvoiceLineCosts>
            <SellOnPrices></SellOnPrices>
            <DeliveryInfo>
                <DelNoteDate>
                    <Date>2018-04-27</Date>
                    <Time>22:59:00</Time>
                </DelNoteDate>
                <ActualDesp>
                    <Date>2018-04-27</Date>
                    <Time>22:59:00</Time>
                </ActualDesp>
                <ActualDel>
                    <Date>2018-04-27</Date>
                    <Time>22:59:00</Time>
                </ActualDel>
                <DelUnits>30</DelUnits>
                <DespatchFrom>
                    <Code>000</Code>
                    <EAN13>000</EAN13>
                    <Name>DemoCo</Name>
                    <Address1>DemoCo House</Address1>
                    <Address2>100 High Street</Address2>
                    <Address5>Worcester</Address5>
                    <PostCode>WR5 1AA</PostCode>
                    <Country>GB</Country>
                    <VAT>
                        <Num>001000203</Num>
                        <Alpha>GB001000203</Alpha>
                    </VAT>
                    <Contact1>
                        <Name>Ellie Jones</Name>
                        <Phone>01905 321987</Phone>
                        <Email><EMAIL></Email>
                        <Fax>01905 321988</Fax>
                    </Contact1>
                    <Contact2>
                        <Name>Elizabeth Kilbey</Name>
                        <Phone>01905 321986</Phone>
                        <Email><EMAIL></Email>
                        <Fax>01905 321985</Fax>
                    </Contact2>
                </DespatchFrom>
                <DeliverTo>
                    <Code>000</Code>
                    <EAN13>5024657121941</EAN13>
                    <Name>ABC Foods Limited</Name>
                    <Address1>1 High Street</Address1>
                    <Address5>London</Address5>
                    <Country>GB</Country>
                    <VAT>
                        <Num>***********-123</Num>
                        <Alpha>***********-123</Alpha>
                    </VAT>
                    <Contact1>
                        <Name>Carl Denver</Name>
                        <Phone>********** 456</Phone>
                        <Fax>********** 457</Fax>
                    </Contact1>
                    <Contact2>
                        <Name>Elisabeth Francis</Name>
                        <Phone>********** 458</Phone>
                    </Contact2>
                </DeliverTo>
            </DeliveryInfo>
            <CreditLineInfo></CreditLineInfo>
        </InvoiceLine>
        <InvoiceTrailer>
            <TAXSummary>
                <NoOfLines>3</NoOfLines>
                <TAXRate>
                    <TAXCode>S</TAXCode>
                    <TAXPercentage>20.000</TAXPercentage>
                </TAXRate>
                <TAXDetails>
                    <LineAmount>51.40</LineAmount>
                    <DiscForInvoiceQty>0.00</DiscForInvoiceQty>
                    <DiscForInvoiceValue>0.00</DiscForInvoiceValue>
                    <Surcharge>0.00</Surcharge>
                    <Subsidy>0.00</Subsidy>
                    <ExtAmount>51.40</ExtAmount>
                    <SDiscountAmount>0.00</SDiscountAmount>
                    <ExtAmountInclSDisc>51.40</ExtAmountInclSDisc>
                    <TAXAmount>10.28</TAXAmount>
                    <ExtAmountInclTAX>61.68</ExtAmountInclTAX>
                    <ExtAmountInclSDiscAndTAX>61.68</ExtAmountInclSDiscAndTAX>
                    <HashTotalQty>0.000</HashTotalQty>
                    <HashTotalMeasure>0.000</HashTotalMeasure>
                </TAXDetails>
            </TAXSummary>
            <InvoiceTotal>
                <LineAmount>51.40</LineAmount>
                <DiscForInvoiceQty>0.00</DiscForInvoiceQty>
                <DiscForInvoiceValue>0.00</DiscForInvoiceValue>
                <Surcharge>10.00</Surcharge>
                <Subsidy>0.00</Subsidy>
                <ExtAmount>51.40</ExtAmount>
                <SDiscountAmount>0.00</SDiscountAmount>
                <ExtAmountInclSDisc>51.40</ExtAmountInclSDisc>
                <TAXAmount>10.28</TAXAmount>
                <ExtAmountInclTAX>71.68</ExtAmountInclTAX>
                <ExtAmountInclSDiscAndTAX>71.68</ExtAmountInclSDiscAndTAX>
                <HashTotalQty>0.000</HashTotalQty>
                <HashTotalMeasure>0.000</HashTotalMeasure>
            </InvoiceTotal>
        </InvoiceTrailer>
        <DocTrailer>
            <TotalLines>3</TotalLines>
        </DocTrailer>
    </Document>
    <TaxControl>
        <Summary>
            <Generated>
                <Date>2018-04-27</Date>
                <Time>14:43:30</Time>
            </Generated>
        </Summary>
        <Document>
            <DocType>INVOICE</DocType>
            <DocCount>1</DocCount>
            <TaxableAmount>51.40</TaxableAmount>
            <VATAmount>10.28</VATAmount>
            <TotalInclVAT>71.68</TotalInclVAT>
            <VATDetail>
                <VATCode>S</VATCode>
                <VATPercentage>20.000</VATPercentage>
                <Currency>
                    <InvCurr>GBP</InvCurr>
                </Currency>
                <TaxableAmount>51.40</TaxableAmount>
                <VATAmount>10.28</VATAmount>
                <TotalInclVAT>61.68</TotalInclVAT>
            </VATDetail>
        </Document>
    </TaxControl>
    <MsgTrailer>
        <TotalDocs>1</TotalDocs>
    </MsgTrailer>
</tc:TrueCommerceInvoice>