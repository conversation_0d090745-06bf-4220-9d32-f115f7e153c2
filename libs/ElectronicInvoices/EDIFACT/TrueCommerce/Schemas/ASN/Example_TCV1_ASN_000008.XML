<?xml version="1.0" encoding="UTF-8"?>
<tc:TrueCommerceAsn xmlns:tc="http://www.truecommerce.com/docs/asn" xmlns:cm="http://www.truecommerce.com/docs/common/components" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.truecommerce.com/docs/asn truecommerce_ASN_v1.0.xsd">
	<MsgHeader>
		<MsgType>ASN</MsgType>
		<VersionID>v1.0</VersionID>
		<TransmittedDate>
			<Date>2018-11-07</Date>
			<Time>10:50:55</Time>
		</TransmittedDate>
		<FileDate>
			<Date>2018-11-07</Date>
			<Time>10:50:55</Time>
		</FileDate>
		<Interchange>
			<Standard>TRUECOMMERCE</Standard>
			<SenderRef>            32</SenderRef>
			<FileGenNo>1</FileGenNo>
			<FileVerNo>1</FileVerNo>
		</Interchange>
	</MsgHeader>
	<Document>
		<DocHeader>
			<DocType>ASN</DocType>
			<DocFunction>ORIGINAL</DocFunction>
			<CustAddr>
				<Code>CUST-CODE</Code>
				<AltCode>MAG001</AltCode>
				<Name>CUST-NAME</Name>
				<Address1>ADD1</Address1>
				<Address2>ADD2</Address2>
				<Address3>ADD3</Address3>
				<Country>GB</Country>
				<VAT/>
			</CustAddr>
			<SuppAddr>
				<Code>SUPPCODE</Code>
				<AltCode>331458</AltCode>
				<EAN13></EAN13>
				<Name>SUPPNAME</Name>
				<Address1>ADD1</Address1>
				<Address2>ADD2</Address2>
				<Address3>ADD3</Address3>
				<Address4>ADD4</Address4>
				<Country>GB</Country>
				<VAT>
					<Num>*********</Num>
					<Alpha>GB*********</Alpha>
				</VAT>
				<Contact1>
					<Name></Name>
					<Phone></Phone>
				</Contact1>
			</SuppAddr>
			<DocDate>
				<Date>2018-10-30</Date>
				<Time>00:16:28</Time>
			</DocDate>
			<TranInfo/>
			<RoutingCode>T81107AAAN</RoutingCode>
		</DocHeader>
		<ASNHeader>
			<DelNoteNo>11416901_TEST</DelNoteNo>
			<DelNoteDate>
				<Date>2018-10-30</Date>
				<Time>00:16:28</Time>
			</DelNoteDate>
			<Type>FULL</Type>
			<BookingRef>AA</BookingRef>
			<ShipmentClass>DEPOT</ShipmentClass>
			<Locations>
				<OrderBranch>
					<Code>DELTOCODE</Code>
					<AltCode>1179</AltCode>
					<Name>ORDBRNCODE</Name>
					<Address1>ADD1</Address1>
					<Address2>ADD2</Address2>
					<Address3>ADD3</Address3>
					<PostCode></PostCode>
					<Country>GB</Country>
					<VAT/>
				</OrderBranch>
				<DespatchFrom>
					<EAN13>*************</EAN13>
					<Name>SUPPNAME</Name>
					<Address1>ADD1</Address1>
					<Address2>ADD2</Address2>
					<Address3>ADD3</Address3>
					<Address4>ADD4</Address4>
					<Address5>ADD5</Address5>
					<PostCode>POSTCODE</PostCode>
					<Country>GB</Country>
					<VAT>
						<Num>*********</Num>
						<Alpha>GB*********</Alpha>
					</VAT>
					<Contact1>
						<Name>contact</Name>
						<Phone>phone</Phone>
					</Contact1>
				</DespatchFrom>
				<DeliverTo>
					<Code>DELTOCODE</Code>
					<AltCode>1179</AltCode>
					<Name>NAME</Name>
					<Address1>ADD1</Address1>
					<Address2>ADD2</Address2>
					<Address3>46440</Address3>
					<PostCode>46440</PostCode>
					<Country>GB</Country>
					<VAT/>
				</DeliverTo>
				<Carrier>
					<Code>INXL</Code>
					<Name>INCOAL COMPANY</Name>
					<VAT/>
				</Carrier>
				<InvoiceFrom>
					<EAN13>*************</EAN13>
					<Name>SUPPNAME</Name>
					<Address1>ADD1</Address1>
					<Address2>ADD2</Address2>
					<Address3>ADD3</Address3>
					<Address4>ADD4</Address4>
					<Address5>ADD5</Address5>
					<PostCode>POSTCODE</PostCode>
					<Country>GB</Country>
					<VAT>
						<Num>*********</Num>
						<Alpha>GB*********</Alpha>
					</VAT>
					<Contact1>
						<Name>contact</Name>
						<Phone>	</Phone>
					</Contact1>
				</InvoiceFrom>
				<InvoiceTo>
					<Code>DELTOCODE</Code>
					<Name>NAME</Name>
					<Address1>ADD1</Address1>
					<Address2>ADD2</Address2>
					<Address3>46440</Address3>
					<PostCode>46440</PostCode>
					<Country>GB</Country>
					<VAT/>
				</InvoiceTo>
			</Locations>
			<DespatchDates>
				<Actual>
					<Date>2018-10-26</Date>
					<Time>00:05:16+01:00</Time>
				</Actual>
			</DespatchDates>
			<DeliveryDates>
				<Expected>
					<Date>2018-10-29</Date>
					<Time>00:11:16</Time>
				</Expected>
			</DeliveryDates>
			<DelPoint>1179A</DelPoint>
			<Instructions/>
			<GoodsWeight>
				<Weight>33.700</Weight>
			</GoodsWeight>
			<GoodsVolume>
				<Volume>23925.000</Volume>
			</GoodsVolume>
			<Perishable>false</Perishable>
			<AdditionalRef1>2481</AdditionalRef1>
			<Additional>
				<DataItem>
					<Ref>TRANSPORT_DETAILS</Ref>
					<UserDefined>
						<Alpha2>30</Alpha2>
						<Alpha3>TRVAN1234</Alpha3>
						<Num1>0.000</Num1>
						<Num2>0.000</Num2>
						<Num3>0.000</Num3>
						<Num4>0.000</Num4>
						<Num5>0.000</Num5>
					</UserDefined>
				</DataItem>
			</Additional>
		</ASNHeader>
		<OrderPallet>
			<Order>
				<CustOrder>5500305984</CustOrder>
				<CustOrderLineNo>1</CustOrderLineNo>
				<CustOrderDate>
					<Date>2018-10-30</Date>
					<Time>12:00:00</Time>
				</CustOrderDate>
			</Order>
			<Pallet>
				<Supplier>
					<Code>SUPPNAME</Code>
					<AltCode>331458</AltCode>
					<EAN13>*************</EAN13>
					<Name>Name</Name>
					<Address1>ADD1</Address1>
					<Address2>ADD2</Address2>
					<Address3>ADD3</Address3>
					<Address4>ADD4</Address4>
					<Country>GB</Country>
					<VAT>
						<Num>*********</Num>
						<Alpha>GB*********</Alpha>
					</VAT>
					<Contact1>
						<Name>contact</Name>
						<Phone>phone</Phone>
					</Contact1>
				</Supplier>
				<DespatchFrom>
					<EAN13>*************</EAN13>
					<Name>SUPPNAME</Name>
					<Address1>ADD1</Address1>
					<Address2>ADD2</Address2>
					<Address3>ADD3</Address3>
					<Address4>ADD4</Address4>
					<Address5>ADD5</Address5>
					<PostCode>POSTCODE</PostCode>
					<Country>GB</Country>
					<VAT>
						<Num>*********</Num>
						<Alpha>GB*********</Alpha>
					</VAT>
					<Contact1>
						<Name>contact</Name>
						<Phone>phone</Phone>
					</Contact1>
				</DespatchFrom>
				<DeliverTo>
					<Code>DELTOCODE</Code>
					<AltCode>1179</AltCode>
					<Name>NAME</Name>
					<Address1>ADD1</Address1>
					<Address2>ADD2</Address2>
					<Address3>46440</Address3>
					<PostCode>46440</PostCode>
					<Country>GB</Country>
					<VAT/>
				</DeliverTo>
				<OrderLinePack>
					<Order>
						<CustOrder>5500305984</CustOrder>
						<CustOrderLineNo>1</CustOrderLineNo>
						<CustOrderDate>
							<Date>2018-10-30</Date>
							<Time>12:00:00</Time>
						</CustOrderDate>
					</Order>
					<LinePack>
						<OrderedItem>
							<CustItem>
								<Code>701445189901</Code>
							</CustItem>
							<SuppItem>
								<Code>701445189901</Code>
								<Desc1>M6 Hex Flange Locking Nut</Desc1>
								<Desc2>Ford Zc Cr3+seal 96h</Desc2>
							</SuppItem>
							<Desc1>M6 Hex Flange Locking Nut</Desc1>
							<Desc2>Ford Zc Cr3+seal 96h</Desc2>
						</OrderedItem>
						<UnitOfOrder>
							<Unit>1</Unit>
							<OrderMeasure>0</OrderMeasure>
						</UnitOfOrder>
						<Substitution>
							<SubstituteItem>
								<CustItem/>
								<SuppItem/>
							</SubstituteItem>
							<UnitOfSubstitution>
								<Unit>0</Unit>
								<OrderMeasure>0</OrderMeasure>
							</UnitOfSubstitution>
						</Substitution>
						<DeliveryQuantity>
							<Unit>7500.000</Unit>
							<UOM>PCE</UOM>
						</DeliveryQuantity>
						<PricingMeasure>
							<Measure>###NONE###</Measure>
						</PricingMeasure>
						<LineAmount>168.750</LineAmount>
						<SellPrice>
							<Measure>###NONE###</Measure>
						</SellPrice>
						<NoOfPacks>3</NoOfPacks>
						<NetWeight>
							<Weight>32.400</Weight>
						</NetWeight>
						<NewRequirement>false</NewRequirement>
						<CommodityClass>DEPOT</CommodityClass>
						<Additional>
							<DataItem>
								<Ref>*ASN_ORDERREF</Ref>
								<Value>1</Value>
							</DataItem>
							<DataItem>
								<Ref>CURRENCY_EXTRA</Ref>
								<Value>EUR</Value>
							</DataItem>
							<DataItem>
								<Ref>CUSTOMS</Ref>
								<UserDefined>
									<Alpha1>7318166090</Alpha1>
									<Num1>0.000</Num1>
									<Num2>0.000</Num2>
									<Num3>0.000</Num3>
									<Num4>0.000</Num4>
									<Num5>0.000</Num5>
								</UserDefined>
							</DataItem>
							<DataItem>
								<Ref>ITEM_DIMENSIONS</Ref>
								<UserDefined>
									<Num1>0.000</Num1>
									<Num2>0.000</Num2>
									<Num3>0.000</Num3>
									<Num4>33.696</Num4>
									<Num5>23925.000</Num5>
								</UserDefined>
							</DataItem>
							<DataItem>
								<Ref>ITEM_ORIGIN</Ref>
								<UserDefined>
									<Alpha3>NQ</Alpha3>
									<Num1>0.000</Num1>
									<Num2>0.000</Num2>
									<Num3>0.000</Num3>
									<Num4>0.000</Num4>
									<Num5>0.000</Num5>
								</UserDefined>
							</DataItem>
						</Additional>
						<Pack>
							<SSCC>200074550</SSCC>
							<Type>CARTON</Type>
							<GrossWeight>
								<Weight>11.232</Weight>
								<Measure>KGM</Measure>
							</GrossWeight>
							<NoOfUnits>
								<Unit>2500.000</Unit>
								<UOM>PCE</UOM>
							</NoOfUnits>
						</Pack>
						<Pack>
							<SSCC>200074551</SSCC>
							<Type>CARTON</Type>
							<GrossWeight>
								<Weight>11.232</Weight>
								<Measure>KGM</Measure>
							</GrossWeight>
							<NoOfUnits>
								<Unit>2500.000</Unit>
								<UOM>PCE</UOM>
							</NoOfUnits>
						</Pack>
						<Pack>
							<SSCC>200074552</SSCC>
							<Type>CARTON</Type>
							<GrossWeight>
								<Weight>11.232</Weight>
								<Measure>KGM</Measure>
							</GrossWeight>
							<NoOfUnits>
								<Unit>2500.000</Unit>
								<UOM>PCE</UOM>
							</NoOfUnits>
						</Pack>
					</LinePack>
				</OrderLinePack>
				<OrderPackLine>
					<Order>
						<CustOrder>5500305984</CustOrder>
						<CustOrderLineNo>1</CustOrderLineNo>
						<CustOrderDate>
							<Date>2018-10-30</Date>
							<Time>12:00:00</Time>
						</CustOrderDate>
					</Order>
					<PackLine>
						<SSCC>200074550</SSCC>
						<Type>CARTON</Type>
						<GrossWeight>
							<Weight>11.232</Weight>
							<Measure>KGM</Measure>
						</GrossWeight>
						<NoOfUnits>
							<Unit>2500.000</Unit>
							<UOM>PCE</UOM>
						</NoOfUnits>
						<Line>
							<OrderedItem>
								<CustItem>
									<Code>701445189901</Code>
								</CustItem>
								<SuppItem>
									<Code>701445189901</Code>
									<Desc1>M6 Hex Flange Locking Nut</Desc1>
									<Desc2>Ford Zc Cr3+seal 96h</Desc2>
								</SuppItem>
								<Desc1>M6 Hex Flange Locking Nut</Desc1>
								<Desc2>Ford Zc Cr3+seal 96h</Desc2>
							</OrderedItem>
							<UnitOfOrder>
								<Unit>1</Unit>
								<OrderMeasure>0</OrderMeasure>
							</UnitOfOrder>
							<Substitution>
								<SubstituteItem>
									<CustItem/>
									<SuppItem/>
								</SubstituteItem>
								<UnitOfSubstitution>
									<Unit>0</Unit>
									<OrderMeasure>0</OrderMeasure>
								</UnitOfSubstitution>
							</Substitution>
							<DeliveryQuantity>
								<Unit>7500.000</Unit>
								<UOM>PCE</UOM>
							</DeliveryQuantity>
							<PricingMeasure>
								<Measure>###NONE###</Measure>
							</PricingMeasure>
							<LineAmount>168.750</LineAmount>
							<SellPrice>
								<Measure>###NONE###</Measure>
							</SellPrice>
							<NoOfPacks>3</NoOfPacks>
							<NetWeight>
								<Weight>32.400</Weight>
							</NetWeight>
							<NewRequirement>false</NewRequirement>
							<CommodityClass>DEPOT</CommodityClass>
						</Line>
					</PackLine>
					<PackLine>
						<SSCC>200074551</SSCC>
						<Type>CARTON</Type>
						<GrossWeight>
							<Weight>11.232</Weight>
							<Measure>KGM</Measure>
						</GrossWeight>
						<NoOfUnits>
							<Unit>2500.000</Unit>
							<UOM>PCE</UOM>
						</NoOfUnits>
						<Line>
							<OrderedItem>
								<CustItem>
									<Code>701445189901</Code>
								</CustItem>
								<SuppItem>
									<Code>701445189901</Code>
									<Desc1>M6 Hex Flange Locking Nut</Desc1>
									<Desc2>Ford Zc Cr3+seal 96h</Desc2>
								</SuppItem>
								<Desc1>M6 Hex Flange Locking Nut</Desc1>
								<Desc2>Ford Zc Cr3+seal 96h</Desc2>
							</OrderedItem>
							<UnitOfOrder>
								<Unit>1</Unit>
								<OrderMeasure>0</OrderMeasure>
							</UnitOfOrder>
							<Substitution>
								<SubstituteItem>
									<CustItem/>
									<SuppItem/>
								</SubstituteItem>
								<UnitOfSubstitution>
									<Unit>0</Unit>
									<OrderMeasure>0</OrderMeasure>
								</UnitOfSubstitution>
							</Substitution>
							<DeliveryQuantity>
								<Unit>7500.000</Unit>
								<UOM>PCE</UOM>
							</DeliveryQuantity>
							<PricingMeasure>
								<Measure>###NONE###</Measure>
							</PricingMeasure>
							<LineAmount>168.750</LineAmount>
							<SellPrice>
								<Measure>###NONE###</Measure>
							</SellPrice>
							<NoOfPacks>3</NoOfPacks>
							<NetWeight>
								<Weight>32.400</Weight>
							</NetWeight>
							<NewRequirement>false</NewRequirement>
							<CommodityClass>DEPOT</CommodityClass>
						</Line>
					</PackLine>
					<PackLine>
						<SSCC>200074552</SSCC>
						<Type>CARTON</Type>
						<GrossWeight>
							<Weight>11.232</Weight>
							<Measure>KGM</Measure>
						</GrossWeight>
						<NoOfUnits>
							<Unit>2500.000</Unit>
							<UOM>PCE</UOM>
						</NoOfUnits>
						<Line>
							<OrderedItem>
								<CustItem>
									<Code>701445189901</Code>
								</CustItem>
								<SuppItem>
									<Code>701445189901</Code>
									<Desc1>M6 Hex Flange Locking Nut</Desc1>
									<Desc2>Ford Zc Cr3+seal 96h</Desc2>
								</SuppItem>
								<Desc1>M6 Hex Flange Locking Nut</Desc1>
								<Desc2>Ford Zc Cr3+seal 96h</Desc2>
							</OrderedItem>
							<UnitOfOrder>
								<Unit>1</Unit>
								<OrderMeasure>0</OrderMeasure>
							</UnitOfOrder>
							<Substitution>
								<SubstituteItem>
									<CustItem/>
									<SuppItem/>
								</SubstituteItem>
								<UnitOfSubstitution>
									<Unit>0</Unit>
									<OrderMeasure>0</OrderMeasure>
								</UnitOfSubstitution>
							</Substitution>
							<DeliveryQuantity>
								<Unit>7500.000</Unit>
								<UOM>PCE</UOM>
							</DeliveryQuantity>
							<PricingMeasure>
								<Measure>###NONE###</Measure>
							</PricingMeasure>
							<LineAmount>168.750</LineAmount>
							<SellPrice>
								<Measure>###NONE###</Measure>
							</SellPrice>
							<NoOfPacks>3</NoOfPacks>
							<NetWeight>
								<Weight>32.400</Weight>
							</NetWeight>
							<NewRequirement>false</NewRequirement>
							<CommodityClass>DEPOT</CommodityClass>
						</Line>
					</PackLine>
				</OrderPackLine>
			</Pallet>
		</OrderPallet>
		<DocTrailer>
			<TotalLines>1</TotalLines>
		</DocTrailer>
	</Document>
	<MsgTrailer>
		<TotalDocs>1</TotalDocs>
	</MsgTrailer>
</tc:TrueCommerceAsn>
