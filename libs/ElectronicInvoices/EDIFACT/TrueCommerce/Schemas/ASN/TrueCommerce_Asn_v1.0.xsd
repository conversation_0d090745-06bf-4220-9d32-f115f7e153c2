<?xml version="1.0" encoding="UTF-8"?>
<!-- edited with XMLSpy v2015 rel. 3 sp1 (x64) (http://www.altova.com) by <PERSON> (<PERSON><PERSON><PERSON><PERSON>) -->
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:tc="http://www.truecommerce.com/docs/asn" xmlns:cm="http://www.truecommerce.com/docs/common/components" targetNamespace="http://www.truecommerce.com/docs/asn" elementFormDefault="unqualified" attributeFormDefault="unqualified">
	<xs:import namespace="http://www.truecommerce.com/docs/common/components" schemaLocation="truecommerce_components_v1.0.xsd"/>
	<xs:element name="TrueCommerceAsn">
		<xs:annotation>
			<xs:documentation>TrueCommerce Advance Shipping Notification specification (Pallet-based, with multi-packs)</xs:documentation>
		</xs:annotation>
		<xs:complexType>
			<xs:sequence>
				<xs:element name="MsgHeader" type="cm:MessageHeader"/>
				<xs:element name="Document" type="tc:ASNDoc" maxOccurs="unbounded"/>
				<xs:element name="MsgTrailer" type="cm:MessageTrailer"/>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<xs:complexType name="ASNDoc">
		<xs:annotation>
			<xs:documentation>ASN document 
(Multi-layer - pallet / pack).

This structure allows for the following:
> Pallet, Order, Item
> Pallet, Order, Item, Pack
> Pallet, Order, Pack, Item
</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="DocHeader" type="cm:DocumentHeader"/>
			<xs:element name="ASNHeader" type="tc:ASNHeader"/>
			<xs:element name="OrderPallet" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>Pallets (tares) included in the delivery</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:complexContent>
						<xs:extension base="tc:PalletGroup">
							<xs:sequence>
								<xs:element name="Order" type="tc:OrderReferences" minOccurs="0"/>
								<xs:element name="Pallet" type="tc:Pallet" maxOccurs="unbounded">
									<xs:annotation>
										<xs:documentation>Pallets (tares) included in the delivery</xs:documentation>
									</xs:annotation>
								</xs:element>
							</xs:sequence>
						</xs:extension>
					</xs:complexContent>
				</xs:complexType>
			</xs:element>
			<xs:element name="DocTrailer" type="cm:DocumentTrailer"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="ASNHeader">
		<xs:annotation>
			<xs:documentation>ASN header</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="DelNoteNo" type="xs:string" id="DOC_1" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Delivery Note No (aka ASN No.)</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DelNoteDate" type="cm:DateTime" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Delivery Note Date</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Type" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Type of delivery.            FULL for despatched ASNs PLANNED for built ASNs  SHIP_VIA for ASNs going to a regional distribution centre (RDC).  This ASN will have a secondary ship location                    CONSOL (consolidated shipment) for ASNs going from a RDC</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:enumeration value="FULL"/>
						<xs:enumeration value="PLANNED"/>
						<xs:enumeration value="SHIP_VIA"/>
						<xs:enumeration value="CONSOL"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="ShipmentRef" type="xs:string" id="DOC_2" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Shipment Reference, usually allocated by the customer/app on behalf of the supplier.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="BookingRef" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Booking Reference, (aka Transport Delivery Number)</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DelScheduleNo" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Aka Delivery Wave Number</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="CommonRef" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Common Reference number (aka Customer Reference)</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DelMethod" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Delivery Method: BRANCH_COLLECTION, DELIVER_TO_BRANCH, DELIVER_TO_DEPOT, DIRECT_DELIVERY,
CUSTOMER_COLLECTION</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:pattern value="BRANCH_COLLECTION"/>
						<xs:pattern value="DELIVER_TO_BRANCH"/>
						<xs:pattern value="DELIVER_TO_DEPOT"/>
						<xs:pattern value="DIRECT_DELIVERY"/>
						<xs:pattern value="CUSTOMER_COLLECTION"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="ShipmentClass" type="xs:string" id="DOC_CLASS" minOccurs="0"/>
			<xs:element name="Brand" type="xs:string" minOccurs="0"/>
			<xs:element name="SupplyGroup" type="xs:string" minOccurs="0"/>
			<xs:element name="Locations" type="tc:DeliveryLocations" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Location Addresses.  Note: Customer and Supplier don't exist here because they are stored within the Document Header section.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DespatchDates" type="tc:DespatchDates" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Dates of Despatch.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DeliveryDates" type="tc:DeliveryDates" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Dates of Delivery.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="PayMethod" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Payment method:        CUSTOMER_PAYS,  SUPPLIER_PAYS,  3RD_PARTY_PAYS,  NO_CHARGE</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:pattern value="CUSTOMER_PAYS"/>
						<xs:pattern value="SUPPLIER_PAYS"/>
						<xs:pattern value="3RD_PARTY_PAYS"/>
						<xs:pattern value="NO_CHARGE"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="DelPoint" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Delivery point</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DelGate" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Delivery gate</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DelDock" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Delivery dock</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="AETC" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Authorised Excess Transportation Cost (AETC) code. </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="VehicleID" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Vehicle Identifier No.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="JourneyRef" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Reference number for the journey</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="JourneyDesc" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Textual description of the journey route</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Instructions" type="cm:Narrative" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Delivery Instruction notes</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="NumberOfPallets" type="xs:integer" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Total number of pallets in the delivery</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="NumberOfUnits" type="xs:long" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Number of delivery units (cases, cartons, packages) transported</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="TareWeight" type="cm:Weight" minOccurs="0">
				<xs:annotation>
					<xs:documentation>This is the weight of the unladen vehicle, i.e. the weight you will use to calculate the vehicle's load.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="GoodsWeight" type="cm:Weight" minOccurs="0">
				<xs:annotation>
					<xs:documentation>This is the weight of the load</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="GoodsVolume" type="cm:Volume" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Volume of the load</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="TotalWeight" type="cm:Weight" minOccurs="0">
				<xs:annotation>
					<xs:documentation>This is the weight of the vehicle fully laden, and is an indication of the total weight of everything on the bridge.
Total = tare + goods</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Notes" type="cm:Notes" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>General notes pertaining to the ASN</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Perishable" type="xs:boolean" default="false" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Indicates whether the delivery contains only perishable items.  Defaults to false</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ExpectedInvoiceDate" type="xs:date" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Expected invoice date.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="AdditionalRef1" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Additional Reference Data 1</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="AdditionalRef2" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Additional Reference Data 2</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Additional" type="cm:AdditionalInfo" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Addtional information</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="DeliveryLocations">
		<xs:annotation>
			<xs:documentation>Delivery location addresses, despatch from/to, etc</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="OrderBranch" type="cm:Address" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Full address of the ordering branch.  </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DespatchFrom" type="cm:Address" id="SHFR" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Full address of the despatch (ship-from) location</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DeliverTo" type="cm:Address" id="SHTO" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Full address of the deliver-to location.  This will usually match the ordering branch address, except for direct deliveries, or depot deliveries</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Carrier" type="cm:Address" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Location details of any carrier used.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="SecondaryShipLoc" type="cm:Address" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Secondary or trans-shipment location</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="InvoiceFrom" type="cm:Address" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Full address of the invoice_from location</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="InvoiceTo" type="cm:Address" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Full address of the invoice-to location</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="DespatchDates">
		<xs:annotation>
			<xs:documentation>Details the despatch dates - from requirements to actuals</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="Expected" type="cm:DateTime" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Expected despatch date/time from the ship-from location</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Actual" type="cm:DateTime" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Actual despatch (shipment) date/time</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="DeliveryDates">
		<xs:annotation>
			<xs:documentation>Details the delivery dates - from requirements to actuals</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="Expected" type="cm:DateTime" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Expected delivery date/time at the deliver-to location</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="ASNLine">
		<xs:annotation>
			<xs:documentation>ASN line information</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="LineNo" type="xs:int" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Delivery Line Number</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="OrderedItem" type="cm:ItemDetails" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Item details as ordered by the customer</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="UnitOfOrder" type="cm:UnitOfOrderQty" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Describes what units are being ordered within the qty_ordered field. The qty ordered may be 100, but this is 100 x 24 cans.  The 24 cans is described within this field.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Substitution" type="tc:DeliveryLineSubstitution" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Item substitution details. Only specified if the supplier is delivering a different item than the one ordered</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DeliveryQuantity" type="cm:Quantity" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Describes what quantities are being delivered on this pallet.  If no substitution has been made to the item then this is relative to the ordering unit, otherwise it's relative to the substitution unit.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="OrderQuantity" type="cm:Quantity" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Describes what quantity, if any, was on the original Order Line being shipped on this ASN Line.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="PricingMeasure" type="cm:PricingMeasure" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Describes the mechanism for pricing the invoice line</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="CostPrice" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Cost price, before deducting line discounts, etc (excluding VAT) per  pricing measure</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:decimal">
						<xs:totalDigits value="19"/>
						<xs:fractionDigits value="7"/>
						<xs:minInclusive value="-999999999999.9999999"/>
						<xs:maxInclusive value="999999999999.9999999"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="LineAmount" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Net cost of detail line, including any line discounts, etc (excluding VAT) of the order line</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:decimal">
						<xs:totalDigits value="19"/>
						<xs:fractionDigits value="7"/>
						<xs:minInclusive value="-999999999999.9999999"/>
						<xs:maxInclusive value="999999999999.9999999"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="SellPrice" type="cm:Price" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Selling price (before the application of VAT) of a traded unit or measure.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Tax" type="cm:Tax" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Detail tax information for the line, as provided by the supplier</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="NoOfPacks" type="xs:long" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Number of packs on the pallet line</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="LotDetails" type="tc:Lot" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>Lot details for the delivery line (aka Lots contained within the Delivery Line Items)</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="BestBeforeEndDate" type="xs:int" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Best Before End date for perishable goods. In the format CCYYMMDD</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ToFollow" type="tc:ToFollow" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>Details of items and qtys to follow</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DelPoint" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Delivery Point</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DelGate" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Delivery Gate</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DelDock" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Delivery Dock</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DelRef" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Delivery Reference</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="SupplyGroup" type="xs:string" minOccurs="0"/>
			<xs:element name="NetWeight" type="cm:Weight" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Net weight of the items on this order line.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Notes" type="cm:Notes" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>General notes pertaining to this ASN Line</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="NewRequirement" type="xs:boolean" default="false" minOccurs="0">
				<xs:annotation>
					<xs:documentation>This line is not known to the orderer.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="CommodityClass" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Commodity Class</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Additional" type="cm:AdditionalInfo" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Addtional information</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="ToFollow">
		<xs:annotation>
			<xs:documentation>Details of qtys and items to follow</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="ToFollowQuantity" type="cm:Quantity" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Quantity to follow</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ToFollowDate" type="cm:DateTime" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Date for quantity to follow.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Substitution" type="tc:DeliveryLineSubstitution" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Item substitution details. Only specified if the supplier intends to follow-up the delivery with a different item than the one ordered</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Narrative" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Narrative/Reasons pertaining  to this 'ToFollow' entry</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="SpecialPriceCode" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Used to indicate whether any special pricing applies, e.g. FREE, PROMOTION</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="CreditLineCode" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Used to indicate the nature of a credit line, e.g. RETURNS, HANDLING_ALLOWANCE</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Additional" type="cm:AdditionalInfo" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Addtional information</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="DeliveryPallet">
		<xs:annotation>
			<xs:documentation>Delivery Line Pallet Details</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="SSCC" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Serial Shipping Container Code</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="UnitsOnPallet" type="cm:Quantity" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Number of units on the pallet for this delivery line</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="PacksOnPallet" type="xs:long" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Number of packs on the pallet for this delivery line</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="PalletDeliveryNetWeight" type="cm:Weight" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Weight of the delivery line on the pallet</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="DeliveryLineSubstitution">
		<xs:annotation>
			<xs:documentation>Delivery line substitution details</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="SubstituteItem" type="cm:ItemDetails">
				<xs:annotation>
					<xs:documentation>Item details as supplied by  supplier.  Only required if a substitution has occured</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="UnitOfSubstitution" type="cm:UnitOfOrderQty">
				<xs:annotation>
					<xs:documentation>Describes what units are being substituted within the delivered qty field. The qty delivered may be 100, but this is 100 x 12 cans.  The '12 cans' is described within this field.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="OrderReferences">
		<xs:annotation>
			<xs:documentation>Customer and Supplier Order numbers and dates</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="CustOrder" type="xs:string" id="PONO" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Purchase order number of the customer (aka buyer)</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="CustOrderLineNo" type="xs:int" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Purchase order line number</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="CustOrderDate" type="cm:DateTime" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Date order placed by customer</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="OrigCustOrder" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Order number reference of the originating customer</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ThirdPartyOrderRef" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Order number reference supplied by a third-party.  This is applicable in situations where a 3rd Party is selling goods on behalf of the TrueCommerce Customer.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="SuppOrder" type="xs:string" id="SONO" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Purchase order reference of the supplier (aka vendor)</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="SuppOrderDate" type="cm:DateTime" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Date order received by supplier</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Contract" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Contract number to which this order line relates.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ContractLine" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Contract Line number to which this order line relates.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="SpecNo" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Specification number to which this order line relates.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Additional" type="cm:AdditionalInfo" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Addtional information</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="Pallet">
		<xs:annotation>
			<xs:documentation>Pallet details</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="SSCC" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Serial Shipping Container Code</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="PalletType" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Type of pallet or container</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="CommonRef" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Common Reference number for Pallet</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="GoodsWeight" type="cm:Weight" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Net Weight of the contents</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="PalletWeight" type="cm:Weight" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Net Weight of the pallet</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="PalletGrossWeight" type="cm:Weight" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Gross weight of pallet and contents</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="NoOfSKUCodes" type="xs:long" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Describes the number of different SKU codes rather than the number of units. A value of 1 means the pallet conatins the same type of item</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="NoOfDeliveryLines" type="xs:long" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Number of delivery lines stored on the pallet</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="NoOfUnits" type="cm:Quantity" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Number of units on the pallet </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="NoOfPacks" type="xs:long" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Number of packs on the pallet layer</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="NoOfLayers" type="xs:long" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Number of layers on the pallet</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Supplier" type="cm:Address" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Original Supplier Address</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DespatchFrom" type="cm:Address" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Original Supplier Despatch From Address</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DeliverTo" type="cm:Address" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Ultimate Deliver To Address</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="PurchaseOrder" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Will only be populated if the pallet contains stock for a single purchase order.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Narrative" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Narrative pertaining to this ASN Pallet</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:minLength value="0"/>
						<xs:maxLength value="80"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="RFID" minOccurs="0">
				<xs:annotation>
					<xs:documentation>RFID</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:minLength value="0"/>
						<xs:maxLength value="35"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="Additional" type="cm:AdditionalInfo" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Addtional information</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:sequence>
				<xs:annotation>
					<xs:documentation>Please use the appropriate line structure according to your needs.  
If you don't have packs then use OrderLinePack and omit the pack details.
If you have packs then use the structure appropriate to the structure of the shipment packs.  Packs with multiple lines will be best served using OrderPackLine. </xs:documentation>
				</xs:annotation>
				<xs:element name="OrderLinePack" type="tc:OrderLinePack" minOccurs="0" maxOccurs="unbounded"/>
				<xs:element name="OrderPackLine" type="tc:OrderPackLine" minOccurs="0" maxOccurs="unbounded"/>
			</xs:sequence>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="Pack">
		<xs:annotation>
			<xs:documentation>Pack details</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="SSCC" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Serial Shipping Container Code</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Type" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Type of carton or container</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="CommonRef" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Common Reference number</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="GoodsWeight" type="cm:Weight" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Net Weight of the contents</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="PackWeight" type="cm:Weight" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Net Weight of the pack / carton</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="GrossWeight" type="cm:Weight" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Gross weight of pack / carton and contents</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="NoOfSKUCodes" type="xs:long" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Describes the number of different SKU codes rather than the number of units. A value of 1 means the pack conatins the same type of item</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="NoOfDeliveryLines" type="xs:long" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Number of delivery lines stored within the pack</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="NoOfUnits" type="cm:Quantity" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Number of units on the packs</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Narrative" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Narrative pertaining to this ASN Pack</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:minLength value="0"/>
						<xs:maxLength value="80"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="RFID" minOccurs="0">
				<xs:annotation>
					<xs:documentation>RFID</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:minLength value="0"/>
						<xs:maxLength value="35"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="BestBeforeEndDate" type="xs:int" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Best Before End date for perishable goods. In the format CCYYMMDD</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Additional" type="cm:AdditionalInfo" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Addtional information</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="Lot">
		<xs:annotation>
			<xs:documentation>Lot Details </xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="LotNo" type="xs:string">
				<xs:annotation>
					<xs:documentation>Lot Number</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="LotQuantity" type="cm:Quantity" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Quantity of Line items belonging to this Lot</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="BestBeforeEndDate" type="xs:int" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Expiry date for Lot items. In the format CCYYMMDD</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="OriginalBatchNo" type="xs:string" minOccurs="0"/>
			<xs:element name="Manufacturer" type="xs:string" minOccurs="0"/>
			<xs:element name="CountryOfOrigin" type="xs:string" minOccurs="0"/>
			<xs:element name="MaterialSpecification" type="xs:string" minOccurs="0"/>
			<xs:element name="Concession" type="xs:string" minOccurs="0"/>
			<xs:element name="Grade" type="xs:string" minOccurs="0"/>
			<xs:element name="CureDateCode" type="xs:string" minOccurs="0"/>
			<xs:element name="Issue" type="xs:string" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="OrderLinePack">
		<xs:annotation>
			<xs:documentation>An order with Items lines, optionally within one or more packs</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="Order" type="tc:OrderReferences" minOccurs="0"/>
			<xs:element name="LinePack" type="tc:LinePack" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>Line / Pack details</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="OrderPackLine">
		<xs:annotation>
			<xs:documentation>An order with packs containing item lines</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="Order" type="tc:OrderReferences" minOccurs="0"/>
			<xs:element name="PackLine" type="tc:PackLine" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>Pack / Line details</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="LinePack">
		<xs:annotation>
			<xs:documentation>An item line, optionally within one or more packs</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="LineNo" type="xs:int" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Delivery Line Number</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="OrderedItem" type="cm:ItemDetails" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Item details as ordered by the customer</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="UnitOfOrder" type="cm:UnitOfOrderQty" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Describes what units are being ordered within the qty_ordered field. The qty ordered may be 100, but this is 100 x 24 cans.  The 24 cans is described within this field.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Substitution" type="tc:DeliveryLineSubstitution" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Item substitution details. Only specified if the supplier is delivering a different item than the one ordered</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DeliveryQuantity" type="cm:Quantity" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Describes what quantities are being delivered on this pallet.  If no substitution has been made to the item then this is relative to the ordering unit, otherwise it's relative to the substitution unit.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="OrderQuantity" type="cm:Quantity" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Describes what quantity, if any, was on the original Order Line being shipped on this ASN Line.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="PricingMeasure" type="cm:PricingMeasure" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Describes the mechanism for pricing the invoice line</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="CostPrice" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Cost price, before deducting line discounts, etc (excluding VAT) per  pricing measure</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:decimal">
						<xs:totalDigits value="19"/>
						<xs:fractionDigits value="7"/>
						<xs:minInclusive value="-999999999999.9999999"/>
						<xs:maxInclusive value="999999999999.9999999"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="LineAmount" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Net cost of detail line, including any line discounts, etc (excluding VAT) of the order line</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:decimal">
						<xs:totalDigits value="19"/>
						<xs:fractionDigits value="7"/>
						<xs:minInclusive value="-999999999999.9999999"/>
						<xs:maxInclusive value="999999999999.9999999"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="SellPrice" type="cm:Price" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Selling price (before the application of VAT) of a traded unit or measure.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Tax" type="cm:Tax" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Detail tax information for the line, as provided by the supplier</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="NoOfPacks" type="xs:long" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Number of packs on the pallet line</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="LotDetails" type="tc:Lot" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>Lot details for the delivery line (aka Lots contained within the Delivery Line Items)</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="BestBeforeEndDate" type="xs:date" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Best Before End date for perishable goods. In the format CCYYMMDD</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ToFollow" type="tc:ToFollow" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>Details of items and qtys to follow</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DelPoint" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Delivery Point</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DelGate" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Delivery Gate</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DelDock" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Delivery Dock</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DelRef" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Delivery Reference</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="SupplyGroup" type="xs:string" minOccurs="0"/>
			<xs:element name="NetWeight" type="cm:Weight" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Net weight of the items on this order line.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Notes" type="cm:Notes" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>General notes pertaining to this ASN Line</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="NewRequirement" type="xs:boolean" default="false" minOccurs="0">
				<xs:annotation>
					<xs:documentation>This line is not known to the orderer.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="CommodityClass" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Commodity Class</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Additional" type="cm:AdditionalInfo" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Addtional information</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Pack" type="tc:Pack" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>Pack details.  Optional, will only be populated if pack details are known for the item.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="PackLine">
		<xs:annotation>
			<xs:documentation>A pack, containing one or more items</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="SSCC" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Serial Shipping Container Code</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Type" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Type of carton or container</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="CommonRef" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Common Reference number</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="GoodsWeight" type="cm:Weight" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Net Weight of the contents</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="PackWeight" type="cm:Weight" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Net Weight of the pack / carton</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="GrossWeight" type="cm:Weight" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Gross weight of pack / carton and contents</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="NoOfSKUCodes" type="xs:long" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Describes the number of different SKU codes rather than the number of units. A value of 1 means the pack conatins the same type of item</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="NoOfDeliveryLines" type="xs:long" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Number of delivery lines stored within the pack</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="NoOfUnits" type="cm:Quantity" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Number of units on the packs</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Narrative" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Narrative pertaining to this ASN Pack</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:minLength value="0"/>
						<xs:maxLength value="80"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="RFID" minOccurs="0">
				<xs:annotation>
					<xs:documentation>RFID</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:minLength value="0"/>
						<xs:maxLength value="35"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="BestBeforeEndDate" type="xs:date" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Best Before End date for perishable goods. In the format CCYYMMDD</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Additional" type="cm:AdditionalInfo" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Addtional information</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Line" type="tc:ASNLine" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>Pack item line</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="PalletGroup">
		<xs:annotation>
			<xs:documentation>Pallet grouping, allowing for optional order information</xs:documentation>
		</xs:annotation>
	</xs:complexType>
</xs:schema>
