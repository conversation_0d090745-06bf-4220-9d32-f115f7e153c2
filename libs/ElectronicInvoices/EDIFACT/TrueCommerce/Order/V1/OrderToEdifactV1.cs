using System.Reflection;
using System.Xml;
using EDIFACT.TrueCommerce.Exceptions;
using WingCore.domain.Models;
using ObjectDeAndSerialize;
using TrueCommerceInvoiceV1;
using WingCore.application.Contract;
using WingCore.application.Contract.IModels.Helper;
using WingCore.application.Contract.Services;
using WingCore.domain.Common.CustomerAndSuppliers;
using DateTime = System.DateTime;

namespace EDIFACT.TrueCommerce.Order.V1;


public class ConvertEdifactToOrderV1(IKundenService kundenService, 
                                     ISelectedMandant selectedMandant,
                                     IArticleService articleService,
                                     IOrderService orderService,
                                     ICountryService countryService) : IConvertEdifactToOrderV1
{
    public async Task<IEnumerable<Auftragskopf>> CreateOrder(string xmlString)
    {
        ValidateXmlXsd(xmlString);
        var trueCommerceOrder = xmlString.DeserializeObject<TrueCommerceOrder>();
        if(trueCommerceOrder is null)
            throw new Exception("Deserialization failed.");
        
        return await ConvertToOrder(trueCommerceOrder);
    }
    private void ValidateXmlXsd(string xmlString)
    {
        var settings = new XmlReaderSettings();
        
        // Load XSD from embedded resource
        var assembly = Assembly.GetExecutingAssembly();
        
        var xsdResourceName = "EDIFACT.TrueCommerce.Schemas.Order.TrueCommerce_Order_v1.0.xsd";
        using (var xsdStream = assembly.GetManifestResourceStream(xsdResourceName))
        {
            if (xsdStream == null)
            {
                throw new FileNotFoundException($"Resource '{xsdResourceName}' not found.");
            }
            using (var xsdReader = XmlReader.Create(xsdStream))
            {
                settings.Schemas.Add(null, xsdReader);
            }
        }

        xsdResourceName = "EDIFACT.TrueCommerce.Schemas.Order.truecommerce_components_v1.0.xsd";
        using (var xsdStream = assembly.GetManifestResourceStream(xsdResourceName))
        {
            if (xsdStream == null)
            {
                throw new FileNotFoundException($"Resource '{xsdResourceName}' not found.");
            }
            using (var xsdReader = XmlReader.Create(xsdStream))
            {
                settings.Schemas.Add(null, xsdReader);
            }
        }
        
        settings.ValidationType = ValidationType.Schema;
        settings.ValidationEventHandler += XmlUtilities.XsdValidationEventHandler;

        using var stringReader = new StringReader(xmlString);
        using var reader = XmlReader.Create(stringReader, settings);
        while (reader.Read()) { }
    }
    
    public async Task<IEnumerable<Auftragskopf>> ConvertToOrder(TrueCommerceOrder order)
    {
        var listOfOrders = new List<Auftragskopf>();
        
        foreach (var document in order.Document)
        {
            if(document is null)
                continue;
            
            await CheckIfDataAreCorrect(document);
            //TODO if cancelleation handling
            // possible values NEW, CHANGE, CANCELLATION,REDUCTION

            switch (document.OrderHeader.OrderCode )
            {
                case "NEW":
                        var newAuftrag = await NewAuftrag(document);
                        listOfOrders.Add(newAuftrag);
                    break;
                case "CHANGE":
                     listOfOrders.Add(await ChangeAuftrag(document));
                    break;
            }
            
        }

        return listOfOrders;
    }

    private async Task<Auftragskopf> NewAuftrag(OrderDoc document)
    {
        await CheckIfOrderWithBestellNumberExist(document);

        CreationParamForAuftragskopf creationParam = new()
        {
            Mode = CreationParamForAuftragskopf.AuftragskopfMode.Auftrag,
            Bestellnr = document.OrderHeader.CustOrder,
            //Auftragsdatum = document.DocHeader.DocDate.Date,
            Auftragsdatum = DateTime.Now,
            LvonDatum = document.OrderHeader.Delivery.EarliestDel.Date.Add(new TimeSpan(document.OrderHeader.Delivery.EarliestDel.Time.Hour,document.OrderHeader.Delivery.EarliestDel.Time.Minute,document.OrderHeader.Delivery.EarliestDel.Time.Second)),
            LbisDatum = document.OrderHeader.Delivery.LatestDel.Date.Add(new TimeSpan(document.OrderHeader.Delivery.LatestDel.Time.Hour,document.OrderHeader.Delivery.LatestDel.Time.Minute,document.OrderHeader.Delivery.LatestDel.Time.Second)),
            Eldatum = document.OrderHeader.Delivery.LatestDel.Date.Add(new TimeSpan(document.OrderHeader.Delivery.LatestDel.Time.Hour,document.OrderHeader.Delivery.LatestDel.Time.Minute,document.OrderHeader.Delivery.LatestDel.Time.Second)),
            InvoiceHolder = await FindAndGetCustomerAsync(document.DocHeader.CustAddr.EAN13),
            WarenEmpfaenger = await FindAndGetCustomerAsync(document.OrderHeader.Delivery.DeliverTo.EAN13)
            //KontraktNr = document.OrderHeader.OrigCustOrder,erst nicht wichtig -> Vllt Kontragt
        };

        var newAuftrag = Auftragskopf.Create(creationParam);
        newAuftrag.Auftragspositions = await CreateAuftragsPositions(document, newAuftrag.Eldatum);
        newAuftrag.ErstArtikel = newAuftrag.Auftragspositions.First().ArtikelNummer;
        
        return newAuftrag;
    }

    private async Task<List<Auftragspo>> CreateAuftragsPositions(OrderDoc document, DateTime? baseDateTimeForMhd)
    {
        var gridPos = 1;
        List<Auftragspo> auftragspositions = [];
        foreach (var orderLine in document.OrderLine)
        {
            var searchParam = new ArticleSearchParam()
            {
                Ean = orderLine.Item.SuppItem.EAN13
            };
            
            var artikelsFromSearch = (await articleService.Get(false, searchParam)).ToList();

            if (artikelsFromSearch is null || artikelsFromSearch.Count == 0)
            {
                throw new ArticleNotFoundException(orderLine.LineNo, orderLine.LineCode, orderLine.Item.SuppItem.EAN13);
            }

            var newAuftragPos = Auftragspo.CreatePo(artikelsFromSearch.First(), gridPos,  baseDateTimeForMhd,0,orderLine.UnitOfOrder.Unit,orderLine.OrderQty.Unit,orderLine.CostPrice, orderLine.LineAmount);

            auftragspositions.Add(newAuftragPos);
            
            var tePos = Auftragspo.CreateTeList(artikelsFromSearch.First(), gridPos);
            gridPos += tePos.Count;
            gridPos++;
            auftragspositions.AddRange(tePos);
        }

        return auftragspositions;
    }

    private async Task<Auftragskopf> ChangeAuftrag(OrderDoc document)
    {
        var existingAuftragskopf = await orderService.GetOverBestellNumber(true, document.OrderHeader.CustOrder);
        if(existingAuftragskopf is null)
        {
            throw new Exception($"Order with Bestellnr {document.OrderHeader.CustOrder} not found.");
        }
        
        var newAuftragspos = await CreateAuftragsPositions(document, existingAuftragskopf.Eldatum);
        
        existingAuftragskopf.Auftragspositions = [];
        await orderService.Update("AutomaticFromEdifactImport", existingAuftragskopf);
        existingAuftragskopf.Auftragspositions = newAuftragspos; 
        existingAuftragskopf.ErstArtikel = existingAuftragskopf.Auftragspositions.First().ArtikelNummer;
        
        return existingAuftragskopf;
    }

    private async Task CheckIfDataAreCorrect(OrderDoc document)
    {
        if (document.DocHeader is null)
        {
            throw new Exception("DocHeader is missing.");
        }

        if (document.DocHeader.CustAddr is null)
        {
            throw new Exception("CustAddr is missing.");
        }

        if (document.DocHeader.CustAddr.EAN13 is null)
        {
            throw new Exception("EAN13 is missing.");
        }
        
        if (document.DocHeader.SuppAddr is null)
        {
            throw new Exception("SuppAdr is missing.");
        }
        
        if (document.DocHeader.SuppAddr.EAN13 is null)
        {
            throw new Exception("EAN13 is missing.");
        }
        
        var mandant = await selectedMandant.GetCompanyMasterData();
        if(document.DocHeader.SuppAddr.EAN13 != mandant?.IlnNummer )
        {   
            throw new Exception($"Mismatch in EAN13 values. Document EAN13: {document.DocHeader.SuppAddr.EAN13}, Mandant EAN13: {mandant?.IlnNummer}");
        }
    }

    private async Task CheckIfOrderWithBestellNumberExist(OrderDoc document)
    {
        var existingAuftragskopf = await orderService.GetOverBestellNumber(false, document.OrderHeader.CustOrder);
        if(existingAuftragskopf is not null)
        {
            throw new Exception($"Order with Bestellnr {document.OrderHeader.CustOrder} already exists.");
        }
    }

    private async Task<AuftragCustomerOrSupplierData> FindAndGetCustomerAsync(string kdleh)
    {
        var searchParam = new CustomerSearchParam()
        {
            Kdleh = kdleh
        };
        
        var customers = await kundenService.GetCustomer(false,searchParam);
        var enumerable = customers.ToList();
        if (customers is null || enumerable.Count == 0)
        {
            throw new Exception($"Customer with Kdleh {kdleh} not found.");
        }
        var customer = (AuftragCustomerOrSupplierData) enumerable.First();
        
        if(!string.IsNullOrWhiteSpace(customer.Country))
            customer.CountryId = await countryService.GetCountryIdByName(customer.Country);
        if (!string.IsNullOrWhiteSpace(customer.Bundesland))
        {
            var bundLand = await countryService.GetBundesLaendAsync(customer.Bundesland);
            if(short.TryParse(bundLand.BlNr, out short result))
                customer.BundeslandNumber = result;
            else
                customer.BundeslandNumber = 0;
        }
        
        return customer;
    }
}