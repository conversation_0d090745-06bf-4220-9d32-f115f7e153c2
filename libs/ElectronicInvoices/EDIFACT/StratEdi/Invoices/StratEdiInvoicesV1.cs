using EDIFACT.StratEdi.Models;
using WingCore.application.DataTransferObjects.Invoices;
using WingCore.domain.Models;

namespace EDIFACT.StratEdi.Invoices;

public class StratEdiInvoicesV1 : BaseInvoiceToEdifactV1
{
    public InterchangeHeaderV1? InterchangeHeader { get; set; }
    public TransactionsHeaderV1? TransactionsHeader { get; set; }
    public TransactionV1? Transaction { get; set; }
    
    public IEnumerable<CustomerData> CustomerDatas { get; set; } = [];
    public CurrencyVatAndPaymentTermsV1? CurrencyVatAndPaymentTerms { get; set; }
    public IEnumerable<InvoicePositionV1> InvoicePositions { get; set; } = [];
    public InvoiceSummaryV1? InvoiceSummary { get; set; }
    //public InvoiceListV1? InvoiceList { get; set; }
    public IEnumerable<VatSummaryV1> VatSummaries { get; set; } = [];
    public override string GetFileName(BaseInvoiceOverviewEntryDto invoiceOverview)
    {
        return $"invoic{invoiceOverview.InvoiceNumber}_{DateTime.UtcNow:MMddyyyyhhmmssff}_FIX.text";
    }

    public override MemoryStream CreateInvoiceToMemStream(BaseInvoiceOverviewEntryDto invoiceOverview,
                                                          Auftragskopf? auftragskopf,
                                                          Dictionary<long,int?> dicArticleNumber2ArtRefNr)
    {
        var asString = invoiceOverview switch
        {
            OutgoingInvoiceOverviewDto outgoingInvoiceOverviewDto => outgoingInvoiceOverviewDto.Convert(auftragskopf,dicArticleNumber2ArtRefNr).WriteFieldsToString(),
            _ => throw new NotImplementedException()
        };

        
        MemoryStream outStream = new();
        StreamWriter writer = new(outStream);
        writer.Write(asString);
        writer.Flush();

        outStream.Position = 0;
        return outStream;
    }
}  
