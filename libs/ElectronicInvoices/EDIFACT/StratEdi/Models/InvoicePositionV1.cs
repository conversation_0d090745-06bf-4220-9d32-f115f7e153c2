using CsvHelper.Configuration.Attributes;
using FixedWidthParserWriter;

namespace EDIFACT.StratEdi.Models;

[Delimiter(";")]
[CultureInfo("de-DE")]
public class InvoicePositionV1
{
    [FixedWidthLineField(Start = 1, Length = 3, PadSide = PadSide.Left)]
    public string Satzart => "500";

    [FixedWidthLineField(Start = 4, Length = 6, PadSide = PadSide.Left)]
    public int? Positionsnummer { get; set; }

    [FixedWidthLineField(Start = 10, Length = 3, PadSide = PadSide.Left)]
    public string ArtDerAktivitaetCodiert { get; set; } = string.Empty;

    [FixedWidthLineField(Start = 13, Length = 13, PadSide = PadSide.Left)]
    public string GtinDerFakturiereinheit { get; set; } = string.Empty;

    [FixedWidthLineField(Start = 26, Length = 35, PadSide = PadSide.Left)]
    public string ArtikelnummerDesLieferanten { get; set; } = string.Empty;

    [FixedWidthLineField(Start = 61, Length = 35, PadSide = PadSide.Left)]
    public string ArtikelnummerDesKunden { get; set; } = string.Empty;

    [FixedWidthLineField(Start = 96, Length = 35, PadSide = PadSide.Left)]
    public string ProduktWarengruppe { get; set; } = string.Empty;

    [FixedWidthLineField(Start = 131, Length = 35, PadSide = PadSide.Left)]
    public string Artikelbezeichnung1 { get; set; } = string.Empty;

    [FixedWidthLineField(Start = 166, Length = 35, PadSide = PadSide.Left)]
    public string Artikelbezeichnung2 { get; set; } = string.Empty;

    [FixedWidthLineField(Start = 201, Length = 35, PadSide = PadSide.Left)]
    public string Artikelgroesse { get; set; } = string.Empty;

    [FixedWidthLineField(Start = 236, Length = 35, PadSide = PadSide.Left)]
    public string Artikelfarbe { get; set; } = string.Empty;

    [FixedWidthLineField(Start = 271, Length = 15, Format = "F3", PadSide = PadSide.Left)]
    public decimal? BerechneteMenge { get; set; }

    [FixedWidthLineField(Start = 286, Length = 15, Format = "F3", PadSide = PadSide.Left)]
    public decimal? MengeOhneBerechnung { get; set; }

    [FixedWidthLineField(Start = 301, Length = 3, PadSide = PadSide.Left)]
    public string WaehrungskennzeichenCodiert { get; set; } = "EUR";

    [FixedWidthLineField(Start = 304, Length = 5, Format = "F2", PadSide = PadSide.Left)]
    public decimal? MwStSatzDesArtikels { get; set; }

    [FixedWidthLineField(Start = 309, Length = 15, Format = "F4", PadSide = PadSide.Left)]
    public decimal? NettoStueckpreis { get; set; }
    
    // existieren Artikelzu-/abschläge, ist das Feld Brutto-Stückpreis zu füllen
    [FixedWidthLineField(Start = 324, Length = 15, Format = "F4", PadSide = PadSide.Left)]
    public decimal? BruttoStueckpreis { get; set; }

    [FixedWidthLineField(Start = 339, Length = 15, Format = "F4", PadSide = PadSide.Left)]
    public decimal? VerkaufspreisJeEinheit { get; set; }

    [FixedWidthLineField(Start = 354, Length = 15, Format = "F4", PadSide = PadSide.Left)]
    public decimal? AktionspreisJeEinheit { get; set; }

    [FixedWidthLineField(Start = 369, Length = 15, Format = "F4", PadSide = PadSide.Left)]
    public decimal? KatalogListenpreisJeEinheit { get; set; }

    [FixedWidthLineField(Start = 384, Length = 9, Format = "F2", PadSide = PadSide.Left)]
    public decimal? Preisbasis { get; set; }

    // "PCE" = Stück; "KGM" = Kilogramm
    [FixedWidthLineField(Start = 393, Length = 3, PadSide = PadSide.Left)]
    public string Mengeneinheit { get; set; } = string.Empty;

    // Nettowarenwert = Menge x Bruttopreis ./. Artikelrabatte bzw. Menge x Nettopreis (Rabatte sind im Preis eingerechnet)
    // Bei Gutschriftspositionen innerhalb einer Rechnung ist der Nettowarenwert negativ einzustellen.
    [FixedWidthLineField(Start = 396, Length = 18, Format = "F3", PadSide = PadSide.Left)]
    public decimal NettoWarenwert { get; set; }

    // Bruttowarenwert = Menge x Bruttopreis ohne MWSt., vor Abzug der Artikelrabatte
    [FixedWidthLineField(Start = 414, Length = 18, Format = "F3", PadSide = PadSide.Left)]
    public decimal? BruttoWarenwert { get; set; }

    // Summe aller Zu- und Abschläge aus Satzart(en) 513 mit vorzeichengerechter Darstellung
    [FixedWidthLineField(Start = 432, Length = 18, Format = "F3", PadSide = PadSide.Left)]
    public decimal? SummeArtikelzuUndAbschlaege { get; set; }

    [FixedWidthLineField(Start = 450, Length = 7, PadSide = PadSide.Left)]
    public string ArtDerVerpackungCodiert { get; set; } = string.Empty;

    [FixedWidthLineField(Start = 457, Length = 7, PadSide = PadSide.Left)]
    public int? AnzahlVerpackungen { get; set; }

    //"D" / "G" / "V" / "S"
    // handelt es sich bei der Fakturiereinheit um ein Gebinde, ist hier ein "G" einzustellen;
    // handelt es sich bei der Fakturiereinheit um ein Display/Sortiment, ist hier ein "D" einzustellen;
    // wird eine Verbrauchereinheit fakturiert, ist hier ein "V" einzustellen.
    // „P“ = Pfandartikel (Mehrweg); „E“ = Einweg
    // Sonderfall „Stückberechnung im Gebinde/Sortiment“:
    // 1. Satzart 500 mit 500-28 = „D“ oder „G“ / 500-12 = Anzahl gelieferter Display-/Sortimentseinheiten / 500-23 = 0
    // 2. - n. Satzart 500 mit 500-28 = „S“ / 500-12 = Anzahl der in allen Displays enthaltenen Einzelartikel
    // Bei Stückberechnung darf keine Satzart 601 gebildet werden
    [FixedWidthLineField(Start = 464, Length = 1, PadSide = PadSide.Left)]
    public string KennzeichenGebindeDisplayVerbrauchereinheit { get; set; } = string.Empty;

    [FixedWidthLineField(Start = 465, Length = 15, Format = "F2", PadSide = PadSide.Left)]
    public decimal? BestellteGelieferteMenge { get; set; }

    [FixedWidthLineField(Start = 480, Length = 15, PadSide = PadSide.Left)]
    public string NummerDerAktionsvariante { get; set; } = string.Empty;

    //"Y" / "N"
    // Mußfeld (Inhalt: "Y"), wenn die Artikelposition keiner MwSt. unterliegt
    [FixedWidthLineField(Start = 495, Length = 3, PadSide = PadSide.Left)]
    public string KennzeichenSteuerbefreit { get; set; } = "N";

    //"PFAND" / "THM"
    // „THM“ = Kennzeichen für eine Artikelposition, mit der Transporthilfsmittel (THM) abgerechnet werden.
    [FixedWidthLineField(Start = 498, Length = 10, PadSide = PadSide.Left)]
    public string SonstigesKennzeichen { get; set; } = "THM";

    //"Y" / "N"
    [FixedWidthLineField(Start = 508, Length = 1, PadSide = PadSide.Left)]
    public string KennzeichenNachlieferungErfolgt { get; set; } = "N";

    [FixedWidthLineField(Start = 509, Length = 3, PadSide = PadSide.Left)]
    public string GrundDerAbweichung { get; set; } = string.Empty;

    // falls das Artikelident eine 14stellige EAN/UPC ist, wird diese hier abgelegt. Das Feld 500-04 bleibt dann leer.
    [FixedWidthLineField(Start = 512, Length = 18, Format = "F3", PadSide = PadSide.Left)]
    public decimal? MwStBetragDesArtikels { get; set; }

    [FixedWidthLineField(Start = 530, Length = 14, PadSide = PadSide.Left)]
    public string EanUpcDerFakturiereinheit { get; set; } = string.Empty;

    //"Y" / "N"
    [FixedWidthLineField(Start = 544, Length = 1, PadSide = PadSide.Left)]
    public string KennzeichenKonditionssperre { get; set; } = "N";

    // "HS" = Zolltarifnummer
    [FixedWidthLineField(Start = 545, Length = 3, PadSide = PadSide.Left)]
    public string KennzeichenReferenznummer { get; set; } = string.Empty;

    [FixedWidthLineField(Start = 548, Length = 35, PadSide = PadSide.Left)]
    public string Referenznummer { get; set; } = string.Empty;

    [FixedWidthLineField(Start = 583, Length = 3, PadSide = PadSide.Left)]
    public string Ursprungsland { get; set; } = string.Empty;

    [FixedWidthLineField(Start = 586, Length = 3, PadSide = PadSide.Left)]
    public string CodeFuerMehrwertsteuer { get; set; } = string.Empty;

    [FixedWidthLineField(Start = 589, Length = 12, PadSide = PadSide.Left)]
    public string Filler { get; set; } = string.Empty;
}