using System.ComponentModel.DataAnnotations;
using CsvHelper.Configuration.Attributes;
using FixedWidthParserWriter;

namespace EDIFACT.StratEdi.Models;

[Delimiter(";")]
[CultureInfo("de-DE")]
public class CurrencyVatAndPaymentTermsV1
{
    [FixedWidthLineField(Start = 1, Length = 3, PadSide = PadSide.Left)]
    [MaxLength(3)]
    public string Satzart => "120";
    
    //EUR" = EURO
    [FixedWidthLineField(Start = 4, Length = 3, PadSide = PadSide.Left)]
    [MaxLength(3)]
    public string WaehrungCodiert { get; set; } = "EUR";

    [FixedWidthLineField(Start = 7, Length = 5, PadSide = PadSide.Left)]
    [MaxLength(5)]
    public decimal MwStSatz { get; set; }

    [FixedWidthLineField(Start = 12, Length = 3, PadSide = PadSide.Left)]
    [MaxLength(3)]
    public int? ZahlungszielInTagen { get; set; }

    [FixedWidthLineField(Start = 15, Length = 8, Format = "yyyyMMdd", PadSide = PadSide.Left)]
    [Format("yyyyMMdd")]
    public DateTime? Valutadatum { get; set; }

    [FixedWidthLineField(Start = 23, Length = 8, Format = "yyyyMMdd", PadSide = PadSide.Left)]
    [Format("yyyyMMdd")]
    public DateTime? NettoFaelligkeitsdatum { get; set; }

    [FixedWidthLineField(Start = 31, Length = 3, PadSide = PadSide.Left)]
    [MaxLength(3)]
    public string LieferbedingungenIncoterms { get; set; } = string.Empty;

    [FixedWidthLineField(Start = 34, Length = 3, PadSide = PadSide.Left)]
    [MaxLength(3)]
    public int? Valutatage { get; set; }

    [FixedWidthLineField(Start = 37, Length = 35, PadSide = PadSide.Left)]
    [MaxLength(35)]
    public string Frei { get; set; } = string.Empty;

    [FixedWidthLineField(Start = 72, Length = 3, PadSide = PadSide.Left)]
    [MaxLength(3)]
    public string KennzeichenKonditionssperre { get; set; } = "15";

    // Mußfeld (Inhalt: "Y"), wenn der gesamte Beleg keiner MwSt. unterliegt
    [FixedWidthLineField(Start = 75, Length = 3, PadSide = PadSide.Left)]
    [MaxLength(3)]
    public string KennzeichenSteuerbefreit { get; set; } = "N";

    [FixedWidthLineField(Start = 78, Length = 35, PadSide = PadSide.Left)]
    [MaxLength(35)]
    public string LieferbedingungText { get; set; } = string.Empty;

    [FixedWidthLineField(Start = 113, Length = 35, PadSide = PadSide.Left)]
    [MaxLength(35)]
    public string ArtDesTransportmittelsCodiert { get; set; } = string.Empty;

    [FixedWidthLineField(Start = 148, Length = 3, PadSide = PadSide.Left)]
    [MaxLength(3)]
    public string ZahlungsartFuerTransportkostenCodiert { get; set; } = string.Empty;

    [FixedWidthLineField(Start = 151, Length = 35, PadSide = PadSide.Left)]
    [MaxLength(35)]
    public string ZahlungsbedingungenText { get; set; } = string.Empty;

    [FixedWidthLineField(Start = 186, Length = 17, PadSide = PadSide.Left)]
    [MaxLength(17)]
    public string Befoerderungsnummer { get; set; } = string.Empty;

    [FixedWidthLineField(Start = 203, Length = 35, PadSide = PadSide.Left)]
    [MaxLength(35)]
    public string Trailernummer { get; set; } = string.Empty;

    [FixedWidthLineField(Start = 238, Length = 8, Format = "yyyyMMdd", PadSide = PadSide.Left)]
    [Format("yyyyMMdd")]
    public DateTime? Wechselkursdatum { get; set; }

    [FixedWidthLineField(Start = 246, Length = 355, PadSide = PadSide.Left)]
    [MaxLength(355)]
    public string Filler { get; set; } = string.Empty;
}