using CsvHelper.Configuration.Attributes;
using FixedWidthParserWriter;

namespace EDIFACT.StratEdi.Models;

[Delimiter(";")]
[CultureInfo("de-DE")]
public class InvoiceSummaryV1
{
    [FixedWidthLineField(Start = 1, Length = 3, PadSide = PadSide.Left)]
    public string Satzart => "900";

    [FixedWidthLineField(Start = 4, Length = 18, Format = "F2", PadSide = PadSide.Left)]
    public decimal Rechnungsendbetrag { get; set; }

    [FixedWidthLineField(Start = 22, Length = 18, Format = "F2", PadSide = PadSide.Left)]
    public decimal GesamterMwStBetragDesBeleges { get; set; }

    [FixedWidthLineField(Start = 40, Length = 18, Format = "F2", PadSide = PadSide.Left)]
    public decimal NettowarenwertGesamt { get; set; }

    [FixedWidthLineField(Start = 58, Length = 18, Format = "F2", PadSide = PadSide.Left)]
    public decimal? SteuerpflichtigerBetragGesamt { get; set; }

    [FixedWidthLineField(Start = 76, Length = 18, Format = "F2", PadSide = PadSide.Left)]
    public decimal? SkontofaehigerBetragGesamt { get; set; }

    [FixedWidthLineField(Start = 94, Length = 18, Format = "F2", PadSide = PadSide.Left)]
    public decimal? BelegzuAbschlaegeGesamt { get; set; }

    [FixedWidthLineField(Start = 112, Length = 18, Format = "F2", PadSide = PadSide.Left)]
    public decimal? GesamtVerkaufswert { get; set; }

    [FixedWidthLineField(Start = 130, Length = 18, Format = "F2", PadSide = PadSide.Left)]
    public decimal? SummeNebenkosten { get; set; }

    [FixedWidthLineField(Start = 148, Length = 18, Format = "F2", PadSide = PadSide.Left)]
    public decimal? NettobetragWare { get; set; }

    [FixedWidthLineField(Start = 166, Length = 18, Format = "F2", PadSide = PadSide.Left)]
    public decimal? NettobetragGebinde { get; set; }

    [FixedWidthLineField(Start = 184, Length = 18, Format = "F2", PadSide = PadSide.Left)]
    public decimal? SummeBelegabschlaege { get; set; }

    [FixedWidthLineField(Start = 202, Length = 18, Format = "F2", PadSide = PadSide.Left)]
    public decimal? SummeBelegzuschlaege { get; set; }

    [FixedWidthLineField(Start = 220, Length = 381, PadSide = PadSide.Left)]
    public string Filler { get; set; } = string.Empty;
}