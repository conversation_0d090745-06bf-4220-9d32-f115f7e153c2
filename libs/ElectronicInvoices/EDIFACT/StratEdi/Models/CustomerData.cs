using CsvHelper.Configuration.Attributes;
using FixedWidthParserWriter;

namespace EDIFACT.StratEdi.Models;

[Delimiter(";")]
[CultureInfo("de-DE")]
public class CustomerData
{
    public static string Customer => "BY";
    public static string InvoiceHolder => "IV";
    public static string InvoiceSupplier => "II";
    public static string Supplier => "SU";
    public static string ProductRecipient => "DP";
    
    public CustomerData(string mode)
    {
        ArtDesPartnersCodiert = mode;
    }

    public CustomerData()
    {
    }
    
    [FixedWidthLineField(Start = 1, Length = 3, PadSide = PadSide.Left)]
    public string Satzart => "119";

    //BY" = Kunde, "SU" = Lieferant, "IV" = Rechnungsempfänger, "DP" = Warenempfänger
    // "II" = Rechnungssteller (nur wenn abweichend vom Lieferant)
    [FixedWidthLineField(Start = 4, Length = 3, PadSide = PadSide.Left)]
    public string ArtDesPartnersCodiert { get; set; } = string.Empty;

    [FixedWidthLineField(Start = 7, Length = 13, PadSide = PadSide.Left)]
    public string GlnDesPartners { get; set; } = string.Empty;

    [FixedWidthLineField(Start = 20, Length = 35, PadSide = PadSide.Left)]
    public string Name1DesPartners { get; set; } = string.Empty;

    [FixedWidthLineField(Start = 55, Length = 35, PadSide = PadSide.Left)]
    public string Name2DesPartners { get; set; } = string.Empty;

    [FixedWidthLineField(Start = 90, Length = 35, PadSide = PadSide.Left)]
    public string Name3DesPartners { get; set; } = string.Empty;

    [FixedWidthLineField(Start = 125, Length = 35, PadSide = PadSide.Left)]
    public string Strasse1DesPartners { get; set; } = string.Empty;

    [FixedWidthLineField(Start = 160, Length = 35, PadSide = PadSide.Left)]
    public string Strasse2DesPartners { get; set; } = string.Empty;

    [FixedWidthLineField(Start = 195, Length = 35, PadSide = PadSide.Left)]
    public string Strasse3DesPartners { get; set; } = string.Empty;

    [FixedWidthLineField(Start = 230, Length = 9, PadSide = PadSide.Left)]
    public string Postleitzahl { get; set; } = string.Empty;

    [FixedWidthLineField(Start = 239, Length = 35, PadSide = PadSide.Left)]
    public string Ort { get; set; } = string.Empty;

    [FixedWidthLineField(Start = 274, Length = 3, PadSide = PadSide.Left)]
    public string LandCodiert { get; set; } = string.Empty;

    [FixedWidthLineField(Start = 277, Length = 35, PadSide = PadSide.Left)]
    public string InterneIDDesPartners { get; set; } = string.Empty;

    [FixedWidthLineField(Start = 312, Length = 35, PadSide = PadSide.Left)]
    public string VomPartnerVergebeneID { get; set; } = string.Empty;

    [FixedWidthLineField(Start = 347, Length = 35, PadSide = PadSide.Left)]
    public string UmsatzsteuerIDDesPartners { get; set; } = string.Empty;

    [FixedWidthLineField(Start = 382, Length = 35, PadSide = PadSide.Left)]
    public string AbteilungBeimPartner { get; set; } = string.Empty;

    //Muss für Lieferant/Zahlungsleistende
    [FixedWidthLineField(Start = 417, Length = 35, PadSide = PadSide.Left)]
    public string SteuernummerGemStVBG { get; set; } = string.Empty;

    [FixedWidthLineField(Start = 452, Length = 20, PadSide = PadSide.Left)]
    public string Ansprechpartner { get; set; } = string.Empty;

    [FixedWidthLineField(Start = 472, Length = 20, PadSide = PadSide.Left)]
    public string Telefonnummer { get; set; } = string.Empty;

    [FixedWidthLineField(Start = 492, Length = 20, PadSide = PadSide.Left)]
    public string Telefaxnummer { get; set; } = string.Empty;

    //"XA" = WEEE-Reg.-Nr. und Muss für Lieferant/Zahlungsleistender, wenn WEEE-Reg.-Nr. existiert
    [FixedWidthLineField(Start = 512, Length = 3, PadSide = PadSide.Left)]
    public string KennzeichenReferenznummer { get; set; } = string.Empty;

    //Muss für Lieferant/Zahlungsleistender, wenn WEEE-Reg.-Nr. existiert
    [FixedWidthLineField(Start = 515, Length = 35, PadSide = PadSide.Left)]
    public string Referenznummer { get; set; } = string.Empty;

    [FixedWidthLineField(Start = 550, Length = 51, PadSide = PadSide.Left)]
    public string Filler { get; set; } = string.Empty;
}