using CsvHelper.Configuration.Attributes;
using FixedWidthParserWriter;

namespace EDIFACT.StratEdi.Models;

[Delimiter(";")]
[CultureInfo("de-DE")]
public class VatSummaryV1
{
    [FixedWidthLineField(Start = 1, Length = 3,  PadSide = PadSide.Left)]
    public string Satzart => "901";

    [FixedWidthLineField(Start = 4, Length = 18, Format = "F2",  PadSide = PadSide.Left)]
    public decimal Bruttobetrag { get; set; }

    [FixedWidthLineField(Start = 22, Length = 18, Format = "F2",  PadSide = PadSide.Left)]
    public decimal Mehrwertsteuerbetrag { get; set; }

    [FixedWidthLineField(Start = 40, Length = 18, Format = "F2",  PadSide = PadSide.Left)]
    public decimal Nettowarenwert { get; set; }

    [FixedWidthLineField(Start = 58, Length = 18, Format = "F2",  PadSide = PadSide.Left)]
    public decimal? SteuerpflichtigerBetrag { get; set; }

    [FixedWidthLineField(Start = 76, Length = 18, Format = "F2",  PadSide = PadSide.Left)]
    public decimal? SkontofaehigerBetrag { get; set; }

    [FixedWidthLineField(Start = 94, Length = 18, Format = "F2",  PadSide = PadSide.Left)]
    public decimal? SummeDerBelegzuAbschlaege { get; set; }

    [FixedWidthLineField(Start = 112, Length = 5, Format = "F2",  PadSide = PadSide.Left)]
    public decimal MwStSatz { get; set; }

    [FixedWidthLineField(Start = 117, Length = 10,  PadSide = PadSide.Left)]
    public string SonstigesKennzeichen { get; set; } = string.Empty;

    [FixedWidthLineField(Start = 127, Length = 3,  PadSide = PadSide.Left)]
    public string CodeFuerMehrwertsteuer { get; set; } = string.Empty;

    [FixedWidthLineField(Start = 130, Length = 20,  PadSide = PadSide.Left)]
    public string CodeFuerBefreiungsgrund { get; set; } = string.Empty;

    [FixedWidthLineField(Start = 150, Length = 70,  PadSide = PadSide.Left)]
    public string Befreiungsgrund { get; set; } = string.Empty;

    [FixedWidthLineField(Start = 220, Length = 381,  PadSide = PadSide.Left)]
    public string Filler { get; set; } = string.Empty;
}