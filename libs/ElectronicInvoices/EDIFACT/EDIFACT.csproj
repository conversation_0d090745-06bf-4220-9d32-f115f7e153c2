<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
<!--        <MSBuildDisableGetCopyToPublishDirectoryItemsOptimization>true</MSBuildDisableGetCopyToPublishDirectoryItemsOptimization>-->
    </PropertyGroup>

    <ItemGroup>
      <ProjectReference Include="..\..\..\Modules\WingCore\WingCore.application\WingCore.application.csproj" />
    </ItemGroup>

<!--    <ItemGroup>-->
<!--      <None Remove="TrueCommerce\Schemas\Order\TrueCommerce_Order_v1.0.xsd" />-->
<!--      <EmbeddedResource Include="TrueCommerce\Schemas\Order\TrueCommerce_Order_v1.0.xsd" PublishFolderType="RootDirectory">-->
<!--          <CopyToOutputDirectory>Always</CopyToOutputDirectory>-->
<!--      </EmbeddedResource>-->
<!--      <None Remove="TrueCommerce\Schemas\Order\truecommerce_components_v1.0.xsd" />-->
<!--      <EmbeddedResource Include="TrueCommerce\Schemas\Order\truecommerce_components_v1.0.xsd" PublishFolderType="RootDirectory">-->
<!--          <CopyToOutputDirectory>Always</CopyToOutputDirectory>-->
<!--      </EmbeddedResource>-->
<!--    </ItemGroup>-->

    <ItemGroup>
      <PackageReference Include="CsvHelper" Version="33.1.0" />
      <PackageReference Include="FixedWidthParserWriter" Version="1.2.0" />
    </ItemGroup>
</Project>
