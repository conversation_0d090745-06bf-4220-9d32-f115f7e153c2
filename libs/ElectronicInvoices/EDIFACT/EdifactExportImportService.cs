using System.IO.Compression;
using EDIFACT.StratEdi.Invoices;
using EDIFACT.TrueCommerce.Invoices.V1;
using WingCore.domain.Models;
using WingCore.application.Contract;
using WingCore.application.Contract.IModels.Helper;
using WingCore.application.Contract.Services;
using WingCore.application.DataTransferObjects.Invoices;
using WingCore.domain.Common.Configurations;
using WingCore.domain.Common.Flags;

namespace EDIFACT;

public class EdifactExportImportService(
    ISelectedMandant selectedMandant,
    IOutgoingInvoiceHeaderService outgoingInvoiceHeaderService,
    IIncomingInvoiceHeaderService incomingInvoiceHeaderHeaderService,
    IGrainSettlementInvoiceHeaderService grainSettlementInvoiceHeaderService,
    IRapeSettlementInvoiceHeaderService rapeSettlementInvoiceHeaderService,
    ICountryService countryService,
    
    IConvertEdifactToOrderV1 convertEdifactToOrderV1,
    IOrderService orderService,
    IArticleService articleService,
    ILoggerManager logger)
    : IEdifactExportImportService
{

    private static BaseInvoiceToEdifactV1 ExportObject(IEdifactExportImportService.ExportMode mode)
    {
        return mode switch
        {
            IEdifactExportImportService.ExportMode.TrueCommerce => new OutgoingInvoiceToEdifactV1(),
            IEdifactExportImportService.ExportMode.StratEdi => new StratEdiInvoicesV1(),
            _ => throw new NotImplementedException()
        };
    }

    private async Task<MemoryStream> BaseExportInvoicesAsXmlAsync(
        BaseInvoiceOverviewEntryDto baseInvoiceOverviewEntryDto, IEdifactExportImportService.ExportMode mode)
    {
        var exportObject = ExportObject(mode);
        FileName = exportObject.GetFileName(baseInvoiceOverviewEntryDto);
        
        if (!string.IsNullOrWhiteSpace(baseInvoiceOverviewEntryDto.InvoiceSupplier?.Country))
            baseInvoiceOverviewEntryDto.InvoiceSupplier.CountryId =
                await countryService.GetCountryIdByName(baseInvoiceOverviewEntryDto.InvoiceSupplier.Country);
        if (!string.IsNullOrWhiteSpace(baseInvoiceOverviewEntryDto.InvoiceHolder?.Country))
            baseInvoiceOverviewEntryDto.InvoiceHolder.CountryId =
                await countryService.GetCountryIdByName(baseInvoiceOverviewEntryDto.InvoiceHolder.Country);

        var oderHead = await orderService.GetOverInvoiceNumberForEdifact(baseInvoiceOverviewEntryDto.InvoiceNumber);
        var dicArticleNumber2ArtRefNr = await articleService.GetArticleRefNumber(
                                                            baseInvoiceOverviewEntryDto.ListAllPostings.Select(p=>p.Number).ToList());
        return exportObject.CreateInvoiceToMemStream(baseInvoiceOverviewEntryDto, oderHead,dicArticleNumber2ArtRefNr);
    }

    public long NumberOfExportInvoices { get; set; }
    public string FileName { get; set; } = string.Empty;

    public async Task<MemoryStream> ExportInvoicesAMemStreamAsync(OutgoingInvoiceOverviewDto outgoingInvoiceOverviewDto,
        IEdifactExportImportService.ExportMode mode)
    {
        var invoice = await outgoingInvoiceHeaderService
            .GetInvoiceWithCompleteDataAsync(false, outgoingInvoiceOverviewDto.InvoiceNumber);
        if (invoice is null)
            return new MemoryStream();

        var mandant = await selectedMandant.GetSelectedMandantAsInvoiceHolder();
        if (mandant is not null)
        {
            invoice.InvoiceSupplier = (InvoiceSupplierOrHolderDto)mandant;
            invoice.InvoiceSupplierCurrent = (InvoiceSupplierOrHolderDto)mandant;
        }
        
        if (invoice.InvoiceSupplierCurrent is null)
            throw new Exception("No supplier found for invoice");
        
        return await BaseExportInvoicesAsXmlAsync(invoice, mode);
    }

    public async Task<MemoryStream> ExportInvoicesAMemStreamAsync(IncomingInvoiceOverviewDto incomingInvoiceOverviewDto,
        IEdifactExportImportService.ExportMode mode)
    {
        var invoice = await incomingInvoiceHeaderHeaderService
            .GetInvoiceWithCompleteDataAsync(false, incomingInvoiceOverviewDto.InvoiceNumber);
        if (invoice is null)
            return new MemoryStream();
        var mandant = await selectedMandant.GetSelectedMandantAsInvoiceHolder();
        if (mandant is not null)
            invoice.InvoiceHolder = (InvoiceSupplierOrHolderDto)mandant;
        
        if (invoice.InvoiceHolder is null)
            throw new Exception("No holder found for invoice");
        
        return await BaseExportInvoicesAsXmlAsync(invoice, mode);
    }

    public async Task<MemoryStream> ExportInvoicesAMemStreamAsync(GrainSettlementInvoiceOverviewDto grainSettlementInvoiceOverviewDto,
        IEdifactExportImportService.ExportMode mode)
    {
        var invoice = await grainSettlementInvoiceHeaderService
            .GetInvoiceWithCompleteDataAsync(false, grainSettlementInvoiceOverviewDto.InvoiceNumber);
        if (invoice is null)
            return new MemoryStream();
        var mandant = await selectedMandant.GetSelectedMandantAsInvoiceHolder();
        if (mandant is not null)
            invoice.InvoiceHolder = (InvoiceSupplierOrHolderDto)mandant;
        if (invoice.InvoiceHolder is null)
            throw new Exception("No holder found for invoice");

        return await BaseExportInvoicesAsXmlAsync(invoice, mode);
    }
    
    public async Task<MemoryStream> ExportInvoicesAMemStreamAsync(RapeSettlementInvoiceOverviewDto rapeSettlementInvoiceOverviewDto, IEdifactExportImportService.ExportMode mode)
    {
        var invoice = await rapeSettlementInvoiceHeaderService
            .GetInvoiceWithCompleteDataAsync(false, rapeSettlementInvoiceOverviewDto.InvoiceNumber);
        if (invoice is null)
            return new MemoryStream();
        var mandant = await selectedMandant.GetSelectedMandantAsInvoiceHolder();
        if (mandant is not null)
            invoice.InvoiceHolder = (InvoiceSupplierOrHolderDto)mandant;
        if (invoice.InvoiceHolder is null)
            throw new Exception("No holder found for invoice");

        return await BaseExportInvoicesAsXmlAsync(invoice, mode);
    }

    public async Task<MemoryStream> ExportInvoicesAMemStreamAsync(ConfigurationEdifact configurationEdifact, IEdifactExportImportService.ExportMode mode)
    {
        List<BaseInvoiceOverviewEntryDto> invoiceOverviews = [];


        InvoiceSearchParam searchParam = new()
        {
            FromDate = configurationEdifact.FromDate ?? configurationEdifact.BeginingExportDate,
            ToDate =  configurationEdifact.ToDate,
            FromInvoiceNumber = configurationEdifact.FromInvoiceNumber,
            ToInvoiceNumber = configurationEdifact.ToInvoiceNumber,
            WithAllData = true,
            WithOutFlags = configurationEdifact.WithExportedInvoices ? null : (long)InvoiceFlags.EdifactExportSuccess,
        };

        var memoryStream = new MemoryStream();
        using var archive = new ZipArchive(memoryStream, ZipArchiveMode.Create, true);
        
        
        var exportObject = ExportObject(mode);
        logger.LogInfo("Start exporting invoices as edifact! Mode: "+mode);
        if (configurationEdifact.WithOutgoingInvoice)
        {
            var outgoingInvoiceOverviews = await outgoingInvoiceHeaderService.GetAllInvoiceCompleteDataForEdifact(searchParam);

            var baseInvoiceOverviewEntryDtos = outgoingInvoiceOverviews.ToList();
            var mandant = await selectedMandant.GetSelectedMandantAsInvoiceHolder();

            foreach (var outgoingInvoiceOverview in baseInvoiceOverviewEntryDtos)
            {
                if (mandant is not null)
                {
                    outgoingInvoiceOverview.InvoiceSupplier = mandant;
                    outgoingInvoiceOverview.InvoiceSupplierCurrent = mandant;
                }
                
                logger.LogInfo("Exporting outgoing invoice: " + outgoingInvoiceOverview.InvoiceNumber);
                var oderHead = await orderService.GetOverInvoiceNumberForEdifact(outgoingInvoiceOverview.InvoiceNumber);
                
                logger.LogInfo( $"Found Orderheader{oderHead?.Auftragsnummer} from invoice: " + outgoingInvoiceOverview.InvoiceNumber);


                logger.LogInfo("Search all liefernrs for articles");
                var dicArticleNumber2ArtRefNr = await articleService.GetArticleRefNumber(
                                                                            outgoingInvoiceOverview.ListAllPostings.Select(p=>p.Number).ToList());

                logger.LogInfo("Found {0} dicArticleNumber2ArtLiefNr", dicArticleNumber2ArtRefNr.Count);

                var xmlByte = exportObject.CreateInvoiceToMemStream(outgoingInvoiceOverview,oderHead,dicArticleNumber2ArtRefNr);
                await AddEntryToZip(archive, xmlByte.ToArray(), exportObject.GetFileName(outgoingInvoiceOverview));
                
                logger.LogInfo( $"Exported Invoice {outgoingInvoiceOverview.InvoiceNumber} as edifact");
            }

            invoiceOverviews = [..invoiceOverviews, ..baseInvoiceOverviewEntryDtos];
        }
        
        NumberOfExportInvoices = invoiceOverviews.Count;

        var outgoingInvoiceIds = invoiceOverviews
            .Where(i=>i.Type.IsOutgoingInvoice)
            .Select(x => x.Id).ToList();
        
        SetAllOutgoingInvoiceFibu2(outgoingInvoiceIds);


        FileName = $"Edifact_Export_{DateTime.Now:yyyyMMdd-HHmmss}.zip";
        
        return memoryStream;
    }

    private void SetAllOutgoingInvoiceFibu2(List<long> invoiceIds)
    { 
        outgoingInvoiceHeaderService.SetAllEdifactExportSuccessAndFibu2(invoiceIds);
    }
    
    private Task AddEntryToZip(ZipArchive archive, byte[] contentForFileEntry, string fileName)
    {
        var fileEntry = archive.CreateEntry(fileName);
        using var entryStream = fileEntry.Open();
        entryStream.Write(contentForFileEntry, 0, contentForFileEntry.Length);

        return Task.CompletedTask;
    }
    
    public async Task<IEnumerable<Auftragskopf>> ImportOrderFromEdifactAsync(string xml)
    {
        var generateOrders = await convertEdifactToOrderV1.CreateOrder(xml);
        var generateOrdersList = generateOrders.ToList();
        
        foreach (var generateOrder in generateOrdersList)
        {
            if(generateOrder.Id == 0)
                await orderService.Create("AutomaticFromEdifactImport", generateOrder);
            else
                await orderService.Update("AutomaticFromEdifactImport", generateOrder);
        }

        return generateOrdersList;
    }
    
}