using System.Text;
using WingCore.application.DataTransferObjects.Invoices;
using ZugFerd.Model.ZugFerdEN16931;
using ZUGFeRDXRechnung.Validations;
using ZUGFeRDXRechnung.Validations.Exceptions;

namespace ZUGFeRDXRechnung;

public abstract class BaseInvoice
{
    public static readonly HashSet<string> IsVatFree =
    [
        "0",
        "0,00",
        "0.00"
    ];

    private static readonly string Keypaymentcodebar = "0";
    private static readonly string Keypaymentcodescheck = "1";
    private static readonly string Keypaymentcodekreditkarte = "2";
    private static readonly string Keypaymentcodeueberweisung = "3";
    private static readonly string Keypaymentcodeoffenerposten = "9";

    /* Can be used if needed
    static readonly string KEYPAYMENTCODENN = "4";
    static readonly string KEYPAYMENTCODEUMBUCHUNG = "5";
    static readonly string KEYPAYMENTCODERABATT = "6";
    static readonly string KEYPAYMENTCODEPROVISION = "7";
    static readonly string KEYPAYMENTCODEGUTSCHRIFT = "8";*/

    private static readonly PaymentMeansCodeContentType Keyzugferdpaymentcodebar = PaymentMeansCodeContentType.Item10;

    private static readonly PaymentMeansCodeContentType
        Keyzugferdpaymentcodescheck = PaymentMeansCodeContentType.Item20;

    private static readonly PaymentMeansCodeContentType Keyzugferdpaymentcodeueberweisung =
        PaymentMeansCodeContentType.Item31;

    private static readonly PaymentMeansCodeContentType Keyzugferdpaymentcodeoffenerposten =
        PaymentMeansCodeContentType.Item1;

    private static readonly PaymentMeansCodeContentType Keyzugferdpaymentcodekreditkarte =
        PaymentMeansCodeContentType.Item48;

    private static readonly PaymentMeansCodeContentType Keyzugferdpaymentcodekreditkarteec =
        PaymentMeansCodeContentType.Item54;

    private static readonly PaymentMeansCodeContentType Keyzugferdpaymentcodekreditkartemaestrored6000 =
        PaymentMeansCodeContentType.Item54;

    private static readonly PaymentMeansCodeContentType Keyzugferdpaymentcodekreditkartevpay =
        PaymentMeansCodeContentType.Item54;

    /* Can be used if needed
    static readonly PaymentMeansCodeContentType KEYZUGFERDPAYMENTCODENN;
    static readonly PaymentMeansCodeContentType KEYZUGFERDPAYMENTCODEUMBUCHUNG;
    static readonly PaymentMeansCodeContentType KEYZUGFERDPAYMENTCODERABATT;
    static readonly PaymentMeansCodeContentType KEYZUGFERDPAYMENTCODEPROVISION;
    static readonly PaymentMeansCodeContentType KEYZUGFERDPAYMENTCODEGUTSCHRIFT*/

    protected record KeyPaymentType(string PaymentType, string CardType);

    protected static Dictionary<KeyPaymentType, PaymentMeansCodeContentType> PaymentCodeToZugFerdPaymentCode =
        new Dictionary<KeyPaymentType, PaymentMeansCodeContentType>()
        {
            { new KeyPaymentType(Keypaymentcodebar, string.Empty), Keyzugferdpaymentcodebar },
            { new KeyPaymentType(Keypaymentcodescheck, string.Empty), Keyzugferdpaymentcodescheck },
            { new KeyPaymentType(Keypaymentcodeueberweisung, string.Empty), Keyzugferdpaymentcodeueberweisung },
            { new KeyPaymentType(Keypaymentcodeoffenerposten, string.Empty), Keyzugferdpaymentcodeoffenerposten },

            { new KeyPaymentType(Keypaymentcodekreditkarte, string.Empty), Keyzugferdpaymentcodekreditkarte },
            { new KeyPaymentType(Keypaymentcodekreditkarte, "8"), Keyzugferdpaymentcodekreditkarteec },
            { new KeyPaymentType(Keypaymentcodekreditkarte, "9"), Keyzugferdpaymentcodekreditkartemaestrored6000 },
            { new KeyPaymentType(Keypaymentcodekreditkarte, "13"), Keyzugferdpaymentcodekreditkartevpay },
            /* Can be used if needed
            { new KeyPaymentType( KEYPAYMENTCODENN, string.Empty)                 ,  },
            { new KeyPaymentType( KEYPAYMENTCODEPROVISION, string.Empty)          ,  },
            { new KeyPaymentType( KEYPAYMENTCODEUMBUCHUNG, string.Empty)          ,  },
            { new KeyPaymentType( KEYPAYMENTCODERABATT, string.Empty)             ,  },
            { new KeyPaymentType( KEYPAYMENTCODEGUTSCHRIFT, string.Empty)         ,  },
            */
        };

    protected static List<PaymentMeansCodeContentType> PaymentTypeWithCardInformation = new()
    {
        PaymentMeansCodeContentType.Item48, // Bank card
        PaymentMeansCodeContentType.Item54, // Credit card
        PaymentMeansCodeContentType.Item55 // Debit card
    };

    protected static string? ValidationErrors { get; set; }


    public abstract string GetDocumentTypeComfort();
    public abstract string GetAttachmentFileName();
    public abstract string GetAttachmentName();
    public abstract MemoryStream CreateInvoiceXml(BaseInvoiceOverviewEntryDto invoiceOverview);

    protected virtual void CheckBuyer(InvoiceSupplierOrHolderDto? invoiceHolder)
    {
        Buyer.CheckBuyer(invoiceHolder, false);
    }

    protected void CheckSeller(InvoiceSupplierOrHolderDto? invoiceSupplier)
    {
        Seller.CheckSeller(invoiceSupplier);
    }

    protected virtual string GetInvoiceNumber(BaseInvoiceOverviewEntryDto invoiceOverview)
    {
        var invoiceNumber = invoiceOverview.InvoiceNumber.ToString();

        return invoiceOverview switch
        {
            OutgoingInvoiceOverviewDto => $"Ausgangsrechnung/{invoiceNumber}",
            IncomingInvoiceOverviewDto => $"Eingangsrechnung/{invoiceNumber}",
            GrainSettlementInvoiceOverviewDto => $"Getreideabr./{invoiceNumber}",
            RapeSettlementInvoiceOverviewDto => $"Abrechnung/{invoiceNumber}",
            _ => invoiceNumber
        };
    }

    protected virtual DocumentCodeContentType GetDocumentTypeCode(BaseInvoiceOverviewEntryDto invoiceOverview)
    {
        return invoiceOverview.IsCancellationInvoice
            ? DocumentCodeContentType.Item384
            : DocumentCodeContentType.Item380;
    }

    protected virtual string GetInvoiceCurrency(BaseInvoiceOverviewEntryDto invoiceOverview)
    {
        var currency = "EUR";
        
        if (string.IsNullOrEmpty(currency))
            throw new InvoiceIncompleteException("No currency found!");

        return currency;
    }

    protected virtual string GetPaymentTerm()
    {
        return "Die Rechnung ist sofort fällig.";
    }

    protected virtual bool NeedPaymentTermInfo(BaseInvoiceOverviewEntryDto invoiceOverview)
    {
        return !invoiceOverview.IsCancellationInvoice;
    }

    
     protected TaxCategoryCodeContentType GetVatCategoryCode(string vATPercent)
     {
         if (!string.IsNullOrEmpty(vATPercent) && IsVatFree.Contains(vATPercent))
         {
             return TaxCategoryCodeContentType.Z;
         }
         else
         {
             return TaxCategoryCodeContentType.S;
         }
     }

    protected static bool CheckVat(string vat, string country)
    {
        if (string.IsNullOrEmpty(vat))
        {
            return false;
        }

        if (string.IsNullOrEmpty(country))
        {
            return false;
        }

        if (vat.Length < 2)
        {
            return false;
        }

        return vat.StartsWith(country);
    }


    #region Serialize

    protected abstract string SerializeObject<T>(T dataObject);

    protected sealed class ExtentedstringWriter(StringBuilder builder, Encoding desiredEncoding)
        : StringWriter(builder)
    {
        public override Encoding Encoding => desiredEncoding;
    }

    #endregion
}