namespace ElectronicInvoice.Util;

public static class StringExtension
{
    public static string GetLastOrFirstDigit(this string source, int taillength)
    {
        if (taillength >= source.Length)
            return source;

        string s = source.Substring(source.Length - taillength);

        if (!s.Contains('*'))
        {
            return s;
        }

        return source.Substring(0, taillength);
    }
}