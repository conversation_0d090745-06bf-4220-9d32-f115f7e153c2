using System.Globalization;
using System.Text;
using System.Xml.Serialization;
using WingCore.application.DataTransferObjects.Invoices;
using ZugFerd.Model.ZugFerdEN16931;
using ZUGFeRDXRechnung.XRechnungInvoices.Model.XRechnung23;
using XRechnung23_IDType = ZUGFeRDXRechnung.XRechnungInvoices.Model.XRechnung23.IDType;
 using XRechnung23_NoteType = ZUGFeRDXRechnung.XRechnungInvoices.Model.XRechnung23.NoteType;

 namespace ZUGFeRDXRechnung.XRechnungInvoices.Export;

 public class XRechnung : BaseInvoice
{
    public override MemoryStream CreateInvoiceXml(BaseInvoiceOverviewEntryDto invoiceOverview)
    {
         CheckBuyer(invoiceOverview.InvoiceHolder);
        CheckSeller(invoiceOverview.InvoiceSupplier);

        //if (!CheckVat(invoiceOverview.InvoiceSupplier.UstIdNr, invoiceOverview.InvoiceSupplier.Country))
            //throw new InvalidTaxInformationException();
        
        var cultureInfo = new CultureInfo("de-DE", false);
        var nfi = cultureInfo.NumberFormat;
        nfi.CurrencyDecimalDigits = 6;

        InvoiceType xInvoice = new InvoiceType();

        AddBaseInformation(ref xInvoice,invoiceOverview);
        AddInvoicePeriod(ref xInvoice, invoiceOverview);
        AddOrderReference(ref xInvoice, invoiceOverview);
        AddBillingReference(ref xInvoice, invoiceOverview);
        AddAccountingSupplierParty(ref xInvoice, invoiceOverview);
        AddAccountingCustomerParty(ref xInvoice, invoiceOverview);
        AddDelivery(ref xInvoice, invoiceOverview);
        AddPaymentMeans(ref xInvoice);
        AddPaymentTerms(ref xInvoice, invoiceOverview, out decimal payableAmount);
        AddInvoiceLine(ref xInvoice, invoiceOverview, out var listUsedVatInPostings, out decimal netTotalAmount);
        AddTaxTotal(ref xInvoice, invoiceOverview, listUsedVatInPostings);
        AddLegalMonetaryTotal(ref xInvoice, invoiceOverview, payableAmount, netTotalAmount);

        MemoryStream outStream = new MemoryStream();
        var writer = new StreamWriter(outStream);
        writer.Write(SerializeObject(xInvoice));
        writer.Flush();

        outStream.Position = 0;

        return outStream;
    }

    public override string GetDocumentTypeComfort() =>
        throw new InvalidOperationException($"XRechung does not contain DocumentTypeComfort");


    public override string GetAttachmentFileName()
    {
        return GetAttachmentName() + ".xml";
    }


    public override string GetAttachmentName()
    {
        return "xrechnung";
    }


    private void AddBaseInformation(ref InvoiceType invoiceType, BaseInvoiceOverviewEntryDto invoiceOverview)
    {
        invoiceType.CustomizationID = new CustomizationIDType
        {
            Value = "urn:cen.eu:en16931:2017#compliant#urn:xeinkauf.de:kosit:xrechnung_3.0"
        };
        invoiceType.ProfileID = new ProfileIDType
        {
            Value = "urn:fdc:peppol.eu:2017:poacc:billing:01:1.0"
        };

        invoiceType.ID = new XRechnung23_IDType
        {
            Value = GetInvoiceNumber(invoiceOverview)
        };

        invoiceType.IssueDate = new IssueDateType
        {
            Value = invoiceOverview.InvoiceDate
        };

        invoiceType.DueDate = new DueDateType
        {
            Value = invoiceOverview.InvoiceDate
        };

        invoiceType.InvoiceTypeCode = new InvoiceTypeCodeType
        {
            Value = GetDocumentTypeCode(invoiceOverview).ToString().Replace("Item", "")
        };

        invoiceType.DocumentCurrencyCode = new DocumentCurrencyCodeType
        {
            Value = GetInvoiceCurrency(invoiceOverview)
        };

        invoiceType.BuyerReference = new BuyerReferenceType
        {
            //Value = LeitWegID
            Value = invoiceOverview.InvoiceHolder?.Kdleh
        };
    }


    private void AddInvoicePeriod(ref InvoiceType invoiceType, BaseInvoiceOverviewEntryDto invoiceOverview)
    {
        DateTime? arrivalDate = null;
        DateTime? depatureDate = null;

        if (arrivalDate is null ||
            depatureDate is null)
        {
            invoiceType.InvoicePeriod = null;
            return;
        }


        invoiceType.InvoicePeriod = new PeriodType[1]
        {
            new PeriodType
            {
                StartDate = new StartDateType
                {
                    Value = arrivalDate.Value,
                },

                EndDate = new EndDateType
                {
                    Value = depatureDate.Value,
                }
            }
        };
    }


    private void AddOrderReference(ref InvoiceType invoiceType, BaseInvoiceOverviewEntryDto invoiceOverview)
    {
        string orderRef = string.Empty; //todo reference

        if (string.IsNullOrEmpty(orderRef))
        {
            invoiceType.OrderReference = null;
            return;
        }


        invoiceType.OrderReference = new OrderReferenceType
        {
            ID = new XRechnung23_IDType
            {
                Value = orderRef
            }
        };
    }


    private void AddBillingReference(ref InvoiceType invoiceType, 
        BaseInvoiceOverviewEntryDto invoiceOverview)
    {
        invoiceType.BillingReference = null;

        if (GetDocumentTypeCode(invoiceOverview) is DocumentCodeContentType.Item384)
        {
            invoiceType.BillingReference =
            [
                new BillingReferenceType
                {
                    InvoiceDocumentReference = new DocumentReferenceType
                    {
                        ID = new XRechnung23_IDType { Value = invoiceOverview.CancelOrCreditNoteInvoiceNumber.ToString() }
                    }
                }
            ];
        }
    }


    private void AddAccountingSupplierParty(ref InvoiceType invoiceType, BaseInvoiceOverviewEntryDto invoiceOverview)
    {
        var street = string.IsNullOrWhiteSpace(invoiceOverview.InvoiceSupplier?.Street) ? invoiceOverview.InvoiceSupplierCurrent?.Street : invoiceOverview.InvoiceSupplier?.Street;
        var ort = string.IsNullOrWhiteSpace(invoiceOverview.InvoiceSupplier?.Ort) ? invoiceOverview.InvoiceSupplierCurrent?.Ort : invoiceOverview.InvoiceSupplier?.Ort;
        var plz = string.IsNullOrWhiteSpace(invoiceOverview.InvoiceSupplier?.Plz) ? invoiceOverview.InvoiceSupplierCurrent?.Plz : invoiceOverview.InvoiceSupplier?.Plz;
        var countryId = string.IsNullOrWhiteSpace(invoiceOverview.InvoiceSupplier?.CountryId) ? invoiceOverview.InvoiceSupplierCurrent?.CountryId : invoiceOverview.InvoiceSupplier?.CountryId;
        var ustIdNr = string.IsNullOrWhiteSpace(invoiceOverview.InvoiceSupplier?.UstIdNr) ? invoiceOverview.InvoiceSupplierCurrent?.UstIdNr : invoiceOverview.InvoiceSupplier?.UstIdNr;
        var fullName = string.IsNullOrWhiteSpace(invoiceOverview.InvoiceSupplier?.FullName) ? invoiceOverview.InvoiceSupplierCurrent?.FullName : invoiceOverview.InvoiceSupplier?.FullName;
        var phone = string.IsNullOrWhiteSpace(invoiceOverview.InvoiceSupplier?.Phone) ? invoiceOverview.InvoiceSupplierCurrent?.Phone : invoiceOverview.InvoiceSupplier?.Phone;
        var email = string.IsNullOrWhiteSpace(invoiceOverview.InvoiceSupplier?.Email) ? invoiceOverview.InvoiceSupplierCurrent?.Email : invoiceOverview.InvoiceSupplier?.Email;
        
        invoiceType.AccountingSupplierParty = new SupplierPartyType
        {
            Party = new PartyType
            {
                PostalAddress = new AddressType
                {
                    StreetName = new StreetNameType { Value = street?.TrimEnd() },
                    CityName = new CityNameType { Value = ort?.TrimEnd() },
                    PostalZone = new PostalZoneType { Value = plz?.TrimEnd() },
                    Country = new CountryType
                    {
                        IdentificationCode = new IdentificationCodeType { Value = countryId }
                    }
                },
                PartyTaxScheme = new PartyTaxSchemeType[1]
                {
                    new PartyTaxSchemeType
                    {
                        CompanyID = new CompanyIDType { Value = ustIdNr },
                        TaxScheme = new TaxSchemeType
                        {
                            ID = new XRechnung23_IDType { Value = "VAT" }
                        }
                    }
                },
                PartyLegalEntity = new PartyLegalEntityType[1]
                {
                    new PartyLegalEntityType
                    {
                        RegistrationName = new RegistrationNameType { Value = fullName }
                    }
                },
                Contact = new ContactType
                {
                    Name = new NameType1 { Value = fullName },
                    Telephone = new TelephoneType { Value = phone },
                    ElectronicMail = new ElectronicMailType { Value = email }
                }
            }
        };
    }


    private void AddAccountingCustomerParty(ref InvoiceType invoiceType, BaseInvoiceOverviewEntryDto invoiceOverview)
    {
        var street = string.IsNullOrWhiteSpace(invoiceOverview.InvoiceHolder?.Street) ? invoiceOverview.InvoiceHolderCurrent?.Street : invoiceOverview.InvoiceHolder?.Street;
        var ort = string.IsNullOrWhiteSpace(invoiceOverview.InvoiceHolder?.Ort) ? invoiceOverview.InvoiceHolderCurrent?.Ort : invoiceOverview.InvoiceHolder?.Ort;
        var plz = string.IsNullOrWhiteSpace(invoiceOverview.InvoiceHolder?.Plz) ? invoiceOverview.InvoiceHolderCurrent?.Plz : invoiceOverview.InvoiceHolder?.Plz;
        var countryId = string.IsNullOrWhiteSpace(invoiceOverview.InvoiceHolder?.CountryId) ? invoiceOverview.InvoiceHolderCurrent?.CountryId : invoiceOverview.InvoiceHolder?.CountryId;
        var ustIdNr = string.IsNullOrWhiteSpace(invoiceOverview.InvoiceHolder?.UstIdNr) ? invoiceOverview.InvoiceHolderCurrent?.UstIdNr : invoiceOverview.InvoiceSupplier?.UstIdNr;
        var fullName = string.IsNullOrWhiteSpace(invoiceOverview.InvoiceHolder?.FullName) ? invoiceOverview.InvoiceHolderCurrent?.FullName : invoiceOverview.InvoiceSupplier?.FullName;
        var phone = string.IsNullOrWhiteSpace(invoiceOverview.InvoiceHolder?.Phone) ? invoiceOverview.InvoiceHolderCurrent?.Phone : invoiceOverview.InvoiceSupplier?.Phone;
        var email = string.IsNullOrWhiteSpace(invoiceOverview.InvoiceHolder?.EInvoiceEmail) ? invoiceOverview.InvoiceHolderCurrent?.EInvoiceEmail : invoiceOverview.InvoiceSupplier?.EInvoiceEmail;
        
        invoiceType.AccountingCustomerParty = new CustomerPartyType
        {
            Party = new PartyType
            {
                PartyIdentification = new PartyIdentificationType[1]
                {
                    new PartyIdentificationType
                    {
                        ID = new XRechnung23_IDType { Value = invoiceOverview.InvoiceHolder?.Number.ToString() }
                    }
                },
                PostalAddress = new AddressType
                {
                    StreetName = new StreetNameType { Value = street?.TrimEnd() },
                    CityName = new CityNameType { Value = ort?.TrimEnd() },
                    PostalZone = new PostalZoneType { Value = plz?.TrimEnd() },
                    Country = new CountryType
                    {
                        IdentificationCode = new IdentificationCodeType { Value = countryId }
                    }
                },
                PartyLegalEntity = new PartyLegalEntityType[1]
                {
                    new PartyLegalEntityType
                    {
                        RegistrationName = new RegistrationNameType { Value = fullName?.TrimEnd() }
                    }
                },
                Contact = new ContactType
                {
                    Name = new NameType1 { Value = fullName?.TrimEnd() },
                    Telephone = new TelephoneType { Value = phone?.TrimEnd() },
                    ElectronicMail = new ElectronicMailType { Value = email?.TrimEnd() }
                }
            }
        };
    }


    private void AddDelivery(ref InvoiceType invoiceType, BaseInvoiceOverviewEntryDto invoiceOverview)
    {
        invoiceType.Delivery = new DeliveryType[1]
        {
            new DeliveryType
            {
                DeliveryLocation = new LocationType1
                {
                    ID = new XRechnung23_IDType { Value = invoiceOverview.InvoiceHolder?.Number.ToString() },
                    Address = new AddressType
                    {
                        StreetName = new StreetNameType { Value = invoiceOverview.InvoiceHolder?.Street },
                        CityName = new CityNameType { Value = invoiceOverview.InvoiceHolder?.Ort },
                        PostalZone = new PostalZoneType { Value = invoiceOverview.InvoiceHolder?.Plz },
                        Country = new CountryType
                        {
                            IdentificationCode = new IdentificationCodeType { Value = invoiceOverview.InvoiceHolder?.CountryId }
                        }
                    }
                },
                DeliveryParty = new PartyType
                {
                    PartyName =
                    [
                        new PartyNameType
                        {
                            Name = new NameType1 { Value = invoiceOverview.InvoiceHolder?.FullName }
                        }
                    ]
                }
            }
        };
    }


    private void AddPaymentTerms(ref InvoiceType invoiceType, BaseInvoiceOverviewEntryDto invoiceOverview, out decimal payableAmount)
    {
        var listTypeToPaymentMeansType = new List<PaymentMeansType>();
        payableAmount = 0;
        
        /*
        foreach (XmlNode payment in payments)
        {
            payableAmount += TryGetNodeValue<decimal>(payment, "amount", "0", typeof(InvoiceIncompleteException),
                ParseOption.Optional);
            PaymentMeansCodeContentType? paymentMeansCodeContentType = GetPaymentTypeCode(
                payment.SelectSingleNode("tPaymentType").InnerText,
                payment.SelectSingleNode("tCardTypeLong").InnerText);
            CardAccountType cardAccountType = null;
            FinancialAccountType financialAccountType = null;

            if (paymentMeansCodeContentType is null)
            {
                continue;
            }


            if (NeedPaymenCardInformation(paymentMeansCodeContentType.Value))
            {
                cardAccountType = new CardAccountType()
                {
                    PrimaryAccountNumberID = new PrimaryAccountNumberIDType { Value = GetCardNo(payment) },
                    NetworkID = new NetworkIDType { Value = GetCardDescription(payment) },
                    HolderName = new HolderNameType { Value = GetCardHolderName(payment) }
                };
            }


            if (seller.HasBankInformation)
            {
                financialAccountType = new FinancialAccountType()
                {
                    ID = new XRechnung23_IDType { Value = invoiceOverview.InvoiceSupplier?.IBAN },
                    Name = new NameType1 { Value = seller.BankName }
                };
            }


            PaymentMeansType PaymentMeansType = new PaymentMeansType()
            {
                PaymentMeansCode = new PaymentMeansCodeType1
                    { Value = Convert.ToString((int)paymentMeansCodeContentType + 1) },
                InstructionNote = new InstructionNoteType[1]
                {
                    new InstructionNoteType { Value = payment.SelectSingleNode("paymentDesc").InnerText }
                },
                CardAccount = cardAccountType,
                PayeeFinancialAccount = financialAccountType
            };

            if (!listTypeToPaymentMeansType.Contains(PaymentMeansType))
            {
                listTypeToPaymentMeansType.Add(PaymentMeansType);
            }
        }

        */
        
        var paymentMeansType = new PaymentMeansType()
        {
            PaymentMeansCode = new PaymentMeansCodeType1
                { Value = Convert.ToString(1) },
            InstructionNote = new InstructionNoteType[1]
            {
                new InstructionNoteType { Value = "Keine Informationen" }
            },
        };

        listTypeToPaymentMeansType.Add(paymentMeansType);
        invoiceType.PaymentMeans = listTypeToPaymentMeansType.ToArray();
    }


    private void AddPaymentMeans(ref InvoiceType invoiceType)
    {
        invoiceType.PaymentTerms = new PaymentTermsType[1]
        {
            new PaymentTermsType
            {
                Note = new XRechnung23_NoteType[1] { new XRechnung23_NoteType { Value = GetPaymentTerm() } }
            }
        };
    }


    private void AddInvoiceLine(ref InvoiceType invoiceType, BaseInvoiceOverviewEntryDto invoiceOverview, out List<string> listUsedVatInPostings,
        out decimal netTotalAmount)
    {
        listUsedVatInPostings = [];
        netTotalAmount = 0;
        
        var postings =  invoiceOverview switch
        {
            OutgoingInvoiceOverviewDto invoice => AddIncludedSupplyChainTradeLineItems(invoice.ListPostings,invoice.InvoiceDate, out listUsedVatInPostings,out netTotalAmount),
            IncomingInvoiceOverviewDto invoice => AddIncludedSupplyChainTradeLineItems(invoice.ListPostings, invoice.InvoiceDate, out listUsedVatInPostings,out netTotalAmount),
            GrainSettlementInvoiceOverviewDto invoice => AddIncludedSupplyChainTradeLineItems(invoice.ListPostings, invoice.InvoiceDate, out listUsedVatInPostings,out netTotalAmount),
            RapeSettlementInvoiceOverviewDto invoice => AddIncludedSupplyChainTradeLineItems(invoice.ListPostings, invoice.InvoiceDate, out listUsedVatInPostings,out netTotalAmount),
            _ => null
        };
        
        if (postings is not null)
        {
            invoiceType.InvoiceLine = [..postings];
        }
    }

    private List<InvoiceLineType> AddIncludedSupplyChainTradeLineItems(
        IEnumerable<BaseInvoicePostingDto> listPostings,
        DateTime invoiceDate,
        out List<string> listUsedVatInPostings,
        out decimal netTotalAmount)
    {
        listUsedVatInPostings = [];
        netTotalAmount = 0;
        List<InvoiceLineType> postings = new ();

        var baseInvoicePostingDtos = listPostings.ToList();
        for (var i = 0; i < baseInvoicePostingDtos.Count; i++)
        {
            if(baseInvoicePostingDtos[i].Tax.IsAllAmountZero)
                continue;
            
            netTotalAmount += baseInvoicePostingDtos[i].TotalNettoWithDiscount;
            postings.Add(GetInvoiceLineType(baseInvoicePostingDtos[i], "EUR",invoiceDate, i + 1));
        }
        
        return postings;
    }
    
    private InvoiceLineType GetInvoiceLineType(BaseInvoicePostingDto servicePosition, string currency,DateTime invoiceDate, int pos)
    {
        var totalNetto = Math.Round(servicePosition.TotalNettoWithDiscount, 2, MidpointRounding.AwayFromZero);
            
        var invoiceLineType = new InvoiceLineType
        {
            ID = new XRechnung23_IDType { Value = pos.ToString() },
            InvoicedQuantity = new InvoicedQuantityType { unitCode = "H87", Value = servicePosition.Quantity },
            LineExtensionAmount = new LineExtensionAmountType
                { currencyID = currency, Value = totalNetto },
            InvoicePeriod = new PeriodType[1]
            {
                new PeriodType
                {
                    StartDate = new StartDateType { Value = invoiceDate },
                    EndDate = new EndDateType { Value = invoiceDate }
                }
            },
            Item = new ItemType
            {
                Description = new DescriptionType[1]
                {
                    new DescriptionType { Value = servicePosition.Number.ToString() },
                },
                Name = new NameType1 { Value = servicePosition.Description.TrimEnd() },
                ClassifiedTaxCategory = new TaxCategoryType[1]
                {
                    new TaxCategoryType
                    {
                        ID = new XRechnung23_IDType { Value = GetVatCategoryCode(servicePosition.Tax.Vat.ToString(CultureInfo.InvariantCulture)).ToString() },
                        Percent = new PercentType1 { Value = Math.Abs(servicePosition.Tax?.Vat ?? 0) },
                        TaxScheme = new TaxSchemeType
                        {
                            ID = new XRechnung23_IDType { Value = "VAT" }
                        }
                    }
                }
            },
            Price = new PriceType
            {
                PriceAmount = new PriceAmountType { currencyID = currency, Value = Math.Abs(totalNetto / servicePosition.Quantity) },
                BaseQuantity = null,
            }
        };

        return invoiceLineType;
    }


    private void AddTaxTotal(ref InvoiceType invoiceType, BaseInvoiceOverviewEntryDto invoiceOverview,
        List<string> listUsedVatInPostings)
    {
        var vAtPositions = invoiceOverview.ListTax.Where(x => !x.IsAllAmountZero).ToList();
        var usedVats = 0;
        invoiceType.TaxTotal = new TaxTotalType[1]
        {
            new TaxTotalType
            {
                TaxAmount = new TaxAmountType
                {
                    Value = 0,
                    currencyID = invoiceType.DocumentCurrencyCode.Value
                },
                TaxSubtotal = new TaxSubtotalType[vAtPositions.Count]
            }
        };
        
        for (int i = 0; i < vAtPositions.Count; i++)
        {
            var vAtPosition = vAtPositions[i];
            
            if(vAtPosition.IsAllAmountZero)
                continue;
            usedVats++;
            
            var tradeTaxType = new TaxSubtotalType
            {
                TaxAmount = new TaxAmountType
                {
                    Value = decimal.Round(vAtPosition.VatAmount, 2),
                    currencyID = invoiceType.DocumentCurrencyCode.Value
                },
                TaxableAmount = new TaxableAmountType
                {
                    Value = decimal.Round(vAtPosition.NetAmount, 2),
                    currencyID = invoiceType.DocumentCurrencyCode.Value
                },

                TaxCategory = new TaxCategoryType
                {
                    ID = new XRechnung23_IDType { Value = GetVatCategoryCode(vAtPosition.Vat.ToString(CultureInfo.InvariantCulture)).ToString() },
                    Percent = new PercentType1() { Value = Math.Abs(vAtPosition.Vat) },
                    TaxScheme = new TaxSchemeType()
                    {
                        ID = new XRechnung23_IDType { Value = "VAT" }
                    }
                }
            };

            invoiceType.TaxTotal[0].TaxAmount.Value += tradeTaxType.TaxAmount.Value;
            invoiceType.TaxTotal[0].TaxSubtotal[i] = tradeTaxType;
        }
        
        invoiceType.TaxTotal[0].TaxSubtotal = invoiceType.TaxTotal[0].TaxSubtotal.Take(usedVats).ToArray();
    }


    private void AddLegalMonetaryTotal(ref InvoiceType invoiceType, 
        BaseInvoiceOverviewEntryDto invoiceOverview,
        decimal payableAmount,
        decimal netTotalAmount)
    {
        decimal vatAmount = 0;

        if (invoiceType.TaxTotal.Length > 0)
        {
            vatAmount = invoiceType.TaxTotal[0].TaxAmount.Value;
        }


        var total = netTotalAmount + vatAmount;
        payableAmount *= -1;
        var rounding = payableAmount - total;

        invoiceType.LegalMonetaryTotal = new MonetaryTotalType
        {
            LineExtensionAmount = new LineExtensionAmountType
                { currencyID = invoiceType.DocumentCurrencyCode.Value, Value = Math.Round(netTotalAmount, 2, MidpointRounding.AwayFromZero) },
            TaxExclusiveAmount = new TaxExclusiveAmountType
                { currencyID = invoiceType.DocumentCurrencyCode.Value, Value = Math.Round(netTotalAmount, 2, MidpointRounding.AwayFromZero) },
            TaxInclusiveAmount = new TaxInclusiveAmountType
                { currencyID = invoiceType.DocumentCurrencyCode.Value, Value = Math.Round(total, 2, MidpointRounding.AwayFromZero) },
            PayableRoundingAmount = new PayableRoundingAmountType
                { currencyID = invoiceType.DocumentCurrencyCode.Value, Value = Math.Round(rounding, 2, MidpointRounding.AwayFromZero) },
            PrepaidAmount = new PrepaidAmountType
                { currencyID = invoiceType.DocumentCurrencyCode.Value, Value = decimal.Zero },
            PayableAmount = new PayableAmountType
                { currencyID = invoiceType.DocumentCurrencyCode.Value, Value = Math.Round(payableAmount, 2, MidpointRounding.AwayFromZero) }
        };
    }



    protected override string SerializeObject<T>(T dataObject)
    {
        var xmlSerializer = XmlSerializer.FromTypes(new[] { typeof(T) })[0];

        using (ExtentedstringWriter extentedstringWriter = new ExtentedstringWriter(new StringBuilder(), Encoding.UTF8))
        {
            XmlSerializerNamespaces xmlSerializerNamespaces = new XmlSerializerNamespaces();

            xmlSerializerNamespaces.Add("ubl", "urn:oasis:names:specification:ubl:schema:xsd:Invoice-2");
            xmlSerializerNamespaces.Add("cac", "urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2");
            xmlSerializerNamespaces.Add("cbc", "urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2");
            xmlSerializerNamespaces.Add("xsi", "http:www.w3.org/2001/XMLSchema-instance");

            xmlSerializer?.Serialize(extentedstringWriter, dataObject, xmlSerializerNamespaces);
            return extentedstringWriter.ToString();
        }
    }
}