<!-- 
  Library:           OASIS Universal Business Language (UBL) 2.2 OS
                     http://docs.oasis-open.org/ubl/os-UBL-2.2/
  Release Date:      09 July 2018
  Module:            UBL-ExtensionContentDataType-2.2.xsd
  Generated on:      2016-06-24 19:50(UTC)
  Copyright (c) OASIS Open 2016. All Rights Reserved.
  
  Release Note: This is a module that can be modified by users, typically
  only to add extension schema fragments for lax detection when encountered.
  Changes to the complex type should not be required for typical use.
 -->
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns="urn:oasis:names:specification:ubl:schema:xsd:CommonExtensionComponents-2" targetNamespace="urn:oasis:names:specification:ubl:schema:xsd:CommonExtensionComponents-2" elementFormDefault="qualified" attributeFormDefault="unqualified" version="2.2">
<!--  ===== import here all extension schemas =====  -->
<!--  UBL Technical Committee extensions  -->
<xsd:import namespace="urn:oasis:names:specification:ubl:schema:xsd:CommonSignatureComponents-2" schemaLocation="UBL-CommonSignatureComponents-2.2.xsd"/>
<!--  Incorporate ETSI signature specifications  -->
<xsd:import namespace="http://uri.etsi.org/01903/v1.3.2#" schemaLocation="UBL-XAdES01903v132-201601-2.2.xsd"/>
<xsd:import namespace="http://uri.etsi.org/01903/v1.4.1#" schemaLocation="UBL-XAdES01903v141-201601-2.2.xsd"/>
<!--  ===== Type Declaration =====  -->
<xsd:complexType name="ExtensionContentType">
<xsd:sequence>
<xsd:any namespace="##other" processContents="lax" minOccurs="1" maxOccurs="1">
<xsd:annotation>
<xsd:documentation> Any element in any namespace other than the UBL extension namespace is allowed to be the apex element of an extension. Only those elements found in the UBL schemas and in the trees of schemas imported in this module are validated. Any element for which there is no schema declaration in any of the trees of schemas passes validation and is not treated as a schema constraint violation. </xsd:documentation>
</xsd:annotation>
</xsd:any>
</xsd:sequence>
</xsd:complexType>
</xsd:schema>
<!--  ===== Copyright Notice =====  -->
<!-- 
  OASIS takes no position regarding the validity or scope of any 
  intellectual property or other rights that might be claimed to pertain 
  to the implementation or use of the technology described in this 
  document or the extent to which any license under such rights 
  might or might not be available; neither does it represent that it has 
  made any effort to identify any such rights. Information on OASIS's 
  procedures with respect to rights in OASIS specifications can be 
  found at the OASIS website. Copies of claims of rights made 
  available for publication and any assurances of licenses to be made 
  available, or the result of an attempt made to obtain a general 
  license or permission for the use of such proprietary rights by 
  implementors or users of this specification, can be obtained from 
  the OASIS Executive Director.

  OASIS invites any interested party to bring to its attention any 
  copyrights, patents or patent applications, or other proprietary 
  rights which may cover technology that may be required to 
  implement this specification. Please address the information to the 
  OASIS Executive Director.
  
  This document and translations of it may be copied and furnished to 
  others, and derivative works that comment on or otherwise explain 
  it or assist in its implementation may be prepared, copied, 
  published and distributed, in whole or in part, without restriction of 
  any kind, provided that the above copyright notice and this 
  paragraph are included on all such copies and derivative works. 
  However, this document itself may not be modified in any way, 
  such as by removing the copyright notice or references to OASIS, 
  except as needed for the purpose of developing OASIS 
  specifications, in which case the procedures for copyrights defined 
  in the OASIS Intellectual Property Rights document must be 
  followed, or as required to translate it into languages other than 
  English. 

  The limited permissions granted above are perpetual and will not be 
  revoked by OASIS or its successors or assigns. 

  This document and the information contained herein is provided on 
  an "AS IS" basis and OASIS DISCLAIMS ALL WARRANTIES, 
  EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO ANY 
  WARRANTY THAT THE USE OF THE INFORMATION HEREIN 
  WILL NOT INFRINGE ANY RIGHTS OR ANY IMPLIED 
  WARRANTIES OF MERCHANTABILITY OR FITNESS FOR A 
  PARTICULAR PURPOSE.
 -->