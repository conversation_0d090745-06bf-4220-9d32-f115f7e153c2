using System.Net.Http.Headers;
using System.Text;
using AdginityRestApi.Output.cs;
using Microsoft.Extensions.Logging;
using ObjectDeAndSerialize;
using Refit;
using WingCore.domain.Common.Configurations;

namespace AdginityRestApi;

public class AdfinityClient(ILogger<AdfinityClient> logger) : IAdfinityClient 
{
    public async Task<IApiResponse<_v3_companies_OUTPUT_ITEM>?> GetCompany(int companyNummer, ConfigurationEasiAdfinityRestApi config)
    {
        var httpClient = new HttpClient
        {
            BaseAddress = new Uri(config.BaseUrl)
        };
        
        httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Basic", GenerateAuthorizationBasicAsBase64(config.UserName, config.UserPassword));
   
        var clientForCompanies = RestService.For<ICompaniesV3Api>(httpClient);

        logger.LogInformation("Send GetCompany start: {GetCompany}", companyNummer);
        
        try
        {
            var response = await clientForCompanies.CompaniesGET2(
                companyNummer,
                config.EasiAdfinityDatabase,
                config.EasiAdfinityEnvir);
            
            logger.LogInformation("Response: {response}", response.Content?.SerializeToJson() ?? string.Empty);
            return response;
        }
        catch (Exception e)
        {
            logger.LogError("Error serializing response or send request: {e}", e);
            throw;
        }
        
    }

    public async Task<IApiResponse<string>?> SendCompany(_v3_companies_INPUT_ITEM companyRequest, ConfigurationEasiAdfinityRestApi config)
    {
        var httpClient = new HttpClient
        {
            BaseAddress = new Uri(config.BaseUrl)
        };
        
        httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Basic", GenerateAuthorizationBasicAsBase64(config.UserName, config.UserPassword));
   
        var clientForCompanies = RestService.For<ICompaniesV3Api>(httpClient);

        logger.LogInformation("Send companyRequest start: {number} {Name}", companyRequest.Number,companyRequest.Name);
        
        try
        {
            logger.LogInformation("Request: {companieRequest}", companyRequest.SerializeToJson());
        }
        catch (Exception e)
        {
            logger.LogError("Error serializing request: {e}", e);
        }
        
        try
        {
            var response = await clientForCompanies.CompaniesPOST(
                companyRequest.SerializeToJson(),
                config.EasiAdfinityDatabase,
                config.EasiAdfinityEnvir);
            
            logger.LogInformation("Response: {response}", response.Content?.SerializeToJson() ?? string.Empty);
            return response;
        }
        catch (Exception e)
        {
            logger.LogError("Error serializing response or send request: {e}", e);
            throw;
        }
    }

    public async Task<IApiResponse?> UpdateCompany(_v3_companies_INPUT_ITEM companyRequest, ConfigurationEasiAdfinityRestApi config)
    {
        var httpClient = new HttpClient
        {
            BaseAddress = new Uri(config.BaseUrl),
            DefaultRequestHeaders = 
            {
            TransferEncodingChunked = false // Deaktiviert chunked encoding
            }
        };
        
        httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Basic", GenerateAuthorizationBasicAsBase64(config.UserName, config.UserPassword));
   
        var clientForCompanies = RestService.For<ICompaniesV3Api>(httpClient);

        logger.LogInformation("Update companyRequest start: {number} {Name}", companyRequest.Number,companyRequest.Name);
        
        try
        {
            logger.LogInformation("Request: {companieRequest}", companyRequest.SerializeToJson());
        }
        catch (Exception e)
        {
            logger.LogError("Error serializing request: {e}", e);
        }
        
        try
        {
            var response = await clientForCompanies.CompaniesPUT(
                companyRequest.Number,
                companyRequest.SerializeToJson(),
                config.EasiAdfinityDatabase,
                config.EasiAdfinityEnvir);

            logger.LogInformation("Response: {response}",
                response.Error is null
                    ? response.Headers.ToString()
                    : response.Error.Content!.SerializeToJson());

            return response;
        }
        catch (Exception e)
        {
            logger.LogError("Error serializing response or send request: {e}", e);
            throw;
        }
    }
    
    public async Task<IApiResponse<string>?> SendInvoiceEntries(_v1_accountingEntriesBulk_INPUT_ITEM_entries request, ConfigurationEasiAdfinityRestApi config)
    {
        var httpClient = new HttpClient
        {
            BaseAddress = new Uri(config.BaseUrl)
        };
        
        httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Basic", GenerateAuthorizationBasicAsBase64(config.UserName, config.UserPassword));
   
        var clientForAccountEntries = RestService.For<IAccountingEntriesBulkV1Api>(httpClient);
        logger.LogInformation("Send InvoiceEntries start:");
        try
        {
            logger.LogInformation("Request: {request}", request.SerializeToJson());
        }
        catch (Exception e)
        {
            logger.LogError("Error serializing request: {e}", e);
        }
        
        try
        {
            var response = await clientForAccountEntries.AccountingEntriesV2(
                request.SerializeToJson(),
                config.EasiAdfinityDatabase,
                config.EasiAdfinityEnvir);
            
            logger.LogInformation("Response: {response}", response.Headers.ToString() ?? string.Empty);
            return response;
        }
        catch (Exception e)
        {
            logger.LogError("Error serializing response or send request: {e}", e);
            throw;
        }
    }

    public async Task<IApiResponse<string>?> SendPsmDocument(PsmDocument request, ConfigurationEasiAdfinityRestApi config)
    {
        var httpClient = new HttpClient
        {
            BaseAddress = new Uri(config.BaseUrl)
        };
        
        httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Basic", GenerateAuthorizationBasicAsBase64(config.UserName, config.UserPassword));
   
        var clientForIPsmDocumentV2Api = RestService.For<IPsmDocumentV2Api>(httpClient);
        logger.LogInformation("Send InvoiceEntries start:");
        try
        {
            logger.LogInformation("Request: {request}", request.SerializeToJson());
        }
        catch (Exception e)
        {
            logger.LogError("Error serializing request: {e}", e);
        }
        
        try
        {
            var response = await clientForIPsmDocumentV2Api.PsmEntriesV2(
                request.SerializeToJson(),
                config.EasiAdfinityDatabase,
                config.EasiAdfinityEnvir);
            
            logger.LogInformation("Response: {response}", response.Headers.ToString() ?? string.Empty);
            return response;
        }
        catch (Exception e)
        {
            logger.LogError("Error serializing response or send request: {e}", e);
            throw;
        }
    }

    public async Task<IApiResponse<string>?> SendProduct(ProductRequest request, ConfigurationEasiAdfinityRestApi config)
    {
        var httpClient = new HttpClient
        {
            BaseAddress = new Uri(config.BaseUrl)
        };
        
        httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Basic", GenerateAuthorizationBasicAsBase64(config.UserName, config.UserPassword));
   
        var clientForIPsmDocumentV2Api = RestService.For<IProductsV1Api>(httpClient);
        logger.LogInformation("Send Product start:");
        try
        {
            logger.LogInformation("Request: {request}", request.SerializeToJson());
        }
        catch (Exception e)
        {
            logger.LogError("Error serializing request: {e}", e);
        }
        
        try
        {
            var response = await clientForIPsmDocumentV2Api.CreateOrUpdateProductV1(
                request.ProductCode,
                request.SerializeToJson(),
                config.EasiAdfinityDatabase,
                config.EasiAdfinityEnvir);
            
            if (response.Error?.Content is not null)
                logger.LogInformation("Response error during product send: {response.Error.Content}", response.Error.Content);
                
            logger.LogInformation("Response: {response}", response.Headers.ToString());
            return response;
        }
        catch (Exception e)
        {
            logger.LogError("Error serializing response or send request: {e}", e);
            throw;
        }
    }


    private static string GenerateAuthorizationBasicAsBase64(string username, string password)
    {
        var authString = $"{username}:{password}";
        var authBytes = System.Text.Encoding.UTF8.GetBytes(authString);
        return Convert.ToBase64String(authBytes);
    }
}