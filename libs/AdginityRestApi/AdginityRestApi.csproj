<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
      <PackageReference Include="Refit" Version="8.0.0" />
      <PackageReference Include="Refit.HttpClientFactory" Version="8.0.0" />
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\..\Modules\WingCore\WingCore.domain\WingCore.domain.csproj" />
      <ProjectReference Include="..\ObjectDeAndSerialize\ObjectDeAndSerialize.csproj" />
    </ItemGroup>

</Project>
