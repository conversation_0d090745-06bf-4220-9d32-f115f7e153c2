using AdginityRestApi.Output.cs;
using Refit;
using WingCore.domain.Common.Configurations;

namespace AdginityRestApi;

public interface IAdfinityClient
{
    Task<IApiResponse<_v3_companies_OUTPUT_ITEM>?> GetCompany(int companyNummer, ConfigurationEasiAdfinityRestApi config);
    Task<IApiResponse<string>?> SendCompany(_v3_companies_INPUT_ITEM companyRequest, ConfigurationEasiAdfinityRestApi config);
    Task<IApiResponse?> UpdateCompany(_v3_companies_INPUT_ITEM companyRequest, ConfigurationEasiAdfinityRestApi config);

    Task<IApiResponse<string>?> SendInvoiceEntries(_v1_accountingEntriesBulk_INPUT_ITEM_entries request, ConfigurationEasiAdfinityRestApi config);
    Task<IApiResponse<string>?> SendPsmDocument(PsmDocument request, ConfigurationEasiAdfinityRestApi config);
    Task<IApiResponse<string>?> SendProduct(ProductRequest request, ConfigurationEasiAdfinityRestApi config);
}