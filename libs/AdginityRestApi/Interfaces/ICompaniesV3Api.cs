// <auto-generated>
//     This code was generated by Refitter.
// </auto-generated>


using Refit;
using System.Collections.Generic;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

#nullable enable annotations

namespace AdginityRestApi
{
    /// <summary>Get a collection of companies</summary>
    [System.CodeDom.Compiler.GeneratedCode("Refitter", "*******")]
    public partial interface ICompaniesV3Api
    {
        /// <summary>Get a collection of companies</summary>
        /// <remarks>Get a collection of companies</remarks>
        /// <param name="adfinity_database">Name of Adfinity database</param>
        /// <param name="adfinity_envir">Code of Adfinity environment</param>
        /// <param name="multisearch">Advanced search on multiple fields. Example: ?multisearch[contains]=easi nivelles</param>
        /// <param name="authorization">Authorization: Basic [user:pw in base64]</param>
        /// <returns>
        /// A <see cref="Task"/> representing the <see cref="IApiResponse"/> instance containing the result:
        /// <list type="table">
        /// <listheader>
        /// <term>Status</term>
        /// <description>Description</description>
        /// </listheader>
        /// <item>
        /// <term>200</term>
        /// <description>OK</description>
        /// </item>
        /// </list>
        /// </returns>
        [Headers("Accept: application/json")]
        [Get("/v3/companies")]
        Task<IApiResponse<_v3_companies_OUTPUT_ITEMS>> CompaniesGET(string multisearch, [Header("adfinity-database")] string adfinity_database, [Header("adfinity-envir")] string adfinity_envir);

        /// <summary>Create a new company</summary>
        /// <remarks>Create a new company</remarks>
        /// <param name="adfinity_database">Name of Adfinity database</param>
        /// <param name="adfinity_envir">Code of Adfinity environment</param>
        /// <returns>
        /// A <see cref="Task"/> representing the <see cref="IApiResponse"/> instance containing the result:
        /// <list type="table">
        /// <listheader>
        /// <term>Status</term>
        /// <description>Description</description>
        /// </listheader>
        /// <item>
        /// <term>201</term>
        /// <description>Created : See \'location\' http header</description>
        /// </item>
        /// </list>
        /// </returns>
        [Headers("Content-Type: All fields")]
        [Post("/v3/companies")]
        Task<IApiResponse<string>> CompaniesPOST([Body] string body, [Header("adfinity-database")] string adfinity_database, [Header("adfinity-envir")] string adfinity_envir);

        /// <summary>Get company for a given {companyNumber}</summary>
        /// <remarks>Get company for a given {companyNumber}</remarks>
        /// <param name="adfinity_database">Name of Adfinity database</param>
        /// <param name="adfinity_envir">Code of Adfinity environment</param>
        /// <param name="companyNumber">company number</param>
        /// <param name="multisearch">Advanced search on multiple fields. Example: ?multisearch[contains]=easi nivelles</param>
        /// <returns>
        /// A <see cref="Task"/> representing the <see cref="IApiResponse"/> instance containing the result:
        /// <list type="table">
        /// <listheader>
        /// <term>Status</term>
        /// <description>Description</description>
        /// </listheader>
        /// <item>
        /// <term>200</term>
        /// <description>OK</description>
        /// </item>
        /// </list>
        /// </returns>
        [Headers("Accept: application/json")]
        [Get("/v3/companies/{companyNumber}")]
        Task<IApiResponse<_v3_companies_OUTPUT_ITEM>> CompaniesGET2(int companyNumber, [Header("adfinity-database")] string adfinity_database, [Header("adfinity-envir")] string adfinity_envir);

        /// <summary>Add or update company for a given {companyNumber}</summary>
        /// <remarks>Add or update company for a given {companyNumber}</remarks>
        /// <param name="adfinity_database">Name of Adfinity database</param>
        /// <param name="adfinity_envir">Code of Adfinity environment</param>
        /// <param name="companyNumber">company number</param>
        /// <returns>
        /// A <see cref="Task"/> representing the <see cref="IApiResponse"/> instance containing the result:
        /// <list type="table">
        /// <listheader>
        /// <term>Status</term>
        /// <description>Description</description>
        /// </listheader>
        /// <item>
        /// <term>201</term>
        /// <description>Created : See \'location\' http header</description>
        /// </item>
        /// <item>
        /// <term>204</term>
        /// <description>Updated : See \'location\' http header</description>
        /// </item>
        /// </list>
        /// </returns>
        [Headers("Content-Type: application/json")]
        [Put("/v3/companies/{companyNumber}")]
        Task<IApiResponse<string>> CompaniesPUT(int companyNumber, [Body] string body, [Header("adfinity-database")] string adfinity_database, [Header("adfinity-envir")] string adfinity_envir);

        /// <summary>Delete company for a given {companyNumber}</summary>
        /// <remarks>Delete company for a given {companyNumber}</remarks>
        /// <param name="adfinity_database">Name of Adfinity database</param>
        /// <param name="adfinity_envir">Code of Adfinity environment</param>
        /// <param name="companyNumber">company number</param>
        /// <returns>
        /// A <see cref="Task"/> representing the <see cref="IApiResponse"/> instance containing the result:
        /// <list type="table">
        /// <listheader>
        /// <term>Status</term>
        /// <description>Description</description>
        /// </listheader>
        /// <item>
        /// <term>204</term>
        /// <description> Resource deleted</description>
        /// </item>
        /// </list>
        /// </returns>
        [Delete("/v3/companies/{companyNumber}")]
        Task<IApiResponse> CompaniesDELETE(int companyNumber, [Header("adfinity-database")] string adfinity_database, [Header("adfinity-envir")] string adfinity_envir1);
    }

}