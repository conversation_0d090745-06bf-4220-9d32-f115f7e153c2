// <auto-generated>
//     This code was generated by Refitter.
// </auto-generated>


using Refit;
using System.Collections.Generic;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

#nullable enable annotations

namespace AdginityRestApi
{
    public partial interface IPsmDocumentV2Api
    {

        [Headers("Content-Type: All fields")]
        [Post("/v2/psmEntries")]
        Task<IApiResponse<string>> PsmEntriesV2([Body] string body, [Header("adfinity-database")] string adfinity_database, [Header("adfinity-envir")] string adfinity_envir);
    }

}