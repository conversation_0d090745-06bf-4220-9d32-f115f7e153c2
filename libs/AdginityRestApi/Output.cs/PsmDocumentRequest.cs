namespace AdginityRestApi.Output.cs;

using System.Text.Json.Serialization;

public class PsmDocument
{
    [JsonPropertyName("movementType")]
    public string MovementType { get; set; } = string.Empty;
    [JsonPropertyName("number")]
    public int Number { get; set; } = 0;

    [JsonPropertyName("invoiceDate")]
    public string InvoiceDate { get; set; } = string.Empty;

    [JsonPropertyName("year")]
    public string Year { get; set; } = string.Empty;

    [JsonPropertyName("orderInfo")]
    public OrderInfo OrderInfo { get; set; } = new();

    [JsonPropertyName("invoicingInfo")]
    public InvoicingInfo InvoicingInfo { get; set; } = new();

    [JsonPropertyName("externalReference")]
    public string ExternalReference { get; set; } = string.Empty;

    [JsonPropertyName("internalReference")]
    public string InternalReference { get; set; } = string.Empty;

    [JsonPropertyName("analyticalAxis")]
    public AnalyticalAxis AnalyticalAxis { get; set; } = new();

    [JsonPropertyName("movements")]
    public List<Movement> Movements { get; set; } = new();
}

public class InvoicingInfo
{
    [JsonPropertyName("company")]
    public int Company { get; set; }

    [JsonPropertyName("amounts")]
    public InvoicingAmounts Amounts { get; set; } = new();
}

public class InvoicingAmounts
{
    [JsonPropertyName("rate")]
    public decimal Rate { get; set; }

    [JsonPropertyName("discount")]
    public decimal Discount { get; set; }

    [JsonPropertyName("totalBase")]
    public decimal TotalBase { get; set; }
}

public class OrderInfo
{
    [JsonPropertyName("company")]
    public int Company { get; set; }
}

public class AnalyticalAxis
{
    [JsonPropertyName("axe1")]
    public string Axe1 { get; set; } = string.Empty;

    [JsonPropertyName("axe2")]
    public string Axe2 { get; set; } = string.Empty;
}

public record Movement
{
    [JsonPropertyName("productCode")]
    public string ProductCode { get; set; } = string.Empty;

    [JsonPropertyName("vatCode")]
    public string VatCode { get; set; } = string.Empty;
    
    [JsonPropertyName("vatRate")]
    public double VatRate { get; set; }

    [JsonPropertyName("quantity")]
    public double Quantity { get; set; }

    [JsonPropertyName("orderAmounts")]
    public OrderAmounts OrderAmounts { get; set; } = new();

    [JsonPropertyName("externalReference")]
    public string ExternalReference { get; set; } = string.Empty;

    [JsonPropertyName("externalDescription")]
    public string ExternalDescription { get; set; } = string.Empty;

    [JsonPropertyName("internalDescription")]
    public string InternalDescription { get; set; } = string.Empty;

    [JsonPropertyName("generalAccount")]
    public string GeneralAccount { get; set; } = string.Empty;

    [JsonPropertyName("analyticalAxis")]
    public AnalyticalAxis AnalyticalAxis { get; set; } = new();

    [JsonPropertyName("netPrice")]
    public double NetPrice { get; set; }
}

public class OrderAmounts
{
    [JsonPropertyName("currency")]
    public string Currency { get; set; } = string.Empty;

    [JsonPropertyName("unitPrice")]
    public double UnitPrice { get; set; }
}